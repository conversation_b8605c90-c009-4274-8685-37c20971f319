"use strict";
Object.defineProperty(exports, "__esModule", {
    value: true
});
Object.defineProperty(exports, "ExampleController", {
    enumerable: true,
    get: function() {
        return ExampleController;
    }
});
const _common = require("@nestjs/common");
const _bullmq = require("@nestjs/bullmq");
const _queuenameenum = require("../queue-name.enum");
const _bullmq1 = require("bullmq");
const _agent = require("../../agent");
const _infra = require("../../infra");
const _messages = require("@langchain/core/messages");
function _ts_decorate(decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for(var i = decorators.length - 1; i >= 0; i--)if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
}
function _ts_metadata(k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
}
function _ts_param(paramIndex, decorator) {
    return function(target, key) {
        decorator(target, key, paramIndex);
    };
}
let ExampleController = class ExampleController {
    async enqueue(data) {
        const client = this.redisService.getRawClient();
        await client.publish(`cancel:${data?.threadId}`, 'interrupted');
        // 2. Lấy checkpointId (gọi getState)
        const state = await _agent.workflow.getState({
            configurable: {
                thread_id: data?.threadId
            }
        });
        this.logger.debug(`state = ${JSON.stringify(state, null, 2)}`);
        const checkpointId = state?.config?.configurable?.checkpoint_id;
        this.logger.debug(`checkpointId = ${checkpointId}`);
        const isToolCallMessage = state?.values?.messages?.at(-1) instanceof _messages.AIMessage && state?.values?.messages?.at(-1)?.tool_calls?.length > 0;
        await this.agentQueue.add('example', {
            ...data,
            checkpointId,
            lastToolMessageIds: isToolCallMessage ? state?.values?.messages?.at(-1)?.tool_calls?.map((tc)=>tc.id) : null
        });
        this.logger.log('Job added to queue');
    }
    constructor(agentQueue, redisService){
        this.redisService = redisService;
        this.logger = new _common.Logger(ExampleController.name);
        this.agentQueue = agentQueue;
    }
};
_ts_decorate([
    (0, _common.Post)(),
    _ts_param(0, (0, _common.Body)()),
    _ts_metadata("design:type", Function),
    _ts_metadata("design:paramtypes", [
        Object
    ]),
    _ts_metadata("design:returntype", Promise)
], ExampleController.prototype, "enqueue", null);
ExampleController = _ts_decorate([
    (0, _common.Controller)('example'),
    _ts_param(0, (0, _bullmq.InjectQueue)(_queuenameenum.QueueName.AGENT)),
    _ts_metadata("design:type", Function),
    _ts_metadata("design:paramtypes", [
        typeof _bullmq1.Queue === "undefined" ? Object : _bullmq1.Queue,
        typeof _infra.RedisService === "undefined" ? Object : _infra.RedisService
    ])
], ExampleController);

//# sourceMappingURL=example.controller.js.map