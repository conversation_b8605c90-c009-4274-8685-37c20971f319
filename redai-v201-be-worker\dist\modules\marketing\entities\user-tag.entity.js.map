{"version": 3, "sources": ["../../../../src/modules/marketing/entities/user-tag.entity.ts"], "sourcesContent": ["import { Column, Entity, PrimaryGeneratedColumn } from 'typeorm';\r\n\r\n/**\r\n * Entity đại diện cho bảng user_tags trong cơ sở dữ liệu\r\n */\r\n@Entity('user_tags')\r\nexport class UserTag {\r\n  /**\r\n   * ID của tag\r\n   */\r\n  @PrimaryGeneratedColumn({ name: 'id', type: 'bigint' })\r\n  id: number;\r\n\r\n  /**\r\n   * ID của người dùng\r\n   */\r\n  @Column({ name: 'user_id' })\r\n  userId: number;\r\n\r\n  // Không sử dụng quan hệ với bảng User, chỉ lưu ID\r\n\r\n  /**\r\n   * Tên tag\r\n   */\r\n  @Column({ name: 'name', length: 255, nullable: true })\r\n  name: string;\r\n\r\n  /**\r\n   * Mã màu của tag (định dạng HEX, ví dụ: #FF0000)\r\n   */\r\n  @Column({ name: 'color', length: 7, nullable: true })\r\n  color: string;\r\n\r\n  /**\r\n   * Thời gian tạo (Unix timestamp)\r\n   */\r\n  @Column({ name: 'created_at', type: 'bigint', nullable: true })\r\n  createdAt: number;\r\n\r\n  /**\r\n   * Thời gian cập nhật (Unix timestamp)\r\n   */\r\n  @Column({ name: 'updated_at', type: 'bigint', nullable: true })\r\n  updatedAt: number;\r\n}\r\n"], "names": ["UserTag", "name", "type", "length", "nullable"], "mappings": ";;;;+BAMaA;;;eAAAA;;;yBAN0C;;;;;;;;;;AAMhD,IAAA,AAAMA,UAAN,MAAMA;AAsCb;;;QAlC4BC,MAAM;QAAMC,MAAM;;;;;;QAMlCD,MAAM;;;;;;QAQNA,MAAM;QAAQE,QAAQ;QAAKC,UAAU;;;;;;QAMrCH,MAAM;QAASE,QAAQ;QAAGC,UAAU;;;;;;QAMpCH,MAAM;QAAcC,MAAM;QAAUE,UAAU;;;;;;QAM9CH,MAAM;QAAcC,MAAM;QAAUE,UAAU"}