{"version": 3, "sources": ["../../../../../src/modules/marketing/dto/zalo/official-account-response.dto.ts"], "sourcesContent": ["import { ApiProperty } from '@nestjs/swagger';\r\n\r\n/**\r\n * DTO cho phản hồi thông tin Official Account\r\n */\r\nexport class OfficialAccountResponseDto {\r\n  @ApiProperty({\r\n    description: 'ID của Official Account trong hệ thống',\r\n    example: 1,\r\n  })\r\n  id: number;\r\n\r\n  @ApiProperty({\r\n    description: 'ID của Official Account trên <PERSON>',\r\n    example: '*********',\r\n  })\r\n  oaId: string;\r\n\r\n  @ApiProperty({\r\n    description: 'Tên của Official Account',\r\n    example: 'RedAI Official',\r\n  })\r\n  name: string;\r\n\r\n  @ApiProperty({\r\n    description: 'Mô tả của Official Account',\r\n    example: '<PERSON><PERSON><PERSON> chính thức của RedAI',\r\n    nullable: true,\r\n  })\r\n  description?: string;\r\n\r\n  @ApiProperty({\r\n    description: 'URL avatar của Official Account',\r\n    example: 'https://zalo.me/avatar/*********.jpg',\r\n    nullable: true,\r\n  })\r\n  avatarUrl?: string;\r\n\r\n  @ApiProperty({\r\n    description: 'Tr<PERSON>ng thái kết nối (active, inactive)',\r\n    example: 'active',\r\n  })\r\n  status: string;\r\n\r\n  @ApiProperty({\r\n    description: 'Thời điểm tạo (Unix timestamp)',\r\n    example: *************,\r\n  })\r\n  createdAt: number;\r\n\r\n  @ApiProperty({\r\n    description: 'Thời điểm cập nhật (Unix timestamp)',\r\n    example: *************,\r\n  })\r\n  updatedAt: number;\r\n}\r\n"], "names": ["OfficialAccountResponseDto", "description", "example", "nullable"], "mappings": ";;;;+BAKaA;;;eAAAA;;;yBALe;;;;;;;;;;AAKrB,IAAA,AAAMA,6BAAN,MAAMA;AAkDb;;;QAhDIC,aAAa;QACbC,SAAS;;;;;;QAKTD,aAAa;QACbC,SAAS;;;;;;QAKTD,aAAa;QACbC,SAAS;;;;;;QAKTD,aAAa;QACbC,SAAS;QACTC,UAAU;;;;;;QAKVF,aAAa;QACbC,SAAS;QACTC,UAAU;;;;;;QAKVF,aAAa;QACbC,SAAS;;;;;;QAKTD,aAAa;QACbC,SAAS;;;;;;QAKTD,aAAa;QACbC,SAAS"}