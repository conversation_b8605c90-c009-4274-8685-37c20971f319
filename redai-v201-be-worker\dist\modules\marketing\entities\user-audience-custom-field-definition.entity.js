"use strict";
Object.defineProperty(exports, "__esModule", {
    value: true
});
Object.defineProperty(exports, "UserAudienceCustomFieldDefinition", {
    enumerable: true,
    get: function() {
        return UserAudienceCustomFieldDefinition;
    }
});
const _typeorm = require("typeorm");
function _ts_decorate(decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for(var i = decorators.length - 1; i >= 0; i--)if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
}
function _ts_metadata(k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
}
let UserAudienceCustomFieldDefinition = class UserAudienceCustomFieldDefinition {
};
_ts_decorate([
    (0, _typeorm.PrimaryGeneratedColumn)({
        name: 'id',
        type: 'bigint'
    }),
    _ts_metadata("design:type", Number)
], UserAudienceCustomFieldDefinition.prototype, "id", void 0);
_ts_decorate([
    (0, _typeorm.Column)({
        name: 'field_key',
        length: 100
    }),
    _ts_metadata("design:type", String)
], UserAudienceCustomFieldDefinition.prototype, "fieldKey", void 0);
_ts_decorate([
    (0, _typeorm.Column)({
        name: 'user_id',
        type: 'bigint'
    }),
    _ts_metadata("design:type", Number)
], UserAudienceCustomFieldDefinition.prototype, "userId", void 0);
_ts_decorate([
    (0, _typeorm.Column)({
        name: 'display_name',
        length: 255
    }),
    _ts_metadata("design:type", String)
], UserAudienceCustomFieldDefinition.prototype, "displayName", void 0);
_ts_decorate([
    (0, _typeorm.Column)({
        name: 'data_type',
        length: 50
    }),
    _ts_metadata("design:type", String)
], UserAudienceCustomFieldDefinition.prototype, "dataType", void 0);
_ts_decorate([
    (0, _typeorm.Column)({
        name: 'description',
        type: 'text',
        nullable: true
    }),
    _ts_metadata("design:type", String)
], UserAudienceCustomFieldDefinition.prototype, "description", void 0);
UserAudienceCustomFieldDefinition = _ts_decorate([
    (0, _typeorm.Entity)('audience_user_custom_fields')
], UserAudienceCustomFieldDefinition);

//# sourceMappingURL=user-audience-custom-field-definition.entity.js.map