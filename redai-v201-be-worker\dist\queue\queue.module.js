"use strict";
Object.defineProperty(exports, "__esModule", {
    value: true
});
Object.defineProperty(exports, "QueueModule", {
    enumerable: true,
    get: function() {
        return QueueModule;
    }
});
const _common = require("@nestjs/common");
const _bullmq = require("@nestjs/bullmq");
const _index = require("./index");
const _nestjs = require("@bull-board/nestjs");
const _express = require("@bull-board/express");
const _expressbasicauth = /*#__PURE__*/ _interop_require_default(require("express-basic-auth"));
const _bullMQAdapter = require("@bull-board/api/bullMQAdapter");
const _examplecontroller = require("./example/example.controller");
function _interop_require_default(obj) {
    return obj && obj.__esModule ? obj : {
        default: obj
    };
}
function _ts_decorate(decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for(var i = decorators.length - 1; i >= 0; i--)if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
}
let QueueModule = class QueueModule {
};
QueueModule = _ts_decorate([
    (0, _common.Global)(),
    (0, _common.Module)({
        imports: [
            _bullmq.BullModule.registerQueue({
                name: _index.QueueName.AGENT
            }),
            _bullmq.BullModule.registerQueue({
                name: _index.QueueName.EMAIL_SYSTEM
            }),
            _bullmq.BullModule.registerQueue({
                name: _index.QueueName.EMAIL_MARKETING
            }),
            _nestjs.BullBoardModule.forRoot({
                route: '/queues',
                adapter: _express.ExpressAdapter,
                middleware: (0, _expressbasicauth.default)({
                    challenge: true,
                    users: {
                        admin: 'redai@123'
                    }
                })
            }),
            _nestjs.BullBoardModule.forFeature({
                name: _index.QueueName.AGENT,
                adapter: _bullMQAdapter.BullMQAdapter
            }),
            _nestjs.BullBoardModule.forFeature({
                name: _index.QueueName.EMAIL_SYSTEM,
                adapter: _bullMQAdapter.BullMQAdapter
            }),
            _nestjs.BullBoardModule.forFeature({
                name: _index.QueueName.EMAIL_MARKETING,
                adapter: _bullMQAdapter.BullMQAdapter
            })
        ],
        controllers: [
            _examplecontroller.ExampleController
        ]
    })
], QueueModule);

//# sourceMappingURL=queue.module.js.map