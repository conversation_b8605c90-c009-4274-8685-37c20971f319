"use strict";
Object.defineProperty(exports, "__esModule", {
    value: true
});
function _export(target, all) {
    for(var name in all)Object.defineProperty(target, name, {
        enumerable: true,
        get: Object.getOwnPropertyDescriptor(all, name).get
    });
}
_export(exports, {
    get ConditionType () {
        return ConditionType;
    },
    get FilterConditionDto () {
        return FilterConditionDto;
    },
    get OperatorType () {
        return OperatorType;
    },
    get SegmentCriteriaDto () {
        return SegmentCriteriaDto;
    }
});
const _classvalidator = require("class-validator");
const _swagger = require("@nestjs/swagger");
const _classtransformer = require("class-transformer");
function _ts_decorate(decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for(var i = decorators.length - 1; i >= 0; i--)if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
}
function _ts_metadata(k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
}
var ConditionType = /*#__PURE__*/ function(ConditionType) {
    ConditionType["AND"] = "and";
    ConditionType["OR"] = "or";
    return ConditionType;
}({});
var OperatorType = /*#__PURE__*/ function(OperatorType) {
    OperatorType["EQUALS"] = "equals";
    OperatorType["NOT_EQUALS"] = "not_equals";
    OperatorType["CONTAINS"] = "contains";
    OperatorType["NOT_CONTAINS"] = "not_contains";
    OperatorType["GREATER_THAN"] = "greater_than";
    OperatorType["LESS_THAN"] = "less_than";
    OperatorType["IN"] = "in";
    OperatorType["NOT_IN"] = "not_in";
    OperatorType["EXISTS"] = "exists";
    OperatorType["NOT_EXISTS"] = "not_exists";
    return OperatorType;
}({});
let FilterConditionDto = class FilterConditionDto {
};
_ts_decorate([
    (0, _swagger.ApiProperty)({
        description: 'Tên trường cần lọc',
        example: 'email'
    }),
    (0, _classvalidator.IsNotEmpty)({
        message: 'Tên trường không được để trống'
    }),
    (0, _classvalidator.IsString)({
        message: 'Tên trường phải là chuỗi'
    }),
    _ts_metadata("design:type", String)
], FilterConditionDto.prototype, "field", void 0);
_ts_decorate([
    (0, _swagger.ApiProperty)({
        description: 'Toán tử so sánh',
        enum: OperatorType,
        example: "contains"
    }),
    (0, _classvalidator.IsNotEmpty)({
        message: 'Toán tử không được để trống'
    }),
    (0, _classvalidator.IsIn)(Object.values(OperatorType), {
        message: `Toán tử phải là một trong các giá trị: ${Object.values(OperatorType).join(', ')}`
    }),
    _ts_metadata("design:type", String)
], FilterConditionDto.prototype, "operator", void 0);
_ts_decorate([
    (0, _swagger.ApiProperty)({
        description: 'Giá trị cần so sánh',
        example: 'example.com'
    }),
    (0, _classvalidator.IsOptional)(),
    _ts_metadata("design:type", Object)
], FilterConditionDto.prototype, "value", void 0);
let SegmentCriteriaDto = class SegmentCriteriaDto {
};
_ts_decorate([
    (0, _swagger.ApiProperty)({
        description: 'Loại điều kiện (AND/OR)',
        enum: ConditionType,
        example: "and"
    }),
    (0, _classvalidator.IsNotEmpty)({
        message: 'Loại điều kiện không được để trống'
    }),
    (0, _classvalidator.IsIn)(Object.values(ConditionType), {
        message: `Loại điều kiện phải là một trong các giá trị: ${Object.values(ConditionType).join(', ')}`
    }),
    _ts_metadata("design:type", String)
], SegmentCriteriaDto.prototype, "conditionType", void 0);
_ts_decorate([
    (0, _swagger.ApiProperty)({
        description: 'Danh sách các điều kiện lọc',
        type: [
            FilterConditionDto
        ],
        required: false
    }),
    (0, _classvalidator.IsOptional)(),
    (0, _classvalidator.IsArray)({
        message: 'Danh sách điều kiện phải là mảng'
    }),
    (0, _classvalidator.ValidateNested)({
        each: true
    }),
    (0, _classtransformer.Type)(()=>FilterConditionDto),
    _ts_metadata("design:type", Array)
], SegmentCriteriaDto.prototype, "conditions", void 0);
_ts_decorate([
    (0, _swagger.ApiProperty)({
        description: 'Danh sách các nhóm điều kiện con',
        type: [
            SegmentCriteriaDto
        ],
        required: false
    }),
    (0, _classvalidator.IsOptional)(),
    (0, _classvalidator.IsArray)({
        message: 'Danh sách nhóm điều kiện phải là mảng'
    }),
    (0, _classvalidator.ValidateNested)({
        each: true
    }),
    (0, _classtransformer.Type)(()=>SegmentCriteriaDto),
    _ts_metadata("design:type", Array)
], SegmentCriteriaDto.prototype, "groups", void 0);

//# sourceMappingURL=segment-criteria.dto.js.map