{"version": 3, "sources": ["../../../../src/modules/marketing/entities/zalo-segment.entity.ts"], "sourcesContent": ["import { Column, Entity, PrimaryGeneratedColumn } from 'typeorm';\r\nimport { ZaloSegmentConditionDto } from '../dto/zalo';\r\n\r\n/**\r\n * Entity cho phân đoạn Zalo\r\n */\r\n@Entity('zalo_segments')\r\nexport class ZaloSegment {\r\n  @PrimaryGeneratedColumn()\r\n  id: number;\r\n\r\n  @Column({ name: 'user_id' })\r\n  userId: number;\r\n\r\n  @Column({ name: 'oa_id' })\r\n  oaId: string;\r\n\r\n  @Column()\r\n  name: string;\r\n\r\n  @Column({ nullable: true })\r\n  description?: string;\r\n\r\n  @Column({ type: 'json' })\r\n  conditions: ZaloSegmentConditionDto[];\r\n\r\n  @Column({ name: 'follower_count', default: 0 })\r\n  followerCount: number;\r\n\r\n  @Column({ name: 'created_at', type: 'bigint' })\r\n  createdAt: number;\r\n\r\n  @Column({ name: 'updated_at', type: 'bigint' })\r\n  updatedAt: number;\r\n}\r\n"], "names": ["ZaloSegment", "name", "nullable", "type", "default"], "mappings": ";;;;+BAOaA;;;eAAAA;;;yBAP0C;;;;;;;;;;AAOhD,IAAA,AAAMA,cAAN,MAAMA;AA2Bb;;;;;;;QAvBYC,MAAM;;;;;;QAGNA,MAAM;;;;;;;;;;QAMNC,UAAU;;;;;;QAGVC,MAAM;;;;;;QAGNF,MAAM;QAAkBG,SAAS;;;;;;QAGjCH,MAAM;QAAcE,MAAM;;;;;;QAG1BF,MAAM;QAAcE,MAAM"}