"use strict";
Object.defineProperty(exports, "__esModule", {
    value: true
});
Object.defineProperty(exports, "AudienceResponseDto", {
    enumerable: true,
    get: function() {
        return AudienceResponseDto;
    }
});
const _swagger = require("@nestjs/swagger");
const _customfieldresponsedto = require("./custom-field-response.dto");
const _tagresponsedto = require("../tag/tag-response.dto");
function _ts_decorate(decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for(var i = decorators.length - 1; i >= 0; i--)if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
}
function _ts_metadata(k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
}
let AudienceResponseDto = class AudienceResponseDto {
};
_ts_decorate([
    (0, _swagger.ApiProperty)({
        description: 'ID của audience',
        example: 1
    }),
    _ts_metadata("design:type", Number)
], AudienceResponseDto.prototype, "id", void 0);
_ts_decorate([
    (0, _swagger.ApiProperty)({
        description: 'Email của khách hàng',
        example: '<EMAIL>'
    }),
    _ts_metadata("design:type", String)
], AudienceResponseDto.prototype, "email", void 0);
_ts_decorate([
    (0, _swagger.ApiProperty)({
        description: 'Số điện thoại của khách hàng',
        example: '+84912345678',
        nullable: true
    }),
    _ts_metadata("design:type", Object)
], AudienceResponseDto.prototype, "phone", void 0);
_ts_decorate([
    (0, _swagger.ApiProperty)({
        description: 'Danh sách các trường tùy chỉnh',
        type: [
            _customfieldresponsedto.CustomFieldResponseDto
        ]
    }),
    _ts_metadata("design:type", Array)
], AudienceResponseDto.prototype, "customFields", void 0);
_ts_decorate([
    (0, _swagger.ApiProperty)({
        description: 'Danh sách các tag',
        type: [
            _tagresponsedto.TagResponseDto
        ]
    }),
    _ts_metadata("design:type", Array)
], AudienceResponseDto.prototype, "tags", void 0);
_ts_decorate([
    (0, _swagger.ApiProperty)({
        description: 'Thời gian tạo (Unix timestamp)',
        example: 1619171200
    }),
    _ts_metadata("design:type", Number)
], AudienceResponseDto.prototype, "createdAt", void 0);
_ts_decorate([
    (0, _swagger.ApiProperty)({
        description: 'Thời gian cập nhật (Unix timestamp)',
        example: 1619171200
    }),
    _ts_metadata("design:type", Number)
], AudienceResponseDto.prototype, "updatedAt", void 0);

//# sourceMappingURL=audience-response.dto.js.map