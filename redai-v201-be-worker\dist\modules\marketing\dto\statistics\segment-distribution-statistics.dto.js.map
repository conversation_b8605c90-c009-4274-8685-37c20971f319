{"version": 3, "sources": ["../../../../../src/modules/marketing/dto/statistics/segment-distribution-statistics.dto.ts"], "sourcesContent": ["import { ApiProperty } from '@nestjs/swagger';\r\n\r\n/**\r\n * DTO cho thống kê phân phối của một segment\r\n */\r\nexport class SegmentDistributionDto {\r\n  @ApiProperty({\r\n    description: 'ID của segment',\r\n    example: 1,\r\n  })\r\n  id: number;\r\n\r\n  @ApiProperty({\r\n    description: 'Tên segment',\r\n    example: 'Kh<PERSON>ch hàng tiềm năng',\r\n  })\r\n  name: string;\r\n\r\n  @ApiProperty({\r\n    description: 'Số lượng audience trong segment',\r\n    example: 50,\r\n  })\r\n  count: number;\r\n\r\n  @ApiProperty({\r\n    description: 'Tỷ lệ phần trăm so với tổng số audience (%)',\r\n    example: 33.3,\r\n  })\r\n  percentage: number;\r\n}\r\n\r\n/**\r\n * DTO cho thống kê phân phối segment\r\n */\r\nexport class SegmentDistributionStatisticsDto {\r\n  @ApiProperty({\r\n    description: 'Danh sách phân phối segment',\r\n    type: [SegmentDistributionDto],\r\n  })\r\n  segments: SegmentDistributionDto[];\r\n\r\n  @ApiProperty({\r\n    description: 'Tổng số audience',\r\n    example: 150,\r\n  })\r\n  totalAudiences: number;\r\n\r\n  @ApiProperty({\r\n    description: 'Số lượng audience không thuộc segment nào',\r\n    example: 20,\r\n  })\r\n  unassignedAudiences: number;\r\n\r\n  @ApiProperty({\r\n    description: 'Thời gian cập nhật thống kê (Unix timestamp)',\r\n    example: 1619171200,\r\n  })\r\n  updatedAt: number;\r\n}\r\n"], "names": ["SegmentDistributionDto", "SegmentDistributionStatisticsDto", "description", "example", "type"], "mappings": ";;;;;;;;;;;QAKaA;eAAAA;;QA6BAC;eAAAA;;;yBAlCe;;;;;;;;;;AAKrB,IAAA,AAAMD,yBAAN,MAAMA;AAwBb;;;QAtBIE,aAAa;QACbC,SAAS;;;;;;QAKTD,aAAa;QACbC,SAAS;;;;;;QAKTD,aAAa;QACbC,SAAS;;;;;;QAKTD,aAAa;QACbC,SAAS;;;;AAQN,IAAA,AAAMF,mCAAN,MAAMA;AAwBb;;;QAtBIC,aAAa;QACbE,MAAM;YAACJ;SAAuB;;;;;;QAK9BE,aAAa;QACbC,SAAS;;;;;;QAKTD,aAAa;QACbC,SAAS;;;;;;QAKTD,aAAa;QACbC,SAAS"}