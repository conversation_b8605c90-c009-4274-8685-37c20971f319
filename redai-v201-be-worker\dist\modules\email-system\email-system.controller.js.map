{"version": 3, "sources": ["../../../src/modules/email-system/email-system.controller.ts"], "sourcesContent": ["import { <PERSON>, Post, Body, Get, Logger } from '@nestjs/common';\r\nimport { EmailSystemService } from './email-system.service';\r\nimport { EmailSystemJobDto } from './dto/email-system-job.dto';\r\n\r\n/**\r\n * Controller để test email system\r\n */\r\n@Controller('api/email-system')\r\nexport class EmailSystemController {\r\n  private readonly logger = new Logger(EmailSystemController.name);\r\n\r\n  constructor(private readonly emailSystemService: EmailSystemService) {}\r\n\r\n  /**\r\n   * Test thêm job email vào queue\r\n   */\r\n  @Post('test/add-job')\r\n  async testAddJob(@Body() jobData?: Partial<EmailSystemJobDto>) {\r\n    try {\r\n      // Dữ liệu test mặc định\r\n      const testJobData: EmailSystemJobDto = {\r\n        category: 'test',\r\n        data: {\r\n          name: 'Test User',\r\n          email: '<EMAIL>',\r\n          message: 'This is a test email from worker app',\r\n        },\r\n        to: '<EMAIL>',\r\n        ...jobData,\r\n      };\r\n\r\n      this.logger.log(\r\n        `<PERSON><PERSON> thêm test job vào queue: ${JSON.stringify(testJobData)}`,\r\n      );\r\n\r\n      const jobId = await this.emailSystemService.addEmailJob(testJobData);\r\n\r\n      return {\r\n        success: true,\r\n        message: 'Job đã được thêm vào queue thành công',\r\n        jobId,\r\n        data: testJobData,\r\n      };\r\n    } catch (error) {\r\n      this.logger.error(`Lỗi khi thêm job: ${error.message}`, error.stack);\r\n      return {\r\n        success: false,\r\n        message: 'Lỗi khi thêm job vào queue',\r\n        error: error.message,\r\n      };\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Lấy thông tin queue\r\n   */\r\n  @Get('queue/info')\r\n  async getQueueInfo() {\r\n    try {\r\n      // Thông tin cơ bản về queue\r\n      return {\r\n        success: true,\r\n        message: 'Email system queue đang hoạt động',\r\n        queueName: 'EMAIL_SYSTEM',\r\n        timestamp: new Date().toISOString(),\r\n      };\r\n    } catch (error) {\r\n      this.logger.error(\r\n        `Lỗi khi lấy thông tin queue: ${error.message}`,\r\n        error.stack,\r\n      );\r\n      return {\r\n        success: false,\r\n        message: 'Lỗi khi lấy thông tin queue',\r\n        error: error.message,\r\n      };\r\n    }\r\n  }\r\n}\r\n"], "names": ["EmailSystemController", "test<PERSON>dd<PERSON><PERSON>", "jobData", "testJobData", "category", "data", "name", "email", "message", "to", "logger", "log", "JSON", "stringify", "jobId", "emailSystemService", "addEmailJob", "success", "error", "stack", "getQueueInfo", "queueName", "timestamp", "Date", "toISOString", "constructor", "<PERSON><PERSON>"], "mappings": ";;;;+BAQaA;;;eAAAA;;;wBARuC;oCACjB;;;;;;;;;;;;;;;AAO5B,IAAA,AAAMA,wBAAN,MAAMA;IAKX;;GAEC,GACD,MACMC,WAAW,AAAQC,OAAoC,EAAE;QAC7D,IAAI;YACF,wBAAwB;YACxB,MAAMC,cAAiC;gBACrCC,UAAU;gBACVC,MAAM;oBACJC,MAAM;oBACNC,OAAO;oBACPC,SAAS;gBACX;gBACAC,IAAI;gBACJ,GAAGP,OAAO;YACZ;YAEA,IAAI,CAACQ,MAAM,CAACC,GAAG,CACb,CAAC,8BAA8B,EAAEC,KAAKC,SAAS,CAACV,cAAc;YAGhE,MAAMW,QAAQ,MAAM,IAAI,CAACC,kBAAkB,CAACC,WAAW,CAACb;YAExD,OAAO;gBACLc,SAAS;gBACTT,SAAS;gBACTM;gBACAT,MAAMF;YACR;QACF,EAAE,OAAOe,OAAO;YACd,IAAI,CAACR,MAAM,CAACQ,KAAK,CAAC,CAAC,kBAAkB,EAAEA,MAAMV,OAAO,EAAE,EAAEU,MAAMC,KAAK;YACnE,OAAO;gBACLF,SAAS;gBACTT,SAAS;gBACTU,OAAOA,MAAMV,OAAO;YACtB;QACF;IACF;IAEA;;GAEC,GACD,MACMY,eAAe;QACnB,IAAI;YACF,4BAA4B;YAC5B,OAAO;gBACLH,SAAS;gBACTT,SAAS;gBACTa,WAAW;gBACXC,WAAW,IAAIC,OAAOC,WAAW;YACnC;QACF,EAAE,OAAON,OAAO;YACd,IAAI,CAACR,MAAM,CAACQ,KAAK,CACf,CAAC,6BAA6B,EAAEA,MAAMV,OAAO,EAAE,EAC/CU,MAAMC,KAAK;YAEb,OAAO;gBACLF,SAAS;gBACTT,SAAS;gBACTU,OAAOA,MAAMV,OAAO;YACtB;QACF;IACF;IAlEAiB,YAAY,AAAiBV,kBAAsC,CAAE;aAAxCA,qBAAAA;aAFZL,SAAS,IAAIgB,cAAM,CAAC1B,sBAAsBM,IAAI;IAEO;AAmExE"}