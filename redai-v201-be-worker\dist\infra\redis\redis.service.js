// src/redis/redis.service.ts
"use strict";
Object.defineProperty(exports, "__esModule", {
    value: true
});
Object.defineProperty(exports, "RedisService", {
    enumerable: true,
    get: function() {
        return RedisService;
    }
});
const _common = require("@nestjs/common");
const _ioredis = /*#__PURE__*/ _interop_require_default(require("ioredis"));
const _config = require("../../config");
const _rediserrors = require("./redis-errors");
function _interop_require_default(obj) {
    return obj && obj.__esModule ? obj : {
        default: obj
    };
}
function _ts_decorate(decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for(var i = decorators.length - 1; i >= 0; i--)if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
}
// Import opossum using require for better compatibility
const CircuitBreaker = require('opossum');
let RedisService = class RedisService {
    async onModuleInit() {
        this.serviceStartTime = Date.now();
        this.initializeHealthMetrics();
        await this.initializeRedisConnection();
        this.initializeCircuitBreaker();
        this.setupHealthMonitoring();
        this.startHealthCheckInterval();
    }
    /**
   * Initialize Redis connection with pooling and retry configuration
   */ async initializeRedisConnection() {
        const redisUrl = _config.env.external.REDIS_URL;
        const redisOptions = {
            // Connection pooling configuration
            lazyConnect: true,
            maxRetriesPerRequest: _config.env.redis.REDIS_RETRY_ATTEMPTS,
            connectTimeout: _config.env.redis.REDIS_CONNECTION_TIMEOUT_MS,
            commandTimeout: _config.env.redis.REDIS_COMMAND_TIMEOUT_MS,
            // Enhanced retry strategy with exponential backoff and jitter
            retryStrategy: (times)=>{
                if (times > _config.env.redis.REDIS_RETRY_ATTEMPTS) {
                    this.logger.error(`Redis connection failed after ${times} attempts`);
                    return null; // Stop retrying
                }
                // Exponential backoff: base * 2^(attempt-1) with jitter
                const baseDelay = _config.env.redis.REDIS_RETRY_DELAY_MS;
                const exponentialDelay = baseDelay * Math.pow(2, times - 1);
                // Add jitter (±20% of delay) to prevent thundering herd
                const jitterRange = exponentialDelay * 0.2;
                const jitter = (Math.random() * 2 - 1) * jitterRange;
                const finalDelay = Math.min(exponentialDelay + jitter, 10000); // Cap at 10 seconds
                this.logger.warn(`Redis connection attempt ${times} failed, retrying in ${Math.round(finalDelay)}ms (base: ${baseDelay}ms, exponential: ${exponentialDelay}ms, jitter: ${Math.round(jitter)}ms)`);
                return Math.max(finalDelay, 100); // Minimum 100ms delay
            },
            // Connection event handlers
            enableReadyCheck: true,
            maxLoadingRetryTime: 5000
        };
        try {
            this.redis = new _ioredis.default(redisUrl, redisOptions);
            await this.setupConnectionEventHandlers();
            await this.redis.connect();
            this.logger.log('Redis connection initialized successfully with pooling');
            this.isHealthy = true;
            this.connectionAttempts = 0;
        } catch (error) {
            this.connectionAttempts++;
            this.isHealthy = false;
            const redisError = _rediserrors.RedisErrorFactory.createFromError(error, 'Redis Connection Initialization', {
                attempt: this.connectionAttempts,
                maxAttempts: this.maxConnectionAttempts
            });
            this.logger.error('Failed to initialize Redis connection:', redisError.toLogObject());
            if (this.connectionAttempts < this.maxConnectionAttempts) {
                // Retry connection after delay
                const retryDelay = _config.env.redis.REDIS_RETRY_DELAY_MS * this.connectionAttempts;
                this.logger.log(`Retrying Redis connection in ${retryDelay}ms...`);
                setTimeout(()=>this.initializeRedisConnection(), retryDelay);
            } else {
                this.logger.error('Max Redis connection attempts reached, service will be unhealthy');
                throw new _rediserrors.RedisConnectionError('Failed to establish Redis connection after maximum attempts', {
                    operation: 'Redis Connection Initialization',
                    attempt: this.connectionAttempts,
                    maxAttempts: this.maxConnectionAttempts,
                    timestamp: Date.now()
                }, error);
            }
        }
    }
    /**
   * Initialize health metrics tracking
   */ initializeHealthMetrics() {
        this.healthMetrics = {
            isHealthy: false,
            connectionStatus: 'initializing',
            connectionAttempts: 0,
            lastPingTime: 0,
            lastPingDuration: 0,
            circuitBreakerState: 'closed',
            operationCounts: {
                xadd: 0,
                xread: 0,
                publish: 0,
                ping: 0
            },
            errorCounts: {
                connection: 0,
                timeout: 0,
                circuit: 0,
                retry: 0
            },
            uptime: 0
        };
    }
    /**
   * Start periodic health check interval
   */ startHealthCheckInterval() {
        // Health check every 30 seconds
        this.healthCheckInterval = setInterval(async ()=>{
            await this.performHealthCheck();
        }, 30000);
        this.logger.log('Health check interval started (30s)');
    }
    /**
   * Perform comprehensive health check
   */ async performHealthCheck() {
        const startTime = Date.now();
        try {
            // Update uptime
            this.healthMetrics.uptime = Date.now() - this.serviceStartTime;
            // Perform ping test
            await this.ping();
            // Update health metrics
            this.lastPingTime = Date.now();
            this.lastPingDuration = this.lastPingTime - startTime;
            this.healthMetrics.lastPingTime = this.lastPingTime;
            this.healthMetrics.lastPingDuration = this.lastPingDuration;
            this.healthMetrics.connectionStatus = this.redis?.status || 'unknown';
            this.healthMetrics.circuitBreakerState = this.circuitBreaker?.opened ? 'open' : this.circuitBreaker?.halfOpen ? 'half-open' : 'closed';
            // Check if health status changed
            const wasHealthy = this.healthMetrics.isHealthy;
            this.healthMetrics.isHealthy = this.isConnectionHealthy();
            if (!wasHealthy && this.healthMetrics.isHealthy) {
                this.logger.log('Redis health check: Service recovered and is now healthy');
            }
            // Log health status periodically (every 5 minutes)
            if (this.lastPingTime % (5 * 60 * 1000) < 30000) {
                this.logger.log('Redis health status:', this.getHealthSummary());
            }
        } catch (error) {
            this.lastPingDuration = Date.now() - startTime;
            this.healthMetrics.lastPingDuration = this.lastPingDuration;
            this.healthMetrics.isHealthy = false;
            this.healthMetrics.errorCounts.connection++;
            this.logger.warn('Redis health check failed:', {
                error: error instanceof Error ? error.message : String(error),
                duration: this.lastPingDuration,
                connectionStatus: this.redis?.status
            });
        }
    }
    /**
   * Get health summary for logging
   */ getHealthSummary() {
        return {
            isHealthy: this.healthMetrics.isHealthy,
            connectionStatus: this.healthMetrics.connectionStatus,
            uptime: Math.round(this.healthMetrics.uptime / 1000) + 's',
            lastPingDuration: this.healthMetrics.lastPingDuration + 'ms',
            circuitBreakerState: this.healthMetrics.circuitBreakerState,
            operationCounts: this.healthMetrics.operationCounts,
            errorCounts: this.healthMetrics.errorCounts
        };
    }
    /**
   * Initialize circuit breaker for Redis operations
   */ initializeCircuitBreaker() {
        const circuitBreakerOptions = {
            timeout: _config.env.redis.REDIS_COMMAND_TIMEOUT_MS,
            errorThresholdPercentage: 50,
            resetTimeout: 30000,
            rollingCountTimeout: 10000,
            rollingCountBuckets: 10,
            name: 'RedisCircuitBreaker',
            group: 'redis-operations'
        };
        // Create circuit breaker for ping operation (health check)
        this.circuitBreaker = new CircuitBreaker(this.executeRedisOperation.bind(this), circuitBreakerOptions);
        // Circuit breaker event handlers
        this.circuitBreaker.on('open', ()=>{
            this.logger.warn('Redis circuit breaker opened - Redis operations will be rejected');
            this.isHealthy = false;
        });
        this.circuitBreaker.on('halfOpen', ()=>{
            this.logger.log('Redis circuit breaker half-open - testing Redis connection');
        });
        this.circuitBreaker.on('close', ()=>{
            this.logger.log('Redis circuit breaker closed - Redis operations resumed');
            this.isHealthy = true;
        });
        this.circuitBreaker.on('fallback', (result)=>{
            this.logger.warn('Redis circuit breaker fallback triggered:', result);
        });
        // Enable fallback for circuit breaker
        this.circuitBreaker.fallback(()=>{
            const context = {
                operation: 'Circuit Breaker Fallback',
                timestamp: Date.now()
            };
            // Track circuit breaker errors
            this.healthMetrics.errorCounts.circuit++;
            throw new _rediserrors.RedisCircuitBreakerError('Redis service unavailable - circuit breaker is open', context);
        });
        this.logger.log('Circuit breaker initialized for Redis operations');
    }
    /**
   * Execute Redis operation with error handling (used by circuit breaker)
   */ async executeRedisOperation(operation) {
        if (!this.redis) {
            throw new Error('Redis client not initialized');
        }
        return await operation();
    }
    /**
   * Execute operation with circuit breaker protection
   */ async executeWithBreaker(operation, operationName = 'Unknown Operation') {
        const startTime = Date.now();
        try {
            const result = await this.circuitBreaker.fire(operation);
            // Log successful operation
            const duration = Date.now() - startTime;
            this.logger.debug(`Circuit breaker operation succeeded: ${operationName}`, {
                operation: operationName,
                duration,
                circuitBreakerState: this.circuitBreaker.stats
            });
            return result;
        } catch (error) {
            const duration = Date.now() - startTime;
            // Create structured error
            const redisError = error instanceof _rediserrors.RedisError ? error : _rediserrors.RedisErrorFactory.createFromError(error, operationName, {
                duration
            });
            // Log with structured information
            this.logger.error(`Circuit breaker operation failed: ${operationName}`, {
                ...redisError.toLogObject(),
                circuitBreakerState: this.circuitBreaker.stats
            });
            throw redisError;
        }
    }
    /**
   * Execute operation with retry logic and exponential backoff
   */ async executeWithRetry(operation, operationName, maxRetries = _config.env.redis.REDIS_RETRY_ATTEMPTS) {
        let lastError;
        const startTime = Date.now();
        for(let attempt = 1; attempt <= maxRetries + 1; attempt++){
            const attemptStartTime = Date.now();
            try {
                const result = await operation();
                if (attempt > 1) {
                    const totalDuration = Date.now() - startTime;
                    this.logger.log(`${operationName} succeeded on attempt ${attempt}`, {
                        operation: operationName,
                        attempt,
                        maxAttempts: maxRetries + 1,
                        totalDuration,
                        finalAttempt: true
                    });
                }
                return result;
            } catch (error) {
                lastError = error;
                const attemptDuration = Date.now() - attemptStartTime;
                // Create structured error for logging
                const redisError = error instanceof _rediserrors.RedisError ? error : _rediserrors.RedisErrorFactory.createFromError(error, operationName, {
                    attempt,
                    maxAttempts: maxRetries + 1,
                    duration: attemptDuration
                });
                // Check if this is a transient error that should be retried
                const isRetryable = redisError.isRetryable && attempt <= maxRetries;
                if (!isRetryable) {
                    const totalDuration = Date.now() - startTime;
                    // Create final error for retry exhaustion
                    const finalError = attempt > maxRetries ? new _rediserrors.RedisRetryExhaustedError(`${operationName} failed after ${attempt} attempts`, {
                        operation: operationName,
                        attempt,
                        maxAttempts: maxRetries + 1,
                        duration: totalDuration,
                        timestamp: Date.now()
                    }, lastError) : redisError;
                    this.logger.error(`${operationName} failed permanently:`, finalError.toLogObject());
                    throw finalError;
                }
                // Calculate delay with exponential backoff and jitter
                const baseDelay = _config.env.redis.REDIS_RETRY_DELAY_MS;
                const exponentialDelay = baseDelay * Math.pow(2, attempt - 1);
                const jitterRange = exponentialDelay * 0.2;
                const jitter = (Math.random() * 2 - 1) * jitterRange;
                const delay = Math.min(exponentialDelay + jitter, 5000); // Cap at 5 seconds for operations
                this.logger.warn(`${operationName} failed on attempt ${attempt}, retrying in ${Math.round(delay)}ms`, {
                    ...redisError.toLogObject(),
                    retryDelay: Math.round(delay),
                    nextAttempt: attempt + 1
                });
                // Wait before retrying
                await new Promise((resolve)=>setTimeout(resolve, Math.max(delay, 50)));
            }
        }
        // This should never be reached, but TypeScript requires it
        throw lastError;
    }
    /**
   * Setup connection event handlers for monitoring and logging
   */ async setupConnectionEventHandlers() {
        this.redis.on('connect', ()=>{
            this.logger.log('Redis client connected successfully');
            this.isHealthy = true;
        });
        this.redis.on('ready', ()=>{
            this.logger.log('Redis client ready for commands');
            this.isHealthy = true;
        });
        this.redis.on('error', (error)=>{
            this.logger.error('Redis connection error:', error);
            this.isHealthy = false;
        });
        this.redis.on('close', ()=>{
            this.logger.warn('Redis connection closed');
            this.isHealthy = false;
        });
        this.redis.on('reconnecting', (delay)=>{
            this.logger.log(`Redis reconnecting in ${delay}ms`);
        });
        this.redis.on('end', ()=>{
            this.logger.warn('Redis connection ended');
            this.isHealthy = false;
        });
    }
    /**
   * Setup periodic health monitoring
   */ setupHealthMonitoring() {
        // Health check every 30 seconds
        setInterval(async ()=>{
            try {
                await this.ping();
                if (!this.isHealthy) {
                    this.logger.log('Redis health check passed, marking as healthy');
                    this.isHealthy = true;
                }
            } catch (error) {
                if (this.isHealthy) {
                    this.logger.error('Redis health check failed, marking as unhealthy:', error);
                    this.isHealthy = false;
                }
            }
        }, 30000);
    }
    /**
   * Execute Redis ping command for health checking
   */ async ping() {
        this.healthMetrics.operationCounts.ping++;
        return this.executeWithBreaker(async ()=>{
            if (!this.redis) {
                throw new Error('Redis client not initialized');
            }
            return this.redis.ping();
        }, 'PING');
    }
    /**
   * Get connection health status
   */ isConnectionHealthy() {
        return this.isHealthy && this.redis?.status === 'ready';
    }
    /**
   * Get connection statistics for monitoring
   */ getConnectionStats() {
        return {
            status: this.redis?.status || 'disconnected',
            isHealthy: this.isHealthy,
            connectionAttempts: this.connectionAttempts,
            uptime: this.redis ? Date.now() - this.redis.createBuiltinCommand?.startTime || 0 : 0
        };
    }
    /**
   * Get comprehensive health metrics
   */ getHealthMetrics() {
        // Update uptime before returning
        this.healthMetrics.uptime = Date.now() - this.serviceStartTime;
        this.healthMetrics.isHealthy = this.isConnectionHealthy();
        this.healthMetrics.connectionStatus = this.redis?.status || 'disconnected';
        this.healthMetrics.connectionAttempts = this.connectionAttempts;
        return {
            ...this.healthMetrics
        };
    }
    /**
   * Get health status for health check endpoints
   */ getHealthStatus() {
        const metrics = this.getHealthMetrics();
        return {
            status: metrics.isHealthy ? 'healthy' : 'unhealthy',
            details: {
                redis: {
                    connectionStatus: metrics.connectionStatus,
                    lastPingDuration: metrics.lastPingDuration,
                    circuitBreakerState: metrics.circuitBreakerState,
                    uptime: Math.round(metrics.uptime / 1000) + 's'
                },
                operations: metrics.operationCounts,
                errors: metrics.errorCounts,
                lastHealthCheck: new Date(metrics.lastPingTime).toISOString()
            }
        };
    }
    /**
   * Execute Redis XADD command with error handling, circuit breaker, and retry logic
   */ async xadd(stream, data) {
        const operationName = `XADD to stream ${stream}`;
        this.healthMetrics.operationCounts.xadd++;
        return this.executeWithBreaker(async ()=>{
            return this.executeWithRetry(async ()=>{
                if (!this.isConnectionHealthy()) {
                    const context = {
                        operation: operationName,
                        stream,
                        data,
                        timestamp: Date.now()
                    };
                    throw new _rediserrors.RedisConnectionError('Redis connection is not healthy', context);
                }
                const result = await this.redis.xadd(stream, '*', ...Object.entries(data).flat());
                this.logger.debug(`Successfully added entry to stream ${stream}: ${result}`, {
                    operation: operationName,
                    stream,
                    entryId: result,
                    dataSize: Object.keys(data).length
                });
                return result;
            }, operationName);
        }, operationName);
    }
    /**
   * Execute Redis XREAD command with error handling, circuit breaker, and retry logic
   */ async xread(stream, lastId = '$') {
        const operationName = `XREAD from stream ${stream}`;
        this.healthMetrics.operationCounts.xread++;
        return this.executeWithBreaker(async ()=>{
            return this.executeWithRetry(async ()=>{
                if (!this.isConnectionHealthy()) {
                    const context = {
                        operation: operationName,
                        stream,
                        data: {
                            lastId
                        },
                        timestamp: Date.now()
                    };
                    throw new _rediserrors.RedisConnectionError('Redis connection is not healthy', context);
                }
                const result = await this.redis.xread('BLOCK', 0, 'STREAMS', stream, lastId);
                this.logger.debug(`Successfully read from stream ${stream} from ${lastId}`, {
                    operation: operationName,
                    stream,
                    lastId,
                    resultCount: result ? result.length : 0
                });
                return result;
            }, operationName);
        }, operationName);
    }
    /**
   * Execute Redis PUBLISH command with error handling, circuit breaker, and retry logic
   */ async publish(channel, message) {
        const operationName = `PUBLISH to channel ${channel}`;
        this.healthMetrics.operationCounts.publish++;
        return this.executeWithBreaker(async ()=>{
            return this.executeWithRetry(async ()=>{
                if (!this.isConnectionHealthy()) {
                    const context = {
                        operation: operationName,
                        channel,
                        data: {
                            messageLength: message.length
                        },
                        timestamp: Date.now()
                    };
                    throw new _rediserrors.RedisConnectionError('Redis connection is not healthy', context);
                }
                const result = await this.redis.publish(channel, message);
                this.logger.debug(`Successfully published to channel ${channel}`, {
                    operation: operationName,
                    channel,
                    messageLength: message.length,
                    subscriberCount: result
                });
                return result;
            }, operationName);
        }, operationName);
    }
    /**
   * Get raw Redis client for advanced operations
   * @deprecated Use specific methods instead for better error handling
   */ getRawClient() {
        if (!this.redis) {
            throw new Error('Redis client not initialized');
        }
        if (!this.isConnectionHealthy()) {
            this.logger.warn('Returning Redis client in unhealthy state - operations may fail');
        }
        return this.redis;
    }
    /**
   * Create a duplicate Redis connection for pub/sub operations
   */ getDuplicateClient() {
        if (!this.redis) {
            throw new Error('Redis client not initialized');
        }
        const duplicate = this.redis.duplicate();
        this.logger.debug('Created duplicate Redis client for pub/sub operations');
        return duplicate;
    }
    /**
   * Graceful shutdown with connection cleanup
   */ async onModuleDestroy() {
        try {
            // Stop health check interval
            if (this.healthCheckInterval) {
                clearInterval(this.healthCheckInterval);
                this.healthCheckInterval = null;
                this.logger.log('Health check interval stopped');
            }
            // Close Redis connection
            if (this.redis) {
                this.logger.log('Closing Redis connection...');
                await this.redis.quit();
                this.logger.log('Redis connection closed successfully');
            }
            // Log final health metrics
            this.logger.log('Final Redis health metrics:', this.getHealthSummary());
        } catch (error) {
            this.logger.error('Error during Redis service shutdown:', error);
            // Force disconnect if graceful quit fails
            if (this.redis) {
                this.redis.disconnect();
            }
        }
    }
    constructor(){
        this.logger = new _common.Logger(RedisService.name);
        this.isHealthy = true;
        this.connectionAttempts = 0;
        this.maxConnectionAttempts = 5;
        this.healthCheckInterval = null;
        this.lastPingTime = 0;
        this.lastPingDuration = 0;
    }
};
RedisService = _ts_decorate([
    (0, _common.Injectable)()
], RedisService);

//# sourceMappingURL=redis.service.js.map