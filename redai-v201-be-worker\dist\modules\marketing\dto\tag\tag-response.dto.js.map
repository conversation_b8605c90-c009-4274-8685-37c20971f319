{"version": 3, "sources": ["../../../../../src/modules/marketing/dto/tag/tag-response.dto.ts"], "sourcesContent": ["import { ApiProperty } from '@nestjs/swagger';\r\n\r\n/**\r\n * DTO cho phản hồi thông tin tag\r\n */\r\nexport class TagResponseDto {\r\n  /**\r\n   * ID của tag\r\n   * @example 1\r\n   */\r\n  @ApiProperty({\r\n    description: 'ID của tag',\r\n    example: 1,\r\n  })\r\n  id: number;\r\n\r\n  /**\r\n   * Tên tag\r\n   * @example \"Khách hàng VIP\"\r\n   */\r\n  @ApiProperty({\r\n    description: 'Tên tag',\r\n    example: 'Khách hàng VIP',\r\n  })\r\n  name: string;\r\n\r\n  /**\r\n   * Mã màu của tag (định dạng HEX)\r\n   * @example \"#FF5733\"\r\n   */\r\n  @ApiProperty({\r\n    description: 'Mã màu của tag (định dạng HEX)',\r\n    example: '#FF5733',\r\n  })\r\n  color: string;\r\n\r\n  /**\r\n   * Thời gian tạo (Unix timestamp)\r\n   * @example 1619171200\r\n   */\r\n  @ApiProperty({\r\n    description: 'Thời gian tạo (Unix timestamp)',\r\n    example: 1619171200,\r\n  })\r\n  createdAt: number;\r\n\r\n  /**\r\n   * Thời gian cập nhật (Unix timestamp)\r\n   * @example 1619171200\r\n   */\r\n  @ApiProperty({\r\n    description: 'Thời gian cập nhật (Unix timestamp)',\r\n    example: 1619171200,\r\n  })\r\n  updatedAt: number;\r\n}\r\n"], "names": ["TagResponseDto", "description", "example"], "mappings": ";;;;+BAKaA;;;eAAAA;;;yBALe;;;;;;;;;;AAKrB,IAAA,AAAMA,iBAAN,MAAMA;AAkDb;;;QA5CIC,aAAa;QACbC,SAAS;;;;;;QASTD,aAAa;QACbC,SAAS;;;;;;QASTD,aAAa;QACbC,SAAS;;;;;;QASTD,aAAa;QACbC,SAAS;;;;;;QASTD,aAAa;QACbC,SAAS"}