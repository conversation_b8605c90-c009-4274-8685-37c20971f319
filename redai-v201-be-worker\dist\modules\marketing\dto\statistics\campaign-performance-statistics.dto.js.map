{"version": 3, "sources": ["../../../../../src/modules/marketing/dto/statistics/campaign-performance-statistics.dto.ts"], "sourcesContent": ["import { ApiProperty } from '@nestjs/swagger';\r\n\r\n/**\r\n * DTO cho thống kê hiệu suất của một campaign\r\n */\r\nexport class CampaignPerformanceDto {\r\n  @ApiProperty({\r\n    description: 'ID của campaign',\r\n    example: 1,\r\n  })\r\n  id: number;\r\n\r\n  @ApiProperty({\r\n    description: 'Tên campaign',\r\n    example: 'Chiến dịch khuyến mãi mùa hè',\r\n  })\r\n  name: string;\r\n\r\n  @ApiProperty({\r\n    description: 'Tổng số người nhận',\r\n    example: 100,\r\n  })\r\n  totalRecipients: number;\r\n\r\n  @ApiProperty({\r\n    description: 'Số lượng đã gửi',\r\n    example: 100,\r\n  })\r\n  sent: number;\r\n\r\n  @ApiProperty({\r\n    description: 'Số lượng đã nhận',\r\n    example: 95,\r\n  })\r\n  delivered: number;\r\n\r\n  @ApiProperty({\r\n    description: 'Số lượng đã mở (chỉ áp dụng cho email)',\r\n    example: 50,\r\n  })\r\n  opened: number;\r\n\r\n  @ApiProperty({\r\n    description: 'Số lượng đã nhấp (chỉ áp dụng cho email)',\r\n    example: 20,\r\n  })\r\n  clicked: number;\r\n\r\n  @ApiProperty({\r\n    description: 'Tỷ lệ mở (%)',\r\n    example: 50,\r\n  })\r\n  openRate: number;\r\n\r\n  @ApiProperty({\r\n    description: 'Tỷ lệ nhấp (%)',\r\n    example: 20,\r\n  })\r\n  clickRate: number;\r\n\r\n  @ApiProperty({\r\n    description: 'Thời gian chạy campaign (Unix timestamp)',\r\n    example: 1619171200,\r\n  })\r\n  runAt: number;\r\n}\r\n\r\n/**\r\n * DTO cho thống kê hiệu suất campaign\r\n */\r\nexport class CampaignPerformanceStatisticsDto {\r\n  @ApiProperty({\r\n    description: 'Danh sách hiệu suất campaign',\r\n    type: [CampaignPerformanceDto],\r\n  })\r\n  campaigns: CampaignPerformanceDto[];\r\n\r\n  @ApiProperty({\r\n    description: 'Tỷ lệ mở trung bình (%)',\r\n    example: 45.5,\r\n  })\r\n  averageOpenRate: number;\r\n\r\n  @ApiProperty({\r\n    description: 'Tỷ lệ nhấp trung bình (%)',\r\n    example: 18.3,\r\n  })\r\n  averageClickRate: number;\r\n\r\n  @ApiProperty({\r\n    description: 'Thời gian cập nhật thống kê (Unix timestamp)',\r\n    example: 1619171200,\r\n  })\r\n  updatedAt: number;\r\n}\r\n"], "names": ["CampaignPerformanceDto", "CampaignPerformanceStatisticsDto", "description", "example", "type"], "mappings": ";;;;;;;;;;;QAKaA;eAAAA;;QAiEAC;eAAAA;;;yBAtEe;;;;;;;;;;AAKrB,IAAA,AAAMD,yBAAN,MAAMA;AA4Db;;;QA1DIE,aAAa;QACbC,SAAS;;;;;;QAKTD,aAAa;QACbC,SAAS;;;;;;QAKTD,aAAa;QACbC,SAAS;;;;;;QAKTD,aAAa;QACbC,SAAS;;;;;;QAKTD,aAAa;QACbC,SAAS;;;;;;QAKTD,aAAa;QACbC,SAAS;;;;;;QAKTD,aAAa;QACbC,SAAS;;;;;;QAKTD,aAAa;QACbC,SAAS;;;;;;QAKTD,aAAa;QACbC,SAAS;;;;;;QAKTD,aAAa;QACbC,SAAS;;;;AAQN,IAAA,AAAMF,mCAAN,MAAMA;AAwBb;;;QAtBIC,aAAa;QACbE,MAAM;YAACJ;SAAuB;;;;;;QAK9BE,aAAa;QACbC,SAAS;;;;;;QAKTD,aAAa;QACbC,SAAS;;;;;;QAKTD,aAAa;QACbC,SAAS"}