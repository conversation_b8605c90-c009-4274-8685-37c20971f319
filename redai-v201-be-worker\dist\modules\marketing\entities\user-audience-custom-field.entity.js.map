{"version": 3, "sources": ["../../../../src/modules/marketing/entities/user-audience-custom-field.entity.ts"], "sourcesContent": ["import { Column, Entity, PrimaryGeneratedColumn } from 'typeorm';\r\n\r\n/**\r\n * Entity đại diện cho bảng user_audience_custom_fields trong cơ sở dữ liệu\r\n * Bảng danh sách trường tùy chỉnh của audience\r\n */\r\n@Entity('user_audience_custom_fields')\r\nexport class UserAudienceCustomField {\r\n  /**\r\n   * ID của custom field\r\n   */\r\n  @PrimaryGeneratedColumn({ name: 'id', type: 'bigint' })\r\n  id: number;\r\n\r\n  /**\r\n   * ID của audience\r\n   */\r\n  @Column({ name: 'audience_id', nullable: true })\r\n  audienceId: number;\r\n\r\n  // Không sử dụng quan hệ với bảng UserAudience, chỉ lưu ID\r\n\r\n  /**\r\n   * Tên trường\r\n   */\r\n  @Column({\r\n    name: 'field_name',\r\n    length: 255,\r\n    nullable: true,\r\n    comment: 'Tên trường',\r\n  })\r\n  fieldName: string;\r\n\r\n  /**\r\n   * Giá trị trường\r\n   */\r\n  @Column({\r\n    name: 'field_value',\r\n    type: 'jsonb',\r\n    nullable: true,\r\n    comment: '<PERSON>i<PERSON> trị',\r\n  })\r\n  fieldValue: any;\r\n\r\n  /**\r\n   * <PERSON><PERSON><PERSON> dữ liệu của trường\r\n   */\r\n  @Column({\r\n    name: 'field_type',\r\n    length: 20,\r\n    nullable: true,\r\n    comment: 'Kiểu giá trị',\r\n  })\r\n  fieldType: string;\r\n\r\n  /**\r\n   * Thời gian tạo (Unix timestamp)\r\n   */\r\n  @Column({\r\n    name: 'created_at',\r\n    type: 'bigint',\r\n    nullable: true,\r\n    comment: 'Thời gian tạo',\r\n  })\r\n  createdAt: number;\r\n\r\n  /**\r\n   * Thời gian cập nhật (Unix timestamp)\r\n   */\r\n  @Column({\r\n    name: 'updated_at',\r\n    type: 'bigint',\r\n    nullable: true,\r\n    comment: 'Thời gian cập nhật',\r\n  })\r\n  updatedAt: number;\r\n}\r\n"], "names": ["UserAudienceCustomField", "name", "type", "nullable", "length", "comment"], "mappings": ";;;;+BAOaA;;;eAAAA;;;yBAP0C;;;;;;;;;;AAOhD,IAAA,AAAMA,0BAAN,MAAMA;AAqEb;;;QAjE4BC,MAAM;QAAMC,MAAM;;;;;;QAMlCD,MAAM;QAAeE,UAAU;;;;;;QASvCF,MAAM;QACNG,QAAQ;QACRD,UAAU;QACVE,SAAS;;;;;;QAQTJ,MAAM;QACNC,MAAM;QACNC,UAAU;QACVE,SAAS;;;;;;QAQTJ,MAAM;QACNG,QAAQ;QACRD,UAAU;QACVE,SAAS;;;;;;QAQTJ,MAAM;QACNC,MAAM;QACNC,UAAU;QACVE,SAAS;;;;;;QAQTJ,MAAM;QACNC,MAAM;QACNC,UAAU;QACVE,SAAS"}