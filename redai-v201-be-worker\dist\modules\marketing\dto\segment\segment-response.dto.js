"use strict";
Object.defineProperty(exports, "__esModule", {
    value: true
});
Object.defineProperty(exports, "SegmentResponseDto", {
    enumerable: true,
    get: function() {
        return SegmentResponseDto;
    }
});
const _swagger = require("@nestjs/swagger");
const _segmentcriteriadto = require("./segment-criteria.dto");
function _ts_decorate(decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for(var i = decorators.length - 1; i >= 0; i--)if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
}
function _ts_metadata(k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
}
let SegmentResponseDto = class SegmentResponseDto {
};
_ts_decorate([
    (0, _swagger.ApiProperty)({
        description: 'ID của segment',
        example: 1
    }),
    _ts_metadata("design:type", Number)
], SegmentResponseDto.prototype, "id", void 0);
_ts_decorate([
    (0, _swagger.ApiProperty)({
        description: 'Tên segment',
        example: 'Khách hàng tiềm năng'
    }),
    _ts_metadata("design:type", String)
], SegmentResponseDto.prototype, "name", void 0);
_ts_decorate([
    (0, _swagger.ApiProperty)({
        description: 'Mô tả segment',
        example: 'Khách hàng có khả năng mua hàng cao'
    }),
    _ts_metadata("design:type", String)
], SegmentResponseDto.prototype, "description", void 0);
_ts_decorate([
    (0, _swagger.ApiProperty)({
        description: 'Điều kiện lọc khách hàng',
        type: _segmentcriteriadto.SegmentCriteriaDto
    }),
    _ts_metadata("design:type", typeof _segmentcriteriadto.SegmentCriteriaDto === "undefined" ? Object : _segmentcriteriadto.SegmentCriteriaDto)
], SegmentResponseDto.prototype, "criteria", void 0);
_ts_decorate([
    (0, _swagger.ApiProperty)({
        description: 'Thời gian tạo (Unix timestamp)',
        example: 1619171200
    }),
    _ts_metadata("design:type", Number)
], SegmentResponseDto.prototype, "createdAt", void 0);
_ts_decorate([
    (0, _swagger.ApiProperty)({
        description: 'Thời gian cập nhật (Unix timestamp)',
        example: 1619171200
    }),
    _ts_metadata("design:type", Number)
], SegmentResponseDto.prototype, "updatedAt", void 0);

//# sourceMappingURL=segment-response.dto.js.map