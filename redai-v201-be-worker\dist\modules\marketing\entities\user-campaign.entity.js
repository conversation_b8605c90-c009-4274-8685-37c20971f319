"use strict";
Object.defineProperty(exports, "__esModule", {
    value: true
});
Object.defineProperty(exports, "UserCampaign", {
    enumerable: true,
    get: function() {
        return UserCampaign;
    }
});
const _typeorm = require("typeorm");
function _ts_decorate(decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for(var i = decorators.length - 1; i >= 0; i--)if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
}
function _ts_metadata(k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
}
let UserCampaign = class UserCampaign {
};
_ts_decorate([
    (0, _typeorm.PrimaryGeneratedColumn)({
        name: 'id',
        type: 'bigint'
    }),
    _ts_metadata("design:type", Number)
], UserCampaign.prototype, "id", void 0);
_ts_decorate([
    (0, _typeorm.Column)({
        name: 'user_id',
        nullable: true,
        comment: 'Mã người dùng'
    }),
    _ts_metadata("design:type", Number)
], UserCampaign.prototype, "userId", void 0);
_ts_decorate([
    (0, _typeorm.Column)({
        name: 'title',
        length: 255,
        nullable: true,
        comment: 'Tiêu đề'
    }),
    _ts_metadata("design:type", String)
], UserCampaign.prototype, "title", void 0);
_ts_decorate([
    (0, _typeorm.Column)({
        name: 'description',
        type: 'text',
        nullable: true,
        comment: 'Mô tả'
    }),
    _ts_metadata("design:type", String)
], UserCampaign.prototype, "description", void 0);
_ts_decorate([
    (0, _typeorm.Column)({
        name: 'platform',
        length: 255,
        nullable: true,
        comment: 'Nền tảng'
    }),
    _ts_metadata("design:type", String)
], UserCampaign.prototype, "platform", void 0);
_ts_decorate([
    (0, _typeorm.Column)({
        name: 'content',
        type: 'text',
        nullable: true,
        comment: 'Nội dung'
    }),
    _ts_metadata("design:type", String)
], UserCampaign.prototype, "content", void 0);
_ts_decorate([
    (0, _typeorm.Column)({
        name: 'server',
        type: 'jsonb',
        nullable: true,
        comment: 'Thông tin máy chủ gửi'
    }),
    _ts_metadata("design:type", Object)
], UserCampaign.prototype, "server", void 0);
_ts_decorate([
    (0, _typeorm.Column)({
        name: 'scheduled_at',
        type: 'bigint',
        nullable: true,
        comment: 'Thời gian dự kiến gửi chiến dịch'
    }),
    _ts_metadata("design:type", Number)
], UserCampaign.prototype, "scheduledAt", void 0);
_ts_decorate([
    (0, _typeorm.Column)({
        name: 'subject',
        length: 255,
        nullable: true,
        comment: 'Nội dung tiêu đề với chiến dịch là email'
    }),
    _ts_metadata("design:type", String)
], UserCampaign.prototype, "subject", void 0);
_ts_decorate([
    (0, _typeorm.Column)({
        name: 'status',
        length: 20,
        nullable: true,
        comment: 'Trạng thái'
    }),
    _ts_metadata("design:type", String)
], UserCampaign.prototype, "status", void 0);
_ts_decorate([
    (0, _typeorm.Column)({
        name: 'created_at',
        type: 'bigint',
        nullable: true,
        comment: 'Ngày tạo'
    }),
    _ts_metadata("design:type", Number)
], UserCampaign.prototype, "createdAt", void 0);
_ts_decorate([
    (0, _typeorm.Column)({
        name: 'updated_at',
        type: 'bigint',
        nullable: true,
        comment: 'Ngày cập nhật'
    }),
    _ts_metadata("design:type", Number)
], UserCampaign.prototype, "updatedAt", void 0);
_ts_decorate([
    (0, _typeorm.Column)({
        name: 'segment_id',
        type: 'bigint',
        nullable: true,
        comment: 'ID của segment'
    }),
    _ts_metadata("design:type", Object)
], UserCampaign.prototype, "segmentId", void 0);
_ts_decorate([
    (0, _typeorm.Column)({
        name: 'audience_ids',
        type: 'jsonb',
        nullable: true,
        comment: 'Danh sách ID của audience'
    }),
    _ts_metadata("design:type", Object)
], UserCampaign.prototype, "audienceIds", void 0);
UserCampaign = _ts_decorate([
    (0, _typeorm.Entity)('user_campaigns')
], UserCampaign);

//# sourceMappingURL=user-campaign.entity.js.map