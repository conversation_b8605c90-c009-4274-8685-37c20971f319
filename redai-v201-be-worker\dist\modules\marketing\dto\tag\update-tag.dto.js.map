{"version": 3, "sources": ["../../../../../src/modules/marketing/dto/tag/update-tag.dto.ts"], "sourcesContent": ["import { IsOptional, IsString, Length, Matches } from 'class-validator';\r\nimport { ApiProperty } from '@nestjs/swagger';\r\n\r\n/**\r\n * DTO cho việc cập nhật tag\r\n */\r\nexport class UpdateTagDto {\r\n  /**\r\n   * Tên tag\r\n   * @example \"Khách hàng VIP\"\r\n   */\r\n  @ApiProperty({\r\n    description: 'Tên tag',\r\n    example: 'Khách hàng VIP',\r\n    required: false,\r\n  })\r\n  @IsOptional()\r\n  @IsString({ message: 'Tên tag phải là chuỗi' })\r\n  @Length(1, 255, { message: 'Tên tag phải từ 1 đến 255 ký tự' })\r\n  name?: string;\r\n\r\n  /**\r\n   * Mã màu của tag (định dạng HEX)\r\n   * @example \"#FF5733\"\r\n   */\r\n  @ApiProperty({\r\n    description: 'Mã màu của tag (định dạng HEX)',\r\n    example: '#FF5733',\r\n    required: false,\r\n  })\r\n  @IsOptional()\r\n  @IsString({ message: 'Mã màu phải là chuỗi' })\r\n  @Matches(/^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$/, {\r\n    message: 'Mã màu phải có định dạng HEX hợp lệ (ví dụ: #FF5733)',\r\n  })\r\n  color?: string;\r\n}\r\n"], "names": ["UpdateTagDto", "description", "example", "required", "message"], "mappings": ";;;;+BAMaA;;;eAAAA;;;gCANyC;yBAC1B;;;;;;;;;;AAKrB,IAAA,AAAMA,eAAN,MAAMA;AA8Bb;;;QAxBIC,aAAa;QACbC,SAAS;QACTC,UAAU;;;;QAGAC,SAAS;;;QACHA,SAAS;;;;;;QAQzBH,aAAa;QACbC,SAAS;QACTC,UAAU;;;;QAGAC,SAAS;;;QAEnBA,SAAS"}