{"version": 3, "sources": ["../../../../src/modules/marketing/entities/google-ads-performance.entity.ts"], "sourcesContent": ["import { Column, Entity, PrimaryGeneratedColumn } from 'typeorm';\r\n\r\n/**\r\n * Entity đại diện cho bảng google_ads_performance trong cơ sở dữ liệu\r\n * Lưu trữ thông tin hiệu suất chiến dịch Google Ads\r\n */\r\n@Entity('google_ads_performance')\r\nexport class GoogleAdsPerformance {\r\n  /**\r\n   * ID của báo cáo hiệu suất trong hệ thống\r\n   */\r\n  @PrimaryGeneratedColumn({ name: 'id', type: 'bigint' })\r\n  id: number;\r\n\r\n  /**\r\n   * ID của người dùng\r\n   */\r\n  @Column({ name: 'user_id', nullable: false, comment: 'ID của người dùng' })\r\n  userId: number;\r\n\r\n  /**\r\n   * ID của chiến dịch trong hệ thống\r\n   */\r\n  @Column({\r\n    name: 'campaign_id',\r\n    nullable: false,\r\n    comment: 'ID của chiến dịch trong hệ thống',\r\n  })\r\n  campaignId: number;\r\n\r\n  /**\r\n   * <PERSON><PERSON><PERSON> của báo cáo (YYYY-MM-DD)\r\n   */\r\n  @Column({\r\n    name: 'date',\r\n    length: 10,\r\n    nullable: false,\r\n    comment: '<PERSON><PERSON><PERSON> của báo cáo (YYYY-MM-DD)',\r\n  })\r\n  date: string;\r\n\r\n  /**\r\n   * Số lần hiển thị\r\n   */\r\n  @Column({\r\n    name: 'impressions',\r\n    type: 'integer',\r\n    nullable: false,\r\n    default: 0,\r\n    comment: 'Số lần hiển thị',\r\n  })\r\n  impressions: number;\r\n\r\n  /**\r\n   * Số lần nhấp chuột\r\n   */\r\n  @Column({\r\n    name: 'clicks',\r\n    type: 'integer',\r\n    nullable: false,\r\n    default: 0,\r\n    comment: 'Số lần nhấp chuột',\r\n  })\r\n  clicks: number;\r\n\r\n  /**\r\n   * Chi phí (micro amount)\r\n   */\r\n  @Column({\r\n    name: 'cost',\r\n    type: 'bigint',\r\n    nullable: false,\r\n    default: 0,\r\n    comment: 'Chi phí (micro amount)',\r\n  })\r\n  cost: number;\r\n\r\n  /**\r\n   * Tỷ lệ nhấp chuột (%)\r\n   */\r\n  @Column({\r\n    name: 'ctr',\r\n    type: 'float',\r\n    nullable: false,\r\n    default: 0,\r\n    comment: 'Tỷ lệ nhấp chuột (%)',\r\n  })\r\n  ctr: number;\r\n\r\n  /**\r\n   * CPC trung bình\r\n   */\r\n  @Column({\r\n    name: 'average_cpc',\r\n    type: 'bigint',\r\n    nullable: false,\r\n    default: 0,\r\n    comment: 'CPC trung bình',\r\n  })\r\n  averageCpc: number;\r\n\r\n  /**\r\n   * Số lượt chuyển đổi\r\n   */\r\n  @Column({\r\n    name: 'conversions',\r\n    type: 'float',\r\n    nullable: false,\r\n    default: 0,\r\n    comment: 'Số lượt chuyển đổi',\r\n  })\r\n  conversions: number;\r\n\r\n  /**\r\n   * Giá trị chuyển đổi\r\n   */\r\n  @Column({\r\n    name: 'conversion_value',\r\n    type: 'float',\r\n    nullable: false,\r\n    default: 0,\r\n    comment: 'Giá trị chuyển đổi',\r\n  })\r\n  conversionValue: number;\r\n\r\n  /**\r\n   * Thời gian tạo (Unix timestamp)\r\n   */\r\n  @Column({\r\n    name: 'created_at',\r\n    type: 'bigint',\r\n    nullable: false,\r\n    comment: 'Thời gian tạo',\r\n  })\r\n  createdAt: number;\r\n}\r\n"], "names": ["GoogleAdsPerformance", "name", "type", "nullable", "comment", "length", "default"], "mappings": ";;;;+BAOaA;;;eAAAA;;;yBAP0C;;;;;;;;;;AAOhD,IAAA,AAAMA,uBAAN,MAAMA;AAgIb;;;QA5H4BC,MAAM;QAAMC,MAAM;;;;;;QAMlCD,MAAM;QAAWE,UAAU;QAAOC,SAAS;;;;;;QAOnDH,MAAM;QACNE,UAAU;QACVC,SAAS;;;;;;QAQTH,MAAM;QACNI,QAAQ;QACRF,UAAU;QACVC,SAAS;;;;;;QAQTH,MAAM;QACNC,MAAM;QACNC,UAAU;QACVG,SAAS;QACTF,SAAS;;;;;;QAQTH,MAAM;QACNC,MAAM;QACNC,UAAU;QACVG,SAAS;QACTF,SAAS;;;;;;QAQTH,MAAM;QACNC,MAAM;QACNC,UAAU;QACVG,SAAS;QACTF,SAAS;;;;;;QAQTH,MAAM;QACNC,MAAM;QACNC,UAAU;QACVG,SAAS;QACTF,SAAS;;;;;;QAQTH,MAAM;QACNC,MAAM;QACNC,UAAU;QACVG,SAAS;QACTF,SAAS;;;;;;QAQTH,MAAM;QACNC,MAAM;QACNC,UAAU;QACVG,SAAS;QACTF,SAAS;;;;;;QAQTH,MAAM;QACNC,MAAM;QACNC,UAAU;QACVG,SAAS;QACTF,SAAS;;;;;;QAQTH,MAAM;QACNC,MAAM;QACNC,UAAU;QACVC,SAAS"}