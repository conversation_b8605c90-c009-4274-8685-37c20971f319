"use strict";
Object.defineProperty(exports, "__esModule", {
    value: true
});
Object.defineProperty(exports, "UserTag", {
    enumerable: true,
    get: function() {
        return UserTag;
    }
});
const _typeorm = require("typeorm");
function _ts_decorate(decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for(var i = decorators.length - 1; i >= 0; i--)if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
}
function _ts_metadata(k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
}
let UserTag = class UserTag {
};
_ts_decorate([
    (0, _typeorm.PrimaryGeneratedColumn)({
        name: 'id',
        type: 'bigint'
    }),
    _ts_metadata("design:type", Number)
], UserTag.prototype, "id", void 0);
_ts_decorate([
    (0, _typeorm.Column)({
        name: 'user_id'
    }),
    _ts_metadata("design:type", Number)
], UserTag.prototype, "userId", void 0);
_ts_decorate([
    (0, _typeorm.Column)({
        name: 'name',
        length: 255,
        nullable: true
    }),
    _ts_metadata("design:type", String)
], UserTag.prototype, "name", void 0);
_ts_decorate([
    (0, _typeorm.Column)({
        name: 'color',
        length: 7,
        nullable: true
    }),
    _ts_metadata("design:type", String)
], UserTag.prototype, "color", void 0);
_ts_decorate([
    (0, _typeorm.Column)({
        name: 'created_at',
        type: 'bigint',
        nullable: true
    }),
    _ts_metadata("design:type", Number)
], UserTag.prototype, "createdAt", void 0);
_ts_decorate([
    (0, _typeorm.Column)({
        name: 'updated_at',
        type: 'bigint',
        nullable: true
    }),
    _ts_metadata("design:type", Number)
], UserTag.prototype, "updatedAt", void 0);
UserTag = _ts_decorate([
    (0, _typeorm.Entity)('user_tags')
], UserTag);

//# sourceMappingURL=user-tag.entity.js.map