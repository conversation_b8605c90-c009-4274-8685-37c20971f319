{"version": 3, "sources": ["../../../../../src/modules/marketing/dto/zalo/message-request.dto.ts"], "sourcesContent": ["import { ApiProperty } from '@nestjs/swagger';\r\nimport {\r\n  IsEnum,\r\n  IsNotEmpty,\r\n  IsOptional,\r\n  IsString,\r\n  ValidateIf,\r\n} from 'class-validator';\r\n\r\n/**\r\n * Enum cho loại tin nhắn\r\n */\r\nexport enum MessageType {\r\n  TEXT = 'text',\r\n  IMAGE = 'image',\r\n  FILE = 'file',\r\n  TEMPLATE = 'template',\r\n}\r\n\r\n/**\r\n * DTO cho việc gửi tin nhắn\r\n */\r\nexport class MessageRequestDto {\r\n  @ApiProperty({\r\n    description: 'ID của người dùng Zalo',\r\n    example: '123456789',\r\n  })\r\n  @IsString()\r\n  @IsNotEmpty()\r\n  userId: string;\r\n\r\n  @ApiProperty({\r\n    description: 'Loại tin nhắn',\r\n    enum: MessageType,\r\n    example: MessageType.TEXT,\r\n  })\r\n  @IsEnum(MessageType)\r\n  @IsNotEmpty()\r\n  messageType: MessageType;\r\n\r\n  @ApiProperty({\r\n    description: 'Nội dung tin nhắn văn bản',\r\n    example: 'Xin chào! Cảm ơn bạn đã quan tâm đến sản phẩm của chúng tôi.',\r\n    required: false,\r\n  })\r\n  @ValidateIf((o) => o.messageType === MessageType.TEXT)\r\n  @IsString()\r\n  @IsNotEmpty()\r\n  message?: string;\r\n\r\n  @ApiProperty({\r\n    description: 'URL của hình ảnh',\r\n    example: 'https://example.com/image.jpg',\r\n    required: false,\r\n  })\r\n  @ValidateIf((o) => o.messageType === MessageType.IMAGE)\r\n  @IsString()\r\n  @IsNotEmpty()\r\n  imageUrl?: string;\r\n\r\n  @ApiProperty({\r\n    description: 'URL của file',\r\n    example: 'https://example.com/document.pdf',\r\n    required: false,\r\n  })\r\n  @ValidateIf((o) => o.messageType === MessageType.FILE)\r\n  @IsString()\r\n  @IsNotEmpty()\r\n  fileUrl?: string;\r\n\r\n  @ApiProperty({\r\n    description: 'ID của template',\r\n    example: 'template123',\r\n    required: false,\r\n  })\r\n  @ValidateIf((o) => o.messageType === MessageType.TEMPLATE)\r\n  @IsString()\r\n  @IsNotEmpty()\r\n  templateId?: string;\r\n\r\n  @ApiProperty({\r\n    description: 'Dữ liệu cho template',\r\n    example: { name: 'Nguyễn Văn A', product: 'RedAI Pro' },\r\n    required: false,\r\n  })\r\n  @ValidateIf((o) => o.messageType === MessageType.TEMPLATE)\r\n  @IsOptional()\r\n  templateData?: Record<string, any>;\r\n}\r\n"], "names": ["MessageRequestDto", "MessageType", "description", "example", "enum", "required", "o", "messageType", "name", "product"], "mappings": ";;;;;;;;;;;QAsBaA;eAAAA;;QAVDC;eAAAA;;;yBAZgB;gCAOrB;;;;;;;;;;AAKA,IAAA,AAAKA,qCAAAA;;;;;WAAAA;;AAUL,IAAA,AAAMD,oBAAN,MAAMA;AAkEb;;;QAhEIE,aAAa;QACbC,SAAS;;;;;;;;QAOTD,aAAa;QACbE,MAAMH;QACNE,OAAO;;;;;;;;QAOPD,aAAa;QACbC,SAAS;QACTE,UAAU;;qCAECC,IAAMA,EAAEC,WAAW;;;;;;;QAM9BL,aAAa;QACbC,SAAS;QACTE,UAAU;;qCAECC,IAAMA,EAAEC,WAAW;;;;;;;QAM9BL,aAAa;QACbC,SAAS;QACTE,UAAU;;qCAECC,IAAMA,EAAEC,WAAW;;;;;;;QAM9BL,aAAa;QACbC,SAAS;QACTE,UAAU;;qCAECC,IAAMA,EAAEC,WAAW;;;;;;;QAM9BL,aAAa;QACbC,SAAS;YAAEK,MAAM;YAAgBC,SAAS;QAAY;QACtDJ,UAAU;;qCAECC,IAAMA,EAAEC,WAAW"}