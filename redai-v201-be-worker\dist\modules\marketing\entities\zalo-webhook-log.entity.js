"use strict";
Object.defineProperty(exports, "__esModule", {
    value: true
});
Object.defineProperty(exports, "ZaloWebhookLog", {
    enumerable: true,
    get: function() {
        return ZaloWebhookLog;
    }
});
const _typeorm = require("typeorm");
function _ts_decorate(decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for(var i = decorators.length - 1; i >= 0; i--)if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
}
function _ts_metadata(k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
}
let ZaloWebhookLog = class ZaloWebhookLog {
};
_ts_decorate([
    (0, _typeorm.PrimaryGeneratedColumn)({
        name: 'id'
    }),
    _ts_metadata("design:type", Number)
], ZaloWebhookLog.prototype, "id", void 0);
_ts_decorate([
    (0, _typeorm.Column)({
        name: 'oa_id',
        length: 50
    }),
    _ts_metadata("design:type", String)
], ZaloWebhookLog.prototype, "oaId", void 0);
_ts_decorate([
    (0, _typeorm.Column)({
        name: 'event_name',
        length: 50
    }),
    _ts_metadata("design:type", String)
], ZaloWebhookLog.prototype, "eventName", void 0);
_ts_decorate([
    (0, _typeorm.Column)({
        name: 'event_id',
        length: 50
    }),
    _ts_metadata("design:type", String)
], ZaloWebhookLog.prototype, "eventId", void 0);
_ts_decorate([
    (0, _typeorm.Column)({
        name: 'data',
        type: 'jsonb'
    }),
    _ts_metadata("design:type", Object)
], ZaloWebhookLog.prototype, "data", void 0);
_ts_decorate([
    (0, _typeorm.Column)({
        name: 'processed',
        default: false
    }),
    _ts_metadata("design:type", Boolean)
], ZaloWebhookLog.prototype, "processed", void 0);
_ts_decorate([
    (0, _typeorm.Column)({
        name: 'timestamp',
        type: 'bigint'
    }),
    _ts_metadata("design:type", Number)
], ZaloWebhookLog.prototype, "timestamp", void 0);
_ts_decorate([
    (0, _typeorm.Column)({
        name: 'created_at',
        type: 'bigint'
    }),
    _ts_metadata("design:type", Number)
], ZaloWebhookLog.prototype, "createdAt", void 0);
ZaloWebhookLog = _ts_decorate([
    (0, _typeorm.Entity)('zalo_webhook_logs')
], ZaloWebhookLog);

//# sourceMappingURL=zalo-webhook-log.entity.js.map