"use strict";
Object.defineProperty(exports, "__esModule", {
    value: true
});
function _export(target, all) {
    for(var name in all)Object.defineProperty(target, name, {
        enumerable: true,
        get: Object.getOwnPropertyDescriptor(all, name).get
    });
}
_export(exports, {
    get SegmentDistributionDto () {
        return SegmentDistributionDto;
    },
    get SegmentDistributionStatisticsDto () {
        return SegmentDistributionStatisticsDto;
    }
});
const _swagger = require("@nestjs/swagger");
function _ts_decorate(decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for(var i = decorators.length - 1; i >= 0; i--)if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
}
function _ts_metadata(k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
}
let SegmentDistributionDto = class SegmentDistributionDto {
};
_ts_decorate([
    (0, _swagger.ApiProperty)({
        description: 'ID của segment',
        example: 1
    }),
    _ts_metadata("design:type", Number)
], SegmentDistributionDto.prototype, "id", void 0);
_ts_decorate([
    (0, _swagger.ApiProperty)({
        description: 'Tên segment',
        example: 'Khách hàng tiềm năng'
    }),
    _ts_metadata("design:type", String)
], SegmentDistributionDto.prototype, "name", void 0);
_ts_decorate([
    (0, _swagger.ApiProperty)({
        description: 'Số lượng audience trong segment',
        example: 50
    }),
    _ts_metadata("design:type", Number)
], SegmentDistributionDto.prototype, "count", void 0);
_ts_decorate([
    (0, _swagger.ApiProperty)({
        description: 'Tỷ lệ phần trăm so với tổng số audience (%)',
        example: 33.3
    }),
    _ts_metadata("design:type", Number)
], SegmentDistributionDto.prototype, "percentage", void 0);
let SegmentDistributionStatisticsDto = class SegmentDistributionStatisticsDto {
};
_ts_decorate([
    (0, _swagger.ApiProperty)({
        description: 'Danh sách phân phối segment',
        type: [
            SegmentDistributionDto
        ]
    }),
    _ts_metadata("design:type", Array)
], SegmentDistributionStatisticsDto.prototype, "segments", void 0);
_ts_decorate([
    (0, _swagger.ApiProperty)({
        description: 'Tổng số audience',
        example: 150
    }),
    _ts_metadata("design:type", Number)
], SegmentDistributionStatisticsDto.prototype, "totalAudiences", void 0);
_ts_decorate([
    (0, _swagger.ApiProperty)({
        description: 'Số lượng audience không thuộc segment nào',
        example: 20
    }),
    _ts_metadata("design:type", Number)
], SegmentDistributionStatisticsDto.prototype, "unassignedAudiences", void 0);
_ts_decorate([
    (0, _swagger.ApiProperty)({
        description: 'Thời gian cập nhật thống kê (Unix timestamp)',
        example: 1619171200
    }),
    _ts_metadata("design:type", Number)
], SegmentDistributionStatisticsDto.prototype, "updatedAt", void 0);

//# sourceMappingURL=segment-distribution-statistics.dto.js.map