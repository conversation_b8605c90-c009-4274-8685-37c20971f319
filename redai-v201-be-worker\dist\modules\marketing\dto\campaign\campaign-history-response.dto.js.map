{"version": 3, "sources": ["../../../../../src/modules/marketing/dto/campaign/campaign-history-response.dto.ts"], "sourcesContent": ["import { ApiProperty } from '@nestjs/swagger';\r\n\r\n/**\r\n * Enum cho các trạng thái gửi\r\n */\r\nexport enum SendStatus {\r\n  PENDING = 'pending',\r\n  SENT = 'sent',\r\n  DELIVERED = 'delivered',\r\n  FAILED = 'failed',\r\n  OPENED = 'opened',\r\n  CLICKED = 'clicked',\r\n}\r\n\r\n/**\r\n * DTO cho phản hồi thông tin lịch sử campaign\r\n */\r\nexport class CampaignHistoryResponseDto {\r\n  /**\r\n   * ID của lịch sử\r\n   * @example 1\r\n   */\r\n  @ApiProperty({\r\n    description: 'ID của lịch sử',\r\n    example: 1,\r\n  })\r\n  id: number;\r\n\r\n  /**\r\n   * ID của campaign\r\n   * @example 1\r\n   */\r\n  @ApiProperty({\r\n    description: 'ID của campaign',\r\n    example: 1,\r\n  })\r\n  campaignId: number;\r\n\r\n  /**\r\n   * ID của audience\r\n   * @example 1\r\n   */\r\n  @ApiProperty({\r\n    description: 'ID của audience',\r\n    example: 1,\r\n  })\r\n  audienceId: number;\r\n\r\n  /**\r\n   * Trạng thái gửi\r\n   * @example \"sent\"\r\n   */\r\n  @ApiProperty({\r\n    description: 'Tr<PERSON>ng thái gửi',\r\n    enum: SendStatus,\r\n    example: SendStatus.SENT,\r\n  })\r\n  status: SendStatus;\r\n\r\n  /**\r\n   * Thời gian gửi (Unix timestamp)\r\n   * @example 1619171200\r\n   */\r\n  @ApiProperty({\r\n    description: 'Thời gian gửi (Unix timestamp)',\r\n    example: 1619171200,\r\n  })\r\n  sentAt: number;\r\n\r\n  /**\r\n   * Thời gian tạo (Unix timestamp)\r\n   * @example 1619171200\r\n   */\r\n  @ApiProperty({\r\n    description: 'Thời gian tạo (Unix timestamp)',\r\n    example: 1619171200,\r\n  })\r\n  createdAt: number;\r\n}\r\n"], "names": ["CampaignHistoryResponseDto", "SendStatus", "description", "example", "enum"], "mappings": ";;;;;;;;;;;QAiBaA;eAAAA;;QAZDC;eAAAA;;;yBALgB;;;;;;;;;;AAKrB,IAAA,AAAKA,oCAAAA;;;;;;;WAAAA;;AAYL,IAAA,AAAMD,6BAAN,MAAMA;AA6Db;;;QAvDIE,aAAa;QACbC,SAAS;;;;;;QASTD,aAAa;QACbC,SAAS;;;;;;QASTD,aAAa;QACbC,SAAS;;;;;;QASTD,aAAa;QACbE,MAAMH;QACNE,OAAO;;;;;;QASPD,aAAa;QACbC,SAAS;;;;;;QASTD,aAAa;QACbC,SAAS"}