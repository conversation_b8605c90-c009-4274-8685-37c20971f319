"use strict";
Object.defineProperty(exports, "__esModule", {
    value: true
});
Object.defineProperty(exports, "GoogleAdsPerformance", {
    enumerable: true,
    get: function() {
        return GoogleAdsPerformance;
    }
});
const _typeorm = require("typeorm");
function _ts_decorate(decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for(var i = decorators.length - 1; i >= 0; i--)if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
}
function _ts_metadata(k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
}
let GoogleAdsPerformance = class GoogleAdsPerformance {
};
_ts_decorate([
    (0, _typeorm.PrimaryGeneratedColumn)({
        name: 'id',
        type: 'bigint'
    }),
    _ts_metadata("design:type", Number)
], GoogleAdsPerformance.prototype, "id", void 0);
_ts_decorate([
    (0, _typeorm.Column)({
        name: 'user_id',
        nullable: false,
        comment: 'ID của người dùng'
    }),
    _ts_metadata("design:type", Number)
], GoogleAdsPerformance.prototype, "userId", void 0);
_ts_decorate([
    (0, _typeorm.Column)({
        name: 'campaign_id',
        nullable: false,
        comment: 'ID của chiến dịch trong hệ thống'
    }),
    _ts_metadata("design:type", Number)
], GoogleAdsPerformance.prototype, "campaignId", void 0);
_ts_decorate([
    (0, _typeorm.Column)({
        name: 'date',
        length: 10,
        nullable: false,
        comment: 'Ngày của báo cáo (YYYY-MM-DD)'
    }),
    _ts_metadata("design:type", String)
], GoogleAdsPerformance.prototype, "date", void 0);
_ts_decorate([
    (0, _typeorm.Column)({
        name: 'impressions',
        type: 'integer',
        nullable: false,
        default: 0,
        comment: 'Số lần hiển thị'
    }),
    _ts_metadata("design:type", Number)
], GoogleAdsPerformance.prototype, "impressions", void 0);
_ts_decorate([
    (0, _typeorm.Column)({
        name: 'clicks',
        type: 'integer',
        nullable: false,
        default: 0,
        comment: 'Số lần nhấp chuột'
    }),
    _ts_metadata("design:type", Number)
], GoogleAdsPerformance.prototype, "clicks", void 0);
_ts_decorate([
    (0, _typeorm.Column)({
        name: 'cost',
        type: 'bigint',
        nullable: false,
        default: 0,
        comment: 'Chi phí (micro amount)'
    }),
    _ts_metadata("design:type", Number)
], GoogleAdsPerformance.prototype, "cost", void 0);
_ts_decorate([
    (0, _typeorm.Column)({
        name: 'ctr',
        type: 'float',
        nullable: false,
        default: 0,
        comment: 'Tỷ lệ nhấp chuột (%)'
    }),
    _ts_metadata("design:type", Number)
], GoogleAdsPerformance.prototype, "ctr", void 0);
_ts_decorate([
    (0, _typeorm.Column)({
        name: 'average_cpc',
        type: 'bigint',
        nullable: false,
        default: 0,
        comment: 'CPC trung bình'
    }),
    _ts_metadata("design:type", Number)
], GoogleAdsPerformance.prototype, "averageCpc", void 0);
_ts_decorate([
    (0, _typeorm.Column)({
        name: 'conversions',
        type: 'float',
        nullable: false,
        default: 0,
        comment: 'Số lượt chuyển đổi'
    }),
    _ts_metadata("design:type", Number)
], GoogleAdsPerformance.prototype, "conversions", void 0);
_ts_decorate([
    (0, _typeorm.Column)({
        name: 'conversion_value',
        type: 'float',
        nullable: false,
        default: 0,
        comment: 'Giá trị chuyển đổi'
    }),
    _ts_metadata("design:type", Number)
], GoogleAdsPerformance.prototype, "conversionValue", void 0);
_ts_decorate([
    (0, _typeorm.Column)({
        name: 'created_at',
        type: 'bigint',
        nullable: false,
        comment: 'Thời gian tạo'
    }),
    _ts_metadata("design:type", Number)
], GoogleAdsPerformance.prototype, "createdAt", void 0);
GoogleAdsPerformance = _ts_decorate([
    (0, _typeorm.Entity)('google_ads_performance')
], GoogleAdsPerformance);

//# sourceMappingURL=google-ads-performance.entity.js.map