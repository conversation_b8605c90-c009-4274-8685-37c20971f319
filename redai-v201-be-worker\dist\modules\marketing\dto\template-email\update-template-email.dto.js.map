{"version": 3, "sources": ["../../../../../src/modules/marketing/dto/template-email/update-template-email.dto.ts"], "sourcesContent": ["import { ApiProperty } from '@nestjs/swagger';\r\nimport {\r\n  IsArray,\r\n  IsNotEmpty,\r\n  IsOptional,\r\n  IsString,\r\n  MaxLength,\r\n} from 'class-validator';\r\n\r\n/**\r\n * DTO para actualizar un template de email existente\r\n */\r\nexport class UpdateTemplateEmailDto {\r\n  @ApiProperty({\r\n    description: 'Nombre del template',\r\n    example: 'Plantilla de bienvenida actualizada',\r\n    maxLength: 255,\r\n    required: false,\r\n  })\r\n  @IsString()\r\n  @IsOptional()\r\n  @MaxLength(255)\r\n  name?: string;\r\n\r\n  @ApiProperty({\r\n    description: 'Asunto del email',\r\n    example: 'Bienvenido a nuestra plataforma - Actualizado',\r\n    maxLength: 255,\r\n    required: false,\r\n  })\r\n  @IsString()\r\n  @IsOptional()\r\n  @MaxLength(255)\r\n  subject?: string;\r\n\r\n  @ApiProperty({\r\n    description: 'Contenido HTML del email',\r\n    example:\r\n      '<h1>Bienvenido</h1><p>Gracias por registrarte en nuestra plataforma actualizada.</p>',\r\n    required: false,\r\n  })\r\n  @IsString()\r\n  @IsOptional()\r\n  content?: string;\r\n\r\n  @ApiProperty({\r\n    description: 'Tags asociados al template',\r\n    example: ['bienvenida', 'registro', 'actualizado'],\r\n    required: false,\r\n  })\r\n  @IsArray()\r\n  @IsOptional()\r\n  tags?: string[];\r\n\r\n  @ApiProperty({\r\n    description: 'Placeholders utilizados en el template',\r\n    example: ['userName', 'companyName', 'date', 'orderNumber'],\r\n    required: false,\r\n    type: [String],\r\n  })\r\n  @IsArray()\r\n  @IsString({ each: true })\r\n  @IsOptional()\r\n  placeholders?: string[];\r\n}\r\n"], "names": ["UpdateTemplateEmailDto", "description", "example", "max<PERSON><PERSON><PERSON>", "required", "type", "String", "each"], "mappings": ";;;;+BAYaA;;;eAAAA;;;yBAZe;gCAOrB;;;;;;;;;;AAKA,IAAA,AAAMA,yBAAN,MAAMA;AAoDb;;;QAlDIC,aAAa;QACbC,SAAS;QACTC,WAAW;QACXC,UAAU;;;;;;;;;QAQVH,aAAa;QACbC,SAAS;QACTC,WAAW;QACXC,UAAU;;;;;;;;;QAQVH,aAAa;QACbC,SACE;QACFE,UAAU;;;;;;;;QAOVH,aAAa;QACbC,SAAS;YAAC;YAAc;YAAY;SAAc;QAClDE,UAAU;;;;;;;;QAOVH,aAAa;QACbC,SAAS;YAAC;YAAY;YAAe;YAAQ;SAAc;QAC3DE,UAAU;QACVC,MAAM;YAACC;SAAO;;;;QAGJC,MAAM"}