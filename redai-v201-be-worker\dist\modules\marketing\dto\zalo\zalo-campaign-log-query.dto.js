"use strict";
Object.defineProperty(exports, "__esModule", {
    value: true
});
function _export(target, all) {
    for(var name in all)Object.defineProperty(target, name, {
        enumerable: true,
        get: Object.getOwnPropertyDescriptor(all, name).get
    });
}
_export(exports, {
    get ZaloCampaignLogQueryDto () {
        return ZaloCampaignLogQueryDto;
    },
    get ZaloCampaignLogStatus () {
        return ZaloCampaignLogStatus;
    }
});
const _swagger = require("@nestjs/swagger");
const _classvalidator = require("class-validator");
const _dto = require("../../../../common/dto");
function _ts_decorate(decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for(var i = decorators.length - 1; i >= 0; i--)if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
}
function _ts_metadata(k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
}
var ZaloCampaignLogStatus = /*#__PURE__*/ function(ZaloCampaignLogStatus) {
    ZaloCampaignLogStatus["PENDING"] = "pending";
    ZaloCampaignLogStatus["SUCCESS"] = "success";
    ZaloCampaignLogStatus["FAILED"] = "failed";
    ZaloCampaignLogStatus["DELETED"] = "deleted";
    ZaloCampaignLogStatus["ALL"] = "all";
    return ZaloCampaignLogStatus;
}({});
let ZaloCampaignLogQueryDto = class ZaloCampaignLogQueryDto extends _dto.QueryDto {
    constructor(){
        super();
        this.sortBy = 'createdAt';
        this.sortDirection = _dto.SortDirection.DESC;
    }
};
_ts_decorate([
    (0, _swagger.ApiProperty)({
        description: 'Lọc theo ID của chiến dịch',
        example: 1,
        required: false
    }),
    (0, _classvalidator.IsOptional)(),
    (0, _classvalidator.IsString)(),
    _ts_metadata("design:type", String)
], ZaloCampaignLogQueryDto.prototype, "campaignId", void 0);
_ts_decorate([
    (0, _swagger.ApiProperty)({
        description: 'Lọc theo ID của người theo dõi',
        example: '123456789',
        required: false
    }),
    (0, _classvalidator.IsOptional)(),
    (0, _classvalidator.IsString)(),
    _ts_metadata("design:type", String)
], ZaloCampaignLogQueryDto.prototype, "followerId", void 0);
_ts_decorate([
    (0, _swagger.ApiProperty)({
        description: 'Lọc theo trạng thái',
        enum: ZaloCampaignLogStatus,
        example: "success",
        required: false
    }),
    (0, _classvalidator.IsOptional)(),
    (0, _classvalidator.IsEnum)(ZaloCampaignLogStatus),
    _ts_metadata("design:type", String)
], ZaloCampaignLogQueryDto.prototype, "status", void 0);

//# sourceMappingURL=zalo-campaign-log-query.dto.js.map