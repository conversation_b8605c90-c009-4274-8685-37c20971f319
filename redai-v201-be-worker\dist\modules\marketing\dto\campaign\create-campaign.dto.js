"use strict";
Object.defineProperty(exports, "__esModule", {
    value: true
});
function _export(target, all) {
    for(var name in all)Object.defineProperty(target, name, {
        enumerable: true,
        get: Object.getOwnPropertyDescriptor(all, name).get
    });
}
_export(exports, {
    get CampaignPlatform () {
        return CampaignPlatform;
    },
    get CreateCampaignDto () {
        return CreateCampaignDto;
    }
});
const _classvalidator = require("class-validator");
const _swagger = require("@nestjs/swagger");
const _classtransformer = require("class-transformer");
const _campaignserverdto = require("./campaign-server.dto");
function _ts_decorate(decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for(var i = decorators.length - 1; i >= 0; i--)if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
}
function _ts_metadata(k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
}
var CampaignPlatform = /*#__PURE__*/ function(CampaignPlatform) {
    CampaignPlatform["EMAIL"] = "email";
    CampaignPlatform["SMS"] = "sms";
    CampaignPlatform["PUSH"] = "push";
    return CampaignPlatform;
}({});
let CreateCampaignDto = class CreateCampaignDto {
};
_ts_decorate([
    (0, _swagger.ApiProperty)({
        description: 'Tiêu đề chiến dịch',
        example: 'Khuyến mãi tháng 5'
    }),
    (0, _classvalidator.IsNotEmpty)({
        message: 'Tiêu đề không được để trống'
    }),
    (0, _classvalidator.IsString)({
        message: 'Tiêu đề phải là chuỗi'
    }),
    _ts_metadata("design:type", String)
], CreateCampaignDto.prototype, "title", void 0);
_ts_decorate([
    (0, _swagger.ApiProperty)({
        description: 'Mô tả chiến dịch',
        example: 'Chiến dịch khuyến mãi dành cho khách hàng VIP',
        required: false
    }),
    (0, _classvalidator.IsOptional)(),
    (0, _classvalidator.IsString)({
        message: 'Mô tả phải là chuỗi'
    }),
    _ts_metadata("design:type", String)
], CreateCampaignDto.prototype, "description", void 0);
_ts_decorate([
    (0, _swagger.ApiProperty)({
        description: 'Nền tảng gửi',
        enum: CampaignPlatform,
        example: "email"
    }),
    (0, _classvalidator.IsNotEmpty)({
        message: 'Nền tảng không được để trống'
    }),
    (0, _classvalidator.IsIn)(Object.values(CampaignPlatform), {
        message: `Nền tảng phải là một trong các giá trị: ${Object.values(CampaignPlatform).join(', ')}`
    }),
    _ts_metadata("design:type", String)
], CreateCampaignDto.prototype, "platform", void 0);
_ts_decorate([
    (0, _swagger.ApiProperty)({
        description: 'Nội dung chiến dịch',
        example: '<p>Xin chào quý khách,</p><p>Chúng tôi xin gửi đến quý khách chương trình khuyến mãi...</p>'
    }),
    (0, _classvalidator.IsNotEmpty)({
        message: 'Nội dung không được để trống'
    }),
    (0, _classvalidator.IsString)({
        message: 'Nội dung phải là chuỗi'
    }),
    _ts_metadata("design:type", String)
], CreateCampaignDto.prototype, "content", void 0);
_ts_decorate([
    (0, _swagger.ApiProperty)({
        description: 'Thông tin máy chủ gửi',
        type: _campaignserverdto.CampaignServerDto
    }),
    (0, _classvalidator.IsNotEmpty)({
        message: 'Thông tin máy chủ không được để trống'
    }),
    (0, _classvalidator.ValidateNested)(),
    (0, _classtransformer.Type)(()=>_campaignserverdto.CampaignServerDto),
    _ts_metadata("design:type", typeof _campaignserverdto.CampaignServerDto === "undefined" ? Object : _campaignserverdto.CampaignServerDto)
], CreateCampaignDto.prototype, "server", void 0);
_ts_decorate([
    (0, _swagger.ApiProperty)({
        description: 'Thời gian dự kiến gửi chiến dịch (Unix timestamp)',
        example: 1619171200,
        required: false
    }),
    (0, _classvalidator.IsOptional)(),
    (0, _classvalidator.IsNumber)({}, {
        message: 'Thời gian dự kiến phải là số'
    }),
    _ts_metadata("design:type", Number)
], CreateCampaignDto.prototype, "scheduledAt", void 0);
_ts_decorate([
    (0, _swagger.ApiProperty)({
        description: 'Tiêu đề email (chỉ áp dụng cho chiến dịch email)',
        example: 'Khuyến mãi đặc biệt dành cho bạn',
        required: false
    }),
    (0, _classvalidator.IsOptional)(),
    (0, _classvalidator.IsString)({
        message: 'Tiêu đề email phải là chuỗi'
    }),
    _ts_metadata("design:type", String)
], CreateCampaignDto.prototype, "subject", void 0);
_ts_decorate([
    (0, _swagger.ApiProperty)({
        description: 'ID của segment',
        example: 1,
        required: false
    }),
    (0, _classvalidator.IsOptional)(),
    (0, _classvalidator.IsNumber)({}, {
        message: 'ID segment phải là số'
    }),
    _ts_metadata("design:type", Number)
], CreateCampaignDto.prototype, "segmentId", void 0);
_ts_decorate([
    (0, _swagger.ApiProperty)({
        description: 'Danh sách ID của audience',
        example: [
            1,
            2,
            3
        ],
        required: false,
        type: [
            Number
        ]
    }),
    (0, _classvalidator.IsOptional)(),
    _ts_metadata("design:type", Array)
], CreateCampaignDto.prototype, "audienceIds", void 0);

//# sourceMappingURL=create-campaign.dto.js.map