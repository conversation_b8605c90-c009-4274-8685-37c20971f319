{"version": 3, "sources": ["../../../../../src/modules/marketing/dto/segment/update-segment.dto.ts"], "sourcesContent": ["import { IsOptional, IsString, ValidateNested } from 'class-validator';\r\nimport { ApiProperty } from '@nestjs/swagger';\r\nimport { Type } from 'class-transformer';\r\nimport { SegmentCriteriaDto } from './segment-criteria.dto';\r\n\r\n/**\r\n * DTO cho việc cập nhật segment\r\n */\r\nexport class UpdateSegmentDto {\r\n  /**\r\n   * Tên segment\r\n   * @example \"Khách hàng tiềm năng\"\r\n   */\r\n  @ApiProperty({\r\n    description: 'Tên segment',\r\n    example: 'Khách hàng tiềm năng',\r\n    required: false,\r\n  })\r\n  @IsOptional()\r\n  @IsString({ message: 'Tên segment phải là chuỗi' })\r\n  name?: string;\r\n\r\n  /**\r\n   * Mô tả segment\r\n   * @example \"Khách hàng có khả năng mua hàng cao\"\r\n   */\r\n  @ApiProperty({\r\n    description: 'Mô tả segment',\r\n    example: 'Khách hàng có khả năng mua hàng cao',\r\n    required: false,\r\n  })\r\n  @IsOptional()\r\n  @IsString({ message: '<PERSON><PERSON> tả phải là chuỗi' })\r\n  description?: string;\r\n\r\n  /**\r\n   * <PERSON>i<PERSON><PERSON> kiện lọc khách hàng\r\n   */\r\n  @ApiProperty({\r\n    description: 'Điều kiện lọc khách hàng',\r\n    type: SegmentCriteriaDto,\r\n    required: false,\r\n  })\r\n  @IsOptional()\r\n  @ValidateNested()\r\n  @Type(() => SegmentCriteriaDto)\r\n  criteria?: SegmentCriteriaDto;\r\n}\r\n"], "names": ["UpdateSegmentDto", "description", "example", "required", "message", "type", "SegmentCriteriaDto"], "mappings": ";;;;+BAQaA;;;eAAAA;;;gCARwC;yBACzB;kCACP;oCACc;;;;;;;;;;AAK5B,IAAA,AAAMA,mBAAN,MAAMA;AAuCb;;;QAjCIC,aAAa;QACbC,SAAS;QACTC,UAAU;;;;QAGAC,SAAS;;;;;;QAQnBH,aAAa;QACbC,SAAS;QACTC,UAAU;;;;QAGAC,SAAS;;;;;;QAOnBH,aAAa;QACbI,MAAMC,sCAAkB;QACxBH,UAAU;;;;oCAIAG,sCAAkB"}