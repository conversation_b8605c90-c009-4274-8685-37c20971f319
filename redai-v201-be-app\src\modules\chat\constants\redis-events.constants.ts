/**
 * Redis Event Patterns for Pub/Sub Communication (Chat Module)
 * 
 * These constants define the event patterns used for Redis pub/sub communication
 * between the backend API and worker services.
 */

/**
 * Event patterns for Redis pub/sub communication between backend and worker
 */
export const REDIS_EVENTS = {
  /**
   * Trigger event to start processing a run
   * Published by: Backend API (Chat Module)
   * Consumed by: Worker Service
   */
  RUN_TRIGGER: 'run.trigger',

  /**
   * Cancel event to stop processing a run
   * Published by: Backend API (Chat Module)
   * Consumed by: Worker Service
   */
  RUN_CANCEL: 'run.cancel',

  /**
   * Status update event for run progress
   * Published by: Worker Service
   * Consumed by: Backend API (optional)
   */
  RUN_STATUS_UPDATE: 'run.status.update',

  /**
   * Completion event when run finishes
   * Published by: Worker Service
   * Consumed by: Backend API (optional)
   */
  RUN_COMPLETED: 'run.completed',

  /**
   * Error event when run fails
   * Published by: Worker Service
   * Consumed by: Backend API (optional)
   */
  RUN_ERROR: 'run.error',
} as const;

/**
 * Type for Redis event patterns
 */
export type RedisEventPattern = typeof REDIS_EVENTS[keyof typeof REDIS_EVENTS];

/**
 * Base interface for all Redis events
 */
export interface BaseRedisEvent {
  eventType: RedisEventPattern;
  publishedAt: number;
  version?: string;
}

/**
 * Interface for run trigger event payload
 */
export interface RunTriggerEvent extends BaseRedisEvent {
  eventType: typeof REDIS_EVENTS.RUN_TRIGGER;
  runId: string;
  threadId: string; // LangGraph thread ID for processing
  sessionId: string; // Frontend-generated session ID for stream management
  agentId: string;
  userId: number;
  jwt: string; // JWT token for authenticated API calls
  timestamp: number;
  priority?: 'high' | 'medium' | 'low';
}

/**
 * Interface for run cancel event payload
 */
export interface RunCancelEvent extends BaseRedisEvent {
  eventType: typeof REDIS_EVENTS.RUN_CANCEL;
  threadId: string; // LangGraph thread ID for cancellation
  runId?: string; // Optional, for logging purposes only
  reason: string;
  timestamp: number;
}

/**
 * Interface for run status update event payload
 */
export interface RunStatusUpdateEvent extends BaseRedisEvent {
  eventType: typeof REDIS_EVENTS.RUN_STATUS_UPDATE;
  runId: string;
  status: string;
  timestamp: number;
  metadata?: any;
}

/**
 * Interface for run completed event payload
 */
export interface RunCompletedEvent extends BaseRedisEvent {
  eventType: typeof REDIS_EVENTS.RUN_COMPLETED;
  runId: string;
  result: any;
  timestamp: number;
  duration?: number;
}

/**
 * Interface for run error event payload
 */
export interface RunErrorEvent extends BaseRedisEvent {
  eventType: typeof REDIS_EVENTS.RUN_ERROR;
  runId: string;
  error: {
    message: string;
    code?: string;
    stack?: string;
  };
  timestamp: number;
}

/**
 * Union type for all Redis event payloads
 */
export type RedisEventPayload = 
  | RunTriggerEvent
  | RunCancelEvent
  | RunStatusUpdateEvent
  | RunCompletedEvent
  | RunErrorEvent;
