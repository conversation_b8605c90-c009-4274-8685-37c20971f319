import {
  <PERSON>,
  <PERSON>,
  Lo<PERSON>,
  <PERSON><PERSON>,
  <PERSON>rseUUI<PERSON><PERSON><PERSON>,
  Query,
  <PERSON>q,
  <PERSON><PERSON>,
  UseGuards,
} from '@nestjs/common';
import { Request, Response } from 'express';
import {
  ApiTags,
  ApiOperation,
  ApiParam,
  ApiQuery,
  ApiResponse,
  ApiBearerAuth,
  ApiOkResponse,
  ApiExtraModels,
} from '@nestjs/swagger';
import { JwtUserGuard } from '@modules/auth/guards';
import { ApiResponseDto } from '@/common/response';
import { ApiErrorResponse } from '@common/decorators/api-error-response.decorator';
import { CHAT_ERROR_CODES } from '../exceptions/chat-error-codes';
import { ErrorCode } from '@common/exceptions';
import { AppException } from '@common/exceptions';
import { RedisService } from '@shared/services/redis.service';
import { nanoid } from 'nanoid';

/**
 * Chat Stream Controller
 *
 * Provides Server-Sent Events (SSE) endpoint for streaming real-time chat events
 * from Redis Streams to frontend clients. Each connection receives messages
 * from the current run only, ensuring consistent experience across multiple devices.
 *
 * Key Features:
 * - Current run messages only (filtered by run ID)
 * - Multi-device synchronization with unique consumer groups
 * - Complete stream replay for current run across all devices
 * - Proper error handling and connection cleanup
 * - Real-time streaming with Redis Streams
 */
@ApiTags('Chat')
@ApiBearerAuth('JWT-auth')
@Controller('chat/stream')
@UseGuards(JwtUserGuard)
export class StreamController {
  private readonly logger = new Logger(StreamController.name);

  constructor(private readonly redisService: RedisService) {}

  /**
   * SSE endpoint for streaming chat events to frontend clients
   * @param req Express request object
   * @param res Express response object
   * @param sessionId Session ID for the chat stream
   */
  @Get('events/:sessionId')
  @ApiOperation({
    summary: 'Stream chat events via Server-Sent Events',
    description:
      'Establishes SSE connection to stream real-time chat events for a specific session. Resumes from last unread position.',
  })
  @ApiParam({
    name: 'sessionId',
    description: 'Session ID generated by frontend for stream management',
    example: 'session_1749269123456_abc123',
  })
  @ApiQuery({
    name: 'from',
    description:
      'Optional message ID to resume from. If not provided, starts from latest.',
    example: '1749266123456-0',
    required: false,
  })
  @ApiResponse({
    status: 200,
    description: 'SSE stream established successfully',
    headers: {
      'Content-Type': { description: 'text/event-stream' },
      'Cache-Control': { description: 'no-cache' },
      Connection: { description: 'keep-alive' },
    },
  })
  @ApiErrorResponse(
    CHAT_ERROR_CODES.THREAD_VALIDATION_FAILED,
    CHAT_ERROR_CODES.STREAM_CONNECTION_FAILED,
    ErrorCode.INTERNAL_SERVER_ERROR,
  )
  async streamEvents(
    @Req() req: Request,
    @Res() res: Response,
    @Param('sessionId') sessionId: string,
    @Query('from') fromMessageId?: string,
  ): Promise<void> {
    this.logger.log(`🔥 Starting chat SSE stream for session ${sessionId}`);

    try {
      // Set SSE headers for streaming
      res.set({
        'Content-Type': 'text/event-stream',
        'Cache-Control': 'no-cache, no-transform',
        Connection: 'keep-alive',
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Headers': 'Cache-Control',
        'X-Accel-Buffering': 'no', // Disable nginx buffering
      });

      // Send initial connection confirmation
      const resumeMode = fromMessageId ? 'resume' : 'unconsumed';
      res.write(`event: connected\n`);
      res.write(
        `data: {"sessionId":"${sessionId}","from":"${fromMessageId || 'unconsumed'}","timestamp":${Date.now()},"status":"connected","mode":"${resumeMode}"}\n\n`,
      );

      this.logger.log(
        `✅ Chat SSE headers set for session ${sessionId} (${resumeMode}: ${fromMessageId || 'unconsumed'})`,
      );

      // Use session-based stream key for proper stream isolation
      const streamKey = `agent_stream:${sessionId}`;
      const groupName = `sse-group:${sessionId}`; // Consistent group per session
      const consumerId = `consumer-${Date.now()}-${Math.random().toString(36).substring(2, 11)}`;

      this.logger.log(
        `🔥 Creating consumer group ${groupName} for stream ${streamKey}`,
      );

      try {
        // Check if stream exists and get info
        const streamExists = await this.checkStreamExists(streamKey);

        if (!streamExists) {
          // Stream doesn't exist yet - create group to wait for new messages
          await this.redisService.createConsumerGroup(streamKey, groupName, '$');
          this.logger.log(`✅ Consumer group ${groupName} created for new stream ${streamKey}`);

          // Send waiting status
          res.write(`event: waiting\n`);
          res.write(`data: {"sessionId":"${sessionId}","status":"waiting_for_stream","timestamp":${Date.now()}}\n\n`);
        } else {
          // Stream exists - create group from beginning to read all messages
          await this.redisService.createConsumerGroup(streamKey, groupName, '0');
          this.logger.log(`✅ Consumer group ${groupName} created for existing stream ${streamKey}`);
        }

        // Start consuming messages (no filtering needed - position-based)
        await this.consumeStreamMessages(
          res,
          streamKey,
          groupName,
          consumerId,
          sessionId,
          fromMessageId,
        );
      } catch (error) {
        this.logger.error(
          `💥 Failed to create consumer group ${groupName}:`,
          error,
        );
        if (!res.destroyed) {
          res.write(`event: error\n`);
          res.write(
            `data: {"error":"Failed to initialize stream consumer","sessionId":"${sessionId}"}\n\n`,
          );
          res.end();
        }
        return;
      }

      // Handle client disconnect
      req.on('close', () => {
        this.logger.log(`🔌 Client disconnected from chat session ${sessionId}`);
        this.cleanupConsumerGroup(streamKey, groupName);
      });

      req.on('error', (error) => {
        this.logger.error(`❌ Chat SSE error for session ${sessionId}:`, error);
        this.cleanupConsumerGroup(streamKey, groupName);
        if (!res.destroyed) {
          res.end();
        }
      });
    } catch (error) {
      this.logger.error(
        `💥 Failed to establish chat SSE for session ${sessionId}:`,
        error,
      );
      if (!res.headersSent) {
        throw error;
      }
      // If headers were already sent, end the response gracefully
      if (!res.destroyed) {
        res.write(`event: error\n`);
        res.write(`data: {"error":"Failed to establish SSE connection","sessionId":"${sessionId}"}\n\n`);
        res.end();
      }
    }
    // Note: No finally block - connection should stay alive until stream ends naturally
  }

  /**
   * Consume messages from Redis Stream and send via SSE (stream resume approach)
   * @param res Express response object
   * @param streamKey Redis stream key
   * @param groupName Consumer group name
   * @param consumerId Consumer ID
   * @param sessionId Session ID for logging
   * @param fromMessageId Optional message ID to resume from
   */
  private async consumeStreamMessages(
    res: Response,
    streamKey: string,
    groupName: string,
    consumerId: string,
    sessionId: string,
    _fromMessageId?: string,
  ): Promise<void> {
    this.logger.log(
      `🔄 Starting stream consumption for session ${sessionId} (pub/sub + initial read)`,
    );

    // Create two separate Redis clients: one for xreadgroup, one for pub/sub
    const client = this.redisService.getDuplicateClient();
    const subscriber = this.redisService.getDuplicateClient();

    // Centralized cleanup function to prevent resource leaks
    const cleanup = async () => {
      try {
        this.logger.debug(`🧹 Cleaning up Redis connections for session ${sessionId}`);

        // Check if subscriber is still connected before unsubscribing
        if (subscriber.status === 'ready' || subscriber.status === 'connecting') {
          try {
            await subscriber.unsubscribe(streamKey);
            this.logger.debug(`✅ Unsubscribed from ${streamKey}`);
          } catch (unsubError) {
            this.logger.debug(`⚠️ Unsubscribe failed (connection may already be closed): ${unsubError.message}`);
          }
        }

        // Disconnect subscriber if not already disconnected
        if (subscriber.status !== 'end' && subscriber.status !== 'close') {
          try {
            subscriber.disconnect();
            this.logger.debug(`✅ Subscriber disconnected`);
          } catch (disconnectError) {
            this.logger.debug(`⚠️ Subscriber disconnect failed: ${disconnectError.message}`);
          }
        }

        // Disconnect main client if not already disconnected
        if (client.status !== 'end' && client.status !== 'close') {
          try {
            client.disconnect();
            this.logger.debug(`✅ Main client disconnected`);
          } catch (disconnectError) {
            this.logger.debug(`⚠️ Main client disconnect failed: ${disconnectError.message}`);
          }
        }

        this.logger.debug(`✅ Redis connections cleanup completed for session ${sessionId}`);
      } catch (error) {
        this.logger.warn(`⚠️ Error during Redis cleanup for session ${sessionId}:`, {
          message: error.message,
          name: error.name,
          // Don't log full stack trace for cleanup errors to reduce noise
        });
        // Don't throw - cleanup errors shouldn't affect the application
      }
    };

    // Helper to parse field arrays
    const parseFields = (fields: string[]): Record<string, any> => {
      const obj: Record<string, any> = {};
      for (let i = 0; i < fields.length; i += 2) {
        const key = fields[i];
        try {
          obj[key] = JSON.parse(fields[i + 1]);
        } catch {
          obj[key] = fields[i + 1];
        }
      }
      return obj;
    };

    // Track if we've completed the initial resume read
    let hasCompletedResume = false;
    let streamHasEnded = false;

    // Helper to read messages (resume from start, then read new)
    const readMessages = async (fromStart: boolean = false) => {
      try {
        // Use '0' to read from beginning (resume), '>' to read only new messages
        const startId = fromStart ? '0' : '>';

        const chunks = await client.xreadgroup(
          'GROUP',
          groupName,
          consumerId,
          'COUNT',
          20,
          'STREAMS',
          streamKey,
          startId,
        );

        if (!chunks) {
          // No messages available
          if (fromStart && !hasCompletedResume) {
            this.logger.log(`📖 Resume complete - no historical messages found for session ${sessionId}`);
            hasCompletedResume = true;

            // Send a status message to client indicating resume is complete
            res.write(`event: resume_complete\n`);
            res.write(`data: {"sessionId":"${sessionId}","status":"no_historical_messages","timestamp":${Date.now()}}\n\n`);
          } else if (!fromStart) {
            this.logger.debug(`🔄 No new messages available for session ${sessionId}`);
          }
          return;
        }

        // @ts-ignore
        const [[, messages]] = chunks;

        if (fromStart && !hasCompletedResume) {
          this.logger.log(`📖 Resuming stream for session ${sessionId} - found ${messages.length} historical messages`);
        }

        for (const [id, fields] of messages) {
          const payload = parseFields(fields);

          // Include SSE id header so client could reconnect with Last-Event-ID
          res.write(`id: ${id}\n`);
          res.write(`data: ${JSON.stringify(payload)}\n\n`);

          this.logger.debug(
            `📤 Sent message ${id} for session ${sessionId}: ${payload.event}`,
          );

          if (payload.event === 'llm_stream_end') {
            this.logger.log(`🏁 Stream ended for session ${sessionId}`);
            streamHasEnded = true;
            res.write(`event: end\n`);
            res.write(`data: ${JSON.stringify(payload.data || {})}\n\n`);
            await cleanup(); // 🔧 Use centralized cleanup
            res.end();
            return;
          }

          if (payload.event === 'stream_error') {
            this.logger.error(
              `💥 Stream error for session ${sessionId}:`,
              payload.data,
            );
            res.write(`event: error\n`);
            res.write(`data: ${JSON.stringify(payload.data)}\n\n`);
            // Continue - wait for stream_end
          }
        }

        // If we were reading from start and got messages, continue reading until no more
        if (fromStart && messages.length > 0 && !streamHasEnded) {
          await readMessages(true); // Continue reading from start
        } else if (fromStart) {
          hasCompletedResume = true;
          this.logger.log(`📖 Resume complete for session ${sessionId}`);

          // Send resume complete status to client
          res.write(`event: resume_complete\n`);
          res.write(`data: {"sessionId":"${sessionId}","status":"historical_messages_sent","timestamp":${Date.now()}}\n\n`);
        }

      } catch (error) {
        this.logger.error(
          `💥 Error reading messages for session ${sessionId}:`,
          error,
        );
        if (!res.destroyed) {
          res.write(`event: error\n`);
          res.write(
            `data: {"error":"Stream reading error","sessionId":"${sessionId}"}\n\n`,
          );
          await cleanup(); // 🔧 Cleanup on error
          res.end();
        }
      }
    };

    try {
      // Subscribe to pub/sub notifications on this thread's channel (following working pattern)
      await subscriber.subscribe(streamKey);
      subscriber.on('message', async () => {
        // Only read new messages after resume is complete
        if (hasCompletedResume && !streamHasEnded) {
          await readMessages(false); // Read new messages only
        }
      });

      // Initial resume: read all historical messages first
      this.logger.log(`📖 Starting stream resume for session ${sessionId}`);
      await readMessages(true); // Read from beginning

      // Handle connection cleanup
      res.on('close', async () => {
        this.logger.log(`🛑 SSE connection closed for session ${sessionId}`);
        await cleanup(); // 🔧 Use centralized cleanup
        await this.cleanupConsumerGroup(streamKey, groupName);
      });

      // Keep the connection alive by returning a Promise that never resolves
      // The connection will only close when:
      // 1. Client disconnects (handled by 'close' event)
      // 2. Stream ends naturally (llm_stream_end event calls res.end())
      // 3. An error occurs (handled in readNew function)
      return new Promise<void>(() => {
        // This Promise never resolves, keeping the SSE connection alive
        this.logger.log(`🔄 SSE connection established and listening for thread ${threadId}`);
      });

    } catch (error) {
      this.logger.error(
        `💥 Error setting up stream consumption for thread ${threadId}:`,
        error,
      );
      if (!res.destroyed) {
        res.write(`event: error\n`);
        res.write(`data: {"error":"Setup error","threadId":"${threadId}"}\n\n`);
        await cleanup(); // 🔧 Cleanup on setup error
        res.end();
      }
    }
  }

  /**
   * Check if a Redis stream exists and has messages
   * @param streamKey Redis stream key to check
   * @returns Promise<boolean> True if stream exists and has messages
   */
  private async checkStreamExists(streamKey: string): Promise<boolean> {
    try {
      const client = this.redisService.getDuplicateClient();
      try {
        // Use XLEN to check if stream exists and has messages
        const length = await client.xlen(streamKey);
        client.disconnect();
        return length > 0;
      } catch (error) {
        client.disconnect();
        // Stream doesn't exist
        return false;
      }
    } catch (error) {
      this.logger.warn(`Error checking stream existence for ${streamKey}:`, error.message);
      return false;
    }
  }

  /**
   * Cleanup consumer group on disconnect
   * @param streamKey Redis stream key
   * @param groupName Consumer group name to cleanup
   */
  private async cleanupConsumerGroup(
    streamKey: string,
    groupName: string,
  ): Promise<void> {
    try {
      await this.redisService.deleteConsumerGroup(streamKey, groupName);
      this.logger.log(
        `🧹 Cleaned up consumer group ${groupName} for stream ${streamKey}`,
      );
    } catch (error) {
      this.logger.error(`💥 Error cleaning up consumer group ${groupName}:`, {
        message: error.message,
        stack: error.stack,
        name: error.name,
        groupName,
        streamKey,
        error: error,
      });
      // Don't throw - cleanup errors shouldn't affect the application
    }
  }

  /**
   * Health check endpoint for chat streaming service
   */
  @Get('health')
  @ApiOperation({
    summary: 'Health check for chat streaming service',
    description: 'Returns the health status of the chat streaming service',
  })
  @ApiOkResponse({
    description: 'Service is healthy',
    schema: ApiResponseDto.getSchema({
      type: 'object',
      properties: {
        status: { type: 'string', example: 'healthy' },
        timestamp: { type: 'number', example: 1749269123456 },
        service: { type: 'string', example: 'chat-streaming' },
      },
    }),
  })
  getHealth() {
    const result = {
      status: 'healthy',
      timestamp: Date.now(),
      service: 'chat-streaming',
    };

    return ApiResponseDto.success(result, 'Chat streaming service is healthy');
  }
}
