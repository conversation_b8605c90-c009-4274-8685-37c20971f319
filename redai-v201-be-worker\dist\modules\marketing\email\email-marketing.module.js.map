{"version": 3, "sources": ["../../../../src/modules/marketing/email/email-marketing.module.ts"], "sourcesContent": ["import { Module } from '@nestjs/common';\r\nimport { TypeOrmModule } from '@nestjs/typeorm';\r\nimport { BullModule } from '@nestjs/bullmq';\r\nimport { QueueName } from '../../../queue';\r\nimport { InfraModule } from '../../../infra';\r\n\r\n// Entities\r\nimport { UserCampaign } from '../entities/user-campaign.entity';\r\nimport { UserAudience } from '../entities/user-audience.entity';\r\nimport { UserAudienceCustomField } from '../entities/user-audience-custom-field.entity';\r\nimport { UserCampaignHistory } from '../entities/user-campaign-history.entity';\r\n\r\n// Services\r\nimport {\r\n  EmailMarketingService,\r\n  EmailTemplateService,\r\n  EmailTrackingService,\r\n} from './services';\r\n\r\n// Processor\r\nimport { EmailMarketingProcessor } from './email-marketing.processor';\r\n\r\n// Controllers\r\nimport { EmailTrackingController } from './email-tracking.controller';\r\nimport { EmailMarketingController } from './email-marketing.controller';\r\n\r\n/**\r\n * Module xử lý email marketing\r\n */\r\n@Module({\r\n  imports: [\r\n    // TypeORM entities\r\n    TypeOrmModule.forFeature([\r\n      UserCampaign,\r\n      UserAudience,\r\n      UserAudienceCustomField,\r\n      UserCampaignHistory,\r\n    ]),\r\n\r\n    // Bull queue\r\n    BullModule.registerQueue({\r\n      name: QueueName.EMAIL_MARKETING,\r\n    }),\r\n\r\n    // Infrastructure module (Redis, etc.)\r\n    InfraModule,\r\n  ],\r\n  providers: [\r\n    EmailMarketingService,\r\n    EmailTemplateService,\r\n    EmailTrackingService,\r\n    EmailMarketingProcessor,\r\n  ],\r\n  controllers: [EmailTrackingController, EmailMarketingController],\r\n  exports: [EmailMarketingService, EmailTemplateService, EmailTrackingService],\r\n})\r\nexport class EmailMarketingModule {}\r\n"], "names": ["EmailMarketingModule", "imports", "TypeOrmModule", "forFeature", "UserCampaign", "UserAudience", "UserAudienceCustomField", "UserCampaignHistory", "BullModule", "registerQueue", "name", "QueueName", "EMAIL_MARKETING", "InfraModule", "providers", "EmailMarketingService", "EmailTemplateService", "EmailTrackingService", "EmailMarketingProcessor", "controllers", "EmailTrackingController", "EmailMarketingController", "exports"], "mappings": ";;;;+BAwDaA;;;eAAAA;;;wBAxDU;yBACO;wBACH;uBACD;uBACE;oCAGC;oCACA;+CACW;2CACJ;0BAO7B;yCAGiC;yCAGA;0CACC;;;;;;;AAgClC,IAAA,AAAMA,uBAAN,MAAMA;AAAsB;;;QA1BjCC,SAAS;YACP,mBAAmB;YACnBC,sBAAa,CAACC,UAAU,CAAC;gBACvBC,gCAAY;gBACZC,gCAAY;gBACZC,sDAAuB;gBACvBC,8CAAmB;aACpB;YAED,aAAa;YACbC,kBAAU,CAACC,aAAa,CAAC;gBACvBC,MAAMC,gBAAS,CAACC,eAAe;YACjC;YAEA,sCAAsC;YACtCC,kBAAW;SACZ;QACDC,WAAW;YACTC,+BAAqB;YACrBC,8BAAoB;YACpBC,8BAAoB;YACpBC,gDAAuB;SACxB;QACDC,aAAa;YAACC,gDAAuB;YAAEC,kDAAwB;SAAC;QAChEC,SAAS;YAACP,+BAAqB;YAAEC,8BAAoB;YAAEC,8BAAoB;SAAC"}