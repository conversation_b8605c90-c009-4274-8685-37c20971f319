{"version": 3, "sources": ["../../../../src/shared/services/sms/vonage-provider.service.ts"], "sourcesContent": ["import { Injectable } from '@nestjs/common';\r\nimport { ConfigService } from '@nestjs/config';\r\nimport { BaseSmsProvider } from './base-sms-provider.service';\r\nimport {\r\n  SmsResponse,\r\n  BulkSmsResponse,\r\n  MessageStatusResponse,\r\n  ConnectionTestResponse,\r\n  MessageStatus,\r\n} from './sms-provider.interface';\r\n\r\n// Importer le client Vonage\r\nlet Vonage;\r\ntry {\r\n  Vonage = require('@vonage/server-sdk').default;\r\n} catch (e) {\r\n  // Vonage n'est pas installé, nous le gérerons dans le constructeur\r\n}\r\n\r\n/**\r\n * Interface pour la configuration de Vonage\r\n */\r\nexport interface VonageConfig {\r\n  /**\r\n   * Clé API Vonage\r\n   */\r\n  apiKey: string;\r\n\r\n  /**\r\n   * Secret API Vonage\r\n   */\r\n  apiSecret: string;\r\n\r\n  /**\r\n   * Nom de l'expéditeur par défaut\r\n   */\r\n  from?: string;\r\n}\r\n\r\n/**\r\n * Service d'intégration avec l'API Vonage (anciennement Nexmo)\r\n */\r\n@Injectable()\r\nexport class VonageProvider extends BaseSmsProvider {\r\n  readonly providerName = 'Vonage';\r\n\r\n  private client: any;\r\n  private readonly apiKey: string;\r\n  private readonly apiSecret: string;\r\n  private readonly defaultFrom: string;\r\n\r\n  constructor(private readonly configService: ConfigService) {\r\n    super('VonageProvider');\r\n\r\n    // Charger la configuration depuis les variables d'environnement ou utiliser des valeurs par défaut\r\n    this.apiKey = this.configService.get<string>('VONAGE_API_KEY') || '';\r\n    this.apiSecret = this.configService.get<string>('VONAGE_API_SECRET') || '';\r\n    this.defaultFrom =\r\n      this.configService.get<string>('VONAGE_FROM') || 'Vonage';\r\n\r\n    // Vérifier si Vonage est installé\r\n    if (!Vonage) {\r\n      this.logger.warn(\r\n        'Le package \"@vonage/server-sdk\" n\\'est pas installé. Veuillez l\\'installer avec \"npm install @vonage/server-sdk\"',\r\n      );\r\n    } else {\r\n      // Initialiser le client Vonage\r\n      this.initClient(this.apiKey, this.apiSecret);\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Initialise le client Vonage avec les identifiants fournis\r\n   * @param apiKey Clé API Vonage\r\n   * @param apiSecret Secret API Vonage\r\n   */\r\n  private initClient(apiKey: string, apiSecret: string): void {\r\n    if (Vonage && apiKey && apiSecret) {\r\n      try {\r\n        this.client = new Vonage({\r\n          apiKey,\r\n          apiSecret,\r\n        });\r\n        this.logger.log('Client Vonage initialisé avec succès');\r\n      } catch (error) {\r\n        this.logger.error(\r\n          `Erreur lors de l'initialisation du client Vonage: ${error.message}`,\r\n          error.stack,\r\n        );\r\n      }\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Envoie un SMS à un numéro de téléphone via Vonage\r\n   * @param phoneNumber Numéro de téléphone du destinataire\r\n   * @param message Contenu du message\r\n   * @param options Options supplémentaires (from)\r\n   * @returns Promesse contenant l'ID du message ou une erreur\r\n   */\r\n  async sendSms(\r\n    phoneNumber: string,\r\n    message: string,\r\n    options?: any,\r\n  ): Promise<SmsResponse> {\r\n    return new Promise((resolve) => {\r\n      try {\r\n        this.logger.debug(`Envoi d'un SMS à ${phoneNumber} via Vonage`);\r\n\r\n        // Vérifier si le client Vonage est initialisé\r\n        if (!this.client) {\r\n          throw new Error(\"Le client Vonage n'est pas initialisé\");\r\n        }\r\n\r\n        const formattedPhoneNumber = this.formatPhoneNumber(phoneNumber);\r\n        const from = options?.from || this.defaultFrom;\r\n\r\n        // Préparer les options pour l'envoi du SMS\r\n        const smsOptions = {\r\n          type: options?.type || 'text',\r\n          ...(options?.ttl && { ttl: options.ttl }),\r\n          ...(options?.callback && { callback: options.callback }),\r\n          ...(options?.messageClass && { message_class: options.messageClass }),\r\n          ...(options?.clientRef && { client_ref: options.clientRef }),\r\n        };\r\n\r\n        // Envoyer le SMS via Vonage\r\n        this.client.message.sendSms(\r\n          from,\r\n          formattedPhoneNumber,\r\n          message,\r\n          smsOptions,\r\n          (err, responseData) => {\r\n            if (err) {\r\n              this.logger.error(\r\n                `Erreur lors de l'envoi du SMS via Vonage: ${err.message}`,\r\n                err.stack,\r\n              );\r\n              resolve({\r\n                success: false,\r\n                errorMessage: err.message || 'Erreur inconnue',\r\n              });\r\n              return;\r\n            }\r\n\r\n            // Vérifier les résultats\r\n            const result = responseData.messages[0];\r\n\r\n            if (result.status === '0') {\r\n              resolve({\r\n                success: true,\r\n                messageId: result['message-id'],\r\n                rawResponse: responseData,\r\n              });\r\n            } else {\r\n              resolve({\r\n                success: false,\r\n                errorCode: result.status,\r\n                errorMessage: result['error-text'] || 'Erreur inconnue',\r\n                rawResponse: responseData,\r\n              });\r\n            }\r\n          },\r\n        );\r\n      } catch (error) {\r\n        this.logger.error(\r\n          `Exception lors de l'envoi du SMS via Vonage: ${error.message}`,\r\n          error.stack,\r\n        );\r\n        resolve({\r\n          success: false,\r\n          errorMessage: error.message || 'Erreur inconnue',\r\n        });\r\n      }\r\n    });\r\n  }\r\n\r\n  /**\r\n   * Vérifie le statut d'un message envoyé via Vonage\r\n   * @param messageId ID du message Vonage à vérifier\r\n   * @returns Promesse contenant le statut du message\r\n   */\r\n  async checkMessageStatus(messageId: string): Promise<MessageStatusResponse> {\r\n    return new Promise((resolve) => {\r\n      try {\r\n        this.logger.debug(\r\n          `Vérification du statut du message ${messageId} via Vonage`,\r\n        );\r\n\r\n        // Vérifier si le client Vonage est initialisé\r\n        if (!this.client) {\r\n          throw new Error(\"Le client Vonage n'est pas initialisé\");\r\n        }\r\n\r\n        // Récupérer le statut du message depuis Vonage\r\n        this.client.message.search(messageId, (err, result) => {\r\n          if (err) {\r\n            this.logger.error(\r\n              `Erreur lors de la vérification du statut du message via Vonage: ${err.message}`,\r\n              err.stack,\r\n            );\r\n            resolve({\r\n              messageId,\r\n              status: MessageStatus.UNKNOWN,\r\n              updatedAt: new Date(),\r\n              details: err.message || 'Erreur inconnue',\r\n            });\r\n            return;\r\n          }\r\n\r\n          resolve({\r\n            messageId,\r\n            status: this.mapVonageStatus(result.status),\r\n            updatedAt: new Date(\r\n              result.date_finalized ||\r\n                result.date_received ||\r\n                result.date_submitted ||\r\n                Date.now(),\r\n            ),\r\n            details: result['error-text'] || '',\r\n            rawResponse: result,\r\n          });\r\n        });\r\n      } catch (error) {\r\n        this.logger.error(\r\n          `Exception lors de la vérification du statut du message via Vonage: ${error.message}`,\r\n          error.stack,\r\n        );\r\n        resolve({\r\n          messageId,\r\n          status: MessageStatus.UNKNOWN,\r\n          updatedAt: new Date(),\r\n          details: error.message || 'Erreur inconnue',\r\n        });\r\n      }\r\n    });\r\n  }\r\n\r\n  /**\r\n   * Envoie un SMS avec un brandname via Vonage\r\n   * @param phoneNumber Numéro de téléphone du destinataire\r\n   * @param message Contenu du message\r\n   * @param brandname Nom de la marque à utiliser comme expéditeur\r\n   * @param options Options supplémentaires\r\n   * @returns Promesse contenant l'ID du message ou une erreur\r\n   */\r\n  async sendBrandnameSms(\r\n    phoneNumber: string,\r\n    message: string,\r\n    brandname: string,\r\n    options?: any,\r\n  ): Promise<SmsResponse> {\r\n    // Pour Vonage, le brandname est simplement le paramètre \"from\"\r\n    return this.sendSms(phoneNumber, message, {\r\n      ...options,\r\n      from: brandname,\r\n    });\r\n  }\r\n\r\n  /**\r\n   * Teste la connexion avec Vonage\r\n   * @param config Configuration de Vonage\r\n   * @returns Promesse indiquant si la connexion est réussie\r\n   */\r\n  async testConnection(config: VonageConfig): Promise<ConnectionTestResponse> {\r\n    return new Promise((resolve) => {\r\n      try {\r\n        this.logger.debug('Test de connexion avec Vonage');\r\n\r\n        const apiKey = config.apiKey || this.apiKey;\r\n        const apiSecret = config.apiSecret || this.apiSecret;\r\n\r\n        if (!apiKey || !apiSecret) {\r\n          resolve({\r\n            success: false,\r\n            message: 'Identifiants Vonage manquants',\r\n          });\r\n          return;\r\n        }\r\n\r\n        // Créer un client temporaire pour le test\r\n        const testClient = new Vonage({\r\n          apiKey,\r\n          apiSecret,\r\n        });\r\n\r\n        // Tester la connexion en récupérant le solde du compte\r\n        testClient.account.checkBalance((err, result) => {\r\n          if (err) {\r\n            this.logger.error(\r\n              `Erreur lors du test de connexion avec Vonage: ${err.message}`,\r\n              err.stack,\r\n            );\r\n            resolve({\r\n              success: false,\r\n              message: err.message || 'Erreur inconnue',\r\n            });\r\n            return;\r\n          }\r\n\r\n          resolve({\r\n            success: true,\r\n            message: 'Connexion réussie',\r\n            details: {\r\n              balance: result.value,\r\n              autoReload: result.autoReload,\r\n            },\r\n          });\r\n        });\r\n      } catch (error) {\r\n        this.logger.error(\r\n          `Exception lors du test de connexion avec Vonage: ${error.message}`,\r\n          error.stack,\r\n        );\r\n        resolve({\r\n          success: false,\r\n          message: error.message || 'Erreur inconnue',\r\n        });\r\n      }\r\n    });\r\n  }\r\n\r\n  /**\r\n   * Convertit un statut Vonage en statut standard\r\n   * @param vonageStatus Statut Vonage\r\n   * @returns Statut standard\r\n   */\r\n  private mapVonageStatus(vonageStatus: string): MessageStatus {\r\n    switch (vonageStatus) {\r\n      case 'submitted':\r\n        return MessageStatus.PENDING;\r\n      case 'delivered':\r\n        return MessageStatus.DELIVERED;\r\n      case 'expired':\r\n        return MessageStatus.EXPIRED;\r\n      case 'failed':\r\n      case 'rejected':\r\n        return MessageStatus.FAILED;\r\n      case 'accepted':\r\n      case 'buffered':\r\n        return MessageStatus.SENDING;\r\n      default:\r\n        return MessageStatus.UNKNOWN;\r\n    }\r\n  }\r\n}\r\n"], "names": ["VonageProvider", "Vonage", "require", "default", "e", "BaseSmsProvider", "initClient", "<PERSON><PERSON><PERSON><PERSON>", "apiSecret", "client", "logger", "log", "error", "message", "stack", "sendSms", "phoneNumber", "options", "Promise", "resolve", "debug", "Error", "formattedPhoneNumber", "formatPhoneNumber", "from", "defaultFrom", "smsOptions", "type", "ttl", "callback", "messageClass", "message_class", "clientRef", "client_ref", "err", "responseData", "success", "errorMessage", "result", "messages", "status", "messageId", "rawResponse", "errorCode", "checkMessageStatus", "search", "MessageStatus", "UNKNOWN", "updatedAt", "Date", "details", "mapVonageStatus", "date_finalized", "date_received", "date_submitted", "now", "sendBrandnameSms", "brandname", "testConnection", "config", "testClient", "account", "checkBalance", "balance", "value", "autoReload", "vonageS<PERSON><PERSON>", "PENDING", "DELIVERED", "EXPIRED", "FAILED", "SENDING", "constructor", "configService", "providerName", "get", "warn"], "mappings": ";;;;+BA2CaA;;;eAAAA;;;wBA3Cc;wBACG;wCACE;sCAOzB;;;;;;;;;;AAEP,4BAA4B;AAC5B,IAAIC;AACJ,IAAI;IACFA,SAASC,QAAQ,sBAAsBC,OAAO;AAChD,EAAE,OAAOC,GAAG;AACV,mEAAmE;AACrE;AA0BO,IAAA,AAAMJ,iBAAN,MAAMA,uBAAuBK,uCAAe;IA4BjD;;;;GAIC,GACD,AAAQC,WAAWC,MAAc,EAAEC,SAAiB,EAAQ;QAC1D,IAAIP,UAAUM,UAAUC,WAAW;YACjC,IAAI;gBACF,IAAI,CAACC,MAAM,GAAG,IAAIR,OAAO;oBACvBM;oBACAC;gBACF;gBACA,IAAI,CAACE,MAAM,CAACC,GAAG,CAAC;YAClB,EAAE,OAAOC,OAAO;gBACd,IAAI,CAACF,MAAM,CAACE,KAAK,CACf,CAAC,kDAAkD,EAAEA,MAAMC,OAAO,EAAE,EACpED,MAAME,KAAK;YAEf;QACF;IACF;IAEA;;;;;;GAMC,GACD,MAAMC,QACJC,WAAmB,EACnBH,OAAe,EACfI,OAAa,EACS;QACtB,OAAO,IAAIC,QAAQ,CAACC;YAClB,IAAI;gBACF,IAAI,CAACT,MAAM,CAACU,KAAK,CAAC,CAAC,iBAAiB,EAAEJ,YAAY,WAAW,CAAC;gBAE9D,8CAA8C;gBAC9C,IAAI,CAAC,IAAI,CAACP,MAAM,EAAE;oBAChB,MAAM,IAAIY,MAAM;gBAClB;gBAEA,MAAMC,uBAAuB,IAAI,CAACC,iBAAiB,CAACP;gBACpD,MAAMQ,OAAOP,SAASO,QAAQ,IAAI,CAACC,WAAW;gBAE9C,2CAA2C;gBAC3C,MAAMC,aAAa;oBACjBC,MAAMV,SAASU,QAAQ;oBACvB,GAAIV,SAASW,OAAO;wBAAEA,KAAKX,QAAQW,GAAG;oBAAC,CAAC;oBACxC,GAAIX,SAASY,YAAY;wBAAEA,UAAUZ,QAAQY,QAAQ;oBAAC,CAAC;oBACvD,GAAIZ,SAASa,gBAAgB;wBAAEC,eAAed,QAAQa,YAAY;oBAAC,CAAC;oBACpE,GAAIb,SAASe,aAAa;wBAAEC,YAAYhB,QAAQe,SAAS;oBAAC,CAAC;gBAC7D;gBAEA,4BAA4B;gBAC5B,IAAI,CAACvB,MAAM,CAACI,OAAO,CAACE,OAAO,CACzBS,MACAF,sBACAT,SACAa,YACA,CAACQ,KAAKC;oBACJ,IAAID,KAAK;wBACP,IAAI,CAACxB,MAAM,CAACE,KAAK,CACf,CAAC,0CAA0C,EAAEsB,IAAIrB,OAAO,EAAE,EAC1DqB,IAAIpB,KAAK;wBAEXK,QAAQ;4BACNiB,SAAS;4BACTC,cAAcH,IAAIrB,OAAO,IAAI;wBAC/B;wBACA;oBACF;oBAEA,yBAAyB;oBACzB,MAAMyB,SAASH,aAAaI,QAAQ,CAAC,EAAE;oBAEvC,IAAID,OAAOE,MAAM,KAAK,KAAK;wBACzBrB,QAAQ;4BACNiB,SAAS;4BACTK,WAAWH,MAAM,CAAC,aAAa;4BAC/BI,aAAaP;wBACf;oBACF,OAAO;wBACLhB,QAAQ;4BACNiB,SAAS;4BACTO,WAAWL,OAAOE,MAAM;4BACxBH,cAAcC,MAAM,CAAC,aAAa,IAAI;4BACtCI,aAAaP;wBACf;oBACF;gBACF;YAEJ,EAAE,OAAOvB,OAAO;gBACd,IAAI,CAACF,MAAM,CAACE,KAAK,CACf,CAAC,6CAA6C,EAAEA,MAAMC,OAAO,EAAE,EAC/DD,MAAME,KAAK;gBAEbK,QAAQ;oBACNiB,SAAS;oBACTC,cAAczB,MAAMC,OAAO,IAAI;gBACjC;YACF;QACF;IACF;IAEA;;;;GAIC,GACD,MAAM+B,mBAAmBH,SAAiB,EAAkC;QAC1E,OAAO,IAAIvB,QAAQ,CAACC;YAClB,IAAI;gBACF,IAAI,CAACT,MAAM,CAACU,KAAK,CACf,CAAC,kCAAkC,EAAEqB,UAAU,WAAW,CAAC;gBAG7D,8CAA8C;gBAC9C,IAAI,CAAC,IAAI,CAAChC,MAAM,EAAE;oBAChB,MAAM,IAAIY,MAAM;gBAClB;gBAEA,+CAA+C;gBAC/C,IAAI,CAACZ,MAAM,CAACI,OAAO,CAACgC,MAAM,CAACJ,WAAW,CAACP,KAAKI;oBAC1C,IAAIJ,KAAK;wBACP,IAAI,CAACxB,MAAM,CAACE,KAAK,CACf,CAAC,gEAAgE,EAAEsB,IAAIrB,OAAO,EAAE,EAChFqB,IAAIpB,KAAK;wBAEXK,QAAQ;4BACNsB;4BACAD,QAAQM,mCAAa,CAACC,OAAO;4BAC7BC,WAAW,IAAIC;4BACfC,SAAShB,IAAIrB,OAAO,IAAI;wBAC1B;wBACA;oBACF;oBAEAM,QAAQ;wBACNsB;wBACAD,QAAQ,IAAI,CAACW,eAAe,CAACb,OAAOE,MAAM;wBAC1CQ,WAAW,IAAIC,KACbX,OAAOc,cAAc,IACnBd,OAAOe,aAAa,IACpBf,OAAOgB,cAAc,IACrBL,KAAKM,GAAG;wBAEZL,SAASZ,MAAM,CAAC,aAAa,IAAI;wBACjCI,aAAaJ;oBACf;gBACF;YACF,EAAE,OAAO1B,OAAO;gBACd,IAAI,CAACF,MAAM,CAACE,KAAK,CACf,CAAC,mEAAmE,EAAEA,MAAMC,OAAO,EAAE,EACrFD,MAAME,KAAK;gBAEbK,QAAQ;oBACNsB;oBACAD,QAAQM,mCAAa,CAACC,OAAO;oBAC7BC,WAAW,IAAIC;oBACfC,SAAStC,MAAMC,OAAO,IAAI;gBAC5B;YACF;QACF;IACF;IAEA;;;;;;;GAOC,GACD,MAAM2C,iBACJxC,WAAmB,EACnBH,OAAe,EACf4C,SAAiB,EACjBxC,OAAa,EACS;QACtB,+DAA+D;QAC/D,OAAO,IAAI,CAACF,OAAO,CAACC,aAAaH,SAAS;YACxC,GAAGI,OAAO;YACVO,MAAMiC;QACR;IACF;IAEA;;;;GAIC,GACD,MAAMC,eAAeC,MAAoB,EAAmC;QAC1E,OAAO,IAAIzC,QAAQ,CAACC;YAClB,IAAI;gBACF,IAAI,CAACT,MAAM,CAACU,KAAK,CAAC;gBAElB,MAAMb,SAASoD,OAAOpD,MAAM,IAAI,IAAI,CAACA,MAAM;gBAC3C,MAAMC,YAAYmD,OAAOnD,SAAS,IAAI,IAAI,CAACA,SAAS;gBAEpD,IAAI,CAACD,UAAU,CAACC,WAAW;oBACzBW,QAAQ;wBACNiB,SAAS;wBACTvB,SAAS;oBACX;oBACA;gBACF;gBAEA,0CAA0C;gBAC1C,MAAM+C,aAAa,IAAI3D,OAAO;oBAC5BM;oBACAC;gBACF;gBAEA,uDAAuD;gBACvDoD,WAAWC,OAAO,CAACC,YAAY,CAAC,CAAC5B,KAAKI;oBACpC,IAAIJ,KAAK;wBACP,IAAI,CAACxB,MAAM,CAACE,KAAK,CACf,CAAC,8CAA8C,EAAEsB,IAAIrB,OAAO,EAAE,EAC9DqB,IAAIpB,KAAK;wBAEXK,QAAQ;4BACNiB,SAAS;4BACTvB,SAASqB,IAAIrB,OAAO,IAAI;wBAC1B;wBACA;oBACF;oBAEAM,QAAQ;wBACNiB,SAAS;wBACTvB,SAAS;wBACTqC,SAAS;4BACPa,SAASzB,OAAO0B,KAAK;4BACrBC,YAAY3B,OAAO2B,UAAU;wBAC/B;oBACF;gBACF;YACF,EAAE,OAAOrD,OAAO;gBACd,IAAI,CAACF,MAAM,CAACE,KAAK,CACf,CAAC,iDAAiD,EAAEA,MAAMC,OAAO,EAAE,EACnED,MAAME,KAAK;gBAEbK,QAAQ;oBACNiB,SAAS;oBACTvB,SAASD,MAAMC,OAAO,IAAI;gBAC5B;YACF;QACF;IACF;IAEA;;;;GAIC,GACD,AAAQsC,gBAAgBe,YAAoB,EAAiB;QAC3D,OAAQA;YACN,KAAK;gBACH,OAAOpB,mCAAa,CAACqB,OAAO;YAC9B,KAAK;gBACH,OAAOrB,mCAAa,CAACsB,SAAS;YAChC,KAAK;gBACH,OAAOtB,mCAAa,CAACuB,OAAO;YAC9B,KAAK;YACL,KAAK;gBACH,OAAOvB,mCAAa,CAACwB,MAAM;YAC7B,KAAK;YACL,KAAK;gBACH,OAAOxB,mCAAa,CAACyB,OAAO;YAC9B;gBACE,OAAOzB,mCAAa,CAACC,OAAO;QAChC;IACF;IArSAyB,YAAY,AAAiBC,aAA4B,CAAE;QACzD,KAAK,CAAC,wBADqBA,gBAAAA,oBAPpBC,eAAe;QAUtB,mGAAmG;QACnG,IAAI,CAACnE,MAAM,GAAG,IAAI,CAACkE,aAAa,CAACE,GAAG,CAAS,qBAAqB;QAClE,IAAI,CAACnE,SAAS,GAAG,IAAI,CAACiE,aAAa,CAACE,GAAG,CAAS,wBAAwB;QACxE,IAAI,CAAClD,WAAW,GACd,IAAI,CAACgD,aAAa,CAACE,GAAG,CAAS,kBAAkB;QAEnD,kCAAkC;QAClC,IAAI,CAAC1E,QAAQ;YACX,IAAI,CAACS,MAAM,CAACkE,IAAI,CACd;QAEJ,OAAO;YACL,+BAA+B;YAC/B,IAAI,CAACtE,UAAU,CAAC,IAAI,CAACC,MAAM,EAAE,IAAI,CAACC,SAAS;QAC7C;IACF;AAoRF"}