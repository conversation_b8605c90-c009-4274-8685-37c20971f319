"use strict";
Object.defineProperty(exports, "__esModule", {
    value: true
});
Object.defineProperty(exports, "UserTemplateEmail", {
    enumerable: true,
    get: function() {
        return UserTemplateEmail;
    }
});
const _typeorm = require("typeorm");
function _ts_decorate(decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for(var i = decorators.length - 1; i >= 0; i--)if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
}
function _ts_metadata(k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
}
let UserTemplateEmail = class UserTemplateEmail {
};
_ts_decorate([
    (0, _typeorm.PrimaryGeneratedColumn)({
        name: 'id',
        type: 'bigint'
    }),
    _ts_metadata("design:type", Number)
], UserTemplateEmail.prototype, "id", void 0);
_ts_decorate([
    (0, _typeorm.Column)({
        name: 'user_id',
        nullable: true
    }),
    _ts_metadata("design:type", Number)
], UserTemplateEmail.prototype, "userId", void 0);
_ts_decorate([
    (0, _typeorm.Column)({
        name: 'name',
        length: 255,
        nullable: true,
        comment: 'Tên email'
    }),
    _ts_metadata("design:type", String)
], UserTemplateEmail.prototype, "name", void 0);
_ts_decorate([
    (0, _typeorm.Column)({
        name: 'subject',
        length: 255,
        nullable: true,
        comment: 'Tiêu đề email'
    }),
    _ts_metadata("design:type", String)
], UserTemplateEmail.prototype, "subject", void 0);
_ts_decorate([
    (0, _typeorm.Column)({
        name: 'content',
        type: 'text',
        nullable: true,
        comment: 'Nội dung email'
    }),
    _ts_metadata("design:type", String)
], UserTemplateEmail.prototype, "content", void 0);
_ts_decorate([
    (0, _typeorm.Column)({
        name: 'tags',
        type: 'jsonb',
        nullable: true,
        comment: 'Nhãn cho email'
    }),
    _ts_metadata("design:type", Object)
], UserTemplateEmail.prototype, "tags", void 0);
_ts_decorate([
    (0, _typeorm.Column)({
        name: 'placeholders',
        type: 'json',
        nullable: true,
        comment: 'Biến truyền vào'
    }),
    _ts_metadata("design:type", Object)
], UserTemplateEmail.prototype, "placeholders", void 0);
_ts_decorate([
    (0, _typeorm.Column)({
        name: 'created_at',
        type: 'bigint',
        nullable: true,
        comment: 'Thời gian tạo'
    }),
    _ts_metadata("design:type", Number)
], UserTemplateEmail.prototype, "createdAt", void 0);
_ts_decorate([
    (0, _typeorm.Column)({
        name: 'updated_at',
        type: 'bigint',
        nullable: true,
        comment: 'Thời gian cập nhật'
    }),
    _ts_metadata("design:type", Number)
], UserTemplateEmail.prototype, "updatedAt", void 0);
UserTemplateEmail = _ts_decorate([
    (0, _typeorm.Entity)('user_template_email')
], UserTemplateEmail);

//# sourceMappingURL=user-template-email.entity.js.map