{"version": 3, "sources": ["../../../../src/shared/services/sms/sms-provider-factory.service.ts"], "sourcesContent": ["import { Injectable } from '@nestjs/common';\r\nimport { ISmsProvider } from './sms-provider.interface';\r\nimport { SpeedSmsProvider } from './speed-sms-provider.service';\r\nimport { TwilioProvider } from './twilio-provider.service';\r\nimport { VonageProvider } from './vonage-provider.service';\r\nimport { FptSmsProvider } from './fpt-sms-provider.service';\r\n\r\n/**\r\n * Énumération des types de fournisseurs SMS supportés\r\n */\r\nexport enum SmsProviderType {\r\n  SPEED_SMS = 'SPEED_SMS',\r\n  TWILIO = 'TWILIO',\r\n  VONAGE = 'VONAGE',\r\n  FPT_SMS = 'FPT_SMS',\r\n}\r\n\r\n/**\r\n * Service de fabrique pour créer des instances de fournisseurs SMS\r\n */\r\n@Injectable()\r\nexport class SmsProviderFactory {\r\n  constructor(\r\n    private readonly speedSmsProvider: SpeedSmsProvider,\r\n    private readonly twilioProvider: TwilioProvider,\r\n    private readonly vonageProvider: VonageProvider,\r\n    private readonly fptSmsProvider: FptSmsProvider,\r\n  ) {}\r\n\r\n  /**\r\n   * Crée une instance de fournisseur SMS en fonction du type spécifié\r\n   * @param providerType Type de fournisseur SMS\r\n   * @returns Instance du fournisseur SMS\r\n   * @throws Error si le type de fournisseur n'est pas supporté\r\n   */\r\n  createProvider(providerType: SmsProviderType): ISmsProvider {\r\n    switch (providerType) {\r\n      case SmsProviderType.SPEED_SMS:\r\n        return this.speedSmsProvider;\r\n      case SmsProviderType.TWILIO:\r\n        return this.twilioProvider;\r\n      case SmsProviderType.VONAGE:\r\n        return this.vonageProvider;\r\n      case SmsProviderType.FPT_SMS:\r\n        return this.fptSmsProvider;\r\n      default:\r\n        throw new Error(\r\n          `Type de fournisseur SMS non supporté: ${providerType}`,\r\n        );\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Crée une instance de fournisseur SMS en fonction du nom du fournisseur\r\n   * @param providerName Nom du fournisseur SMS\r\n   * @returns Instance du fournisseur SMS\r\n   * @throws Error si le nom du fournisseur n'est pas supporté\r\n   */\r\n  createProviderByName(providerName: string): ISmsProvider {\r\n    const normalizedName = providerName\r\n      .toUpperCase()\r\n      .replace(/[^A-Z0-9_]/g, '_');\r\n\r\n    try {\r\n      const providerType = SmsProviderType[normalizedName];\r\n      return this.createProvider(providerType);\r\n    } catch (error) {\r\n      throw new Error(`Nom de fournisseur SMS non supporté: ${providerName}`);\r\n    }\r\n  }\r\n}\r\n"], "names": ["SmsProviderFactory", "SmsProviderType", "createProvider", "providerType", "speedSmsProvider", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "fptSmsProvider", "Error", "createProviderByName", "providerName", "normalizedName", "toUpperCase", "replace", "error", "constructor"], "mappings": ";;;;;;;;;;;QAqBaA;eAAAA;;QAXDC;eAAAA;;;wBAVe;yCAEM;uCACF;uCACA;uCACA;;;;;;;;;;AAKxB,IAAA,AAAKA,yCAAAA;;;;;WAAAA;;AAWL,IAAA,AAAMD,qBAAN,MAAMA;IAQX;;;;;GAKC,GACDE,eAAeC,YAA6B,EAAgB;QAC1D,OAAQA;YACN;gBACE,OAAO,IAAI,CAACC,gBAAgB;YAC9B;gBACE,OAAO,IAAI,CAACC,cAAc;YAC5B;gBACE,OAAO,IAAI,CAACC,cAAc;YAC5B;gBACE,OAAO,IAAI,CAACC,cAAc;YAC5B;gBACE,MAAM,IAAIC,MACR,CAAC,sCAAsC,EAAEL,cAAc;QAE7D;IACF;IAEA;;;;;GAKC,GACDM,qBAAqBC,YAAoB,EAAgB;QACvD,MAAMC,iBAAiBD,aACpBE,WAAW,GACXC,OAAO,CAAC,eAAe;QAE1B,IAAI;YACF,MAAMV,eAAeF,eAAe,CAACU,eAAe;YACpD,OAAO,IAAI,CAACT,cAAc,CAACC;QAC7B,EAAE,OAAOW,OAAO;YACd,MAAM,IAAIN,MAAM,CAAC,qCAAqC,EAAEE,cAAc;QACxE;IACF;IA/CAK,YACE,AAAiBX,gBAAkC,EACnD,AAAiBC,cAA8B,EAC/C,AAAiBC,cAA8B,EAC/C,AAAiBC,cAA8B,CAC/C;aAJiBH,mBAAAA;aACAC,iBAAAA;aACAC,iBAAAA;aACAC,iBAAAA;IAChB;AA2CL"}