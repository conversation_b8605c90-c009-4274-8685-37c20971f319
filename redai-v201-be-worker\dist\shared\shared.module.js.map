{"version": 3, "sources": ["../../src/shared/shared.module.ts"], "sourcesContent": ["import { Global, Module } from '@nestjs/common';\r\nimport { EmailService } from './services/email.service';\r\n\r\n/**\r\n * Module chứa các service dùng chung trong toàn bộ ứng dụng\r\n */\r\n@Global()\r\n@Module({\r\n  providers: [EmailService],\r\n  exports: [EmailService],\r\n})\r\nexport class SharedModule {}\r\n"], "names": ["SharedModule", "providers", "EmailService", "exports"], "mappings": ";;;;+BAWaA;;;eAAAA;;;wBAXkB;8BACF;;;;;;;AAUtB,IAAA,AAAMA,eAAN,MAAMA;AAAc;;;;QAHzBC,WAAW;YAACC,0BAAY;SAAC;QACzBC,SAAS;YAACD,0BAAY;SAAC"}