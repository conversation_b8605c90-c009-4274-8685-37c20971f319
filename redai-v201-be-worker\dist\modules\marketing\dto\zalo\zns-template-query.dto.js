"use strict";
Object.defineProperty(exports, "__esModule", {
    value: true
});
function _export(target, all) {
    for(var name in all)Object.defineProperty(target, name, {
        enumerable: true,
        get: Object.getOwnPropertyDescriptor(all, name).get
    });
}
_export(exports, {
    get ZnsTemplateQueryDto () {
        return ZnsTemplateQueryDto;
    },
    get ZnsTemplateStatus () {
        return ZnsTemplateStatus;
    }
});
const _swagger = require("@nestjs/swagger");
const _classvalidator = require("class-validator");
const _dto = require("../../../../common/dto");
function _ts_decorate(decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for(var i = decorators.length - 1; i >= 0; i--)if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
}
function _ts_metadata(k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
}
var ZnsTemplateStatus = /*#__PURE__*/ function(ZnsTemplateStatus) {
    ZnsTemplateStatus["APPROVED"] = "approved";
    ZnsTemplateStatus["PENDING"] = "pending";
    ZnsTemplateStatus["REJECTED"] = "rejected";
    ZnsTemplateStatus["ALL"] = "all";
    return ZnsTemplateStatus;
}({});
let ZnsTemplateQueryDto = class ZnsTemplateQueryDto extends _dto.QueryDto {
    constructor(){
        super();
        this.sortBy = 'createdAt';
        this.sortDirection = _dto.SortDirection.DESC;
    }
};
_ts_decorate([
    (0, _swagger.ApiProperty)({
        description: 'Tìm kiếm theo tên template',
        example: 'đơn hàng',
        required: false
    }),
    (0, _classvalidator.IsOptional)(),
    (0, _classvalidator.IsString)(),
    _ts_metadata("design:type", String)
], ZnsTemplateQueryDto.prototype, "templateName", void 0);
_ts_decorate([
    (0, _swagger.ApiProperty)({
        description: 'Lọc theo trạng thái',
        enum: ZnsTemplateStatus,
        example: "approved",
        required: false
    }),
    (0, _classvalidator.IsOptional)(),
    (0, _classvalidator.IsEnum)(ZnsTemplateStatus),
    _ts_metadata("design:type", String)
], ZnsTemplateQueryDto.prototype, "status", void 0);

//# sourceMappingURL=zns-template-query.dto.js.map