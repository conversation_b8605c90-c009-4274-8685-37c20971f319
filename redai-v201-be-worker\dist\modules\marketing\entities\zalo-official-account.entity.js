"use strict";
Object.defineProperty(exports, "__esModule", {
    value: true
});
Object.defineProperty(exports, "ZaloOfficialAccount", {
    enumerable: true,
    get: function() {
        return ZaloOfficialAccount;
    }
});
const _typeorm = require("typeorm");
function _ts_decorate(decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for(var i = decorators.length - 1; i >= 0; i--)if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
}
function _ts_metadata(k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
}
let ZaloOfficialAccount = class ZaloOfficialAccount {
};
_ts_decorate([
    (0, _typeorm.PrimaryGeneratedColumn)({
        name: 'id'
    }),
    _ts_metadata("design:type", Number)
], ZaloOfficialAccount.prototype, "id", void 0);
_ts_decorate([
    (0, _typeorm.Column)({
        name: 'user_id'
    }),
    _ts_metadata("design:type", Number)
], ZaloOfficialAccount.prototype, "userId", void 0);
_ts_decorate([
    (0, _typeorm.Column)({
        name: 'oa_id',
        length: 50
    }),
    _ts_metadata("design:type", String)
], ZaloOfficialAccount.prototype, "oaId", void 0);
_ts_decorate([
    (0, _typeorm.Column)({
        name: 'name',
        length: 255
    }),
    _ts_metadata("design:type", String)
], ZaloOfficialAccount.prototype, "name", void 0);
_ts_decorate([
    (0, _typeorm.Column)({
        name: 'description',
        length: 500,
        nullable: true
    }),
    _ts_metadata("design:type", String)
], ZaloOfficialAccount.prototype, "description", void 0);
_ts_decorate([
    (0, _typeorm.Column)({
        name: 'avatar_url',
        length: 500,
        nullable: true
    }),
    _ts_metadata("design:type", String)
], ZaloOfficialAccount.prototype, "avatarUrl", void 0);
_ts_decorate([
    (0, _typeorm.Column)({
        name: 'access_token',
        length: 500
    }),
    _ts_metadata("design:type", String)
], ZaloOfficialAccount.prototype, "accessToken", void 0);
_ts_decorate([
    (0, _typeorm.Column)({
        name: 'refresh_token',
        length: 500,
        nullable: true
    }),
    _ts_metadata("design:type", String)
], ZaloOfficialAccount.prototype, "refreshToken", void 0);
_ts_decorate([
    (0, _typeorm.Column)({
        name: 'expires_at',
        type: 'bigint'
    }),
    _ts_metadata("design:type", Number)
], ZaloOfficialAccount.prototype, "expiresAt", void 0);
_ts_decorate([
    (0, _typeorm.Column)({
        name: 'agent_id',
        nullable: true
    }),
    _ts_metadata("design:type", Number)
], ZaloOfficialAccount.prototype, "agentId", void 0);
_ts_decorate([
    (0, _typeorm.Column)({
        name: 'status',
        length: 20,
        default: 'active'
    }),
    _ts_metadata("design:type", String)
], ZaloOfficialAccount.prototype, "status", void 0);
_ts_decorate([
    (0, _typeorm.Column)({
        name: 'created_at',
        type: 'bigint'
    }),
    _ts_metadata("design:type", Number)
], ZaloOfficialAccount.prototype, "createdAt", void 0);
_ts_decorate([
    (0, _typeorm.Column)({
        name: 'updated_at',
        type: 'bigint'
    }),
    _ts_metadata("design:type", Number)
], ZaloOfficialAccount.prototype, "updatedAt", void 0);
ZaloOfficialAccount = _ts_decorate([
    (0, _typeorm.Entity)('zalo_official_accounts')
], ZaloOfficialAccount);

//# sourceMappingURL=zalo-official-account.entity.js.map