{"version": 3, "sources": ["../../../../../src/modules/marketing/dto/zalo/zns-message-response.dto.ts"], "sourcesContent": ["import { ApiProperty } from '@nestjs/swagger';\r\n\r\n/**\r\n * DTO cho phản hồi thông tin tin nhắn ZNS\r\n */\r\nexport class ZnsMessageResponseDto {\r\n  @ApiProperty({\r\n    description: 'ID của tin nhắn trong hệ thống',\r\n    example: 1,\r\n  })\r\n  id: number;\r\n\r\n  @ApiProperty({\r\n    description: 'ID của tin nhắn trên Z<PERSON>',\r\n    example: 'msg123456789',\r\n    nullable: true,\r\n  })\r\n  messageId?: string;\r\n\r\n  @ApiProperty({\r\n    description: 'ID của template',\r\n    example: 'template123456789',\r\n  })\r\n  templateId: string;\r\n\r\n  @ApiProperty({\r\n    description: 'Số điện thoại người nhận',\r\n    example: '0912345678',\r\n  })\r\n  phone: string;\r\n\r\n  @ApiProperty({\r\n    description: 'ID giao dịch',\r\n    example: 'tracking123456789',\r\n  })\r\n  trackingId: string;\r\n\r\n  @ApiProperty({\r\n    description: 'Trạng thái tin nhắn (pending, delivered, failed)',\r\n    example: 'delivered',\r\n  })\r\n  status: string;\r\n\r\n  @ApiProperty({\r\n    description: 'Thời điểm gửi thành công (Unix timestamp)',\r\n    example: 1625097600000,\r\n    nullable: true,\r\n  })\r\n  deliveredTime?: number;\r\n\r\n  @ApiProperty({\r\n    description: 'Thời điểm tạo (Unix timestamp)',\r\n    example: 1625097600000,\r\n  })\r\n  createdAt: number;\r\n}\r\n"], "names": ["ZnsMessageResponseDto", "description", "example", "nullable"], "mappings": ";;;;+BAKaA;;;eAAAA;;;yBALe;;;;;;;;;;AAKrB,IAAA,AAAMA,wBAAN,MAAMA;AAkDb;;;QAhDIC,aAAa;QACbC,SAAS;;;;;;QAKTD,aAAa;QACbC,SAAS;QACTC,UAAU;;;;;;QAKVF,aAAa;QACbC,SAAS;;;;;;QAKTD,aAAa;QACbC,SAAS;;;;;;QAKTD,aAAa;QACbC,SAAS;;;;;;QAKTD,aAAa;QACbC,SAAS;;;;;;QAKTD,aAAa;QACbC,SAAS;QACTC,UAAU;;;;;;QAKVF,aAAa;QACbC,SAAS"}