{"version": 3, "sources": ["../../../../src/shared/services/sms/sms-provider.interface.ts"], "sourcesContent": ["/**\r\n * Interface định nghĩa các phương thức chung cho tất cả các nhà cung cấp SMS\r\n */\r\nexport interface ISmsProvider {\r\n  /**\r\n   * Tên nhà cung cấp SMS\r\n   */\r\n  readonly providerName: string;\r\n\r\n  /**\r\n   * G<PERSON><PERSON> tin nhắn SMS đến một số điện thoại\r\n   * @param phoneNumber Số điện thoại của người nhận\r\n   * @param message Nội dung tin nhắn\r\n   * @param options Các tùy chọn bổ sung dành riêng cho nhà cung cấp\r\n   * @returns Promise chứa ID tin nhắn hoặc lỗi\r\n   */\r\n  sendSms(\r\n    phoneNumber: string,\r\n    message: string,\r\n    options?: any,\r\n  ): Promise<SmsResponse>;\r\n\r\n  /**\r\n   * G<PERSON><PERSON> tin nhắn SMS đến nhiều số điện thoại\r\n   * @param phoneNumbers Danh sách số điện thoại của người nhận\r\n   * @param message Nội dung tin nhắn\r\n   * @param options Các tùy chọn bổ sung dành riêng cho nhà cung cấp\r\n   * @returns Promise chứa kết quả cho từng người nhận\r\n   */\r\n  sendBulkSms(\r\n    phoneNumbers: string[],\r\n    message: string,\r\n    options?: any,\r\n  ): Promise<BulkSmsResponse>;\r\n\r\n  /**\r\n   * Kiểm tra trạng thái của tin nhắn đã gửi\r\n   * @param messageId ID của tin nhắn cần kiểm tra\r\n   * @returns Promise chứa trạng thái của tin nhắn\r\n   */\r\n  checkMessageStatus(messageId: string): Promise<MessageStatusResponse>;\r\n\r\n  /**\r\n   * Gửi tin nhắn SMS với brandname\r\n   * @param phoneNumber Số điện thoại của người nhận\r\n   * @param message Nội dung tin nhắn\r\n   * @param brandname Tên thương hiệu sử dụng làm người gửi\r\n   * @param options Các tùy chọn bổ sung dành riêng cho nhà cung cấp\r\n   * @returns Promise chứa ID tin nhắn hoặc lỗi\r\n   */\r\n  sendBrandnameSms(\r\n    phoneNumber: string,\r\n    message: string,\r\n    brandname: string,\r\n    options?: any,\r\n  ): Promise<SmsResponse>;\r\n\r\n  /**\r\n   * Gửi tin nhắn SMS OTP (One-Time Password)\r\n   * @param phoneNumber Số điện thoại của người nhận\r\n   * @param otpCode Mã OTP cần gửi\r\n   * @param options Các tùy chọn bổ sung dành riêng cho nhà cung cấp\r\n   * @returns Promise chứa ID tin nhắn hoặc lỗi\r\n   */\r\n  sendOtp(\r\n    phoneNumber: string,\r\n    otpCode: string,\r\n    options?: any,\r\n  ): Promise<SmsResponse>;\r\n\r\n  /**\r\n   * Kiểm tra kết nối với nhà cung cấp SMS\r\n   * @param config Cấu hình của nhà cung cấp\r\n   * @returns Promise chỉ ra liệu kết nối có thành công hay không\r\n   */\r\n  testConnection(config: any): Promise<ConnectionTestResponse>;\r\n}\r\n\r\n/**\r\n * Interface cho phản hồi khi gửi SMS\r\n */\r\nexport interface SmsResponse {\r\n  /**\r\n   * Chỉ ra liệu việc gửi tin nhắn có thành công hay không\r\n   */\r\n  success: boolean;\r\n\r\n  /**\r\n   * ID của tin nhắn (nếu thành công)\r\n   */\r\n  messageId?: string;\r\n\r\n  /**\r\n   * Mã lỗi (nếu thất bại)\r\n   */\r\n  errorCode?: string;\r\n\r\n  /**\r\n   * Thông báo lỗi (nếu thất bại)\r\n   */\r\n  errorMessage?: string;\r\n\r\n  /**\r\n   * Dữ liệu thô của phản hồi từ nhà cung cấp\r\n   */\r\n  rawResponse?: any;\r\n}\r\n\r\n/**\r\n * Interface cho phản hồi khi gửi SMS hàng loạt\r\n */\r\nexport interface BulkSmsResponse {\r\n  /**\r\n   * Tổng số tin nhắn đã gửi thành công\r\n   */\r\n  successCount: number;\r\n\r\n  /**\r\n   * Tổng số tin nhắn gửi thất bại\r\n   */\r\n  failureCount: number;\r\n\r\n  /**\r\n   * Kết quả chi tiết cho từng người nhận\r\n   */\r\n  results: {\r\n    /**\r\n     * Số điện thoại của người nhận\r\n     */\r\n    phoneNumber: string;\r\n\r\n    /**\r\n     * Chỉ ra liệu việc gửi tin nhắn có thành công cho người nhận này hay không\r\n     */\r\n    success: boolean;\r\n\r\n    /**\r\n     * ID của tin nhắn (nếu thành công)\r\n     */\r\n    messageId?: string;\r\n\r\n    /**\r\n     * Mã lỗi (nếu thất bại)\r\n     */\r\n    errorCode?: string;\r\n\r\n    /**\r\n     * Thông báo lỗi (nếu thất bại)\r\n     */\r\n    errorMessage?: string;\r\n  }[];\r\n\r\n  /**\r\n   * ID của giao dịch hoặc chiến dịch\r\n   */\r\n  transactionId?: string;\r\n\r\n  /**\r\n   * Tổng chi phí gửi tin nhắn\r\n   */\r\n  totalCost?: number;\r\n\r\n  /**\r\n   * Dữ liệu thô của phản hồi từ nhà cung cấp\r\n   *\r\n   * Données brutes de la réponse du fournisseur\r\n   */\r\n  rawResponse?: any;\r\n}\r\n\r\n/**\r\n * Interface cho phản hồi trạng thái tin nhắn\r\n *\r\n * Interface pour la réponse de statut de message\r\n */\r\nexport interface MessageStatusResponse {\r\n  /**\r\n   * ID của tin nhắn\r\n   *\r\n   * ID du message\r\n   */\r\n  messageId: string;\r\n\r\n  /**\r\n   * Trạng thái của tin nhắn\r\n   *\r\n   * Statut du message\r\n   */\r\n  status: MessageStatus;\r\n\r\n  /**\r\n   * Thời gian cập nhật trạng thái gần nhất\r\n   *\r\n   * Horodatage de la dernière mise à jour du statut\r\n   */\r\n  updatedAt: Date;\r\n\r\n  /**\r\n   * Thông tin chi tiết bổ sung về trạng thái\r\n   *\r\n   * Détails supplémentaires sur le statut\r\n   */\r\n  details?: string;\r\n\r\n  /**\r\n   * Dữ liệu thô của phản hồi từ nhà cung cấp\r\n   *\r\n   * Données brutes de la réponse du fournisseur\r\n   */\r\n  rawResponse?: any;\r\n}\r\n\r\n/**\r\n * Enum các trạng thái có thể có của tin nhắn\r\n *\r\n * Énumération des statuts possibles pour un message\r\n */\r\nexport enum MessageStatus {\r\n  /**\r\n   * Tin nhắn đang chờ gửi\r\n   *\r\n   * Message en attente d'envoi\r\n   */\r\n  PENDING = 'PENDING',\r\n\r\n  /**\r\n   * Tin nhắn đang trong quá trình gửi\r\n   *\r\n   * Message en cours d'envoi\r\n   */\r\n  SENDING = 'SENDING',\r\n\r\n  /**\r\n   * Tin nhắn đã được gửi đến người nhận\r\n   *\r\n   * Message délivré au destinataire\r\n   */\r\n  DELIVERED = 'DELIVERED',\r\n\r\n  /**\r\n   * Tin nhắn không được gửi đến người nhận\r\n   *\r\n   * Message non délivré au destinataire\r\n   */\r\n  FAILED = 'FAILED',\r\n\r\n  /**\r\n   * Tin nhắn đã hết hạn (không được gửi trong khoảng thời gian quy định)\r\n   *\r\n   * Message expiré (non délivré dans le délai imparti)\r\n   */\r\n  EXPIRED = 'EXPIRED',\r\n\r\n  /**\r\n   * Tin nhắn bị từ chối bởi nhà cung cấp\r\n   *\r\n   * Message rejeté par le fournisseur\r\n   */\r\n  REJECTED = 'REJECTED',\r\n\r\n  /**\r\n   * Trạng thái không xác định\r\n   *\r\n   * Statut inconnu\r\n   */\r\n  UNKNOWN = 'UNKNOWN',\r\n}\r\n\r\n/**\r\n * Interface cho phản hồi kiểm tra kết nối\r\n *\r\n * Interface pour la réponse de test de connexion\r\n */\r\nexport interface ConnectionTestResponse {\r\n  /**\r\n   * Chỉ ra liệu kết nối có thành công hay không\r\n   *\r\n   * Indique si la connexion est réussie\r\n   */\r\n  success: boolean;\r\n\r\n  /**\r\n   * Thông báo mô tả kết quả của việc kiểm tra\r\n   *\r\n   * Message décrivant le résultat du test\r\n   */\r\n  message: string;\r\n\r\n  /**\r\n   * Thông tin chi tiết bổ sung (trong trường hợp thất bại)\r\n   *\r\n   * Détails supplémentaires (en cas d'échec)\r\n   */\r\n  details?: any;\r\n}\r\n"], "names": ["MessageStatus"], "mappings": "AAAA;;CAEC;;;;+BAuNWA;;;eAAAA;;;AAAL,IAAA,AAAKA,uCAAAA;IACV;;;;GAIC;IAGD;;;;GAIC;IAGD;;;;GAIC;IAGD;;;;GAIC;IAGD;;;;GAIC;IAGD;;;;GAIC;IAGD;;;;GAIC;WA/CSA"}