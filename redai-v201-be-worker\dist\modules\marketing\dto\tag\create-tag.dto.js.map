{"version": 3, "sources": ["../../../../../src/modules/marketing/dto/tag/create-tag.dto.ts"], "sourcesContent": ["import {\r\n  IsNotEmpty,\r\n  IsOptional,\r\n  IsString,\r\n  Length,\r\n  Matches,\r\n} from 'class-validator';\r\nimport { ApiProperty } from '@nestjs/swagger';\r\n\r\n/**\r\n * DTO cho việc tạo tag mới\r\n */\r\nexport class CreateTagDto {\r\n  /**\r\n   * Tên tag\r\n   * @example \"Khách hàng VIP\"\r\n   */\r\n  @ApiProperty({\r\n    description: 'Tên tag',\r\n    example: 'Khách hàng VIP',\r\n  })\r\n  @IsNotEmpty({ message: 'Tên tag không được để trống' })\r\n  @IsString({ message: 'Tên tag phải là chuỗi' })\r\n  @Length(1, 255, { message: 'Tên tag phải từ 1 đến 255 ký tự' })\r\n  name: string;\r\n\r\n  /**\r\n   * Mã màu của tag (định dạng HEX)\r\n   * @example \"#FF5733\"\r\n   */\r\n  @ApiProperty({\r\n    description: 'Mã màu của tag (định dạng HEX)',\r\n    example: '#FF5733',\r\n  })\r\n  @IsOptional()\r\n  @IsString({ message: '<PERSON><PERSON> màu phải là chuỗi' })\r\n  @Matches(/^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$/, {\r\n    message: 'Mã màu phải có định dạng HEX hợp lệ (ví dụ: #FF5733)',\r\n  })\r\n  color?: string;\r\n}\r\n"], "names": ["CreateTagDto", "description", "example", "message"], "mappings": ";;;;+BAYaA;;;eAAAA;;;gCANN;yBACqB;;;;;;;;;;AAKrB,IAAA,AAAMA,eAAN,MAAMA;AA4Bb;;;QAtBIC,aAAa;QACbC,SAAS;;;QAEGC,SAAS;;;QACXA,SAAS;;;QACHA,SAAS;;;;;;QAQzBF,aAAa;QACbC,SAAS;;;;QAGCC,SAAS;;;QAEnBA,SAAS"}