{"version": 3, "sources": ["../../../../../src/modules/marketing/dto/zalo/connect-official-account.dto.ts"], "sourcesContent": ["import { ApiProperty } from '@nestjs/swagger';\r\nimport { IsNotEmpty, IsNumber, IsString } from 'class-validator';\r\n\r\n/**\r\n * DTO cho vi<PERSON><PERSON> kế<PERSON> nối Official Account\r\n */\r\nexport class ConnectOfficialAccountDto {\r\n  @ApiProperty({\r\n    description: 'Access token của Official Account',\r\n    example: 'abcdef123456789',\r\n  })\r\n  @IsString()\r\n  @IsNotEmpty()\r\n  accessToken: string;\r\n\r\n  @ApiProperty({\r\n    description: 'Refresh token của Official Account',\r\n    example: 'refresh123456789',\r\n  })\r\n  @IsString()\r\n  @IsNotEmpty()\r\n  refreshToken: string;\r\n\r\n  @ApiProperty({\r\n    description: 'Thời gian hết hạn của access token (Unix timestamp)',\r\n    example: *************,\r\n  })\r\n  @IsNumber()\r\n  @IsNotEmpty()\r\n  expiresAt: number;\r\n}\r\n"], "names": ["ConnectOfficialAccountDto", "description", "example"], "mappings": ";;;;+BAMaA;;;eAAAA;;;yBANe;gCACmB;;;;;;;;;;AAKxC,IAAA,AAAMA,4BAAN,MAAMA;AAwBb;;;QAtBIC,aAAa;QACbC,SAAS;;;;;;;;QAOTD,aAAa;QACbC,SAAS;;;;;;;;QAOTD,aAAa;QACbC,SAAS"}