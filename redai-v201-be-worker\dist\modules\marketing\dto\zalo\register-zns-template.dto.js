"use strict";
Object.defineProperty(exports, "__esModule", {
    value: true
});
Object.defineProperty(exports, "RegisterZnsTemplateDto", {
    enumerable: true,
    get: function() {
        return RegisterZnsTemplateDto;
    }
});
const _swagger = require("@nestjs/swagger");
const _classvalidator = require("class-validator");
function _ts_decorate(decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for(var i = decorators.length - 1; i >= 0; i--)if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
}
function _ts_metadata(k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
}
let RegisterZnsTemplateDto = class RegisterZnsTemplateDto {
};
_ts_decorate([
    (0, _swagger.ApiProperty)({
        description: 'Tên của template',
        example: 'Thông báo đơn hàng'
    }),
    (0, _classvalidator.IsString)(),
    (0, _classvalidator.IsNotEmpty)(),
    _ts_metadata("design:type", String)
], RegisterZnsTemplateDto.prototype, "templateName", void 0);
_ts_decorate([
    (0, _swagger.ApiProperty)({
        description: 'Nội dung của template',
        example: 'Đơn hàng #{orderId} của bạn đã được xác nhận. Cảm ơn bạn đã mua hàng tại {shopName}.'
    }),
    (0, _classvalidator.IsString)(),
    (0, _classvalidator.IsNotEmpty)(),
    _ts_metadata("design:type", String)
], RegisterZnsTemplateDto.prototype, "templateContent", void 0);
_ts_decorate([
    (0, _swagger.ApiProperty)({
        description: 'Các tham số của template',
        example: [
            'orderId',
            'shopName'
        ],
        type: [
            String
        ]
    }),
    (0, _classvalidator.IsArray)(),
    (0, _classvalidator.IsString)({
        each: true
    }),
    _ts_metadata("design:type", Array)
], RegisterZnsTemplateDto.prototype, "params", void 0);

//# sourceMappingURL=register-zns-template.dto.js.map