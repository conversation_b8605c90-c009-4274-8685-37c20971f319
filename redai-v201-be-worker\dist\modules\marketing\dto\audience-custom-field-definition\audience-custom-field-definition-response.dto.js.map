{"version": 3, "sources": ["../../../../../src/modules/marketing/dto/audience-custom-field-definition/audience-custom-field-definition-response.dto.ts"], "sourcesContent": ["import { ApiProperty } from '@nestjs/swagger';\r\nimport { CustomFieldDataType } from './create-audience-custom-field-definition.dto';\r\n\r\n/**\r\n * DTO cho phản hồi thông tin trường tùy chỉnh\r\n */\r\nexport class AudienceCustomFieldDefinitionResponseDto {\r\n  /**\r\n   * ID của trường tùy chỉnh\r\n   * @example 1\r\n   */\r\n  @ApiProperty({\r\n    description: 'ID của trường tùy chỉnh',\r\n    example: 1,\r\n  })\r\n  id: number;\r\n\r\n  /**\r\n   * Định danh duy nhất cho trường tùy chỉnh\r\n   * @example \"customer_address\"\r\n   */\r\n  @ApiProperty({\r\n    description: 'Định danh duy nhất cho trường tùy chỉnh',\r\n    example: 'customer_address',\r\n  })\r\n  fieldKey: string;\r\n\r\n  /**\r\n   * ID của người dùng mà trường tùy chỉnh này thuộc về\r\n   * @example 1\r\n   */\r\n  @ApiProperty({\r\n    description: 'ID của người dùng mà trường tùy chỉnh này thuộc về',\r\n    example: 1,\r\n  })\r\n  userId: number;\r\n\r\n  /**\r\n   * Tên hiển thị thân thiện với người dùng\r\n   * @example \"Địa chỉ khách hàng\"\r\n   */\r\n  @ApiProperty({\r\n    description: 'Tên hiển thị thân thiện với người dùng',\r\n    example: 'Địa chỉ khách hàng',\r\n  })\r\n  displayName: string;\r\n\r\n  /**\r\n   * Kiểu dữ liệu\r\n   * @example \"string\"\r\n   */\r\n  @ApiProperty({\r\n    description: 'Kiểu dữ liệu',\r\n    enum: CustomFieldDataType,\r\n    example: CustomFieldDataType.STRING,\r\n  })\r\n  dataType: CustomFieldDataType;\r\n\r\n  /**\r\n   * Mô tả chi tiết hoặc ghi chú về trường tùy chỉnh\r\n   * @example \"Địa chỉ liên hệ của khách hàng\"\r\n   */\r\n  @ApiProperty({\r\n    description: 'Mô tả chi tiết hoặc ghi chú về trường tùy chỉnh',\r\n    example: 'Địa chỉ liên hệ của khách hàng',\r\n    nullable: true,\r\n  })\r\n  description: string | null;\r\n}\r\n"], "names": ["AudienceCustomFieldDefinitionResponseDto", "description", "example", "enum", "CustomFieldDataType", "STRING", "nullable"], "mappings": ";;;;+BAMaA;;;eAAAA;;;yBANe;wDACQ;;;;;;;;;;AAK7B,IAAA,AAAMA,2CAAN,MAAMA;AA8Db;;;QAxDIC,aAAa;QACbC,SAAS;;;;;;QASTD,aAAa;QACbC,SAAS;;;;;;QASTD,aAAa;QACbC,SAAS;;;;;;QASTD,aAAa;QACbC,SAAS;;;;;;QASTD,aAAa;QACbE,MAAMC,2DAAmB;QACzBF,SAASE,2DAAmB,CAACC,MAAM;;;;;;QASnCJ,aAAa;QACbC,SAAS;QACTI,UAAU"}