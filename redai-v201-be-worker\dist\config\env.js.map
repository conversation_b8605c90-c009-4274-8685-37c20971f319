{"version": 3, "sources": ["../../src/config/env.ts"], "sourcesContent": ["import { z, ZodError } from 'zod';\r\nimport { configDotenv } from 'dotenv';\r\nimport { Logger } from '@nestjs/common';\r\n\r\nconfigDotenv();\r\n\r\nconst logger = new Logger('Config');\r\nconst requiredString = (name: string) =>\r\n  z.string().trim().min(1, `${name} is required`);\r\nconst requiredURL = (name: string) =>\r\n  z.string().url(`${name} must be a valid URL`);\r\nconst requiredNumber = (name: string) =>\r\n  z\r\n    .string()\r\n    .transform(Number)\r\n    .refine((v) => !isNaN(v), { message: `${name} must be a number` });\r\n\r\nconst envSchema = z.object({\r\n  misc: z.object({\r\n    NODE_ENV: z.enum(['development', 'test', 'staging', 'production']),\r\n    PORT: requiredNumber('PORT'),\r\n  }),\r\n  app: z.object({\r\n    BASE_URL: z.string().url().optional().default('http://localhost:3000'),\r\n  }),\r\n  database: z.object({\r\n    DB_HOST: requiredString('DB_HOST'),\r\n    DB_URL: requiredURL('DB_URL'),\r\n    DB_PORT: requiredNumber('DB_PORT'),\r\n    DB_USERNAME: requiredString('DB_USERNAME'),\r\n    DB_PASSWORD: requiredString('DB_PASSWORD'),\r\n    DB_DATABASE: requiredString('DB_DATABASE'),\r\n    DB_SSL: z.string().transform((v) => v === 'true'),\r\n  }),\r\n  s3: z.object({\r\n    S3_ACCESS_KEY: requiredString('S3_ACCESS_KEY'),\r\n    S3_SECRET_KEY: requiredString('S3_SECRET_KEY'),\r\n    S3_ENDPOINT: requiredURL('S3_ENDPOINT'),\r\n    S3_BUCKET_NAME: requiredString('S3_BUCKET_NAME'),\r\n    S3_REGION: requiredString('S3_REGION'),\r\n  }),\r\n  cdn: z.object({\r\n    CDN_URL: requiredURL('CDN_URL'),\r\n    CDN_SECRET_KEY: requiredString('CDN_SECRET_KEY'),\r\n  }),\r\n  openai: z.object({\r\n    OPENAI_API_KEY: requiredString('OPENAI_API_KEY'),\r\n  }),\r\n  external: z.object({\r\n    EXTERNAL_EMAIL_API_URL: requiredURL('EXTERNAL_EMAIL_API_URL'),\r\n    REDIS_URL: requiredURL('REDIS_URL'),\r\n    PDF_EDIT_API_URL: requiredURL('PDF_EDIT_API_URL'),\r\n    SERVICE_HOST_SMS: requiredURL('SERVICE_HOST_SMS'),\r\n    SERVICE_SMS_API_KEY: requiredString('SERVICE_SMS_API_KEY'),\r\n    RECAPTCHA_SECRET_KEY: requiredString('RECAPTCHA_SECRET_KEY'),\r\n  }),\r\n  redis: z.object({\r\n    REDIS_POOL_MIN: z.string().transform(Number).optional().default('2'),\r\n    REDIS_POOL_MAX: z.string().transform(Number).optional().default('10'),\r\n    REDIS_RETRY_ATTEMPTS: z.string().transform(Number).optional().default('3'),\r\n    REDIS_RETRY_DELAY_MS: z.string().transform(Number).optional().default('1000'),\r\n    REDIS_CONNECTION_TIMEOUT_MS: z.string().transform(Number).optional().default('5000'),\r\n    REDIS_COMMAND_TIMEOUT_MS: z.string().transform(Number).optional().default('3000'),\r\n  }),\r\n  sepay: z.object({\r\n    SEPAY_HUB_API_KEY: requiredString('SEPAY_HUB_API_KEY'),\r\n    SEPAY_HUB_CLIENT_ID: requiredString('SEPAY_HUB_CLIENT_ID'),\r\n    SEPAY_HUB_CLIENT_SECRET: requiredString('SEPAY_HUB_CLIENT_SECRET'),\r\n    SEPAY_HUB_API_URL: requiredURL('SEPAY_HUB_API_URL'),\r\n    SEPAY_WEBHOOK_API_KEY: requiredString('SEPAY_WEBHOOK_API_KEY'),\r\n  }),\r\n  swagger: z.object({\r\n    SWAGGER_LOCAL_URL: requiredURL('SWAGGER_LOCAL_URL'),\r\n    SWAGGER_DEV_URL: requiredURL('SWAGGER_DEV_URL'),\r\n    SWAGGER_TEST_URL: requiredURL('SWAGGER_TEST_URL'),\r\n    SWAGGER_STAGING_URL: requiredURL('SWAGGER_STAGING_URL'),\r\n    SWAGGER_PROD_URL: requiredURL('SWAGGER_PROD_URL'),\r\n  }),\r\n  zalo: z.object({\r\n    ZALO_APP_ID: requiredString('ZALO_APP_ID'),\r\n    ZALO_APP_SECRET: requiredString('ZALO_APP_SECRET'),\r\n    ZALO_WEBHOOK_SECRET: requiredString('ZALO_WEBHOOK_SECRET'),\r\n    ZALO_WEBHOOK_URL: requiredURL('ZALO_WEBHOOK_URL'),\r\n  }),\r\n  google: z.object({\r\n    GOOGLE_CLIENT_ID: requiredString('GOOGLE_CLIENT_ID'),\r\n    GOOGLE_CLIENT_SECRET: requiredString('GOOGLE_CLIENT_SECRET'),\r\n    GOOGLE_REDIRECT_URI: requiredURL('GOOGLE_REDIRECT_URI'),\r\n    GOOGLE_APPLICATION_CREDENTIALS: requiredString(\r\n      'GOOGLE_APPLICATION_CREDENTIALS',\r\n    ),\r\n    GOOGLE_CLOUD_PROJECT_ID: requiredString('GOOGLE_CLOUD_PROJECT_ID'),\r\n    GOOGLE_CLOUD_STORAGE_BUCKET: requiredString('GOOGLE_CLOUD_STORAGE_BUCKET'),\r\n    GOOGLE_ADS_CLIENT_ID: requiredString('GOOGLE_ADS_CLIENT_ID'),\r\n    GOOGLE_ADS_CLIENT_SECRET: requiredString('GOOGLE_ADS_CLIENT_SECRET'),\r\n    GOOGLE_ADS_DEVELOPER_TOKEN: requiredString('GOOGLE_ADS_DEVELOPER_TOKEN'),\r\n    GOOGLE_ADS_REFRESH_TOKEN: requiredString('GOOGLE_ADS_REFRESH_TOKEN'),\r\n    GOOGLE_ADS_LOGIN_CUSTOMER_ID: requiredString(\r\n      'GOOGLE_ADS_LOGIN_CUSTOMER_ID',\r\n    ),\r\n  }),\r\n  facebook: z.object({\r\n    FACEBOOK_APP_ID: requiredString('FACEBOOK_APP_ID'),\r\n    FACEBOOK_APP_SECRET: requiredString('FACEBOOK_APP_SECRET'),\r\n    FACEBOOK_REDIRECT_URI: requiredURL('FACEBOOK_REDIRECT_URI'),\r\n    FACEBOOK_GRAPH_API_VERSION: requiredString('FACEBOOK_GRAPH_API_VERSION'),\r\n  }),\r\n  agent: z.object({\r\n    AGENT_API_KEY: requiredString('AGENT_API_KEY'),\r\n    ENCRYPTION_SECRET_KEY: requiredString('ENCRYPTION_SECRET_KEY'),\r\n    API_PREFIX_KEY: requiredString('API_PREFIX_KEY'),\r\n    USER_SECRET_MODEL: requiredString('USER_SECRET_MODEL'),\r\n    ADMIN_SECRET_MODEL: requiredString('ADMIN_SECRET_MODEL'),\r\n    API_SECRET_KEY: requiredString('API_SECRET_KEY'),\r\n    MCP_HOST: requiredString('MCP_HOST'),\r\n    MCP_PORT: requiredNumber('MCP_PORT'),\r\n  }),\r\n  email: z.object({\r\n    MAIL_HOST: requiredString('MAIL_HOST'),\r\n    MAIL_PORT: requiredNumber('MAIL_PORT'),\r\n    MAIL_SECURE: z.string().transform((v) => v === 'true'),\r\n    MAIL_USERNAME: requiredString('MAIL_USERNAME'),\r\n    MAIL_PASSWORD: requiredString('MAIL_PASSWORD'),\r\n    MAIL_DEFAULT_FROM: requiredString('MAIL_DEFAULT_FROM'),\r\n  }),\r\n  fpt: z.object({\r\n    FPT_SMS_CLIENT_ID: requiredString('FPT_SMS_CLIENT_ID'),\r\n    FPT_SMS_CLIENT_SECRET: requiredString('FPT_SMS_CLIENT_SECRET'),\r\n    FPT_SMS_SCOPE: requiredString('FPT_SMS_SCOPE'),\r\n    FPT_SMS_API_URL: requiredURL('FPT_SMS_API_URL'),\r\n    FPT_SMS_BRANDNAME: requiredString('FPT_SMS_BRANDNAME'),\r\n  }),\r\n  llmSystemEncryptionKey: z.object({\r\n    USER_SECRECT_MODEL: requiredString('USER_SECRECT_MODEL'),\r\n    ADMIN_SECRECT_MODEL: requiredString('ADMIN_SECRECT_MODEL'),\r\n  })\r\n});\r\n\r\nlet env: z.infer<typeof envSchema>;\r\n\r\ntry {\r\n  env = envSchema.parse({\r\n    misc: process.env,\r\n    app: process.env,\r\n    database: process.env,\r\n    s3: process.env,\r\n    cdn: process.env,\r\n    openai: process.env,\r\n    external: process.env,\r\n    redis: process.env,\r\n    sepay: process.env,\r\n    swagger: process.env,\r\n    zalo: process.env,\r\n    google: process.env,\r\n    facebook: process.env,\r\n    agent: process.env,\r\n    email: process.env,\r\n    fpt: process.env,\r\n    llmSystemEncryptionKey: process.env,\r\n  });\r\n} catch (err: unknown) {\r\n  if (err instanceof ZodError) {\r\n    logger.error(\r\n      '❌ Invalid environment variables:',\r\n      JSON.stringify(err.format(), null, 2),\r\n    );\r\n    process.exit(1);\r\n  }\r\n  throw err;\r\n}\r\n\r\nexport { env };\r\n"], "names": ["env", "configDotenv", "logger", "<PERSON><PERSON>", "requiredString", "name", "z", "string", "trim", "min", "requiredURL", "url", "requiredNumber", "transform", "Number", "refine", "v", "isNaN", "message", "envSchema", "object", "misc", "NODE_ENV", "enum", "PORT", "app", "BASE_URL", "optional", "default", "database", "DB_HOST", "DB_URL", "DB_PORT", "DB_USERNAME", "DB_PASSWORD", "DB_DATABASE", "DB_SSL", "s3", "S3_ACCESS_KEY", "S3_SECRET_KEY", "S3_ENDPOINT", "S3_BUCKET_NAME", "S3_REGION", "cdn", "CDN_URL", "CDN_SECRET_KEY", "openai", "OPENAI_API_KEY", "external", "EXTERNAL_EMAIL_API_URL", "REDIS_URL", "PDF_EDIT_API_URL", "SERVICE_HOST_SMS", "SERVICE_SMS_API_KEY", "RECAPTCHA_SECRET_KEY", "redis", "REDIS_POOL_MIN", "REDIS_POOL_MAX", "REDIS_RETRY_ATTEMPTS", "REDIS_RETRY_DELAY_MS", "REDIS_CONNECTION_TIMEOUT_MS", "REDIS_COMMAND_TIMEOUT_MS", "sepay", "SEPAY_HUB_API_KEY", "SEPAY_HUB_CLIENT_ID", "SEPAY_HUB_CLIENT_SECRET", "SEPAY_HUB_API_URL", "SEPAY_WEBHOOK_API_KEY", "swagger", "SWAGGER_LOCAL_URL", "SWAGGER_DEV_URL", "SWAGGER_TEST_URL", "SWAGGER_STAGING_URL", "SWAGGER_PROD_URL", "zalo", "ZALO_APP_ID", "ZALO_APP_SECRET", "ZALO_WEBHOOK_SECRET", "ZALO_WEBHOOK_URL", "google", "GOOGLE_CLIENT_ID", "GOOGLE_CLIENT_SECRET", "GOOGLE_REDIRECT_URI", "GOOGLE_APPLICATION_CREDENTIALS", "GOOGLE_CLOUD_PROJECT_ID", "GOOGLE_CLOUD_STORAGE_BUCKET", "GOOGLE_ADS_CLIENT_ID", "GOOGLE_ADS_CLIENT_SECRET", "GOOGLE_ADS_DEVELOPER_TOKEN", "GOOGLE_ADS_REFRESH_TOKEN", "GOOGLE_ADS_LOGIN_CUSTOMER_ID", "facebook", "FACEBOOK_APP_ID", "FACEBOOK_APP_SECRET", "FACEBOOK_REDIRECT_URI", "FACEBOOK_GRAPH_API_VERSION", "agent", "AGENT_API_KEY", "ENCRYPTION_SECRET_KEY", "API_PREFIX_KEY", "USER_SECRET_MODEL", "ADMIN_SECRET_MODEL", "API_SECRET_KEY", "MCP_HOST", "MCP_PORT", "email", "MAIL_HOST", "MAIL_PORT", "MAIL_SECURE", "MAIL_USERNAME", "MAIL_PASSWORD", "MAIL_DEFAULT_FROM", "fpt", "FPT_SMS_CLIENT_ID", "FPT_SMS_CLIENT_SECRET", "FPT_SMS_SCOPE", "FPT_SMS_API_URL", "FPT_SMS_BRANDNAME", "llmSystemEncryptionKey", "USER_SECRECT_MODEL", "ADMIN_SECRECT_MODEL", "parse", "process", "err", "ZodError", "error", "JSON", "stringify", "format", "exit"], "mappings": ";;;;+BA2<PERSON><PERSON>;;;eAAAA;;;qBA3KmB;wBACC;wBACN;AAEvBC,IAAAA,oBAAY;AAEZ,MAAMC,SAAS,IAAIC,cAAM,CAAC;AAC1B,MAAMC,iBAAiB,CAACC,OACtBC,MAAC,CAACC,MAAM,GAAGC,IAAI,GAAGC,GAAG,CAAC,GAAG,GAAGJ,KAAK,YAAY,CAAC;AAChD,MAAMK,cAAc,CAACL,OACnBC,MAAC,CAACC,MAAM,GAAGI,GAAG,CAAC,GAAGN,KAAK,oBAAoB,CAAC;AAC9C,MAAMO,iBAAiB,CAACP,OACtBC,MAAC,CACEC,MAAM,GACNM,SAAS,CAACC,QACVC,MAAM,CAAC,CAACC,IAAM,CAACC,MAAMD,IAAI;QAAEE,SAAS,GAAGb,KAAK,iBAAiB,CAAC;IAAC;AAEpE,MAAMc,YAAYb,MAAC,CAACc,MAAM,CAAC;IACzBC,MAAMf,MAAC,CAACc,MAAM,CAAC;QACbE,UAAUhB,MAAC,CAACiB,IAAI,CAAC;YAAC;YAAe;YAAQ;YAAW;SAAa;QACjEC,MAAMZ,eAAe;IACvB;IACAa,KAAKnB,MAAC,CAACc,MAAM,CAAC;QACZM,UAAUpB,MAAC,CAACC,MAAM,GAAGI,GAAG,GAAGgB,QAAQ,GAAGC,OAAO,CAAC;IAChD;IACAC,UAAUvB,MAAC,CAACc,MAAM,CAAC;QACjBU,SAAS1B,eAAe;QACxB2B,QAAQrB,YAAY;QACpBsB,SAASpB,eAAe;QACxBqB,aAAa7B,eAAe;QAC5B8B,aAAa9B,eAAe;QAC5B+B,aAAa/B,eAAe;QAC5BgC,QAAQ9B,MAAC,CAACC,MAAM,GAAGM,SAAS,CAAC,CAACG,IAAMA,MAAM;IAC5C;IACAqB,IAAI/B,MAAC,CAACc,MAAM,CAAC;QACXkB,eAAelC,eAAe;QAC9BmC,eAAenC,eAAe;QAC9BoC,aAAa9B,YAAY;QACzB+B,gBAAgBrC,eAAe;QAC/BsC,WAAWtC,eAAe;IAC5B;IACAuC,KAAKrC,MAAC,CAACc,MAAM,CAAC;QACZwB,SAASlC,YAAY;QACrBmC,gBAAgBzC,eAAe;IACjC;IACA0C,QAAQxC,MAAC,CAACc,MAAM,CAAC;QACf2B,gBAAgB3C,eAAe;IACjC;IACA4C,UAAU1C,MAAC,CAACc,MAAM,CAAC;QACjB6B,wBAAwBvC,YAAY;QACpCwC,WAAWxC,YAAY;QACvByC,kBAAkBzC,YAAY;QAC9B0C,kBAAkB1C,YAAY;QAC9B2C,qBAAqBjD,eAAe;QACpCkD,sBAAsBlD,eAAe;IACvC;IACAmD,OAAOjD,MAAC,CAACc,MAAM,CAAC;QACdoC,gBAAgBlD,MAAC,CAACC,MAAM,GAAGM,SAAS,CAACC,QAAQa,QAAQ,GAAGC,OAAO,CAAC;QAChE6B,gBAAgBnD,MAAC,CAACC,MAAM,GAAGM,SAAS,CAACC,QAAQa,QAAQ,GAAGC,OAAO,CAAC;QAChE8B,sBAAsBpD,MAAC,CAACC,MAAM,GAAGM,SAAS,CAACC,QAAQa,QAAQ,GAAGC,OAAO,CAAC;QACtE+B,sBAAsBrD,MAAC,CAACC,MAAM,GAAGM,SAAS,CAACC,QAAQa,QAAQ,GAAGC,OAAO,CAAC;QACtEgC,6BAA6BtD,MAAC,CAACC,MAAM,GAAGM,SAAS,CAACC,QAAQa,QAAQ,GAAGC,OAAO,CAAC;QAC7EiC,0BAA0BvD,MAAC,CAACC,MAAM,GAAGM,SAAS,CAACC,QAAQa,QAAQ,GAAGC,OAAO,CAAC;IAC5E;IACAkC,OAAOxD,MAAC,CAACc,MAAM,CAAC;QACd2C,mBAAmB3D,eAAe;QAClC4D,qBAAqB5D,eAAe;QACpC6D,yBAAyB7D,eAAe;QACxC8D,mBAAmBxD,YAAY;QAC/ByD,uBAAuB/D,eAAe;IACxC;IACAgE,SAAS9D,MAAC,CAACc,MAAM,CAAC;QAChBiD,mBAAmB3D,YAAY;QAC/B4D,iBAAiB5D,YAAY;QAC7B6D,kBAAkB7D,YAAY;QAC9B8D,qBAAqB9D,YAAY;QACjC+D,kBAAkB/D,YAAY;IAChC;IACAgE,MAAMpE,MAAC,CAACc,MAAM,CAAC;QACbuD,aAAavE,eAAe;QAC5BwE,iBAAiBxE,eAAe;QAChCyE,qBAAqBzE,eAAe;QACpC0E,kBAAkBpE,YAAY;IAChC;IACAqE,QAAQzE,MAAC,CAACc,MAAM,CAAC;QACf4D,kBAAkB5E,eAAe;QACjC6E,sBAAsB7E,eAAe;QACrC8E,qBAAqBxE,YAAY;QACjCyE,gCAAgC/E,eAC9B;QAEFgF,yBAAyBhF,eAAe;QACxCiF,6BAA6BjF,eAAe;QAC5CkF,sBAAsBlF,eAAe;QACrCmF,0BAA0BnF,eAAe;QACzCoF,4BAA4BpF,eAAe;QAC3CqF,0BAA0BrF,eAAe;QACzCsF,8BAA8BtF,eAC5B;IAEJ;IACAuF,UAAUrF,MAAC,CAACc,MAAM,CAAC;QACjBwE,iBAAiBxF,eAAe;QAChCyF,qBAAqBzF,eAAe;QACpC0F,uBAAuBpF,YAAY;QACnCqF,4BAA4B3F,eAAe;IAC7C;IACA4F,OAAO1F,MAAC,CAACc,MAAM,CAAC;QACd6E,eAAe7F,eAAe;QAC9B8F,uBAAuB9F,eAAe;QACtC+F,gBAAgB/F,eAAe;QAC/BgG,mBAAmBhG,eAAe;QAClCiG,oBAAoBjG,eAAe;QACnCkG,gBAAgBlG,eAAe;QAC/BmG,UAAUnG,eAAe;QACzBoG,UAAU5F,eAAe;IAC3B;IACA6F,OAAOnG,MAAC,CAACc,MAAM,CAAC;QACdsF,WAAWtG,eAAe;QAC1BuG,WAAW/F,eAAe;QAC1BgG,aAAatG,MAAC,CAACC,MAAM,GAAGM,SAAS,CAAC,CAACG,IAAMA,MAAM;QAC/C6F,eAAezG,eAAe;QAC9B0G,eAAe1G,eAAe;QAC9B2G,mBAAmB3G,eAAe;IACpC;IACA4G,KAAK1G,MAAC,CAACc,MAAM,CAAC;QACZ6F,mBAAmB7G,eAAe;QAClC8G,uBAAuB9G,eAAe;QACtC+G,eAAe/G,eAAe;QAC9BgH,iBAAiB1G,YAAY;QAC7B2G,mBAAmBjH,eAAe;IACpC;IACAkH,wBAAwBhH,MAAC,CAACc,MAAM,CAAC;QAC/BmG,oBAAoBnH,eAAe;QACnCoH,qBAAqBpH,eAAe;IACtC;AACF;AAEA,IAAIJ;AAEJ,IAAI;IACFA,MAAMmB,UAAUsG,KAAK,CAAC;QACpBpG,MAAMqG,QAAQ1H,GAAG;QACjByB,KAAKiG,QAAQ1H,GAAG;QAChB6B,UAAU6F,QAAQ1H,GAAG;QACrBqC,IAAIqF,QAAQ1H,GAAG;QACf2C,KAAK+E,QAAQ1H,GAAG;QAChB8C,QAAQ4E,QAAQ1H,GAAG;QACnBgD,UAAU0E,QAAQ1H,GAAG;QACrBuD,OAAOmE,QAAQ1H,GAAG;QAClB8D,OAAO4D,QAAQ1H,GAAG;QAClBoE,SAASsD,QAAQ1H,GAAG;QACpB0E,MAAMgD,QAAQ1H,GAAG;QACjB+E,QAAQ2C,QAAQ1H,GAAG;QACnB2F,UAAU+B,QAAQ1H,GAAG;QACrBgG,OAAO0B,QAAQ1H,GAAG;QAClByG,OAAOiB,QAAQ1H,GAAG;QAClBgH,KAAKU,QAAQ1H,GAAG;QAChBsH,wBAAwBI,QAAQ1H,GAAG;IACrC;AACF,EAAE,OAAO2H,KAAc;IACrB,IAAIA,eAAeC,aAAQ,EAAE;QAC3B1H,OAAO2H,KAAK,CACV,oCACAC,KAAKC,SAAS,CAACJ,IAAIK,MAAM,IAAI,MAAM;QAErCN,QAAQO,IAAI,CAAC;IACf;IACA,MAAMN;AACR"}