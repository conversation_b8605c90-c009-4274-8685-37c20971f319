"use strict";
Object.defineProperty(exports, "__esModule", {
    value: true
});
Object.defineProperty(exports, "EmailMarketingModule", {
    enumerable: true,
    get: function() {
        return EmailMarketingModule;
    }
});
const _common = require("@nestjs/common");
const _typeorm = require("@nestjs/typeorm");
const _bullmq = require("@nestjs/bullmq");
const _queue = require("../../../queue");
const _infra = require("../../../infra");
const _usercampaignentity = require("../entities/user-campaign.entity");
const _useraudienceentity = require("../entities/user-audience.entity");
const _useraudiencecustomfieldentity = require("../entities/user-audience-custom-field.entity");
const _usercampaignhistoryentity = require("../entities/user-campaign-history.entity");
const _services = require("./services");
const _emailmarketingprocessor = require("./email-marketing.processor");
const _emailtrackingcontroller = require("./email-tracking.controller");
const _emailmarketingcontroller = require("./email-marketing.controller");
function _ts_decorate(decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for(var i = decorators.length - 1; i >= 0; i--)if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
}
let EmailMarketingModule = class EmailMarketingModule {
};
EmailMarketingModule = _ts_decorate([
    (0, _common.Module)({
        imports: [
            // TypeORM entities
            _typeorm.TypeOrmModule.forFeature([
                _usercampaignentity.UserCampaign,
                _useraudienceentity.UserAudience,
                _useraudiencecustomfieldentity.UserAudienceCustomField,
                _usercampaignhistoryentity.UserCampaignHistory
            ]),
            // Bull queue
            _bullmq.BullModule.registerQueue({
                name: _queue.QueueName.EMAIL_MARKETING
            }),
            // Infrastructure module (Redis, etc.)
            _infra.InfraModule
        ],
        providers: [
            _services.EmailMarketingService,
            _services.EmailTemplateService,
            _services.EmailTrackingService,
            _emailmarketingprocessor.EmailMarketingProcessor
        ],
        controllers: [
            _emailtrackingcontroller.EmailTrackingController,
            _emailmarketingcontroller.EmailMarketingController
        ],
        exports: [
            _services.EmailMarketingService,
            _services.EmailTemplateService,
            _services.EmailTrackingService
        ]
    })
], EmailMarketingModule);

//# sourceMappingURL=email-marketing.module.js.map