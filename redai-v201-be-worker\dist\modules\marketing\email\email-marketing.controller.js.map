{"version": 3, "sources": ["../../../../src/modules/marketing/email/email-marketing.controller.ts"], "sourcesContent": ["import {\r\n  Controller,\r\n  Post,\r\n  Get,\r\n  Delete,\r\n  Param,\r\n  Body,\r\n  Logger,\r\n} from '@nestjs/common';\r\nimport { EmailMarketingService } from './services';\r\n\r\n/**\r\n * Controller quản lý email marketing campaigns\r\n */\r\n@Controller('api/email-marketing')\r\nexport class EmailMarketingController {\r\n  private readonly logger = new Logger(EmailMarketingController.name);\r\n\r\n  constructor(private readonly emailMarketingService: EmailMarketingService) {}\r\n\r\n  /**\r\n   * Tạo jobs email marketing cho một campaign\r\n   * @param campaignId ID của campaign\r\n   * @returns Kết quả tạo jobs\r\n   */\r\n  @Post('campaigns/:campaignId/jobs')\r\n  async createCampaignJobs(@Param('campaignId') campaignId: string) {\r\n    try {\r\n      const id = parseInt(campaignId);\r\n      if (isNaN(id)) {\r\n        return {\r\n          success: false,\r\n          message: 'Invalid campaign ID',\r\n        };\r\n      }\r\n\r\n      const jobCount =\r\n        await this.emailMarketingService.createEmailMarketingJobs(id);\r\n\r\n      return {\r\n        success: true,\r\n        message: `Created ${jobCount} email marketing jobs`,\r\n        data: {\r\n          campaignId: id,\r\n          jobCount,\r\n        },\r\n      };\r\n    } catch (error) {\r\n      this.logger.error(\r\n        `Error creating campaign jobs: ${error.message}`,\r\n        error.stack,\r\n      );\r\n      return {\r\n        success: false,\r\n        message: error.message,\r\n      };\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Hủy tất cả jobs của một campaign\r\n   * @param campaignId ID của campaign\r\n   * @returns Kết quả hủy jobs\r\n   */\r\n  @Delete('campaigns/:campaignId/jobs')\r\n  async cancelCampaignJobs(@Param('campaignId') campaignId: string) {\r\n    try {\r\n      const id = parseInt(campaignId);\r\n      if (isNaN(id)) {\r\n        return {\r\n          success: false,\r\n          message: 'Invalid campaign ID',\r\n        };\r\n      }\r\n\r\n      const canceledCount =\r\n        await this.emailMarketingService.cancelCampaignJobs(id);\r\n\r\n      return {\r\n        success: true,\r\n        message: `Canceled ${canceledCount} jobs`,\r\n        data: {\r\n          campaignId: id,\r\n          canceledCount,\r\n        },\r\n      };\r\n    } catch (error) {\r\n      this.logger.error(\r\n        `Error canceling campaign jobs: ${error.message}`,\r\n        error.stack,\r\n      );\r\n      return {\r\n        success: false,\r\n        message: error.message,\r\n      };\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Lấy thống kê campaign\r\n   * @param campaignId ID của campaign\r\n   * @returns Thống kê campaign\r\n   */\r\n  @Get('campaigns/:campaignId/stats')\r\n  async getCampaignStats(@Param('campaignId') campaignId: string) {\r\n    try {\r\n      const id = parseInt(campaignId);\r\n      if (isNaN(id)) {\r\n        return {\r\n          success: false,\r\n          message: 'Invalid campaign ID',\r\n        };\r\n      }\r\n\r\n      const stats = await this.emailMarketingService.getCampaignStats(id);\r\n\r\n      return {\r\n        success: true,\r\n        data: stats,\r\n      };\r\n    } catch (error) {\r\n      this.logger.error(\r\n        `Error getting campaign stats: ${error.message}`,\r\n        error.stack,\r\n      );\r\n      return {\r\n        success: false,\r\n        message: error.message,\r\n      };\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Lấy trạng thái queue\r\n   * @returns Thông tin trạng thái queue\r\n   */\r\n  @Get('queue/status')\r\n  async getQueueStatus() {\r\n    try {\r\n      const status = await this.emailMarketingService.getQueueStatus();\r\n\r\n      return {\r\n        success: true,\r\n        data: status,\r\n      };\r\n    } catch (error) {\r\n      this.logger.error(\r\n        `Error getting queue status: ${error.message}`,\r\n        error.stack,\r\n      );\r\n      return {\r\n        success: false,\r\n        message: error.message,\r\n      };\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Test endpoint để tạo campaign mẫu\r\n   * @param body Dữ liệu test\r\n   * @returns Kết quả test\r\n   */\r\n  @Post('test/create-sample-jobs')\r\n  async createSampleJobs(@Body() body: { campaignId: number; count?: number }) {\r\n    try {\r\n      const { campaignId, count = 1 } = body;\r\n\r\n      if (!campaignId) {\r\n        return {\r\n          success: false,\r\n          message: 'Campaign ID is required',\r\n        };\r\n      }\r\n\r\n      const jobCount =\r\n        await this.emailMarketingService.createEmailMarketingJobs(campaignId);\r\n\r\n      return {\r\n        success: true,\r\n        message: `Test completed. Created ${jobCount} jobs for campaign ${campaignId}`,\r\n        data: {\r\n          campaignId,\r\n          jobCount,\r\n        },\r\n      };\r\n    } catch (error) {\r\n      this.logger.error(\r\n        `Error creating sample jobs: ${error.message}`,\r\n        error.stack,\r\n      );\r\n      return {\r\n        success: false,\r\n        message: error.message,\r\n      };\r\n    }\r\n  }\r\n}\r\n"], "names": ["EmailMarketingController", "createCampaignJobs", "campaignId", "id", "parseInt", "isNaN", "success", "message", "jobCount", "emailMarketingService", "createEmailMarketingJobs", "data", "error", "logger", "stack", "cancelCampaignJobs", "canceledCount", "getCampaignStats", "stats", "getQueueStatus", "status", "createSampleJobs", "body", "count", "constructor", "<PERSON><PERSON>", "name"], "mappings": ";;;;+BAeaA;;;eAAAA;;;wBAPN;0BAC+B;;;;;;;;;;;;;;;AAM/B,IAAA,AAAMA,2BAAN,MAAMA;IAKX;;;;GAIC,GACD,MACMC,mBAAmB,AAAqBC,UAAkB,EAAE;QAChE,IAAI;YACF,MAAMC,KAAKC,SAASF;YACpB,IAAIG,MAAMF,KAAK;gBACb,OAAO;oBACLG,SAAS;oBACTC,SAAS;gBACX;YACF;YAEA,MAAMC,WACJ,MAAM,IAAI,CAACC,qBAAqB,CAACC,wBAAwB,CAACP;YAE5D,OAAO;gBACLG,SAAS;gBACTC,SAAS,CAAC,QAAQ,EAAEC,SAAS,qBAAqB,CAAC;gBACnDG,MAAM;oBACJT,YAAYC;oBACZK;gBACF;YACF;QACF,EAAE,OAAOI,OAAO;YACd,IAAI,CAACC,MAAM,CAACD,KAAK,CACf,CAAC,8BAA8B,EAAEA,MAAML,OAAO,EAAE,EAChDK,MAAME,KAAK;YAEb,OAAO;gBACLR,SAAS;gBACTC,SAASK,MAAML,OAAO;YACxB;QACF;IACF;IAEA;;;;GAIC,GACD,MACMQ,mBAAmB,AAAqBb,UAAkB,EAAE;QAChE,IAAI;YACF,MAAMC,KAAKC,SAASF;YACpB,IAAIG,MAAMF,KAAK;gBACb,OAAO;oBACLG,SAAS;oBACTC,SAAS;gBACX;YACF;YAEA,MAAMS,gBACJ,MAAM,IAAI,CAACP,qBAAqB,CAACM,kBAAkB,CAACZ;YAEtD,OAAO;gBACLG,SAAS;gBACTC,SAAS,CAAC,SAAS,EAAES,cAAc,KAAK,CAAC;gBACzCL,MAAM;oBACJT,YAAYC;oBACZa;gBACF;YACF;QACF,EAAE,OAAOJ,OAAO;YACd,IAAI,CAACC,MAAM,CAACD,KAAK,CACf,CAAC,+BAA+B,EAAEA,MAAML,OAAO,EAAE,EACjDK,MAAME,KAAK;YAEb,OAAO;gBACLR,SAAS;gBACTC,SAASK,MAAML,OAAO;YACxB;QACF;IACF;IAEA;;;;GAIC,GACD,MACMU,iBAAiB,AAAqBf,UAAkB,EAAE;QAC9D,IAAI;YACF,MAAMC,KAAKC,SAASF;YACpB,IAAIG,MAAMF,KAAK;gBACb,OAAO;oBACLG,SAAS;oBACTC,SAAS;gBACX;YACF;YAEA,MAAMW,QAAQ,MAAM,IAAI,CAACT,qBAAqB,CAACQ,gBAAgB,CAACd;YAEhE,OAAO;gBACLG,SAAS;gBACTK,MAAMO;YACR;QACF,EAAE,OAAON,OAAO;YACd,IAAI,CAACC,MAAM,CAACD,KAAK,CACf,CAAC,8BAA8B,EAAEA,MAAML,OAAO,EAAE,EAChDK,MAAME,KAAK;YAEb,OAAO;gBACLR,SAAS;gBACTC,SAASK,MAAML,OAAO;YACxB;QACF;IACF;IAEA;;;GAGC,GACD,MACMY,iBAAiB;QACrB,IAAI;YACF,MAAMC,SAAS,MAAM,IAAI,CAACX,qBAAqB,CAACU,cAAc;YAE9D,OAAO;gBACLb,SAAS;gBACTK,MAAMS;YACR;QACF,EAAE,OAAOR,OAAO;YACd,IAAI,CAACC,MAAM,CAACD,KAAK,CACf,CAAC,4BAA4B,EAAEA,MAAML,OAAO,EAAE,EAC9CK,MAAME,KAAK;YAEb,OAAO;gBACLR,SAAS;gBACTC,SAASK,MAAML,OAAO;YACxB;QACF;IACF;IAEA;;;;GAIC,GACD,MACMc,iBAAiB,AAAQC,IAA4C,EAAE;QAC3E,IAAI;YACF,MAAM,EAAEpB,UAAU,EAAEqB,QAAQ,CAAC,EAAE,GAAGD;YAElC,IAAI,CAACpB,YAAY;gBACf,OAAO;oBACLI,SAAS;oBACTC,SAAS;gBACX;YACF;YAEA,MAAMC,WACJ,MAAM,IAAI,CAACC,qBAAqB,CAACC,wBAAwB,CAACR;YAE5D,OAAO;gBACLI,SAAS;gBACTC,SAAS,CAAC,wBAAwB,EAAEC,SAAS,mBAAmB,EAAEN,YAAY;gBAC9ES,MAAM;oBACJT;oBACAM;gBACF;YACF;QACF,EAAE,OAAOI,OAAO;YACd,IAAI,CAACC,MAAM,CAACD,KAAK,CACf,CAAC,4BAA4B,EAAEA,MAAML,OAAO,EAAE,EAC9CK,MAAME,KAAK;YAEb,OAAO;gBACLR,SAAS;gBACTC,SAASK,MAAML,OAAO;YACxB;QACF;IACF;IAjLAiB,YAAY,AAAiBf,qBAA4C,CAAE;aAA9CA,wBAAAA;aAFZI,SAAS,IAAIY,cAAM,CAACzB,yBAAyB0B,IAAI;IAEU;AAkL9E"}