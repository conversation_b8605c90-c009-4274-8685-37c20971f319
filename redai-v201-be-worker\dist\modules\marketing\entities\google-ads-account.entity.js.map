{"version": 3, "sources": ["../../../../src/modules/marketing/entities/google-ads-account.entity.ts"], "sourcesContent": ["import { Column, Entity, PrimaryGeneratedColumn } from 'typeorm';\r\n\r\n/**\r\n * Entity đại diện cho bảng google_ads_accounts trong cơ sở dữ liệu\r\n * Lưu trữ thông tin tài khoản Google Ads của người dùng\r\n */\r\n@Entity('google_ads_accounts')\r\nexport class GoogleAdsAccount {\r\n  /**\r\n   * ID của tài khoản Google Ads trong hệ thống\r\n   */\r\n  @PrimaryGeneratedColumn({ name: 'id', type: 'bigint' })\r\n  id: number;\r\n\r\n  /**\r\n   * ID của người dùng\r\n   */\r\n  @Column({ name: 'user_id', nullable: false, comment: 'ID của người dùng' })\r\n  userId: number;\r\n\r\n  /**\r\n   * Customer ID của tài khoản Google Ads\r\n   */\r\n  @Column({\r\n    name: 'customer_id',\r\n    nullable: false,\r\n    comment: 'Customer ID của tài khoản Google Ads',\r\n  })\r\n  customerId: string;\r\n\r\n  /**\r\n   * Refresh token để truy cập Google Ads API\r\n   */\r\n  @Column({\r\n    name: 'refresh_token',\r\n    nullable: false,\r\n    comment: 'Refresh token để truy cập Google Ads API',\r\n  })\r\n  refreshToken: string;\r\n\r\n  /**\r\n   * Tên tài khoản\r\n   */\r\n  @Column({\r\n    name: 'name',\r\n    length: 255,\r\n    nullable: true,\r\n    comment: 'Tên tài khoản',\r\n  })\r\n  name: string;\r\n\r\n  /**\r\n   * Trạng thái tài khoản\r\n   */\r\n  @Column({\r\n    name: 'status',\r\n    length: 20,\r\n    nullable: false,\r\n    default: 'active',\r\n    comment: 'Trạng thái tài khoản',\r\n  })\r\n  status: string;\r\n\r\n  /**\r\n   * Thời gian tạo (Unix timestamp)\r\n   */\r\n  @Column({\r\n    name: 'created_at',\r\n    type: 'bigint',\r\n    nullable: false,\r\n    comment: 'Thời gian tạo',\r\n  })\r\n  createdAt: number;\r\n\r\n  /**\r\n   * Thời gian cập nhật (Unix timestamp)\r\n   */\r\n  @Column({\r\n    name: 'updated_at',\r\n    type: 'bigint',\r\n    nullable: true,\r\n    comment: 'Thời gian cập nhật',\r\n  })\r\n  updatedAt: number;\r\n}\r\n"], "names": ["GoogleAdsAccount", "name", "type", "nullable", "comment", "length", "default"], "mappings": ";;;;+BAOaA;;;eAAAA;;;yBAP0C;;;;;;;;;;AAOhD,IAAA,AAAMA,mBAAN,MAAMA;AA6Eb;;;QAzE4BC,MAAM;QAAMC,MAAM;;;;;;QAMlCD,MAAM;QAAWE,UAAU;QAAOC,SAAS;;;;;;QAOnDH,MAAM;QACNE,UAAU;QACVC,SAAS;;;;;;QAQTH,MAAM;QACNE,UAAU;QACVC,SAAS;;;;;;QAQTH,MAAM;QACNI,QAAQ;QACRF,UAAU;QACVC,SAAS;;;;;;QAQTH,MAAM;QACNI,QAAQ;QACRF,UAAU;QACVG,SAAS;QACTF,SAAS;;;;;;QAQTH,MAAM;QACNC,MAAM;QACNC,UAAU;QACVC,SAAS;;;;;;QAQTH,MAAM;QACNC,MAAM;QACNC,UAAU;QACVC,SAAS"}