"use strict";
Object.defineProperty(exports, "__esModule", {
    value: true
});
function _export(target, all) {
    for(var name in all)Object.defineProperty(target, name, {
        enumerable: true,
        get: Object.getOwnPropertyDescriptor(all, name).get
    });
}
_export(exports, {
    get CreateCustomFieldDto () {
        return CreateCustomFieldDto;
    },
    get CustomFieldType () {
        return CustomFieldType;
    }
});
const _classvalidator = require("class-validator");
const _swagger = require("@nestjs/swagger");
function _ts_decorate(decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for(var i = decorators.length - 1; i >= 0; i--)if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
}
function _ts_metadata(k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
}
var CustomFieldType = /*#__PURE__*/ function(CustomFieldType) {
    CustomFieldType["TEXT"] = "text";
    CustomFieldType["NUMBER"] = "number";
    CustomFieldType["DATE"] = "date";
    CustomFieldType["BOOLEAN"] = "boolean";
    CustomFieldType["JSON"] = "json";
    return CustomFieldType;
}({});
let CreateCustomFieldDto = class CreateCustomFieldDto {
};
_ts_decorate([
    (0, _swagger.ApiProperty)({
        description: 'Tên trường',
        example: 'Địa chỉ'
    }),
    (0, _classvalidator.IsNotEmpty)({
        message: 'Tên trường không được để trống'
    }),
    (0, _classvalidator.IsString)({
        message: 'Tên trường phải là chuỗi'
    }),
    _ts_metadata("design:type", String)
], CreateCustomFieldDto.prototype, "fieldName", void 0);
_ts_decorate([
    (0, _swagger.ApiProperty)({
        description: 'Giá trị trường',
        example: 'Hà Nội, Việt Nam'
    }),
    (0, _classvalidator.IsNotEmpty)({
        message: 'Giá trị trường không được để trống'
    }),
    _ts_metadata("design:type", Object)
], CreateCustomFieldDto.prototype, "fieldValue", void 0);
_ts_decorate([
    (0, _swagger.ApiProperty)({
        description: 'Kiểu dữ liệu của trường',
        enum: CustomFieldType,
        example: "text"
    }),
    (0, _classvalidator.IsNotEmpty)({
        message: 'Kiểu dữ liệu không được để trống'
    }),
    (0, _classvalidator.IsIn)(Object.values(CustomFieldType), {
        message: `Kiểu dữ liệu phải là một trong các giá trị: ${Object.values(CustomFieldType).join(', ')}`
    }),
    _ts_metadata("design:type", String)
], CreateCustomFieldDto.prototype, "fieldType", void 0);

//# sourceMappingURL=create-custom-field.dto.js.map