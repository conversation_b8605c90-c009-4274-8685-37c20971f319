{"version": 3, "sources": ["../../../src/shared/services/email.service.ts"], "sourcesContent": ["import { Injectable, Logger } from '@nestjs/common';\r\nimport * as nodemailer from 'nodemailer';\r\nimport { env } from '../../config/env';\r\n\r\n/**\r\n * Service xử lý gửi email\r\n */\r\n@Injectable()\r\nexport class EmailService {\r\n  private readonly logger = new Logger(EmailService.name);\r\n  private transporter: nodemailer.Transporter;\r\n\r\n  constructor() {\r\n    // Cấu hình transporter cho nodemailer\r\n    this.transporter = nodemailer.createTransport({\r\n      host: env.email.MAIL_HOST,\r\n      port: Number(env.email.MAIL_PORT),\r\n      secure: env.email.MAIL_SECURE,\r\n      auth: {\r\n        user: env.email.MAIL_USERNAME,\r\n        pass: env.email.MAIL_PASSWORD,\r\n      },\r\n    });\r\n  }\r\n\r\n  /**\r\n   * Gửi email\r\n   * @param to Địa chỉ email người nhận\r\n   * @param subject Tiêu đề email\r\n   * @param html Nội dung email dạng HTML\r\n   * @param from Địa chỉ email người gửi (mặc định lấy từ biến môi trường)\r\n   * @returns Kết quả gửi email\r\n   */\r\n  async sendEmail(\r\n    to: string | string[],\r\n    subject: string,\r\n    html: string,\r\n    from: string = env.email.MAIL_DEFAULT_FROM || '<EMAIL>',\r\n  ): Promise<boolean> {\r\n    try {\r\n      const mailOptions = {\r\n        from,\r\n        to,\r\n        subject,\r\n        html,\r\n      };\r\n\r\n      const info = await this.transporter.sendMail(mailOptions);\r\n      this.logger.log(`Email sent: ${info.messageId}`);\r\n      return true;\r\n    } catch (error) {\r\n      this.logger.error('Failed to send email', error);\r\n      return false;\r\n    }\r\n  }\r\n}\r\n"], "names": ["EmailService", "sendEmail", "to", "subject", "html", "from", "env", "email", "MAIL_DEFAULT_FROM", "mailOptions", "info", "transporter", "sendMail", "logger", "log", "messageId", "error", "constructor", "<PERSON><PERSON>", "name", "nodemailer", "createTransport", "host", "MAIL_HOST", "port", "Number", "MAIL_PORT", "secure", "MAIL_SECURE", "auth", "user", "MAIL_USERNAME", "pass", "MAIL_PASSWORD"], "mappings": ";;;;+BAQaA;;;eAAAA;;;wBARsB;oEACP;qBACR;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAMb,IAAA,AAAMA,eAAN,MAAMA;IAiBX;;;;;;;GAOC,GACD,MAAMC,UACJC,EAAqB,EACrBC,OAAe,EACfC,IAAY,EACZC,OAAeC,QAAG,CAACC,KAAK,CAACC,iBAAiB,IAAI,oBAAoB,EAChD;QAClB,IAAI;YACF,MAAMC,cAAc;gBAClBJ;gBACAH;gBACAC;gBACAC;YACF;YAEA,MAAMM,OAAO,MAAM,IAAI,CAACC,WAAW,CAACC,QAAQ,CAACH;YAC7C,IAAI,CAACI,MAAM,CAACC,GAAG,CAAC,CAAC,YAAY,EAAEJ,KAAKK,SAAS,EAAE;YAC/C,OAAO;QACT,EAAE,OAAOC,OAAO;YACd,IAAI,CAACH,MAAM,CAACG,KAAK,CAAC,wBAAwBA;YAC1C,OAAO;QACT;IACF;IA1CAC,aAAc;aAHGJ,SAAS,IAAIK,cAAM,CAAClB,aAAamB,IAAI;QAIpD,sCAAsC;QACtC,IAAI,CAACR,WAAW,GAAGS,YAAWC,eAAe,CAAC;YAC5CC,MAAMhB,QAAG,CAACC,KAAK,CAACgB,SAAS;YACzBC,MAAMC,OAAOnB,QAAG,CAACC,KAAK,CAACmB,SAAS;YAChCC,QAAQrB,QAAG,CAACC,KAAK,CAACqB,WAAW;YAC7BC,MAAM;gBACJC,MAAMxB,QAAG,CAACC,KAAK,CAACwB,aAAa;gBAC7BC,MAAM1B,QAAG,CAACC,KAAK,CAAC0B,aAAa;YAC/B;QACF;IACF;AAgCF"}