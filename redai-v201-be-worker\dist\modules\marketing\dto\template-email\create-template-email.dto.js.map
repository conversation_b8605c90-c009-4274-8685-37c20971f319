{"version": 3, "sources": ["../../../../../src/modules/marketing/dto/template-email/create-template-email.dto.ts"], "sourcesContent": ["import { ApiProperty } from '@nestjs/swagger';\r\nimport {\r\n  IsArray,\r\n  IsNotEmpty,\r\n  IsOptional,\r\n  IsString,\r\n  MaxLength,\r\n} from 'class-validator';\r\n\r\n/**\r\n * DTO para crear un nuevo template de email\r\n */\r\nexport class CreateTemplateEmailDto {\r\n  @ApiProperty({\r\n    description: 'Nombre del template',\r\n    example: 'Plantilla de bienvenida',\r\n    maxLength: 255,\r\n  })\r\n  @IsString()\r\n  @IsNotEmpty()\r\n  @MaxLength(255)\r\n  name: string;\r\n\r\n  @ApiProperty({\r\n    description: 'Asunto del email',\r\n    example: 'Bienvenido a nuestra plataforma',\r\n    maxLength: 255,\r\n  })\r\n  @IsString()\r\n  @IsNotEmpty()\r\n  @MaxLength(255)\r\n  subject: string;\r\n\r\n  @ApiProperty({\r\n    description: 'Contenido HTML del email',\r\n    example:\r\n      '<h1>Bienvenido</h1><p>Gracias por registrarte en nuestra plataforma.</p>',\r\n  })\r\n  @IsString()\r\n  @IsNotEmpty()\r\n  content: string;\r\n\r\n  @ApiProperty({\r\n    description: 'Tags asociados al template',\r\n    example: ['bienvenida', 'registro'],\r\n    required: false,\r\n  })\r\n  @IsArray()\r\n  @IsOptional()\r\n  tags?: string[];\r\n\r\n  @ApiProperty({\r\n    description: 'Placeholders utilizados en el template',\r\n    example: ['userName', 'companyName', 'date'],\r\n    required: false,\r\n    type: [String],\r\n  })\r\n  @IsArray()\r\n  @IsString({ each: true })\r\n  @IsOptional()\r\n  placeholders?: string[];\r\n}\r\n"], "names": ["CreateTemplateEmailDto", "description", "example", "max<PERSON><PERSON><PERSON>", "required", "type", "String", "each"], "mappings": ";;;;+BAYaA;;;eAAAA;;;yBAZe;gCAOrB;;;;;;;;;;AAKA,IAAA,AAAMA,yBAAN,MAAMA;AAiDb;;;QA/CIC,aAAa;QACbC,SAAS;QACTC,WAAW;;;;;;;;;QAQXF,aAAa;QACbC,SAAS;QACTC,WAAW;;;;;;;;;QAQXF,aAAa;QACbC,SACE;;;;;;;;QAOFD,aAAa;QACbC,SAAS;YAAC;YAAc;SAAW;QACnCE,UAAU;;;;;;;;QAOVH,aAAa;QACbC,SAAS;YAAC;YAAY;YAAe;SAAO;QAC5CE,UAAU;QACVC,MAAM;YAACC;SAAO;;;;QAGJC,MAAM"}