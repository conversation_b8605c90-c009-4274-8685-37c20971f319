"use strict";
Object.defineProperty(exports, "__esModule", {
    value: true
});
function _export(target, all) {
    for(var name in all)Object.defineProperty(target, name, {
        enumerable: true,
        get: Object.getOwnPropertyDescriptor(all, name).get
    });
}
_export(exports, {
    get PaginatedResponseDto () {
        return PaginatedResponseDto;
    },
    get PaginationMetaDto () {
        return PaginationMetaDto;
    }
});
const _swagger = require("@nestjs/swagger");
function _ts_decorate(decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for(var i = decorators.length - 1; i >= 0; i--)if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
}
function _ts_metadata(k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
}
let PaginationMetaDto = class PaginationMetaDto {
};
_ts_decorate([
    (0, _swagger.ApiProperty)({
        description: 'Tổng số item',
        example: 100
    }),
    _ts_metadata("design:type", Number)
], PaginationMetaDto.prototype, "total", void 0);
_ts_decorate([
    (0, _swagger.ApiProperty)({
        description: 'Trang hiện tại',
        example: 1
    }),
    _ts_metadata("design:type", Number)
], PaginationMetaDto.prototype, "page", void 0);
_ts_decorate([
    (0, _swagger.ApiProperty)({
        description: 'Số lượng item trên mỗi trang',
        example: 10
    }),
    _ts_metadata("design:type", Number)
], PaginationMetaDto.prototype, "limit", void 0);
_ts_decorate([
    (0, _swagger.ApiProperty)({
        description: 'Tổng số trang',
        example: 10
    }),
    _ts_metadata("design:type", Number)
], PaginationMetaDto.prototype, "totalPages", void 0);
_ts_decorate([
    (0, _swagger.ApiProperty)({
        description: 'Có trang trước không',
        example: false
    }),
    _ts_metadata("design:type", Boolean)
], PaginationMetaDto.prototype, "hasPreviousPage", void 0);
_ts_decorate([
    (0, _swagger.ApiProperty)({
        description: 'Có trang sau không',
        example: true
    }),
    _ts_metadata("design:type", Boolean)
], PaginationMetaDto.prototype, "hasNextPage", void 0);
let PaginatedResponseDto = class PaginatedResponseDto {
};
_ts_decorate([
    (0, _swagger.ApiProperty)({
        description: 'Dữ liệu',
        isArray: true
    }),
    _ts_metadata("design:type", Array)
], PaginatedResponseDto.prototype, "data", void 0);
_ts_decorate([
    (0, _swagger.ApiProperty)({
        description: 'Thông tin phân trang',
        type: ()=>PaginationMetaDto
    }),
    _ts_metadata("design:type", typeof PaginationMetaDto === "undefined" ? Object : PaginationMetaDto)
], PaginatedResponseDto.prototype, "meta", void 0);

//# sourceMappingURL=paginated-response.dto.js.map