"use strict";
Object.defineProperty(exports, "__esModule", {
    value: true
});
Object.defineProperty(exports, "CreateAudienceDto", {
    enumerable: true,
    get: function() {
        return CreateAudienceDto;
    }
});
const _classvalidator = require("class-validator");
const _swagger = require("@nestjs/swagger");
const _classtransformer = require("class-transformer");
const _createcustomfielddto = require("./create-custom-field.dto");
function _ts_decorate(decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for(var i = decorators.length - 1; i >= 0; i--)if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
}
function _ts_metadata(k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
}
let CreateAudienceDto = class CreateAudienceDto {
};
_ts_decorate([
    (0, _swagger.ApiProperty)({
        description: 'Email của khách hàng',
        example: '<EMAIL>'
    }),
    (0, _classvalidator.IsNotEmpty)({
        message: 'Email không được để trống'
    }),
    (0, _classvalidator.IsEmail)({}, {
        message: 'Email không hợp lệ'
    }),
    _ts_metadata("design:type", String)
], CreateAudienceDto.prototype, "email", void 0);
_ts_decorate([
    (0, _swagger.ApiProperty)({
        description: 'Số điện thoại của khách hàng',
        example: '+84912345678'
    }),
    (0, _classvalidator.IsOptional)(),
    (0, _classvalidator.IsPhoneNumber)(undefined, {
        message: 'Số điện thoại không hợp lệ'
    }),
    _ts_metadata("design:type", String)
], CreateAudienceDto.prototype, "phone", void 0);
_ts_decorate([
    (0, _swagger.ApiProperty)({
        description: 'Danh sách các trường tùy chỉnh',
        type: [
            _createcustomfielddto.CreateCustomFieldDto
        ],
        required: false
    }),
    (0, _classvalidator.IsOptional)(),
    (0, _classvalidator.ValidateNested)({
        each: true
    }),
    (0, _classtransformer.Type)(()=>_createcustomfielddto.CreateCustomFieldDto),
    _ts_metadata("design:type", Array)
], CreateAudienceDto.prototype, "customFields", void 0);
_ts_decorate([
    (0, _swagger.ApiProperty)({
        description: 'Danh sách ID của các tag',
        example: [
            1,
            2,
            3
        ],
        required: false,
        type: [
            Number
        ]
    }),
    (0, _classvalidator.IsOptional)(),
    _ts_metadata("design:type", Array)
], CreateAudienceDto.prototype, "tagIds", void 0);

//# sourceMappingURL=create-audience.dto.js.map