{"version": 3, "sources": ["../../../../../src/modules/marketing/dto/zalo/zalo-campaign.dto.ts"], "sourcesContent": ["import { ApiProperty } from '@nestjs/swagger';\r\nimport {\r\n  IsArray,\r\n  IsEnum,\r\n  IsNotEmpty,\r\n  IsNumber,\r\n  IsObject,\r\n  IsOptional,\r\n  IsString,\r\n} from 'class-validator';\r\n\r\n/**\r\n * Enum cho loại chiến dịch Zalo\r\n */\r\nexport enum ZaloCampaignType {\r\n  MESSAGE = 'message',\r\n  ZNS = 'zns',\r\n}\r\n\r\n/**\r\n * Enum cho trạng thái chiến dịch Zalo\r\n */\r\nexport enum ZaloCampaignStatus {\r\n  DRAFT = 'draft',\r\n  SCHEDULED = 'scheduled',\r\n  RUNNING = 'running',\r\n  COMPLETED = 'completed',\r\n  CANCELLED = 'cancelled',\r\n}\r\n\r\n/**\r\n * DTO cho nội dung tin nhắn trong chiến dịch Zalo\r\n */\r\nexport class ZaloCampaignMessageContentDto {\r\n  @ApiProperty({\r\n    description: 'Loại tin nhắn (text, image, file, template)',\r\n    example: 'text',\r\n  })\r\n  @IsString()\r\n  @IsNotEmpty()\r\n  type: string;\r\n\r\n  @ApiProperty({\r\n    description: 'Nội dung tin nhắn văn bản',\r\n    example: 'Xin chào! Cảm ơn bạn đã quan tâm đến sản phẩm của chúng tôi.',\r\n    required: false,\r\n  })\r\n  @IsString()\r\n  @IsOptional()\r\n  text?: string;\r\n\r\n  @ApiProperty({\r\n    description: 'URL của hình ảnh',\r\n    example: 'https://example.com/image.jpg',\r\n    required: false,\r\n  })\r\n  @IsString()\r\n  @IsOptional()\r\n  imageUrl?: string;\r\n\r\n  @ApiProperty({\r\n    description: 'URL của file',\r\n    example: 'https://example.com/document.pdf',\r\n    required: false,\r\n  })\r\n  @IsString()\r\n  @IsOptional()\r\n  fileUrl?: string;\r\n\r\n  @ApiProperty({\r\n    description: 'ID của template',\r\n    example: 'template123',\r\n    required: false,\r\n  })\r\n  @IsString()\r\n  @IsOptional()\r\n  templateId?: string;\r\n\r\n  @ApiProperty({\r\n    description: 'Dữ liệu cho template',\r\n    example: { name: '{name}', product: '{product}' },\r\n    required: false,\r\n  })\r\n  @IsObject()\r\n  @IsOptional()\r\n  templateData?: Record<string, any>;\r\n}\r\n\r\n/**\r\n * DTO cho nội dung ZNS trong chiến dịch Zalo\r\n */\r\nexport class ZaloCampaignZnsContentDto {\r\n  @ApiProperty({\r\n    description: 'ID của template ZNS',\r\n    example: 'template*********',\r\n  })\r\n  @IsString()\r\n  @IsNotEmpty()\r\n  templateId: string;\r\n\r\n  @ApiProperty({\r\n    description: 'Dữ liệu cho template',\r\n    example: { orderId: '{orderId}', shopName: '{shopName}' },\r\n  })\r\n  @IsObject()\r\n  @IsNotEmpty()\r\n  templateData: Record<string, any>;\r\n}\r\n\r\n/**\r\n * DTO cho việc tạo chiến dịch Zalo\r\n */\r\nexport class CreateZaloCampaignDto {\r\n  @ApiProperty({\r\n    description: 'ID của Official Account',\r\n    example: '*********',\r\n  })\r\n  @IsString()\r\n  @IsNotEmpty()\r\n  oaId: string;\r\n\r\n  @ApiProperty({\r\n    description: 'Tên của chiến dịch',\r\n    example: 'Chiến dịch khuyến mãi tháng 7',\r\n  })\r\n  @IsString()\r\n  @IsNotEmpty()\r\n  name: string;\r\n\r\n  @ApiProperty({\r\n    description: 'Mô tả của chiến dịch',\r\n    example: 'Chiến dịch khuyến mãi dành cho khách hàng VIP',\r\n    required: false,\r\n  })\r\n  @IsString()\r\n  @IsOptional()\r\n  description?: string;\r\n\r\n  @ApiProperty({\r\n    description: 'Loại chiến dịch',\r\n    enum: ZaloCampaignType,\r\n    example: ZaloCampaignType.MESSAGE,\r\n  })\r\n  @IsEnum(ZaloCampaignType)\r\n  @IsNotEmpty()\r\n  type: ZaloCampaignType;\r\n\r\n  @ApiProperty({\r\n    description: 'ID của phân đoạn',\r\n    example: 1,\r\n  })\r\n  @IsNumber()\r\n  @IsNotEmpty()\r\n  segmentId: number;\r\n\r\n  @ApiProperty({\r\n    description: 'Thời điểm bắt đầu (Unix timestamp)',\r\n    example: 1625097600000,\r\n    required: false,\r\n  })\r\n  @IsNumber()\r\n  @IsOptional()\r\n  scheduledAt?: number;\r\n\r\n  @ApiProperty({\r\n    description: 'Nội dung tin nhắn (chỉ dùng khi type là message)',\r\n    type: ZaloCampaignMessageContentDto,\r\n    required: false,\r\n  })\r\n  @IsObject()\r\n  @IsOptional()\r\n  messageContent?: ZaloCampaignMessageContentDto;\r\n\r\n  @ApiProperty({\r\n    description: 'Nội dung ZNS (chỉ dùng khi type là zns)',\r\n    type: ZaloCampaignZnsContentDto,\r\n    required: false,\r\n  })\r\n  @IsObject()\r\n  @IsOptional()\r\n  znsContent?: ZaloCampaignZnsContentDto;\r\n}\r\n\r\n/**\r\n * DTO cho việc cập nhật chiến dịch Zalo\r\n */\r\nexport class UpdateZaloCampaignDto {\r\n  @ApiProperty({\r\n    description: 'Tên của chiến dịch',\r\n    example: 'Chiến dịch khuyến mãi tháng 7 - Cập nhật',\r\n    required: false,\r\n  })\r\n  @IsString()\r\n  @IsOptional()\r\n  name?: string;\r\n\r\n  @ApiProperty({\r\n    description: 'Mô tả của chiến dịch',\r\n    example: 'Chiến dịch khuyến mãi dành cho khách hàng VIP - Cập nhật',\r\n    required: false,\r\n  })\r\n  @IsString()\r\n  @IsOptional()\r\n  description?: string;\r\n\r\n  @ApiProperty({\r\n    description: 'ID của phân đoạn',\r\n    example: 2,\r\n    required: false,\r\n  })\r\n  @IsNumber()\r\n  @IsOptional()\r\n  segmentId?: number;\r\n\r\n  @ApiProperty({\r\n    description: 'Thời điểm bắt đầu (Unix timestamp)',\r\n    example: 1625097600000,\r\n    required: false,\r\n  })\r\n  @IsNumber()\r\n  @IsOptional()\r\n  scheduledAt?: number;\r\n\r\n  @ApiProperty({\r\n    description: 'Nội dung tin nhắn (chỉ dùng khi type là message)',\r\n    type: ZaloCampaignMessageContentDto,\r\n    required: false,\r\n  })\r\n  @IsObject()\r\n  @IsOptional()\r\n  messageContent?: ZaloCampaignMessageContentDto;\r\n\r\n  @ApiProperty({\r\n    description: 'Nội dung ZNS (chỉ dùng khi type là zns)',\r\n    type: ZaloCampaignZnsContentDto,\r\n    required: false,\r\n  })\r\n  @IsObject()\r\n  @IsOptional()\r\n  znsContent?: ZaloCampaignZnsContentDto;\r\n}\r\n\r\n/**\r\n * DTO cho phản hồi thông tin chiến dịch Zalo\r\n */\r\nexport class ZaloCampaignResponseDto {\r\n  @ApiProperty({\r\n    description: 'ID của chiến dịch',\r\n    example: 1,\r\n  })\r\n  id: number;\r\n\r\n  @ApiProperty({\r\n    description: 'ID của người dùng',\r\n    example: 123,\r\n  })\r\n  userId: number;\r\n\r\n  @ApiProperty({\r\n    description: 'ID của Official Account',\r\n    example: '*********',\r\n  })\r\n  oaId: string;\r\n\r\n  @ApiProperty({\r\n    description: 'Tên của chiến dịch',\r\n    example: 'Chiến dịch khuyến mãi tháng 7',\r\n  })\r\n  name: string;\r\n\r\n  @ApiProperty({\r\n    description: 'Mô tả của chiến dịch',\r\n    example: 'Chiến dịch khuyến mãi dành cho khách hàng VIP',\r\n    nullable: true,\r\n  })\r\n  description?: string;\r\n\r\n  @ApiProperty({\r\n    description: 'Loại chiến dịch',\r\n    enum: ZaloCampaignType,\r\n    example: ZaloCampaignType.MESSAGE,\r\n  })\r\n  type: ZaloCampaignType;\r\n\r\n  @ApiProperty({\r\n    description: 'ID của phân đoạn',\r\n    example: 1,\r\n  })\r\n  segmentId: number;\r\n\r\n  @ApiProperty({\r\n    description: 'Trạng thái chiến dịch',\r\n    enum: ZaloCampaignStatus,\r\n    example: ZaloCampaignStatus.DRAFT,\r\n  })\r\n  status: ZaloCampaignStatus;\r\n\r\n  @ApiProperty({\r\n    description: 'Thời điểm bắt đầu (Unix timestamp)',\r\n    example: 1625097600000,\r\n    nullable: true,\r\n  })\r\n  scheduledAt?: number;\r\n\r\n  @ApiProperty({\r\n    description: 'Thời điểm bắt đầu thực tế (Unix timestamp)',\r\n    example: 1625097600000,\r\n    nullable: true,\r\n  })\r\n  startedAt?: number;\r\n\r\n  @ApiProperty({\r\n    description: 'Thời điểm kết thúc (Unix timestamp)',\r\n    example: 1625097600000,\r\n    nullable: true,\r\n  })\r\n  completedAt?: number;\r\n\r\n  @ApiProperty({\r\n    description: 'Nội dung tin nhắn (chỉ dùng khi type là message)',\r\n    type: ZaloCampaignMessageContentDto,\r\n    nullable: true,\r\n  })\r\n  messageContent?: ZaloCampaignMessageContentDto;\r\n\r\n  @ApiProperty({\r\n    description: 'Nội dung ZNS (chỉ dùng khi type là zns)',\r\n    type: ZaloCampaignZnsContentDto,\r\n    nullable: true,\r\n  })\r\n  znsContent?: ZaloCampaignZnsContentDto;\r\n\r\n  @ApiProperty({\r\n    description: 'Tổng số người nhận',\r\n    example: 100,\r\n  })\r\n  totalRecipients: number;\r\n\r\n  @ApiProperty({\r\n    description: 'Số người đã gửi thành công',\r\n    example: 95,\r\n  })\r\n  successCount: number;\r\n\r\n  @ApiProperty({\r\n    description: 'Số người gửi thất bại',\r\n    example: 5,\r\n  })\r\n  failureCount: number;\r\n\r\n  @ApiProperty({\r\n    description: 'Thời điểm tạo (Unix timestamp)',\r\n    example: 1625097600000,\r\n  })\r\n  createdAt: number;\r\n\r\n  @ApiProperty({\r\n    description: 'Thời điểm cập nhật (Unix timestamp)',\r\n    example: 1625097600000,\r\n  })\r\n  updatedAt: number;\r\n}\r\n\r\n/**\r\n * DTO cho việc truy vấn danh sách chiến dịch Zalo\r\n */\r\nexport class ZaloCampaignQueryDto {\r\n  @ApiProperty({\r\n    description: 'Tìm kiếm theo tên chiến dịch',\r\n    example: 'khuyến mãi',\r\n    required: false,\r\n  })\r\n  @IsString()\r\n  @IsOptional()\r\n  name?: string;\r\n\r\n  @ApiProperty({\r\n    description: 'Lọc theo loại chiến dịch',\r\n    enum: ZaloCampaignType,\r\n    example: ZaloCampaignType.MESSAGE,\r\n    required: false,\r\n  })\r\n  @IsEnum(ZaloCampaignType)\r\n  @IsOptional()\r\n  type?: ZaloCampaignType;\r\n\r\n  @ApiProperty({\r\n    description: 'Lọc theo trạng thái chiến dịch',\r\n    enum: ZaloCampaignStatus,\r\n    example: ZaloCampaignStatus.RUNNING,\r\n    required: false,\r\n  })\r\n  @IsEnum(ZaloCampaignStatus)\r\n  @IsOptional()\r\n  status?: ZaloCampaignStatus;\r\n\r\n  @ApiProperty({\r\n    description: 'Số trang',\r\n    example: 1,\r\n    default: 1,\r\n    required: false,\r\n  })\r\n  @IsOptional()\r\n  page?: number = 1;\r\n\r\n  @ApiProperty({\r\n    description: 'Số lượng chiến dịch trên mỗi trang',\r\n    example: 10,\r\n    default: 10,\r\n    required: false,\r\n  })\r\n  @IsOptional()\r\n  limit?: number = 10;\r\n}\r\n\r\n/**\r\n * DTO cho việc thực thi chiến dịch Zalo\r\n */\r\nexport class ExecuteZaloCampaignDto {\r\n  @ApiProperty({\r\n    description: 'Hành động (start, cancel)',\r\n    example: 'start',\r\n  })\r\n  @IsString()\r\n  @IsNotEmpty()\r\n  action: string;\r\n}\r\n"], "names": ["CreateZaloCampaignDto", "ExecuteZaloCampaignDto", "UpdateZaloCampaignDto", "ZaloCampaignMessageContentDto", "ZaloCampaignQueryDto", "ZaloCampaignResponseDto", "ZaloCampaignStatus", "ZaloCampaignType", "ZaloCampaignZnsContentDto", "description", "example", "required", "name", "product", "orderId", "shopName", "enum", "type", "nullable", "page", "limit", "default"], "mappings": ";;;;;;;;;;;QAgHaA;eAAAA;;QAkTAC;eAAAA;;QAxOAC;eAAAA;;QAzJAC;eAAAA;;QA6UAC;eAAAA;;QAzHAC;eAAAA;;QA/NDC;eAAAA;;QARAC;eAAAA;;QA6ECC;eAAAA;;;yBA3Fe;gCASrB;;;;;;;;;;AAKA,IAAA,AAAKD,0CAAAA;;;WAAAA;;AAQL,IAAA,AAAKD,4CAAAA;;;;;;WAAAA;;AAWL,IAAA,AAAMH,gCAAN,MAAMA;AAqDb;;;QAnDIM,aAAa;QACbC,SAAS;;;;;;;;QAOTD,aAAa;QACbC,SAAS;QACTC,UAAU;;;;;;;;QAOVF,aAAa;QACbC,SAAS;QACTC,UAAU;;;;;;;;QAOVF,aAAa;QACbC,SAAS;QACTC,UAAU;;;;;;;;QAOVF,aAAa;QACbC,SAAS;QACTC,UAAU;;;;;;;;QAOVF,aAAa;QACbC,SAAS;YAAEE,MAAM;YAAUC,SAAS;QAAY;QAChDF,UAAU;;;;;;AAUP,IAAA,AAAMH,4BAAN,MAAMA;AAgBb;;;QAdIC,aAAa;QACbC,SAAS;;;;;;;;QAOTD,aAAa;QACbC,SAAS;YAAEI,SAAS;YAAaC,UAAU;QAAa;;;;;;AAUrD,IAAA,AAAMf,wBAAN,MAAMA;AAqEb;;;QAnEIS,aAAa;QACbC,SAAS;;;;;;;;QAOTD,aAAa;QACbC,SAAS;;;;;;;;QAOTD,aAAa;QACbC,SAAS;QACTC,UAAU;;;;;;;;QAOVF,aAAa;QACbO,MAAMT;QACNG,OAAO;;;;;;;;QAOPD,aAAa;QACbC,SAAS;;;;;;;;QAOTD,aAAa;QACbC,SAAS;QACTC,UAAU;;;;;;;;QAOVF,aAAa;QACbQ,MAAMd;QACNQ,UAAU;;;;;;;;QAOVF,aAAa;QACbQ,MAAMT;QACNG,UAAU;;;;;;AAUP,IAAA,AAAMT,wBAAN,MAAMA;AAsDb;;;QApDIO,aAAa;QACbC,SAAS;QACTC,UAAU;;;;;;;;QAOVF,aAAa;QACbC,SAAS;QACTC,UAAU;;;;;;;;QAOVF,aAAa;QACbC,SAAS;QACTC,UAAU;;;;;;;;QAOVF,aAAa;QACbC,SAAS;QACTC,UAAU;;;;;;;;QAOVF,aAAa;QACbQ,MAAMd;QACNQ,UAAU;;;;;;;;QAOVF,aAAa;QACbQ,MAAMT;QACNG,UAAU;;;;;;AAUP,IAAA,AAAMN,0BAAN,MAAMA;AAoHb;;;QAlHII,aAAa;QACbC,SAAS;;;;;;QAKTD,aAAa;QACbC,SAAS;;;;;;QAKTD,aAAa;QACbC,SAAS;;;;;;QAKTD,aAAa;QACbC,SAAS;;;;;;QAKTD,aAAa;QACbC,SAAS;QACTQ,UAAU;;;;;;QAKVT,aAAa;QACbO,MAAMT;QACNG,OAAO;;;;;;QAKPD,aAAa;QACbC,SAAS;;;;;;QAKTD,aAAa;QACbO,MAAMV;QACNI,OAAO;;;;;;QAKPD,aAAa;QACbC,SAAS;QACTQ,UAAU;;;;;;QAKVT,aAAa;QACbC,SAAS;QACTQ,UAAU;;;;;;QAKVT,aAAa;QACbC,SAAS;QACTQ,UAAU;;;;;;QAKVT,aAAa;QACbQ,MAAMd;QACNe,UAAU;;;;;;QAKVT,aAAa;QACbQ,MAAMT;QACNU,UAAU;;;;;;QAKVT,aAAa;QACbC,SAAS;;;;;;QAKTD,aAAa;QACbC,SAAS;;;;;;QAKTD,aAAa;QACbC,SAAS;;;;;;QAKTD,aAAa;QACbC,SAAS;;;;;;QAKTD,aAAa;QACbC,SAAS;;;;AAQN,IAAA,AAAMN,uBAAN,MAAMA;;aAqCXe,OAAgB;aAShBC,QAAiB;;AACnB;;;QA7CIX,aAAa;QACbC,SAAS;QACTC,UAAU;;;;;;;;QAOVF,aAAa;QACbO,MAAMT;QACNG,OAAO;QACPC,UAAU;;;;;;;;QAOVF,aAAa;QACbO,MAAMV;QACNI,OAAO;QACPC,UAAU;;;;;;;;QAOVF,aAAa;QACbC,SAAS;QACTW,SAAS;QACTV,UAAU;;;;;;;QAMVF,aAAa;QACbC,SAAS;QACTW,SAAS;QACTV,UAAU;;;;;AASP,IAAA,AAAMV,yBAAN,MAAMA;AAQb;;;QANIQ,aAAa;QACbC,SAAS"}