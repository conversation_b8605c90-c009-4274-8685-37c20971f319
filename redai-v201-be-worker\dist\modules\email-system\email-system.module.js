"use strict";
Object.defineProperty(exports, "__esModule", {
    value: true
});
Object.defineProperty(exports, "EmailSystemModule", {
    enumerable: true,
    get: function() {
        return EmailSystemModule;
    }
});
const _common = require("@nestjs/common");
const _typeorm = require("@nestjs/typeorm");
const _admintemplateemailentity = require("./entities/admin-template-email.entity");
const _emailsystemservice = require("./email-system.service");
const _emailsystemprocessor = require("./email-system.processor");
const _emailsystemcontroller = require("./email-system.controller");
const _bullmq = require("@nestjs/bullmq");
const _queue = require("../../queue");
function _ts_decorate(decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for(var i = decorators.length - 1; i >= 0; i--)if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
}
let EmailSystemModule = class EmailSystemModule {
};
EmailSystemModule = _ts_decorate([
    (0, _common.Module)({
        imports: [
            _typeorm.TypeOrmModule.forFeature([
                _admintemplateemailentity.AdminTemplateEmail
            ]),
            _bullmq.BullModule.registerQueue({
                name: _queue.QueueName.EMAIL_SYSTEM
            })
        ],
        providers: [
            _emailsystemservice.EmailSystemService,
            _emailsystemprocessor.EmailSystemProcessor
        ],
        controllers: [
            _emailsystemcontroller.EmailSystemController
        ],
        exports: [
            _emailsystemservice.EmailSystemService
        ]
    })
], EmailSystemModule);

//# sourceMappingURL=email-system.module.js.map