{"version": 3, "sources": ["../../../../../src/modules/marketing/dto/zalo/tag-request.dto.ts"], "sourcesContent": ["import { ApiProperty } from '@nestjs/swagger';\r\nimport { IsNotEmpty, IsString } from 'class-validator';\r\n\r\n/**\r\n * DTO cho việc thêm/xóa tag cho người theo dõi\r\n */\r\nexport class TagRequestDto {\r\n  @ApiProperty({\r\n    description: 'ID của người theo dõi',\r\n    example: '123456789',\r\n  })\r\n  @IsString()\r\n  @IsNotEmpty()\r\n  followerId: string;\r\n\r\n  @ApiProperty({\r\n    description: 'Tag cần thêm/xóa',\r\n    example: 'vip',\r\n  })\r\n  @IsString()\r\n  @IsNotEmpty()\r\n  tagName: string;\r\n}\r\n"], "names": ["TagRequestDto", "description", "example"], "mappings": ";;;;+BAMaA;;;eAAAA;;;yBANe;gCACS;;;;;;;;;;AAK9B,IAAA,AAAMA,gBAAN,MAAMA;AAgBb;;;QAdIC,aAAa;QACbC,SAAS;;;;;;;;QAOTD,aAAa;QACbC,SAAS"}