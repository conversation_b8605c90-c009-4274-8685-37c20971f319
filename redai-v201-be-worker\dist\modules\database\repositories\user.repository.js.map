{"version": 3, "sources": ["../../../../src/modules/database/repositories/user.repository.ts"], "sourcesContent": ["import { Injectable } from '@nestjs/common';\r\nimport { InjectRepository } from '@nestjs/typeorm';\r\nimport { Repository } from 'typeorm';\r\nimport { User } from '../entities/user.entity';\r\n\r\n@Injectable()\r\nexport class UserRepository {\r\n  constructor(\r\n    @InjectRepository(User)\r\n    private userRepository: Repository<User>,\r\n  ) {}\r\n\r\n  // Tìm tất cả người dùng\r\n  async findAll(): Promise<User[]> {\r\n    return this.userRepository.find();\r\n  }\r\n\r\n  // Tìm người dùng theo ID\r\n  async findById(id: string): Promise<User | null> {\r\n    return this.userRepository.findOne({ where: { id } });\r\n  }\r\n\r\n  // Tìm người dùng theo email\r\n  async findByEmail(email: string): Promise<User | null> {\r\n    return this.userRepository.findOne({ where: { email } });\r\n  }\r\n\r\n  // Tạo người dùng mới\r\n  async create(userData: Partial<User>): Promise<User> {\r\n    const user = this.userRepository.create(userData);\r\n    return this.userRepository.save(user);\r\n  }\r\n\r\n  // Cập nhật thông tin người dùng\r\n  async update(id: string, userData: Partial<User>): Promise<User | null> {\r\n    await this.userRepository.update(id, userData);\r\n    return this.findById(id);\r\n  }\r\n\r\n  // Xóa người dùng\r\n  async remove(id: string): Promise<void> {\r\n    await this.userRepository.delete(id);\r\n  }\r\n}\r\n"], "names": ["UserRepository", "findAll", "userRepository", "find", "findById", "id", "findOne", "where", "findByEmail", "email", "create", "userData", "user", "save", "update", "remove", "delete", "constructor"], "mappings": ";;;;+BAMaA;;;eAAAA;;;wBANc;yBACM;0BACN;4BACN;;;;;;;;;;;;;;;AAGd,IAAA,AAAMA,iBAAN,MAAMA;IAMX,wBAAwB;IACxB,MAAMC,UAA2B;QAC/B,OAAO,IAAI,CAACC,cAAc,CAACC,IAAI;IACjC;IAEA,yBAAyB;IACzB,MAAMC,SAASC,EAAU,EAAwB;QAC/C,OAAO,IAAI,CAACH,cAAc,CAACI,OAAO,CAAC;YAAEC,OAAO;gBAAEF;YAAG;QAAE;IACrD;IAEA,4BAA4B;IAC5B,MAAMG,YAAYC,KAAa,EAAwB;QACrD,OAAO,IAAI,CAACP,cAAc,CAACI,OAAO,CAAC;YAAEC,OAAO;gBAAEE;YAAM;QAAE;IACxD;IAEA,qBAAqB;IACrB,MAAMC,OAAOC,QAAuB,EAAiB;QACnD,MAAMC,OAAO,IAAI,CAACV,cAAc,CAACQ,MAAM,CAACC;QACxC,OAAO,IAAI,CAACT,cAAc,CAACW,IAAI,CAACD;IAClC;IAEA,gCAAgC;IAChC,MAAME,OAAOT,EAAU,EAAEM,QAAuB,EAAwB;QACtE,MAAM,IAAI,CAACT,cAAc,CAACY,MAAM,CAACT,IAAIM;QACrC,OAAO,IAAI,CAACP,QAAQ,CAACC;IACvB;IAEA,iBAAiB;IACjB,MAAMU,OAAOV,EAAU,EAAiB;QACtC,MAAM,IAAI,CAACH,cAAc,CAACc,MAAM,CAACX;IACnC;IAnCAY,YACE,AACQf,cAAgC,CACxC;aADQA,iBAAAA;IACP;AAiCL"}