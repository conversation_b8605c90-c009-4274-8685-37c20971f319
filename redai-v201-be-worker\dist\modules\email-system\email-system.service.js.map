{"version": 3, "sources": ["../../../src/modules/email-system/email-system.service.ts"], "sourcesContent": ["import { Injectable, Logger } from '@nestjs/common';\r\nimport { InjectRepository } from '@nestjs/typeorm';\r\nimport { Repository } from 'typeorm';\r\nimport { AdminTemplateEmail } from './entities/admin-template-email.entity';\r\nimport { InjectQueue } from '@nestjs/bullmq';\r\nimport { Queue } from 'bullmq';\r\nimport { QueueName } from '../../queue';\r\nimport { EmailSystemJobDto } from './dto/email-system-job.dto';\r\n\r\n/**\r\n * Service xử lý các chức năng liên quan đến email system\r\n */\r\n@Injectable()\r\nexport class EmailSystemService {\r\n  private readonly logger = new Logger(EmailSystemService.name);\r\n\r\n  constructor(\r\n    @InjectRepository(AdminTemplateEmail)\r\n    private readonly adminTemplateEmailRepository: Repository<AdminTemplateEmail>,\r\n    @InjectQueue(QueueName.EMAIL_SYSTEM)\r\n    private readonly emailQueue: Queue,\r\n  ) {}\r\n\r\n  /**\r\n   * Thêm job gửi email vào queue\r\n   * @param jobData Dữ liệu job email\r\n   * @returns Job ID\r\n   */\r\n  async addEmailJob(jobData: EmailSystemJobDto): Promise<string> {\r\n    try {\r\n      const job = await this.emailQueue.add('send-email', jobData);\r\n      this.logger.log(`Đã thêm job email vào queue với ID: ${job.id}`);\r\n      return job.id as string;\r\n    } catch (error) {\r\n      this.logger.error(\r\n        `Lỗi khi thêm job email vào queue: ${error.message}`,\r\n        error.stack,\r\n      );\r\n      throw error;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Lấy template email theo category\r\n   * @param category Danh mục email cần tìm\r\n   * @returns Template email tìm thấy hoặc null\r\n   */\r\n  async getTemplateByCategory(\r\n    category: string,\r\n  ): Promise<AdminTemplateEmail | null> {\r\n    try {\r\n      const template = await this.adminTemplateEmailRepository.findOne({\r\n        where: { category },\r\n      });\r\n\r\n      if (!template) {\r\n        this.logger.warn(\r\n          `Template email với category ${category} không tồn tại`,\r\n        );\r\n        return null;\r\n      }\r\n\r\n      return template;\r\n    } catch (error) {\r\n      this.logger.error(\r\n        `Lỗi khi lấy template email: ${error.message}`,\r\n        error.stack,\r\n      );\r\n      throw error;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Thay thế các placeholder trong nội dung email\r\n   * @param content Nội dung cần thay thế\r\n   * @param data Dữ liệu để thay thế\r\n   * @returns Nội dung đã được thay thế\r\n   */\r\n  replacePlaceholders(content: string, data: Record<string, any>): string {\r\n    if (!content) return '';\r\n\r\n    let replacedContent = content;\r\n\r\n    // Thay thế các placeholder dạng {{key}} trong nội dung\r\n    Object.entries(data).forEach(([key, value]) => {\r\n      const placeholder = new RegExp(`{{${key}}}`, 'g');\r\n      replacedContent = replacedContent.replace(placeholder, String(value));\r\n    });\r\n\r\n    return replacedContent;\r\n  }\r\n}\r\n"], "names": ["EmailSystemService", "addEmailJob", "jobData", "job", "emailQueue", "add", "logger", "log", "id", "error", "message", "stack", "getTemplateByCategory", "category", "template", "adminTemplateEmailRepository", "findOne", "where", "warn", "replacePlaceholders", "content", "data", "<PERSON><PERSON><PERSON><PERSON>", "Object", "entries", "for<PERSON>ach", "key", "value", "placeholder", "RegExp", "replace", "String", "constructor", "<PERSON><PERSON>", "name", "EMAIL_SYSTEM"], "mappings": ";;;;+BAaaA;;;eAAAA;;;wBAbsB;yBACF;0BACN;0CACQ;wBACP;yBACN;uBACI;;;;;;;;;;;;;;;AAOnB,IAAA,AAAMA,qBAAN,MAAMA;IAUX;;;;GAIC,GACD,MAAMC,YAAYC,OAA0B,EAAmB;QAC7D,IAAI;YACF,MAAMC,MAAM,MAAM,IAAI,CAACC,UAAU,CAACC,GAAG,CAAC,cAAcH;YACpD,IAAI,CAACI,MAAM,CAACC,GAAG,CAAC,CAAC,oCAAoC,EAAEJ,IAAIK,EAAE,EAAE;YAC/D,OAAOL,IAAIK,EAAE;QACf,EAAE,OAAOC,OAAO;YACd,IAAI,CAACH,MAAM,CAACG,KAAK,CACf,CAAC,kCAAkC,EAAEA,MAAMC,OAAO,EAAE,EACpDD,MAAME,KAAK;YAEb,MAAMF;QACR;IACF;IAEA;;;;GAIC,GACD,MAAMG,sBACJC,QAAgB,EACoB;QACpC,IAAI;YACF,MAAMC,WAAW,MAAM,IAAI,CAACC,4BAA4B,CAACC,OAAO,CAAC;gBAC/DC,OAAO;oBAAEJ;gBAAS;YACpB;YAEA,IAAI,CAACC,UAAU;gBACb,IAAI,CAACR,MAAM,CAACY,IAAI,CACd,CAAC,4BAA4B,EAAEL,SAAS,cAAc,CAAC;gBAEzD,OAAO;YACT;YAEA,OAAOC;QACT,EAAE,OAAOL,OAAO;YACd,IAAI,CAACH,MAAM,CAACG,KAAK,CACf,CAAC,4BAA4B,EAAEA,MAAMC,OAAO,EAAE,EAC9CD,MAAME,KAAK;YAEb,MAAMF;QACR;IACF;IAEA;;;;;GAKC,GACDU,oBAAoBC,OAAe,EAAEC,IAAyB,EAAU;QACtE,IAAI,CAACD,SAAS,OAAO;QAErB,IAAIE,kBAAkBF;QAEtB,uDAAuD;QACvDG,OAAOC,OAAO,CAACH,MAAMI,OAAO,CAAC,CAAC,CAACC,KAAKC,MAAM;YACxC,MAAMC,cAAc,IAAIC,OAAO,CAAC,EAAE,EAAEH,IAAI,EAAE,CAAC,EAAE;YAC7CJ,kBAAkBA,gBAAgBQ,OAAO,CAACF,aAAaG,OAAOJ;QAChE;QAEA,OAAOL;IACT;IA1EAU,YACE,AACiBjB,4BAA4D,EAC7E,AACiBX,UAAiB,CAClC;aAHiBW,+BAAAA;aAEAX,aAAAA;aANFE,SAAS,IAAI2B,cAAM,CAACjC,mBAAmBkC,IAAI;IAOzD;AAsEL;;;;2DAxE2BC"}