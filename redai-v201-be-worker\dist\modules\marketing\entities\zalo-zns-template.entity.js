"use strict";
Object.defineProperty(exports, "__esModule", {
    value: true
});
Object.defineProperty(exports, "ZaloZnsTemplate", {
    enumerable: true,
    get: function() {
        return ZaloZnsTemplate;
    }
});
const _typeorm = require("typeorm");
function _ts_decorate(decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for(var i = decorators.length - 1; i >= 0; i--)if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
}
function _ts_metadata(k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
}
let ZaloZnsTemplate = class ZaloZnsTemplate {
};
_ts_decorate([
    (0, _typeorm.PrimaryGeneratedColumn)({
        name: 'id'
    }),
    _ts_metadata("design:type", Number)
], ZaloZnsTemplate.prototype, "id", void 0);
_ts_decorate([
    (0, _typeorm.Column)({
        name: 'user_id'
    }),
    _ts_metadata("design:type", Number)
], ZaloZnsTemplate.prototype, "userId", void 0);
_ts_decorate([
    (0, _typeorm.Column)({
        name: 'oa_id',
        length: 50
    }),
    _ts_metadata("design:type", String)
], ZaloZnsTemplate.prototype, "oaId", void 0);
_ts_decorate([
    (0, _typeorm.Column)({
        name: 'template_id',
        length: 50
    }),
    _ts_metadata("design:type", String)
], ZaloZnsTemplate.prototype, "templateId", void 0);
_ts_decorate([
    (0, _typeorm.Column)({
        name: 'template_name',
        length: 255
    }),
    _ts_metadata("design:type", String)
], ZaloZnsTemplate.prototype, "templateName", void 0);
_ts_decorate([
    (0, _typeorm.Column)({
        name: 'template_content',
        type: 'text'
    }),
    _ts_metadata("design:type", String)
], ZaloZnsTemplate.prototype, "templateContent", void 0);
_ts_decorate([
    (0, _typeorm.Column)({
        name: 'params',
        type: 'jsonb',
        default: '[]'
    }),
    _ts_metadata("design:type", Array)
], ZaloZnsTemplate.prototype, "params", void 0);
_ts_decorate([
    (0, _typeorm.Column)({
        name: 'status',
        length: 20
    }),
    _ts_metadata("design:type", String)
], ZaloZnsTemplate.prototype, "status", void 0);
_ts_decorate([
    (0, _typeorm.Column)({
        name: 'created_at',
        type: 'bigint'
    }),
    _ts_metadata("design:type", Number)
], ZaloZnsTemplate.prototype, "createdAt", void 0);
_ts_decorate([
    (0, _typeorm.Column)({
        name: 'updated_at',
        type: 'bigint'
    }),
    _ts_metadata("design:type", Number)
], ZaloZnsTemplate.prototype, "updatedAt", void 0);
ZaloZnsTemplate = _ts_decorate([
    (0, _typeorm.Entity)('zalo_zns_templates')
], ZaloZnsTemplate);

//# sourceMappingURL=zalo-zns-template.entity.js.map