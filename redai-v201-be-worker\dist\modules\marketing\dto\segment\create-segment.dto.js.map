{"version": 3, "sources": ["../../../../../src/modules/marketing/dto/segment/create-segment.dto.ts"], "sourcesContent": ["import {\r\n  IsNotEmpty,\r\n  IsOptional,\r\n  IsString,\r\n  ValidateNested,\r\n} from 'class-validator';\r\nimport { ApiProperty } from '@nestjs/swagger';\r\nimport { Type } from 'class-transformer';\r\nimport { SegmentCriteriaDto } from './segment-criteria.dto';\r\n\r\n/**\r\n * DTO cho việc tạo segment mới\r\n */\r\nexport class CreateSegmentDto {\r\n  /**\r\n   * Tên segment\r\n   * @example \"Khách hàng tiềm năng\"\r\n   */\r\n  @ApiProperty({\r\n    description: 'Tên segment',\r\n    example: 'Khách hàng tiềm năng',\r\n  })\r\n  @IsNotEmpty({ message: 'Tên segment không được để trống' })\r\n  @IsString({ message: 'Tên segment phải là chuỗi' })\r\n  name: string;\r\n\r\n  /**\r\n   * Mô tả segment\r\n   * @example \"Khách hàng có khả năng mua hàng cao\"\r\n   */\r\n  @ApiProperty({\r\n    description: 'Mô tả segment',\r\n    example: 'Kh<PERSON>ch hàng có khả năng mua hàng cao',\r\n    required: false,\r\n  })\r\n  @IsOptional()\r\n  @IsString({ message: '<PERSON>ô tả phải là chuỗi' })\r\n  description?: string;\r\n\r\n  /**\r\n   * Điều kiện lọc khách hàng\r\n   */\r\n  @ApiProperty({\r\n    description: 'Điều kiện lọc khách hàng',\r\n    type: SegmentCriteriaDto,\r\n  })\r\n  @IsNotEmpty({ message: 'Điều kiện lọc không được để trống' })\r\n  @ValidateNested()\r\n  @Type(() => SegmentCriteriaDto)\r\n  criteria: SegmentCriteriaDto;\r\n}\r\n"], "names": ["CreateSegmentDto", "description", "example", "message", "required", "type", "SegmentCriteriaDto"], "mappings": ";;;;+BAaaA;;;eAAAA;;;gCARN;yBACqB;kCACP;oCACc;;;;;;;;;;AAK5B,IAAA,AAAMA,mBAAN,MAAMA;AAqCb;;;QA/BIC,aAAa;QACbC,SAAS;;;QAEGC,SAAS;;;QACXA,SAAS;;;;;;QAQnBF,aAAa;QACbC,SAAS;QACTE,UAAU;;;;QAGAD,SAAS;;;;;;QAOnBF,aAAa;QACbI,MAAMC,sCAAkB;;;QAEZH,SAAS;;;oCAEXG,sCAAkB"}