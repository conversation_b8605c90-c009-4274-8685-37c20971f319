{"version": 3, "sources": ["../../../../../src/modules/marketing/dto/segment/segment-criteria.dto.ts"], "sourcesContent": ["import {\r\n  IsArray,\r\n  IsIn,\r\n  IsNotEmpty,\r\n  IsOptional,\r\n  IsString,\r\n  ValidateNested,\r\n} from 'class-validator';\r\nimport { ApiProperty } from '@nestjs/swagger';\r\nimport { Type } from 'class-transformer';\r\n\r\n/**\r\n * Enum cho các loại điều kiện\r\n */\r\nexport enum ConditionType {\r\n  AND = 'and',\r\n  OR = 'or',\r\n}\r\n\r\n/**\r\n * Enum cho các toán tử so sánh\r\n */\r\nexport enum OperatorType {\r\n  EQUALS = 'equals',\r\n  NOT_EQUALS = 'not_equals',\r\n  CONTAINS = 'contains',\r\n  NOT_CONTAINS = 'not_contains',\r\n  GREATER_THAN = 'greater_than',\r\n  LESS_THAN = 'less_than',\r\n  IN = 'in',\r\n  NOT_IN = 'not_in',\r\n  EXISTS = 'exists',\r\n  NOT_EXISTS = 'not_exists',\r\n}\r\n\r\n/**\r\n * DTO cho điều kiện lọc\r\n */\r\nexport class FilterConditionDto {\r\n  /**\r\n   * Tên trường cần lọc\r\n   * @example \"email\"\r\n   */\r\n  @ApiProperty({\r\n    description: 'Tên trường cần lọc',\r\n    example: 'email',\r\n  })\r\n  @IsNotEmpty({ message: 'Tên trường không được để trống' })\r\n  @IsString({ message: 'Tên trường phải là chuỗi' })\r\n  field: string;\r\n\r\n  /**\r\n   * Toán tử so sánh\r\n   * @example \"contains\"\r\n   */\r\n  @ApiProperty({\r\n    description: 'Toán tử so sánh',\r\n    enum: OperatorType,\r\n    example: OperatorType.CONTAINS,\r\n  })\r\n  @IsNotEmpty({ message: 'Toán tử không được để trống' })\r\n  @IsIn(Object.values(OperatorType), {\r\n    message: `Toán tử phải là một trong các giá trị: ${Object.values(OperatorType).join(', ')}`,\r\n  })\r\n  operator: OperatorType;\r\n\r\n  /**\r\n   * Giá trị cần so sánh\r\n   * @example \"example.com\"\r\n   */\r\n  @ApiProperty({\r\n    description: 'Giá trị cần so sánh',\r\n    example: 'example.com',\r\n  })\r\n  @IsOptional()\r\n  value?: any;\r\n}\r\n\r\n/**\r\n * DTO cho nhóm điều kiện lọc\r\n */\r\nexport class SegmentCriteriaDto {\r\n  /**\r\n   * Loại điều kiện (AND/OR)\r\n   * @example \"and\"\r\n   */\r\n  @ApiProperty({\r\n    description: 'Loại điều kiện (AND/OR)',\r\n    enum: ConditionType,\r\n    example: ConditionType.AND,\r\n  })\r\n  @IsNotEmpty({ message: 'Loại điều kiện không được để trống' })\r\n  @IsIn(Object.values(ConditionType), {\r\n    message: `Loại điều kiện phải là một trong các giá trị: ${Object.values(ConditionType).join(', ')}`,\r\n  })\r\n  conditionType: ConditionType;\r\n\r\n  /**\r\n   * Danh sách các điều kiện lọc\r\n   */\r\n  @ApiProperty({\r\n    description: 'Danh sách các điều kiện lọc',\r\n    type: [FilterConditionDto],\r\n    required: false,\r\n  })\r\n  @IsOptional()\r\n  @IsArray({ message: 'Danh sách điều kiện phải là mảng' })\r\n  @ValidateNested({ each: true })\r\n  @Type(() => FilterConditionDto)\r\n  conditions?: FilterConditionDto[];\r\n\r\n  /**\r\n   * Danh sách các nhóm điều kiện con\r\n   */\r\n  @ApiProperty({\r\n    description: 'Danh sách các nhóm điều kiện con',\r\n    type: [SegmentCriteriaDto],\r\n    required: false,\r\n  })\r\n  @IsOptional()\r\n  @IsArray({ message: 'Danh sách nhóm điều kiện phải là mảng' })\r\n  @ValidateNested({ each: true })\r\n  @Type(() => SegmentCriteriaDto)\r\n  groups?: SegmentCriteriaDto[];\r\n}\r\n"], "names": ["ConditionType", "FilterConditionDto", "OperatorType", "SegmentCriteriaDto", "description", "example", "message", "enum", "values", "Object", "join", "type", "required", "each"], "mappings": ";;;;;;;;;;;QAcYA;eAAAA;;QAwBCC;eAAAA;;QAhBDC;eAAAA;;QA2DCC;eAAAA;;;gCA1EN;yBACqB;kCACP;;;;;;;;;;AAKd,IAAA,AAAKH,uCAAAA;;;WAAAA;;AAQL,IAAA,AAAKE,sCAAAA;;;;;;;;;;;WAAAA;;AAgBL,IAAA,AAAMD,qBAAN,MAAMA;AAsCb;;;QAhCIG,aAAa;QACbC,SAAS;;;QAEGC,SAAS;;;QACXA,SAAS;;;;;;QAQnBF,aAAa;QACbG,MAAML;QACNG,OAAO;;;QAEKC,SAAS;;qCACVE;QACXF,SAAS,CAAC,uCAAuC,EAAEG,OAAOD,MAAM,CAACN,cAAcQ,IAAI,CAAC,OAAO;;;;;;QAS3FN,aAAa;QACbC,SAAS;;;;;AASN,IAAA,AAAMF,qBAAN,MAAMA;AA2Cb;;;QArCIC,aAAa;QACbG,MAAMP;QACNK,OAAO;;;QAEKC,SAAS;;qCACVE;QACXF,SAAS,CAAC,8CAA8C,EAAEG,OAAOD,MAAM,CAACR,eAAeU,IAAI,CAAC,OAAO;;;;;;QAQnGN,aAAa;QACbO,MAAM;YAACV;SAAmB;QAC1BW,UAAU;;;;QAGDN,SAAS;;;QACFO,MAAM;;oCACZZ;;;;;QAOVG,aAAa;QACbO,MAAM;YAACR;SAAmB;QAC1BS,UAAU;;;;QAGDN,SAAS;;;QACFO,MAAM;;oCACZV"}