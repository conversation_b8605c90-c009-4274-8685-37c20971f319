import { Injectable, Logger, Inject } from '@nestjs/common';
import { ClientProxy } from '@nestjs/microservices';
import { Transactional } from 'typeorm-transactional';
import { plainToInstance } from 'class-transformer';
import { AppException } from '@common/exceptions';
import { CHAT_ERROR_CODES } from '../exceptions/chat-error-codes';
import { AgentConfigService } from './agent-config.service';
import { UserAgentRunsQueries, UserMessagesQueries, AgentConfigQueries } from '../database';
import { MessageRequestDto } from '../dto/message-request.dto';
import { MessageResponseDto } from '../dto/message-response.dto';
import { UserAgentRunStatus } from '../../../shared/enums';
import { REDIS_EVENTS, RunTriggerEvent, RunCancelEvent } from '../constants';

/**
 * Service for handling chat functionality and run management
 */
@Injectable()
export class ChatService {
  private readonly logger = new Logger(ChatService.name);

  constructor(
    private readonly agentConfigService: AgentConfigService,
    private readonly agentConfigQueries: AgentConfigQueries,
    private readonly userAgentRunsQueries: UserAgentRunsQueries,
    private readonly userMessagesQueries: UserMessagesQueries,
    @Inject('REDIS_CLIENT') private readonly redisClient: ClientProxy,
  ) {}

  /**
   * Process a chat message and create a run for agent processing
   * @param messageRequest Message request data
   * @param userId ID of the user sending the message
   * @param jwt JWT token for authenticated API calls
   * @returns Promise<MessageResponseDto> Response with run information
   */
  @Transactional()
  async processMessage(
    messageRequest: MessageRequestDto,
    userId: number,
    jwt: string = '',
  ): Promise<MessageResponseDto> {
    try {
      this.logger.log(`Processing message for agent supervisor from user ${userId}`);

      // 1. Get all system agent configurations
      const agentConfigMap = await this.agentConfigQueries.getAllSystemAgentConfigs();

      // 2. Validate exactly one supervisor agent exists
      const supervisorAgents = Object.values(agentConfigMap).filter((config: any) =>
        config.system_is_supervisor === true
      );

      if (supervisorAgents.length === 0) {
        throw new AppException(
          CHAT_ERROR_CODES.THREAD_VALIDATION_FAILED,
          'No supervisor agent found. System requires exactly one agent with is_supervisor = true',
        );
      }

      if (supervisorAgents.length > 1) {
        const supervisorIds = supervisorAgents.map((agent: any) => agent.id).join(', ');
        throw new AppException(
          CHAT_ERROR_CODES.THREAD_VALIDATION_FAILED,
          `Multiple supervisor agents found: ${supervisorIds}. System requires exactly one agent with is_supervisor = true`,
        );
      }

      // 3. Get the supervisor agent as primary agent
      const primaryAgent = supervisorAgents[0] as any;
      this.logger.log(`Using supervisor agent ${primaryAgent.id} (${primaryAgent.name}) as primary agent`);

      // 4. Create run payload with agent configuration
      const runPayload = this.buildRunPayload(messageRequest, primaryAgent, agentConfigMap, userId);

      // 5. Create run in database (within transaction)
      const runId = await this.userAgentRunsQueries.createRun({
        payload: runPayload,
        status: UserAgentRunStatus.CREATED,
        created_by: userId
      });

      this.logger.log(`Created run ${runId} for agent ${primaryAgent.id}`);

      // 6. Persist user message to database (within transaction)
      const threadId = messageRequest.threadId;
      const userMessageId = await this.userMessagesQueries.createMessage({
        thread_id: threadId,
        role: 'user',
        content: {
          contentBlocks: messageRequest.contentBlocks,
          agentId: primaryAgent.id,
          runId: runId,
        },
        created_by: userId
      });
      this.logger.log(`Persisted user message ${userMessageId} for thread ${threadId}`);

      // 7. Publish run trigger event to Redis for worker consumption
      const runTriggerEvent: RunTriggerEvent = {
        eventType: REDIS_EVENTS.RUN_TRIGGER,
        runId,
        threadId: messageRequest.threadId, // Use frontend threadId or fallback to runId
        agentId: primaryAgent.id,
        userId,
        jwt, // JWT token for authenticated API calls
        timestamp: Date.now(),
        priority: 'medium',
        publishedAt: Date.now(),
      };

      // Use emit for fire-and-forget pub/sub pattern
      this.redisClient.emit(REDIS_EVENTS.RUN_TRIGGER, runTriggerEvent);
      this.logger.log(`Published run trigger event for run ${runId}`);
      
      return new MessageResponseDto({
        runId: runId,
        agentId: primaryAgent.id,
        agentName: primaryAgent.name,
        status: UserAgentRunStatus.CREATED,
        createdAt: Date.now(),
      });
    } catch (error) {
      // Transaction will be rolled back automatically by @Transactional decorator
      this.logger.error(`Message processing failed: ${error.message}`, error.stack);

      if (error instanceof AppException) {
        throw error;
      }

      throw new AppException(
        CHAT_ERROR_CODES.THREAD_CREATION_FAILED,
        `Message processing failed: ${error.message}`,
      );
    }
  }

  /**
   * Build run payload for agent processing
   * @param messageRequest Message request data
   * @param primaryAgent Primary agent configuration (supervisor)
   * @param agentConfigMap Complete agent configuration map
   * @param userId User ID for processing
   * @returns Run payload object
   */
  private buildRunPayload(
    messageRequest: MessageRequestDto,
    primaryAgent: any,
    agentConfigMap: { [agentId: string]: any },
    userId: number,
  ): any {
    // Normalize content blocks to array
    const contentBlocks = Array.isArray(messageRequest.contentBlocks)
      ? messageRequest.contentBlocks
      : [messageRequest.contentBlocks];

    // Extract text content for processing
    const textContent = contentBlocks
      .filter(block => block.type === 'text' && block.content)
      .map(block => block.content)
      .join('\n');

    // Build the run payload
    const payload = {
      // Message data
      message: {
        content: textContent, // Extracted text for processing
        contentBlocks, // Original structured content
        threadId: messageRequest.threadId,
        sessionId: messageRequest.sessionId, // Frontend-generated session ID for stream management
      },

      // Primary agent ID (full config available in agentConfigMap)
      primaryAgentId: primaryAgent.id,

      // Complete agent configuration map for multi-agent processing
      agentConfigMap,

      // Processing metadata
      metadata: {
        userId, // Essential: actual user ID for processing
        requestId: `req_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`,
        version: '1.0',
      },

      // Processing configuration
      processing: {
        maxRetries: 3,
        timeoutMs: 300000, // 5 minutes
        enableMultiAgent: Object.keys(agentConfigMap).length > 1,
        alwaysApproveToolCall: messageRequest.alwaysApproveToolCall || false,
      },
    };

    this.logger.debug(`Built run payload for agent ${primaryAgent.id}`, {
      agentId: primaryAgent.id,
      messageLength: textContent.length,
      contentBlockCount: contentBlocks.length,
      multiAgentEnabled: payload.processing.enableMultiAgent,
      agentCount: Object.keys(agentConfigMap).length,
      alwaysApproveToolCall: payload.processing.alwaysApproveToolCall,
    });

    return payload;
  }

  /**
   * Get agent configuration summary for debugging
   * @returns Promise<object> Configuration summary
   */
  async getAgentConfigSummary(): Promise<object> {
    return this.agentConfigService.getConfigSummary();
  }

  /**
   * Cancel a run and notify worker
   * @param runId Run ID to cancel
   * @param reason Cancellation reason
   * @returns Promise<boolean> True if cancelled successfully
   */
  async cancelRun(runId: string, reason: string = 'User requested cancellation'): Promise<boolean> {
    try {
      this.logger.log(`Cancelling run ${runId}: ${reason}`);

      // 1. Get run data to extract threadId
      const runData = await this.userAgentRunsQueries.getRunById(runId);
      if (!runData) {
        this.logger.error(`Run not found for cancellation: ${runId}`);
        return false;
      }

      // Extract threadId from run payload (frontend-provided threadId)
      const threadId = runData.payload?.message?.threadId || runId; // Fallback to runId if no threadId

      // 2. Update run status in database (use FAILED for cancellation)
      const updateSuccess = await this.userAgentRunsQueries.updateRunStatus(
        runId,
        UserAgentRunStatus.FAILED
      );

      if (!updateSuccess) {
        this.logger.error(`Failed to update run status for cancellation: ${runId}`);
        return false;
      }

      // 3. Publish run cancel event to Redis using correct threadId
      try {
        const runCancelEvent: RunCancelEvent = {
          eventType: REDIS_EVENTS.RUN_CANCEL,
          threadId, // Use actual threadId from frontend (LangGraph thread)
          runId,
          reason,
          timestamp: Date.now(),
          publishedAt: Date.now(),
        };

        // Use emit for fire-and-forget pub/sub pattern
        this.redisClient.emit(REDIS_EVENTS.RUN_CANCEL, runCancelEvent);
        this.logger.log(`Published run cancel event for thread ${threadId} (run ${runId})`);
      } catch (error) {
        this.logger.warn(`Failed to publish run cancel event for run ${runId}: ${error.message}`);
        // Run is still cancelled in database even if Redis publish fails
      }

      this.logger.log(`Successfully cancelled run ${runId} (thread ${threadId})`);
      return true;
    } catch (error) {
      this.logger.error(`Failed to cancel run ${runId}: ${error.message}`, error.stack);
      return false;
    }
  }

  /**
   * Validate if an agent exists and is properly configured
   * @param agentId Agent ID to validate
   * @returns Promise<boolean> True if agent is valid
   */
  async validateAgent(agentId: string): Promise<boolean> {
    try {
      const agentConfig = await this.agentConfigService.getAgentConfig(agentId);
      return agentConfig ? this.agentConfigService.validateAgentConfig(agentConfig) : false;
    } catch (error) {
      this.logger.error(`Error validating agent ${agentId}: ${error.message}`);
      return false;
    }
  }

  /**
   * Get Redis connection health status
   * @returns Promise<boolean> True if Redis is healthy
   */
  async getRedisHealth(): Promise<boolean> {
    try {
      // ClientProxy doesn't have a direct health check method
      // We can try to emit a test event to check connectivity
      this.redisClient.emit('health.check', { timestamp: Date.now() });
      return true;
    } catch (error) {
      this.logger.error(`Redis health check failed: ${error.message}`);
      return false;
    }
  }
}