{"version": 3, "sources": ["../../../../src/modules/email-system/entities/admin_email_server_configurations.entity.ts"], "sourcesContent": ["import { Column, Entity, PrimaryGeneratedColumn } from 'typeorm';\r\n\r\n/**\r\n * Lưu trữ thông tin cấu hình cụ thể cho máy chủ <PERSON>ail (SMTP server)\r\n */\r\n@Entity({ name: 'admin_email_server_configurations' })\r\nexport class AdminEmailServerConfigurationEntity {\r\n  /**\r\n   * Primary key\r\n   */\r\n  @PrimaryGeneratedColumn({ type: 'integer' })\r\n  id: number;\r\n\r\n  /**\r\n   * Tên hiển thị của cấu hình, ví dụ: \"Mailgun Server #1\" hoặc \"AWS SES\"\r\n   */\r\n  @Column({ name: 'server_name', type: 'varchar', length: 100, nullable: true })\r\n  serverName: string;\r\n\r\n  /**\r\n   * Địa chỉ máy chủ SMTP, ví dụ: smtp.gmail.com, smtp.mailgun.org…\r\n   */\r\n  @Column({ name: 'host', type: 'varchar', length: 255, nullable: true })\r\n  host: string;\r\n\r\n  /**\r\n   * <PERSON><PERSON>ng <PERSON>TP, ví dụ: 465, 587, …\r\n   */\r\n  @Column({ name: 'port', type: 'integer', nullable: true })\r\n  port: number;\r\n\r\n  /**\r\n   * Tên đăng nhập hoặc Email account (nếu sử dụng tài khoản riêng)\r\n   */\r\n  @Column({ name: 'username', type: 'varchar', length: 255, nullable: true })\r\n  username: string;\r\n\r\n  /**\r\n   * Mật khẩu hoặc token xác thực cho SMTP\r\n   */\r\n  @Column({ name: 'password', type: 'varchar', length: 255, nullable: true })\r\n  password: string;\r\n\r\n  /**\r\n   * Xác định có sử dụng SSL/TLS hay không\r\n   */\r\n  @Column({ name: 'use_ssl', type: 'boolean', nullable: true })\r\n  useSsl: boolean;\r\n\r\n  /**\r\n   * Cho phép lưu các cấu hình nâng cao, ví dụ: certificate path, cơ chế xác thực, v.v.\r\n   */\r\n  @Column({ name: 'additional_settings', type: 'json', nullable: true })\r\n  additionalSettings: Record<string, any>;\r\n\r\n  /**\r\n   * Nhân viên tạo\r\n   */\r\n  @Column({ name: 'created_by', type: 'integer', nullable: true })\r\n  createdBy: number;\r\n\r\n  /**\r\n   * Thời gian tạo\r\n   */\r\n  @Column({ name: 'created_at', type: 'bigint', nullable: true })\r\n  createdAt: number;\r\n\r\n  /**\r\n   * Thời gian cập nhật\r\n   */\r\n  @Column({ name: 'updated_at', type: 'bigint', nullable: true })\r\n  updatedAt: number;\r\n}\r\n"], "names": ["AdminEmailServerConfigurationEntity", "type", "name", "length", "nullable"], "mappings": ";;;;+BAMaA;;;eAAAA;;;yBAN0C;;;;;;;;;;AAMhD,IAAA,AAAMA,sCAAN,MAAMA;AAkEb;;;QA9D4BC,MAAM;;;;;;QAMtBC,MAAM;QAAeD,MAAM;QAAWE,QAAQ;QAAKC,UAAU;;;;;;QAM7DF,MAAM;QAAQD,MAAM;QAAWE,QAAQ;QAAKC,UAAU;;;;;;QAMtDF,MAAM;QAAQD,MAAM;QAAWG,UAAU;;;;;;QAMzCF,MAAM;QAAYD,MAAM;QAAWE,QAAQ;QAAKC,UAAU;;;;;;QAM1DF,MAAM;QAAYD,MAAM;QAAWE,QAAQ;QAAKC,UAAU;;;;;;QAM1DF,MAAM;QAAWD,MAAM;QAAWG,UAAU;;;;;;QAM5CF,MAAM;QAAuBD,MAAM;QAAQG,UAAU;;;;;;QAMrDF,MAAM;QAAcD,MAAM;QAAWG,UAAU;;;;;;QAM/CF,MAAM;QAAcD,MAAM;QAAUG,UAAU;;;;;;QAM9CF,MAAM;QAAcD,MAAM;QAAUG,UAAU;;;;;;QAjEhDF,MAAM"}