"use strict";
Object.defineProperty(exports, "__esModule", {
    value: true
});
Object.defineProperty(exports, "AudienceCustomFieldDefinitionResponseDto", {
    enumerable: true,
    get: function() {
        return AudienceCustomFieldDefinitionResponseDto;
    }
});
const _swagger = require("@nestjs/swagger");
const _createaudiencecustomfielddefinitiondto = require("./create-audience-custom-field-definition.dto");
function _ts_decorate(decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for(var i = decorators.length - 1; i >= 0; i--)if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
}
function _ts_metadata(k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
}
let AudienceCustomFieldDefinitionResponseDto = class AudienceCustomFieldDefinitionResponseDto {
};
_ts_decorate([
    (0, _swagger.ApiProperty)({
        description: 'ID của trường tùy chỉnh',
        example: 1
    }),
    _ts_metadata("design:type", Number)
], AudienceCustomFieldDefinitionResponseDto.prototype, "id", void 0);
_ts_decorate([
    (0, _swagger.ApiProperty)({
        description: 'Định danh duy nhất cho trường tùy chỉnh',
        example: 'customer_address'
    }),
    _ts_metadata("design:type", String)
], AudienceCustomFieldDefinitionResponseDto.prototype, "fieldKey", void 0);
_ts_decorate([
    (0, _swagger.ApiProperty)({
        description: 'ID của người dùng mà trường tùy chỉnh này thuộc về',
        example: 1
    }),
    _ts_metadata("design:type", Number)
], AudienceCustomFieldDefinitionResponseDto.prototype, "userId", void 0);
_ts_decorate([
    (0, _swagger.ApiProperty)({
        description: 'Tên hiển thị thân thiện với người dùng',
        example: 'Địa chỉ khách hàng'
    }),
    _ts_metadata("design:type", String)
], AudienceCustomFieldDefinitionResponseDto.prototype, "displayName", void 0);
_ts_decorate([
    (0, _swagger.ApiProperty)({
        description: 'Kiểu dữ liệu',
        enum: _createaudiencecustomfielddefinitiondto.CustomFieldDataType,
        example: _createaudiencecustomfielddefinitiondto.CustomFieldDataType.STRING
    }),
    _ts_metadata("design:type", typeof _createaudiencecustomfielddefinitiondto.CustomFieldDataType === "undefined" ? Object : _createaudiencecustomfielddefinitiondto.CustomFieldDataType)
], AudienceCustomFieldDefinitionResponseDto.prototype, "dataType", void 0);
_ts_decorate([
    (0, _swagger.ApiProperty)({
        description: 'Mô tả chi tiết hoặc ghi chú về trường tùy chỉnh',
        example: 'Địa chỉ liên hệ của khách hàng',
        nullable: true
    }),
    _ts_metadata("design:type", Object)
], AudienceCustomFieldDefinitionResponseDto.prototype, "description", void 0);

//# sourceMappingURL=audience-custom-field-definition-response.dto.js.map