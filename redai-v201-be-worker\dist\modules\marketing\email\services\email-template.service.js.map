{"version": 3, "sources": ["../../../../../src/modules/marketing/email/services/email-template.service.ts"], "sourcesContent": ["import { Injectable, Logger } from '@nestjs/common';\r\n\r\n/**\r\n * Service xử lý template email với biến tùy chỉnh\r\n */\r\n@Injectable()\r\nexport class EmailTemplateService {\r\n  private readonly logger = new Logger(EmailTemplateService.name);\r\n\r\n  /**\r\n   * Inject biến tùy chỉnh vào template\r\n   * @param template Template chứa biến dạng {{variable}}\r\n   * @param variables Dữ liệu biến để thay thế\r\n   * @returns Template đã được inject biến\r\n   */\r\n  injectVariables(template: string, variables: Record<string, any>): string {\r\n    if (!template || !variables) {\r\n      return template;\r\n    }\r\n\r\n    try {\r\n      let result = template;\r\n\r\n      // Tìm tất cả biến dạng {{variable}} trong template\r\n      const variablePattern = /\\{\\{([^}]+)\\}\\}/g;\r\n      let match;\r\n\r\n      while ((match = variablePattern.exec(template)) !== null) {\r\n        const variableName = match[1].trim();\r\n        const variableValue = this.getNestedValue(variables, variableName);\r\n\r\n        if (variableValue !== undefined && variableValue !== null) {\r\n          // Thay thế biến bằng giá trị thực\r\n          const placeholder = match[0]; // {{variable}}\r\n          result = result.replace(\r\n            new RegExp(this.escapeRegExp(placeholder), 'g'),\r\n            String(variableValue),\r\n          );\r\n        } else {\r\n          // Nếu không tìm thấy biến, giữ nguyên hoặc thay bằng chuỗi rỗng\r\n          this.logger.warn(`Variable \"${variableName}\" not found in data`);\r\n          const placeholder = match[0];\r\n          result = result.replace(\r\n            new RegExp(this.escapeRegExp(placeholder), 'g'),\r\n            '',\r\n          );\r\n        }\r\n      }\r\n\r\n      return result;\r\n    } catch (error) {\r\n      this.logger.error(\r\n        `Error injecting variables into template: ${error.message}`,\r\n        error.stack,\r\n      );\r\n      return template; // Trả về template gốc nếu có lỗi\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Inject pixel tracking vào nội dung email\r\n   * @param content Nội dung email HTML\r\n   * @param trackingId ID tracking duy nhất\r\n   * @param baseUrl Base URL của server\r\n   * @returns Nội dung email đã có pixel tracking\r\n   */\r\n  injectTrackingPixel(\r\n    content: string,\r\n    trackingId: string,\r\n    baseUrl: string = '',\r\n  ): string {\r\n    if (!content || !trackingId) {\r\n      return content;\r\n    }\r\n\r\n    try {\r\n      const trackingPixel = `<img src=\"${baseUrl}/api/email-tracking/pixel/${trackingId}\" width=\"1\" height=\"1\" style=\"display:none;\" alt=\"\" />`;\r\n\r\n      // Thêm pixel tracking trước thẻ đóng </body> hoặc cuối nội dung\r\n      if (content.includes('</body>')) {\r\n        return content.replace('</body>', `${trackingPixel}</body>`);\r\n      } else {\r\n        return content + trackingPixel;\r\n      }\r\n    } catch (error) {\r\n      this.logger.error(\r\n        `Error injecting tracking pixel: ${error.message}`,\r\n        error.stack,\r\n      );\r\n      return content;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Lấy giá trị nested từ object bằng dot notation\r\n   * @param obj Object chứa dữ liệu\r\n   * @param path Đường dẫn đến giá trị (vd: user.name, profile.address.city)\r\n   * @returns Giá trị tìm được hoặc undefined\r\n   */\r\n  private getNestedValue(obj: any, path: string): any {\r\n    try {\r\n      return path.split('.').reduce((current, key) => {\r\n        return current && current[key] !== undefined ? current[key] : undefined;\r\n      }, obj);\r\n    } catch (error) {\r\n      return undefined;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Escape special characters for RegExp\r\n   * @param string Chuỗi cần escape\r\n   * @returns Chuỗi đã escape\r\n   */\r\n  private escapeRegExp(string: string): string {\r\n    return string.replace(/[.*+?^${}()|[\\]\\\\]/g, '\\\\$&');\r\n  }\r\n\r\n  /**\r\n   * Validate template syntax\r\n   * @param template Template cần validate\r\n   * @returns True nếu template hợp lệ\r\n   */\r\n  validateTemplate(template: string): boolean {\r\n    if (!template) {\r\n      return true;\r\n    }\r\n\r\n    try {\r\n      // Kiểm tra cặp ngoặc {{ }} có đúng không\r\n      const openBraces = (template.match(/\\{\\{/g) || []).length;\r\n      const closeBraces = (template.match(/\\}\\}/g) || []).length;\r\n\r\n      return openBraces === closeBraces;\r\n    } catch (error) {\r\n      this.logger.error(`Error validating template: ${error.message}`);\r\n      return false;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Lấy danh sách tất cả biến trong template\r\n   * @param template Template cần phân tích\r\n   * @returns Mảng tên biến\r\n   */\r\n  extractVariables(template: string): string[] {\r\n    if (!template) {\r\n      return [];\r\n    }\r\n\r\n    try {\r\n      const variables: string[] = [];\r\n      const variablePattern = /\\{\\{([^}]+)\\}\\}/g;\r\n      let match;\r\n\r\n      while ((match = variablePattern.exec(template)) !== null) {\r\n        const variableName = match[1].trim();\r\n        if (!variables.includes(variableName)) {\r\n          variables.push(variableName);\r\n        }\r\n      }\r\n\r\n      return variables;\r\n    } catch (error) {\r\n      this.logger.error(`Error extracting variables: ${error.message}`);\r\n      return [];\r\n    }\r\n  }\r\n}\r\n"], "names": ["EmailTemplateService", "injectVariables", "template", "variables", "result", "variablePattern", "match", "exec", "variableName", "trim", "variableValue", "getNestedValue", "undefined", "placeholder", "replace", "RegExp", "escapeRegExp", "String", "logger", "warn", "error", "message", "stack", "injectTrackingPixel", "content", "trackingId", "baseUrl", "trackingPixel", "includes", "obj", "path", "split", "reduce", "current", "key", "string", "validateTemplate", "openBraces", "length", "closeBraces", "extractVariables", "push", "<PERSON><PERSON>", "name"], "mappings": ";;;;+BAMaA;;;eAAAA;;;wBANsB;;;;;;;AAM5B,IAAA,AAAMA,uBAAN,MAAMA;IAGX;;;;;GAKC,GACDC,gBAAgBC,QAAgB,EAAEC,SAA8B,EAAU;QACxE,IAAI,CAACD,YAAY,CAACC,WAAW;YAC3B,OAAOD;QACT;QAEA,IAAI;YACF,IAAIE,SAASF;YAEb,mDAAmD;YACnD,MAAMG,kBAAkB;YACxB,IAAIC;YAEJ,MAAO,AAACA,CAAAA,QAAQD,gBAAgBE,IAAI,CAACL,SAAQ,MAAO,KAAM;gBACxD,MAAMM,eAAeF,KAAK,CAAC,EAAE,CAACG,IAAI;gBAClC,MAAMC,gBAAgB,IAAI,CAACC,cAAc,CAACR,WAAWK;gBAErD,IAAIE,kBAAkBE,aAAaF,kBAAkB,MAAM;oBACzD,kCAAkC;oBAClC,MAAMG,cAAcP,KAAK,CAAC,EAAE,EAAE,eAAe;oBAC7CF,SAASA,OAAOU,OAAO,CACrB,IAAIC,OAAO,IAAI,CAACC,YAAY,CAACH,cAAc,MAC3CI,OAAOP;gBAEX,OAAO;oBACL,gEAAgE;oBAChE,IAAI,CAACQ,MAAM,CAACC,IAAI,CAAC,CAAC,UAAU,EAAEX,aAAa,mBAAmB,CAAC;oBAC/D,MAAMK,cAAcP,KAAK,CAAC,EAAE;oBAC5BF,SAASA,OAAOU,OAAO,CACrB,IAAIC,OAAO,IAAI,CAACC,YAAY,CAACH,cAAc,MAC3C;gBAEJ;YACF;YAEA,OAAOT;QACT,EAAE,OAAOgB,OAAO;YACd,IAAI,CAACF,MAAM,CAACE,KAAK,CACf,CAAC,yCAAyC,EAAEA,MAAMC,OAAO,EAAE,EAC3DD,MAAME,KAAK;YAEb,OAAOpB,UAAU,iCAAiC;QACpD;IACF;IAEA;;;;;;GAMC,GACDqB,oBACEC,OAAe,EACfC,UAAkB,EAClBC,UAAkB,EAAE,EACZ;QACR,IAAI,CAACF,WAAW,CAACC,YAAY;YAC3B,OAAOD;QACT;QAEA,IAAI;YACF,MAAMG,gBAAgB,CAAC,UAAU,EAAED,QAAQ,0BAA0B,EAAED,WAAW,sDAAsD,CAAC;YAEzI,gEAAgE;YAChE,IAAID,QAAQI,QAAQ,CAAC,YAAY;gBAC/B,OAAOJ,QAAQV,OAAO,CAAC,WAAW,GAAGa,cAAc,OAAO,CAAC;YAC7D,OAAO;gBACL,OAAOH,UAAUG;YACnB;QACF,EAAE,OAAOP,OAAO;YACd,IAAI,CAACF,MAAM,CAACE,KAAK,CACf,CAAC,gCAAgC,EAAEA,MAAMC,OAAO,EAAE,EAClDD,MAAME,KAAK;YAEb,OAAOE;QACT;IACF;IAEA;;;;;GAKC,GACD,AAAQb,eAAekB,GAAQ,EAAEC,IAAY,EAAO;QAClD,IAAI;YACF,OAAOA,KAAKC,KAAK,CAAC,KAAKC,MAAM,CAAC,CAACC,SAASC;gBACtC,OAAOD,WAAWA,OAAO,CAACC,IAAI,KAAKtB,YAAYqB,OAAO,CAACC,IAAI,GAAGtB;YAChE,GAAGiB;QACL,EAAE,OAAOT,OAAO;YACd,OAAOR;QACT;IACF;IAEA;;;;GAIC,GACD,AAAQI,aAAamB,MAAc,EAAU;QAC3C,OAAOA,OAAOrB,OAAO,CAAC,uBAAuB;IAC/C;IAEA;;;;GAIC,GACDsB,iBAAiBlC,QAAgB,EAAW;QAC1C,IAAI,CAACA,UAAU;YACb,OAAO;QACT;QAEA,IAAI;YACF,yCAAyC;YACzC,MAAMmC,aAAa,AAACnC,CAAAA,SAASI,KAAK,CAAC,YAAY,EAAE,AAAD,EAAGgC,MAAM;YACzD,MAAMC,cAAc,AAACrC,CAAAA,SAASI,KAAK,CAAC,YAAY,EAAE,AAAD,EAAGgC,MAAM;YAE1D,OAAOD,eAAeE;QACxB,EAAE,OAAOnB,OAAO;YACd,IAAI,CAACF,MAAM,CAACE,KAAK,CAAC,CAAC,2BAA2B,EAAEA,MAAMC,OAAO,EAAE;YAC/D,OAAO;QACT;IACF;IAEA;;;;GAIC,GACDmB,iBAAiBtC,QAAgB,EAAY;QAC3C,IAAI,CAACA,UAAU;YACb,OAAO,EAAE;QACX;QAEA,IAAI;YACF,MAAMC,YAAsB,EAAE;YAC9B,MAAME,kBAAkB;YACxB,IAAIC;YAEJ,MAAO,AAACA,CAAAA,QAAQD,gBAAgBE,IAAI,CAACL,SAAQ,MAAO,KAAM;gBACxD,MAAMM,eAAeF,KAAK,CAAC,EAAE,CAACG,IAAI;gBAClC,IAAI,CAACN,UAAUyB,QAAQ,CAACpB,eAAe;oBACrCL,UAAUsC,IAAI,CAACjC;gBACjB;YACF;YAEA,OAAOL;QACT,EAAE,OAAOiB,OAAO;YACd,IAAI,CAACF,MAAM,CAACE,KAAK,CAAC,CAAC,4BAA4B,EAAEA,MAAMC,OAAO,EAAE;YAChE,OAAO,EAAE;QACX;IACF;;aAhKiBH,SAAS,IAAIwB,cAAM,CAAC1C,qBAAqB2C,IAAI;;AAiKhE"}