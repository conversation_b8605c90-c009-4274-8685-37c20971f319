"use strict";
Object.defineProperty(exports, "__esModule", {
    value: true
});
Object.defineProperty(exports, "SmsModule", {
    enumerable: true,
    get: function() {
        return SmsModule;
    }
});
const _common = require("@nestjs/common");
const _axios = require("@nestjs/axios");
const _config = require("@nestjs/config");
const _smsservice = require("./sms.service");
const _smsproviderfactoryservice = require("./sms-provider-factory.service");
const _speedsmsproviderservice = require("./speed-sms-provider.service");
const _twilioproviderservice = require("./twilio-provider.service");
const _vonageproviderservice = require("./vonage-provider.service");
const _fptsmsproviderservice = require("./fpt-sms-provider.service");
function _ts_decorate(decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for(var i = decorators.length - 1; i >= 0; i--)if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
}
let SmsModule = class SmsModule {
};
SmsModule = _ts_decorate([
    (0, _common.Global)(),
    (0, _common.Module)({
        imports: [
            _axios.HttpModule,
            _config.ConfigModule
        ],
        providers: [
            _smsservice.SmsService,
            _smsproviderfactoryservice.SmsProviderFactory,
            _speedsmsproviderservice.SpeedSmsProvider,
            _twilioproviderservice.TwilioProvider,
            _vonageproviderservice.VonageProvider,
            _fptsmsproviderservice.FptSmsProvider
        ],
        exports: [
            _smsservice.SmsService,
            _smsproviderfactoryservice.SmsProviderFactory,
            _speedsmsproviderservice.SpeedSmsProvider,
            _twilioproviderservice.TwilioProvider,
            _vonageproviderservice.VonageProvider,
            _fptsmsproviderservice.FptSmsProvider
        ]
    })
], SmsModule);

//# sourceMappingURL=sms.module.js.map