"use strict";
Object.defineProperty(exports, "__esModule", {
    value: true
});
function _export(target, all) {
    for(var name in all)Object.defineProperty(target, name, {
        enumerable: true,
        get: Object.getOwnPropertyDescriptor(all, name).get
    });
}
_export(exports, {
    get ZaloAutomationLogQueryDto () {
        return ZaloAutomationLogQueryDto;
    },
    get ZaloAutomationLogStatus () {
        return ZaloAutomationLogStatus;
    }
});
const _swagger = require("@nestjs/swagger");
const _classvalidator = require("class-validator");
const _dto = require("../../../../common/dto");
function _ts_decorate(decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for(var i = decorators.length - 1; i >= 0; i--)if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
}
function _ts_metadata(k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
}
var ZaloAutomationLogStatus = /*#__PURE__*/ function(ZaloAutomationLogStatus) {
    ZaloAutomationLogStatus["PENDING"] = "pending";
    ZaloAutomationLogStatus["SUCCESS"] = "success";
    ZaloAutomationLogStatus["FAILED"] = "failed";
    ZaloAutomationLogStatus["ALL"] = "all";
    return ZaloAutomationLogStatus;
}({});
let ZaloAutomationLogQueryDto = class ZaloAutomationLogQueryDto extends _dto.QueryDto {
    constructor(){
        super();
        this.sortBy = 'createdAt';
        this.sortDirection = _dto.SortDirection.DESC;
    }
};
_ts_decorate([
    (0, _swagger.ApiProperty)({
        description: 'Lọc theo ID của tự động hóa',
        example: 1,
        required: false
    }),
    (0, _classvalidator.IsOptional)(),
    (0, _classvalidator.IsString)(),
    _ts_metadata("design:type", String)
], ZaloAutomationLogQueryDto.prototype, "automationId", void 0);
_ts_decorate([
    (0, _swagger.ApiProperty)({
        description: 'Lọc theo ID của người theo dõi',
        example: '123456789',
        required: false
    }),
    (0, _classvalidator.IsOptional)(),
    (0, _classvalidator.IsString)(),
    _ts_metadata("design:type", String)
], ZaloAutomationLogQueryDto.prototype, "followerId", void 0);
_ts_decorate([
    (0, _swagger.ApiProperty)({
        description: 'Lọc theo loại sự kiện kích hoạt',
        example: 'follow',
        required: false
    }),
    (0, _classvalidator.IsOptional)(),
    (0, _classvalidator.IsString)(),
    _ts_metadata("design:type", String)
], ZaloAutomationLogQueryDto.prototype, "triggerType", void 0);
_ts_decorate([
    (0, _swagger.ApiProperty)({
        description: 'Lọc theo loại hành động',
        example: 'send_message',
        required: false
    }),
    (0, _classvalidator.IsOptional)(),
    (0, _classvalidator.IsString)(),
    _ts_metadata("design:type", String)
], ZaloAutomationLogQueryDto.prototype, "actionType", void 0);
_ts_decorate([
    (0, _swagger.ApiProperty)({
        description: 'Lọc theo trạng thái',
        enum: ZaloAutomationLogStatus,
        example: "success",
        required: false
    }),
    (0, _classvalidator.IsOptional)(),
    (0, _classvalidator.IsEnum)(ZaloAutomationLogStatus),
    _ts_metadata("design:type", String)
], ZaloAutomationLogQueryDto.prototype, "status", void 0);

//# sourceMappingURL=zalo-automation-log-query.dto.js.map