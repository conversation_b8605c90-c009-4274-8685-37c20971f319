{"version": 3, "sources": ["../../../../src/modules/marketing/entities/zalo-campaign-log.entity.ts"], "sourcesContent": ["import { Column, Entity, PrimaryGeneratedColumn } from 'typeorm';\r\n\r\n/**\r\n * Entity cho log chiến dịch Zalo\r\n */\r\n@Entity('zalo_campaign_logs')\r\nexport class ZaloCampaignLog {\r\n  @PrimaryGeneratedColumn()\r\n  id: number;\r\n\r\n  @Column({ name: 'campaign_id' })\r\n  campaignId: number;\r\n\r\n  @Column({ name: 'user_id' })\r\n  userId: number;\r\n\r\n  @Column({ name: 'oa_id' })\r\n  oaId: string;\r\n\r\n  @Column({ name: 'follower_id' })\r\n  followerId: number;\r\n\r\n  @Column({ name: 'follower_user_id' })\r\n  followerUserId: string;\r\n\r\n  @Column({ name: 'message_id', nullable: true })\r\n  messageId?: string;\r\n\r\n  @Column()\r\n  status: string;\r\n\r\n  @Column({ nullable: true })\r\n  error?: string;\r\n\r\n  @Column({ name: 'created_at', type: 'bigint' })\r\n  createdAt: number;\r\n}\r\n"], "names": ["ZaloCampaignLog", "name", "nullable", "type"], "mappings": ";;;;+BAMaA;;;eAAAA;;;yBAN0C;;;;;;;;;;AAMhD,IAAA,AAAMA,kBAAN,MAAMA;AA8Bb;;;;;;;QA1BYC,MAAM;;;;;;QAGNA,MAAM;;;;;;QAGNA,MAAM;;;;;;QAGNA,MAAM;;;;;;QAGNA,MAAM;;;;;;QAGNA,MAAM;QAAcC,UAAU;;;;;;;;;;QAM9BA,UAAU;;;;;;QAGVD,MAAM;QAAcE,MAAM"}