{"version": 3, "sources": ["../../../src/agent/interfaces/system-agent-config.interface.ts"], "sourcesContent": ["export enum ModelProviderEnum {\n  OPENAI = 'OPENAI',\n  XAI = 'XAI',\n  ANTHROPIC = 'ANTHROPIC',\n  GOOGLE = 'GOOGLE',\n  DEEPSEEK = 'DEEPSEEK',\n}\n\nexport enum InputModality {\n  TEXT = 'text',\n  IMAGE = 'image',\n  AUDIO = 'audio',\n  VIDEO = 'video',\n}\n\nexport enum OutputModality {\n  TEXT = 'text',\n  IMAGE = 'image',\n  AUDIO = 'audio',\n  VIDEO = 'video',\n}\n\nexport enum SamplingParameter {\n  TEMPERATURE = 'temperature',\n  TOP_P = 'top_p',\n  TOP_K = 'top_k',\n  MAX_TOKENS = 'max_tokens',\n  MAX_OUTPUT_TOKENS = 'max_output_tokens',\n}\n\nexport enum ModelFeature {\n  TOOL_CALL = 'tool_call',\n  PARALLEL_TOOL_CALL = 'parallel_tool_call',\n}\n\n\nexport type TrimmingType = 'top_k' | 'ai' | 'token';\n\nexport interface McpSseConfig {\n  serverName: string;\n  config: Record<string, any>;\n}\n\nexport interface SystemAgentConfig {\n  id: string;\n  name: string;\n  description: string;\n  instruction: string;\n  mcpConfig: McpSseConfig[];\n  vectorStoreId: string;\n  trimmingConfig: {\n    type: TrimmingType; // default to `top_k` always\n    threshold: number;\n  }\n  model: {\n    name: string;\n    provider: ModelProviderEnum;\n    inputModalities: InputModality[];\n    outputModalities: OutputModality[];\n    samplingParameters: SamplingParameter[];\n    features: ModelFeature[];\n    parameters?: {\n      temperature?: number;\n      topP?: number;\n      topK?: number;\n      maxTokens?: number;\n      maxOutputTokens?: number;\n    };\n    type: 'SYSTEM';\n    apiKeys: string[];\n  };\n}\n\n\nexport interface SystemAgentConfigMap {\n  [agentId: string]: SystemAgentConfig;\n}\n"], "names": ["InputModality", "ModelFeature", "ModelProviderEnum", "OutputModality", "SamplingParameter"], "mappings": ";;;;;;;;;;;QAQYA;eAAAA;;QAsBAC;eAAAA;;QA9BAC;eAAAA;;QAeAC;eAAAA;;QAOAC;eAAAA;;;AAtBL,IAAA,AAAKF,2CAAAA;;;;;;WAAAA;;AAQL,IAAA,AAAKF,uCAAAA;;;;;WAAAA;;AAOL,IAAA,AAAKG,wCAAAA;;;;;WAAAA;;AAOL,IAAA,AAAKC,2CAAAA;;;;;;WAAAA;;AAQL,IAAA,AAAKH,sCAAAA;;;WAAAA"}