"use strict";
Object.defineProperty(exports, "__esModule", {
    value: true
});
Object.defineProperty(exports, "FollowerResponseDto", {
    enumerable: true,
    get: function() {
        return FollowerResponseDto;
    }
});
const _swagger = require("@nestjs/swagger");
function _ts_decorate(decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for(var i = decorators.length - 1; i >= 0; i--)if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
}
function _ts_metadata(k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
}
let FollowerResponseDto = class FollowerResponseDto {
};
_ts_decorate([
    (0, _swagger.ApiProperty)({
        description: 'ID của người theo dõi trong hệ thống',
        example: 1
    }),
    _ts_metadata("design:type", Number)
], FollowerResponseDto.prototype, "id", void 0);
_ts_decorate([
    (0, _swagger.ApiProperty)({
        description: 'ID của người dùng Zalo',
        example: '123456789'
    }),
    _ts_metadata("design:type", String)
], FollowerResponseDto.prototype, "userId", void 0);
_ts_decorate([
    (0, _swagger.ApiProperty)({
        description: 'Tên hiển thị của người dùng',
        example: 'Nguyễn Văn A',
        nullable: true
    }),
    _ts_metadata("design:type", String)
], FollowerResponseDto.prototype, "displayName", void 0);
_ts_decorate([
    (0, _swagger.ApiProperty)({
        description: 'URL avatar của người dùng',
        example: 'https://zalo.me/avatar/123456789.jpg',
        nullable: true
    }),
    _ts_metadata("design:type", String)
], FollowerResponseDto.prototype, "avatarUrl", void 0);
_ts_decorate([
    (0, _swagger.ApiProperty)({
        description: 'Số điện thoại của người dùng',
        example: '0912345678',
        nullable: true
    }),
    _ts_metadata("design:type", String)
], FollowerResponseDto.prototype, "phone", void 0);
_ts_decorate([
    (0, _swagger.ApiProperty)({
        description: 'Giới tính của người dùng (1: Nam, 2: Nữ)',
        example: 1,
        nullable: true
    }),
    _ts_metadata("design:type", Number)
], FollowerResponseDto.prototype, "gender", void 0);
_ts_decorate([
    (0, _swagger.ApiProperty)({
        description: 'Ngày sinh của người dùng (định dạng dd/mm/yyyy)',
        example: '01/01/1990',
        nullable: true
    }),
    _ts_metadata("design:type", String)
], FollowerResponseDto.prototype, "birthDate", void 0);
_ts_decorate([
    (0, _swagger.ApiProperty)({
        description: 'Các tag gán cho người dùng',
        example: [
            'vip',
            'new-customer'
        ],
        type: [
            String
        ]
    }),
    _ts_metadata("design:type", Array)
], FollowerResponseDto.prototype, "tags", void 0);
_ts_decorate([
    (0, _swagger.ApiProperty)({
        description: 'Trạng thái (active, unfollowed)',
        example: 'active'
    }),
    _ts_metadata("design:type", String)
], FollowerResponseDto.prototype, "status", void 0);
_ts_decorate([
    (0, _swagger.ApiProperty)({
        description: 'Thời điểm theo dõi (Unix timestamp)',
        example: 1625097600000
    }),
    _ts_metadata("design:type", Number)
], FollowerResponseDto.prototype, "followedAt", void 0);

//# sourceMappingURL=follower-response.dto.js.map