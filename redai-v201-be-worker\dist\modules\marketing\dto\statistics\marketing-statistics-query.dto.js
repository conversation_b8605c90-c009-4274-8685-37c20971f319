"use strict";
Object.defineProperty(exports, "__esModule", {
    value: true
});
function _export(target, all) {
    for(var name in all)Object.defineProperty(target, name, {
        enumerable: true,
        get: Object.getOwnPropertyDescriptor(all, name).get
    });
}
_export(exports, {
    get MarketingStatisticsQueryDto () {
        return MarketingStatisticsQueryDto;
    },
    get StatisticsType () {
        return StatisticsType;
    }
});
const _swagger = require("@nestjs/swagger");
const _classvalidator = require("class-validator");
const _classtransformer = require("class-transformer");
function _ts_decorate(decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for(var i = decorators.length - 1; i >= 0; i--)if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
}
function _ts_metadata(k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
}
var StatisticsType = /*#__PURE__*/ function(StatisticsType) {
    StatisticsType["DAILY"] = "daily";
    StatisticsType["WEEKLY"] = "weekly";
    StatisticsType["MONTHLY"] = "monthly";
    StatisticsType["YEARLY"] = "yearly";
    return StatisticsType;
}({});
let MarketingStatisticsQueryDto = class MarketingStatisticsQueryDto {
    constructor(){
        this.type = "monthly";
    }
};
_ts_decorate([
    (0, _swagger.ApiPropertyOptional)({
        description: 'Thời gian bắt đầu thống kê (Unix timestamp)',
        example: 1672531200
    }),
    (0, _classvalidator.IsOptional)(),
    (0, _classvalidator.IsNumber)(),
    (0, _classtransformer.Type)(()=>Number),
    _ts_metadata("design:type", Number)
], MarketingStatisticsQueryDto.prototype, "startDate", void 0);
_ts_decorate([
    (0, _swagger.ApiPropertyOptional)({
        description: 'Thời gian kết thúc thống kê (Unix timestamp)',
        example: 1675209600
    }),
    (0, _classvalidator.IsOptional)(),
    (0, _classvalidator.IsNumber)(),
    (0, _classtransformer.Type)(()=>Number),
    _ts_metadata("design:type", Number)
], MarketingStatisticsQueryDto.prototype, "endDate", void 0);
_ts_decorate([
    (0, _swagger.ApiPropertyOptional)({
        description: 'Loại thống kê',
        enum: StatisticsType,
        default: "monthly"
    }),
    (0, _classvalidator.IsOptional)(),
    (0, _classvalidator.IsEnum)(StatisticsType),
    _ts_metadata("design:type", String)
], MarketingStatisticsQueryDto.prototype, "type", void 0);

//# sourceMappingURL=marketing-statistics-query.dto.js.map