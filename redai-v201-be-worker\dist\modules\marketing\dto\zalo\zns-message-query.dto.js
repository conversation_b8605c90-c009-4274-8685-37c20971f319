"use strict";
Object.defineProperty(exports, "__esModule", {
    value: true
});
function _export(target, all) {
    for(var name in all)Object.defineProperty(target, name, {
        enumerable: true,
        get: Object.getOwnPropertyDescriptor(all, name).get
    });
}
_export(exports, {
    get ZnsMessageQueryDto () {
        return ZnsMessageQueryDto;
    },
    get ZnsMessageStatus () {
        return ZnsMessageStatus;
    }
});
const _swagger = require("@nestjs/swagger");
const _classvalidator = require("class-validator");
const _dto = require("../../../../common/dto");
function _ts_decorate(decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for(var i = decorators.length - 1; i >= 0; i--)if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
}
function _ts_metadata(k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
}
var ZnsMessageStatus = /*#__PURE__*/ function(ZnsMessageStatus) {
    ZnsMessageStatus["PENDING"] = "pending";
    ZnsMessageStatus["DELIVERED"] = "delivered";
    ZnsMessageStatus["FAILED"] = "failed";
    ZnsMessageStatus["ALL"] = "all";
    return ZnsMessageStatus;
}({});
let ZnsMessageQueryDto = class ZnsMessageQueryDto extends _dto.QueryDto {
    constructor(){
        super();
        this.sortBy = 'createdAt';
        this.sortDirection = _dto.SortDirection.DESC;
    }
};
_ts_decorate([
    (0, _swagger.ApiProperty)({
        description: 'Tìm kiếm theo số điện thoại',
        example: '0912345678',
        required: false
    }),
    (0, _classvalidator.IsOptional)(),
    (0, _classvalidator.IsString)(),
    _ts_metadata("design:type", String)
], ZnsMessageQueryDto.prototype, "phone", void 0);
_ts_decorate([
    (0, _swagger.ApiProperty)({
        description: 'Lọc theo ID template',
        example: 'template123456789',
        required: false
    }),
    (0, _classvalidator.IsOptional)(),
    (0, _classvalidator.IsString)(),
    _ts_metadata("design:type", String)
], ZnsMessageQueryDto.prototype, "templateId", void 0);
_ts_decorate([
    (0, _swagger.ApiProperty)({
        description: 'Lọc theo trạng thái',
        enum: ZnsMessageStatus,
        example: "delivered",
        required: false
    }),
    (0, _classvalidator.IsOptional)(),
    (0, _classvalidator.IsEnum)(ZnsMessageStatus),
    _ts_metadata("design:type", String)
], ZnsMessageQueryDto.prototype, "status", void 0);

//# sourceMappingURL=zns-message-query.dto.js.map