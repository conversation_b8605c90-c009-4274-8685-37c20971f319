"use strict";
Object.defineProperty(exports, "__esModule", {
    value: true
});
Object.defineProperty(exports, "ZnsMessageResponseDto", {
    enumerable: true,
    get: function() {
        return ZnsMessageResponseDto;
    }
});
const _swagger = require("@nestjs/swagger");
function _ts_decorate(decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for(var i = decorators.length - 1; i >= 0; i--)if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
}
function _ts_metadata(k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
}
let ZnsMessageResponseDto = class ZnsMessageResponseDto {
};
_ts_decorate([
    (0, _swagger.ApiProperty)({
        description: 'ID của tin nhắn trong hệ thống',
        example: 1
    }),
    _ts_metadata("design:type", Number)
], ZnsMessageResponseDto.prototype, "id", void 0);
_ts_decorate([
    (0, _swagger.ApiProperty)({
        description: 'ID của tin nhắn trên Zalo',
        example: 'msg123456789',
        nullable: true
    }),
    _ts_metadata("design:type", String)
], ZnsMessageResponseDto.prototype, "messageId", void 0);
_ts_decorate([
    (0, _swagger.ApiProperty)({
        description: 'ID của template',
        example: 'template123456789'
    }),
    _ts_metadata("design:type", String)
], ZnsMessageResponseDto.prototype, "templateId", void 0);
_ts_decorate([
    (0, _swagger.ApiProperty)({
        description: 'Số điện thoại người nhận',
        example: '0912345678'
    }),
    _ts_metadata("design:type", String)
], ZnsMessageResponseDto.prototype, "phone", void 0);
_ts_decorate([
    (0, _swagger.ApiProperty)({
        description: 'ID giao dịch',
        example: 'tracking123456789'
    }),
    _ts_metadata("design:type", String)
], ZnsMessageResponseDto.prototype, "trackingId", void 0);
_ts_decorate([
    (0, _swagger.ApiProperty)({
        description: 'Trạng thái tin nhắn (pending, delivered, failed)',
        example: 'delivered'
    }),
    _ts_metadata("design:type", String)
], ZnsMessageResponseDto.prototype, "status", void 0);
_ts_decorate([
    (0, _swagger.ApiProperty)({
        description: 'Thời điểm gửi thành công (Unix timestamp)',
        example: 1625097600000,
        nullable: true
    }),
    _ts_metadata("design:type", Number)
], ZnsMessageResponseDto.prototype, "deliveredTime", void 0);
_ts_decorate([
    (0, _swagger.ApiProperty)({
        description: 'Thời điểm tạo (Unix timestamp)',
        example: 1625097600000
    }),
    _ts_metadata("design:type", Number)
], ZnsMessageResponseDto.prototype, "createdAt", void 0);

//# sourceMappingURL=zns-message-response.dto.js.map