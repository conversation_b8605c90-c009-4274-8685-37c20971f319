{"version": 3, "sources": ["../../../../../src/modules/marketing/dto/audience/create-audience.dto.ts"], "sourcesContent": ["import {\r\n  IsEmail,\r\n  IsNotEmpty,\r\n  IsOptional,\r\n  IsPhoneNumber,\r\n  IsString,\r\n  ValidateNested,\r\n} from 'class-validator';\r\nimport { ApiProperty } from '@nestjs/swagger';\r\nimport { Type } from 'class-transformer';\r\nimport { CreateCustomFieldDto } from './create-custom-field.dto';\r\n\r\n/**\r\n * DTO cho việc tạo audience mới\r\n */\r\nexport class CreateAudienceDto {\r\n  /**\r\n   * Email của khách hàng\r\n   * @example \"<EMAIL>\"\r\n   */\r\n  @ApiProperty({\r\n    description: 'Email của khách hàng',\r\n    example: '<EMAIL>',\r\n  })\r\n  @IsNotEmpty({ message: 'Email không được để trống' })\r\n  @IsEmail({}, { message: 'Email không hợp lệ' })\r\n  email: string;\r\n\r\n  /**\r\n   * Số điện thoại của khách hàng\r\n   * @example \"+84912345678\"\r\n   */\r\n  @ApiProperty({\r\n    description: '<PERSON><PERSON> điện thoại của khách hàng',\r\n    example: '+84912345678',\r\n  })\r\n  @IsOptional()\r\n  @IsPhoneNumber(undefined, { message: '<PERSON>ố điện thoại không hợp lệ' })\r\n  phone?: string;\r\n\r\n  /**\r\n   * Danh sách các trường tùy chỉnh\r\n   */\r\n  @ApiProperty({\r\n    description: 'Danh sách các trường tùy chỉnh',\r\n    type: [CreateCustomFieldDto],\r\n    required: false,\r\n  })\r\n  @IsOptional()\r\n  @ValidateNested({ each: true })\r\n  @Type(() => CreateCustomFieldDto)\r\n  customFields?: CreateCustomFieldDto[];\r\n\r\n  /**\r\n   * Danh sách ID của các tag\r\n   * @example [1, 2, 3]\r\n   */\r\n  @ApiProperty({\r\n    description: 'Danh sách ID của các tag',\r\n    example: [1, 2, 3],\r\n    required: false,\r\n    type: [Number],\r\n  })\r\n  @IsOptional()\r\n  tagIds?: number[];\r\n}\r\n"], "names": ["CreateAudienceDto", "description", "example", "message", "type", "CreateCustomFieldDto", "required", "each", "Number"], "mappings": ";;;;+BAeaA;;;eAAAA;;;gCARN;yBACqB;kCACP;sCACgB;;;;;;;;;;AAK9B,IAAA,AAAMA,oBAAN,MAAMA;AAkDb;;;QA5CIC,aAAa;QACbC,SAAS;;;QAEGC,SAAS;;;QACRA,SAAS;;;;;;QAQtBF,aAAa;QACbC,SAAS;;;;QAGiBC,SAAS;;;;;;QAOnCF,aAAa;QACbG,MAAM;YAACC,0CAAoB;SAAC;QAC5BC,UAAU;;;;QAGMC,MAAM;;oCACZF,0CAAoB;;;;;QAQ9BJ,aAAa;QACbC,SAAS;YAAC;YAAG;YAAG;SAAE;QAClBI,UAAU;QACVF,MAAM;YAACI;SAAO"}