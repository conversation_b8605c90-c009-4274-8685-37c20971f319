"use strict";
Object.defineProperty(exports, "__esModule", {
    value: true
});
Object.defineProperty(exports, "GoogleAdsKeyword", {
    enumerable: true,
    get: function() {
        return GoogleAdsKeyword;
    }
});
const _typeorm = require("typeorm");
function _ts_decorate(decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for(var i = decorators.length - 1; i >= 0; i--)if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
}
function _ts_metadata(k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
}
let GoogleAdsKeyword = class GoogleAdsKeyword {
};
_ts_decorate([
    (0, _typeorm.PrimaryGeneratedColumn)({
        name: 'id',
        type: 'bigint'
    }),
    _ts_metadata("design:type", Number)
], GoogleAdsKeyword.prototype, "id", void 0);
_ts_decorate([
    (0, _typeorm.Column)({
        name: 'user_id',
        nullable: false,
        comment: 'ID của người dùng'
    }),
    _ts_metadata("design:type", Number)
], GoogleAdsKeyword.prototype, "userId", void 0);
_ts_decorate([
    (0, _typeorm.Column)({
        name: 'ad_group_id',
        nullable: false,
        comment: 'ID của nhóm quảng cáo trong hệ thống'
    }),
    _ts_metadata("design:type", Number)
], GoogleAdsKeyword.prototype, "adGroupId", void 0);
_ts_decorate([
    (0, _typeorm.Column)({
        name: 'keyword_id',
        nullable: false,
        comment: 'ID của từ khóa trên Google Ads'
    }),
    _ts_metadata("design:type", String)
], GoogleAdsKeyword.prototype, "keywordId", void 0);
_ts_decorate([
    (0, _typeorm.Column)({
        name: 'text',
        length: 255,
        nullable: false,
        comment: 'Văn bản từ khóa'
    }),
    _ts_metadata("design:type", String)
], GoogleAdsKeyword.prototype, "text", void 0);
_ts_decorate([
    (0, _typeorm.Column)({
        name: 'match_type',
        length: 20,
        nullable: false,
        comment: 'Loại đối sánh (EXACT, PHRASE, BROAD)'
    }),
    _ts_metadata("design:type", String)
], GoogleAdsKeyword.prototype, "matchType", void 0);
_ts_decorate([
    (0, _typeorm.Column)({
        name: 'status',
        length: 20,
        nullable: false,
        comment: 'Trạng thái từ khóa'
    }),
    _ts_metadata("design:type", String)
], GoogleAdsKeyword.prototype, "status", void 0);
_ts_decorate([
    (0, _typeorm.Column)({
        name: 'cpc_bid_micros',
        type: 'bigint',
        nullable: true,
        comment: 'CPC tối đa (micro amount)'
    }),
    _ts_metadata("design:type", Number)
], GoogleAdsKeyword.prototype, "cpcBidMicros", void 0);
_ts_decorate([
    (0, _typeorm.Column)({
        name: 'created_at',
        type: 'bigint',
        nullable: false,
        comment: 'Thời gian tạo'
    }),
    _ts_metadata("design:type", Number)
], GoogleAdsKeyword.prototype, "createdAt", void 0);
_ts_decorate([
    (0, _typeorm.Column)({
        name: 'updated_at',
        type: 'bigint',
        nullable: true,
        comment: 'Thời gian cập nhật'
    }),
    _ts_metadata("design:type", Number)
], GoogleAdsKeyword.prototype, "updatedAt", void 0);
GoogleAdsKeyword = _ts_decorate([
    (0, _typeorm.Entity)('google_ads_keywords')
], GoogleAdsKeyword);

//# sourceMappingURL=google-ads-keyword.entity.js.map