{"version": 3, "sources": ["../../../../../src/modules/marketing/dto/audience/update-audience.dto.ts"], "sourcesContent": ["import {\r\n  IsEmail,\r\n  IsOptional,\r\n  IsPhoneNumber,\r\n  ValidateNested,\r\n} from 'class-validator';\r\nimport { ApiProperty } from '@nestjs/swagger';\r\nimport { Type } from 'class-transformer';\r\nimport { CreateCustomFieldDto } from './create-custom-field.dto';\r\n\r\n/**\r\n * DTO cho việc cập nhật audience\r\n */\r\nexport class UpdateAudienceDto {\r\n  /**\r\n   * Email của khách hàng\r\n   * @example \"<EMAIL>\"\r\n   */\r\n  @ApiProperty({\r\n    description: 'Email của khách hàng',\r\n    example: '<EMAIL>',\r\n    required: false,\r\n  })\r\n  @IsOptional()\r\n  @IsEmail({}, { message: 'Email không hợp lệ' })\r\n  email?: string;\r\n\r\n  /**\r\n   * Số điện thoại của khách hàng\r\n   * @example \"+84912345678\"\r\n   */\r\n  @ApiProperty({\r\n    description: 'Số điện thoại của khách hàng',\r\n    example: '+84912345678',\r\n    required: false,\r\n  })\r\n  @IsOptional()\r\n  @IsPhoneNumber(undefined, { message: '<PERSON><PERSON> điện thoại không hợp lệ' })\r\n  phone?: string;\r\n\r\n  /**\r\n   * Danh sách các trường tùy chỉnh\r\n   */\r\n  @ApiProperty({\r\n    description: 'Danh sách các trường tùy chỉnh',\r\n    type: [CreateCustomFieldDto],\r\n    required: false,\r\n  })\r\n  @IsOptional()\r\n  @ValidateNested({ each: true })\r\n  @Type(() => CreateCustomFieldDto)\r\n  customFields?: CreateCustomFieldDto[];\r\n\r\n  /**\r\n   * Danh sách ID của các tag\r\n   * @example [1, 2, 3]\r\n   */\r\n  @ApiProperty({\r\n    description: 'Danh sách ID của các tag',\r\n    example: [1, 2, 3],\r\n    required: false,\r\n    type: [Number],\r\n  })\r\n  @IsOptional()\r\n  tagIds?: number[];\r\n}\r\n"], "names": ["UpdateAudienceDto", "description", "example", "required", "message", "type", "CreateCustomFieldDto", "each", "Number"], "mappings": ";;;;+BAaaA;;;eAAAA;;;gCARN;yBACqB;kCACP;sCACgB;;;;;;;;;;AAK9B,IAAA,AAAMA,oBAAN,MAAMA;AAoDb;;;QA9CIC,aAAa;QACbC,SAAS;QACTC,UAAU;;;;QAGGC,SAAS;;;;;;QAQtBH,aAAa;QACbC,SAAS;QACTC,UAAU;;;;QAGgBC,SAAS;;;;;;QAOnCH,aAAa;QACbI,MAAM;YAACC,0CAAoB;SAAC;QAC5BH,UAAU;;;;QAGMI,MAAM;;oCACZD,0CAAoB;;;;;QAQ9BL,aAAa;QACbC,SAAS;YAAC;YAAG;YAAG;SAAE;QAClBC,UAAU;QACVE,MAAM;YAACG;SAAO"}