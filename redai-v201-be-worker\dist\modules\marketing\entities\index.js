"use strict";
Object.defineProperty(exports, "__esModule", {
    value: true
});
_export_star(require("./user-tag.entity"), exports);
_export_star(require("./user-segment.entity"), exports);
_export_star(require("./user-audience.entity"), exports);
_export_star(require("./user-audience-custom-field.entity"), exports);
_export_star(require("./user-audience-custom-field-definition.entity"), exports);
_export_star(require("./user-campaign.entity"), exports);
_export_star(require("./user-campaign-history.entity"), exports);
_export_star(require("./user-template-email.entity"), exports);
_export_star(require("./zalo-official-account.entity"), exports);
_export_star(require("./zalo-zns-template.entity"), exports);
_export_star(require("./zalo-message.entity"), exports);
_export_star(require("./zalo-zns-message.entity"), exports);
_export_star(require("./zalo-follower.entity"), exports);
_export_star(require("./zalo-webhook-log.entity"), exports);
_export_star(require("./zalo-segment.entity"), exports);
_export_star(require("./zalo-campaign.entity"), exports);
_export_star(require("./zalo-campaign-log.entity"), exports);
_export_star(require("./zalo-automation.entity"), exports);
_export_star(require("./zalo-automation-log.entity"), exports);
_export_star(require("./google-ads-account.entity"), exports);
_export_star(require("./google-ads-campaign.entity"), exports);
_export_star(require("./google-ads-ad-group.entity"), exports);
_export_star(require("./google-ads-keyword.entity"), exports);
_export_star(require("./google-ads-performance.entity"), exports);
function _export_star(from, to) {
    Object.keys(from).forEach(function(k) {
        if (k !== "default" && !Object.prototype.hasOwnProperty.call(to, k)) {
            Object.defineProperty(to, k, {
                enumerable: true,
                get: function() {
                    return from[k];
                }
            });
        }
    });
    return from;
}

//# sourceMappingURL=index.js.map