{"version": 3, "sources": ["../../../../src/shared/services/sms/sms.module.ts"], "sourcesContent": ["import { Module, Global } from '@nestjs/common';\r\nimport { HttpModule } from '@nestjs/axios';\r\nimport { ConfigModule } from '@nestjs/config';\r\n\r\nimport { SmsService } from './sms.service';\r\nimport { SmsProviderFactory } from './sms-provider-factory.service';\r\nimport { SpeedSmsProvider } from './speed-sms-provider.service';\r\nimport { TwilioProvider } from './twilio-provider.service';\r\nimport { VonageProvider } from './vonage-provider.service';\r\nimport { FptSmsProvider } from './fpt-sms-provider.service';\r\n\r\n@Global()\r\n@Module({\r\n  imports: [HttpModule, ConfigModule],\r\n  providers: [\r\n    SmsService,\r\n    SmsProviderFactory,\r\n    SpeedSmsProvider,\r\n    TwilioProvider,\r\n    VonageProvider,\r\n    FptSmsProvider,\r\n  ],\r\n  exports: [\r\n    SmsService,\r\n    SmsProviderFactory,\r\n    SpeedSmsProvider,\r\n    TwilioProvider,\r\n    VonageProvider,\r\n    FptSmsProvider,\r\n  ],\r\n})\r\nexport class SmsModule {}\r\n"], "names": ["SmsModule", "imports", "HttpModule", "ConfigModule", "providers", "SmsService", "SmsProviderFactory", "SpeedSmsProvider", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "VonageProvider", "FptSmsProvider", "exports"], "mappings": ";;;;+BA+BaA;;;eAAAA;;;wBA/BkB;uBACJ;wBACE;4BAEF;2CACQ;yCACF;uCACF;uCACA;uCACA;;;;;;;AAsBxB,IAAA,AAAMA,YAAN,MAAMA;AAAW;;;;QAlBtBC,SAAS;YAACC,iBAAU;YAAEC,oBAAY;SAAC;QACnCC,WAAW;YACTC,sBAAU;YACVC,6CAAkB;YAClBC,yCAAgB;YAChBC,qCAAc;YACdC,qCAAc;YACdC,qCAAc;SACf;QACDC,SAAS;YACPN,sBAAU;YACVC,6CAAkB;YAClBC,yCAAgB;YAChBC,qCAAc;YACdC,qCAAc;YACdC,qCAAc;SACf"}