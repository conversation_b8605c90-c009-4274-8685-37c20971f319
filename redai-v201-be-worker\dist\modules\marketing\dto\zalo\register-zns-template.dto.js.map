{"version": 3, "sources": ["../../../../../src/modules/marketing/dto/zalo/register-zns-template.dto.ts"], "sourcesContent": ["import { ApiProperty } from '@nestjs/swagger';\r\nimport { IsArray, IsNotEmpty, IsString } from 'class-validator';\r\n\r\n/**\r\n * DTO cho việc đăng ký template ZNS\r\n */\r\nexport class RegisterZnsTemplateDto {\r\n  @ApiProperty({\r\n    description: 'Tên của template',\r\n    example: 'Thông báo đơn hàng',\r\n  })\r\n  @IsString()\r\n  @IsNotEmpty()\r\n  templateName: string;\r\n\r\n  @ApiProperty({\r\n    description: 'Nội dung của template',\r\n    example:\r\n      'Đơn hàng #{orderId} của bạn đã được xác nhận. Cảm ơn bạn đã mua hàng tại {shopName}.',\r\n  })\r\n  @IsString()\r\n  @IsNotEmpty()\r\n  templateContent: string;\r\n\r\n  @ApiProperty({\r\n    description: 'C<PERSON>c tham số của template',\r\n    example: ['orderId', 'shopName'],\r\n    type: [String],\r\n  })\r\n  @IsArray()\r\n  @IsString({ each: true })\r\n  params: string[];\r\n}\r\n"], "names": ["RegisterZnsTemplateDto", "description", "example", "type", "String", "each"], "mappings": ";;;;+BAMaA;;;eAAAA;;;yBANe;gCACkB;;;;;;;;;;AAKvC,IAAA,AAAMA,yBAAN,MAAMA;AA0Bb;;;QAxBIC,aAAa;QACbC,SAAS;;;;;;;;QAOTD,aAAa;QACbC,SACE;;;;;;;;QAOFD,aAAa;QACbC,SAAS;YAAC;YAAW;SAAW;QAChCC,MAAM;YAACC;SAAO;;;;QAGJC,MAAM"}