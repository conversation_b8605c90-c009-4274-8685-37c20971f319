/**
 * Interface định nghĩa các phương thức chung cho tất cả các nhà cung cấp SMS
 */ "use strict";
Object.defineProperty(exports, "__esModule", {
    value: true
});
Object.defineProperty(exports, "MessageStatus", {
    enumerable: true,
    get: function() {
        return MessageStatus;
    }
});
var MessageStatus = /*#__PURE__*/ function(MessageStatus) {
    /**
   * Tin nhắn đang chờ gửi
   *
   * Message en attente d'envoi
   */ MessageStatus["PENDING"] = "PENDING";
    /**
   * Tin nhắn đang trong quá trình gửi
   *
   * Message en cours d'envoi
   */ MessageStatus["SENDING"] = "SENDING";
    /**
   * Tin nhắn đã được gửi đến người nhận
   *
   * Message délivré au destinataire
   */ MessageStatus["DELIVERED"] = "DELIVERED";
    /**
   * Tin nhắn không được gửi đến người nhận
   *
   * Message non délivré au destinataire
   */ MessageStatus["FAILED"] = "FAILED";
    /**
   * Tin nhắn đã hết hạn (không được gửi trong khoảng thời gian quy định)
   *
   * Message expiré (non délivré dans le délai imparti)
   */ MessageStatus["EXPIRED"] = "EXPIRED";
    /**
   * Tin nhắn bị từ chối bởi nhà cung cấp
   *
   * Message rejeté par le fournisseur
   */ MessageStatus["REJECTED"] = "REJECTED";
    /**
   * Trạng thái không xác định
   *
   * Statut inconnu
   */ MessageStatus["UNKNOWN"] = "UNKNOWN";
    return MessageStatus;
}({});

//# sourceMappingURL=sms-provider.interface.js.map