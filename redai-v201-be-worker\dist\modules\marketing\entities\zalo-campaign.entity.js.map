{"version": 3, "sources": ["../../../../src/modules/marketing/entities/zalo-campaign.entity.ts"], "sourcesContent": ["import { Column, Entity, PrimaryGeneratedColumn } from 'typeorm';\r\nimport {\r\n  ZaloCampaignMessageContentDto,\r\n  ZaloCampaignStatus,\r\n  ZaloCampaignType,\r\n  ZaloCampaignZnsContentDto,\r\n} from '../dto/zalo';\r\n\r\n/**\r\n * Entity cho chiến dịch Zalo\r\n */\r\n@Entity('zalo_campaigns')\r\nexport class ZaloCampaign {\r\n  @PrimaryGeneratedColumn()\r\n  id: number;\r\n\r\n  @Column({ name: 'user_id' })\r\n  userId: number;\r\n\r\n  @Column({ name: 'oa_id' })\r\n  oaId: string;\r\n\r\n  @Column()\r\n  name: string;\r\n\r\n  @Column({ nullable: true })\r\n  description?: string;\r\n\r\n  @Column({ type: 'enum', enum: ZaloCampaignType })\r\n  type: ZaloCampaignType;\r\n\r\n  @Column({ name: 'segment_id' })\r\n  segmentId: number;\r\n\r\n  @Column({\r\n    type: 'enum',\r\n    enum: ZaloCampaignStatus,\r\n    default: ZaloCampaignStatus.DRAFT,\r\n  })\r\n  status: ZaloCampaignStatus;\r\n\r\n  @Column({ name: 'scheduled_at', type: 'bigint', nullable: true })\r\n  scheduledAt?: number;\r\n\r\n  @Column({ name: 'started_at', type: 'bigint', nullable: true })\r\n  startedAt?: number;\r\n\r\n  @Column({ name: 'completed_at', type: 'bigint', nullable: true })\r\n  completedAt?: number;\r\n\r\n  @Column({ name: 'message_content', type: 'json', nullable: true })\r\n  messageContent?: ZaloCampaignMessageContentDto;\r\n\r\n  @Column({ name: 'zns_content', type: 'json', nullable: true })\r\n  znsContent?: ZaloCampaignZnsContentDto;\r\n\r\n  @Column({ name: 'total_recipients', default: 0 })\r\n  totalRecipients: number;\r\n\r\n  @Column({ name: 'success_count', default: 0 })\r\n  successCount: number;\r\n\r\n  @Column({ name: 'failure_count', default: 0 })\r\n  failureCount: number;\r\n\r\n  @Column({ name: 'created_at', type: 'bigint' })\r\n  createdAt: number;\r\n\r\n  @Column({ name: 'updated_at', type: 'bigint' })\r\n  updatedAt: number;\r\n}\r\n"], "names": ["ZaloCampaign", "name", "nullable", "type", "enum", "ZaloCampaignType", "ZaloCampaignStatus", "default", "DRAFT"], "mappings": ";;;;+BAYaA;;;eAAAA;;;yBAZ0C;sBAMhD;;;;;;;;;;AAMA,IAAA,AAAMA,eAAN,MAAMA;AA0Db;;;;;;;QAtDYC,MAAM;;;;;;QAGNA,MAAM;;;;;;;;;;QAMNC,UAAU;;;;;;QAGVC,MAAM;QAAQC,MAAMC,sBAAgB;;;;;;QAGpCJ,MAAM;;;;;;QAIdE,MAAM;QACNC,MAAME,wBAAkB;QACxBC,SAASD,wBAAkB,CAACE,KAAK;;;;;;QAIzBP,MAAM;QAAgBE,MAAM;QAAUD,UAAU;;;;;;QAGhDD,MAAM;QAAcE,MAAM;QAAUD,UAAU;;;;;;QAG9CD,MAAM;QAAgBE,MAAM;QAAUD,UAAU;;;;;;QAGhDD,MAAM;QAAmBE,MAAM;QAAQD,UAAU;;;;;;QAGjDD,MAAM;QAAeE,MAAM;QAAQD,UAAU;;;;;;QAG7CD,MAAM;QAAoBM,SAAS;;;;;;QAGnCN,MAAM;QAAiBM,SAAS;;;;;;QAGhCN,MAAM;QAAiBM,SAAS;;;;;;QAGhCN,MAAM;QAAcE,MAAM;;;;;;QAG1BF,MAAM;QAAcE,MAAM"}