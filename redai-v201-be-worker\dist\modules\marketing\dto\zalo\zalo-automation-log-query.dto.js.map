{"version": 3, "sources": ["../../../../../src/modules/marketing/dto/zalo/zalo-automation-log-query.dto.ts"], "sourcesContent": ["import { ApiProperty } from '@nestjs/swagger';\r\nimport { IsEnum, IsOptional, IsString } from 'class-validator';\r\nimport { QueryDto, SortDirection } from '@common/dto';\r\n\r\n/**\r\n * Enum cho trạng thái log tự động hóa Zalo\r\n */\r\nexport enum ZaloAutomationLogStatus {\r\n  PENDING = 'pending',\r\n  SUCCESS = 'success',\r\n  FAILED = 'failed',\r\n  ALL = 'all',\r\n}\r\n\r\n/**\r\n * DTO cho việc truy vấn danh sách log tự động hóa Zalo\r\n */\r\nexport class ZaloAutomationLogQueryDto extends QueryDto {\r\n  @ApiProperty({\r\n    description: 'Lọc theo ID của tự động hóa',\r\n    example: 1,\r\n    required: false,\r\n  })\r\n  @IsOptional()\r\n  @IsString()\r\n  automationId?: string;\r\n\r\n  @ApiProperty({\r\n    description: 'Lọc theo ID của người theo dõi',\r\n    example: '123456789',\r\n    required: false,\r\n  })\r\n  @IsOptional()\r\n  @IsString()\r\n  followerId?: string;\r\n\r\n  @ApiProperty({\r\n    description: 'Lọc theo loại sự kiện kích hoạt',\r\n    example: 'follow',\r\n    required: false,\r\n  })\r\n  @IsOptional()\r\n  @IsString()\r\n  triggerType?: string;\r\n\r\n  @ApiProperty({\r\n    description: 'Lọc theo loại hành động',\r\n    example: 'send_message',\r\n    required: false,\r\n  })\r\n  @IsOptional()\r\n  @IsString()\r\n  actionType?: string;\r\n\r\n  @ApiProperty({\r\n    description: 'Lọc theo trạng thái',\r\n    enum: ZaloAutomationLogStatus,\r\n    example: ZaloAutomationLogStatus.SUCCESS,\r\n    required: false,\r\n  })\r\n  @IsOptional()\r\n  @IsEnum(ZaloAutomationLogStatus)\r\n  status?: ZaloAutomationLogStatus;\r\n\r\n  constructor() {\r\n    super();\r\n    this.sortBy = 'createdAt';\r\n    this.sortDirection = SortDirection.DESC;\r\n  }\r\n}\r\n"], "names": ["ZaloAutomationLogQueryDto", "ZaloAutomationLogStatus", "QueryDto", "constructor", "sortBy", "sortDirection", "SortDirection", "DESC", "description", "example", "required", "enum"], "mappings": ";;;;;;;;;;;QAiBaA;eAAAA;;QAVDC;eAAAA;;;yBAPgB;gCACiB;qBACL;;;;;;;;;;AAKjC,IAAA,AAAKA,iDAAAA;;;;;WAAAA;;AAUL,IAAA,AAAMD,4BAAN,MAAMA,kCAAkCE,aAAQ;IA+CrDC,aAAc;QACZ,KAAK;QACL,IAAI,CAACC,MAAM,GAAG;QACd,IAAI,CAACC,aAAa,GAAGC,kBAAa,CAACC,IAAI;IACzC;AACF;;;QAlDIC,aAAa;QACbC,SAAS;QACTC,UAAU;;;;;;;;QAOVF,aAAa;QACbC,SAAS;QACTC,UAAU;;;;;;;;QAOVF,aAAa;QACbC,SAAS;QACTC,UAAU;;;;;;;;QAOVF,aAAa;QACbC,SAAS;QACTC,UAAU;;;;;;;;QAOVF,aAAa;QACbG,MAAMV;QACNQ,OAAO;QACPC,UAAU"}