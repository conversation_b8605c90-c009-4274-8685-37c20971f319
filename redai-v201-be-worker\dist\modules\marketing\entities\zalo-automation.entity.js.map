{"version": 3, "sources": ["../../../../src/modules/marketing/entities/zalo-automation.entity.ts"], "sourcesContent": ["import { Column, <PERSON>tity, PrimaryGeneratedColumn } from 'typeorm';\r\nimport {\r\n  ZaloAutomationActionDto,\r\n  ZaloAutomationStatus,\r\n  ZaloAutomationTriggerDto,\r\n} from '../dto/zalo';\r\n\r\n/**\r\n * Entity cho tự động hóa Zalo\r\n */\r\n@Entity('zalo_automations')\r\nexport class ZaloAutomation {\r\n  @PrimaryGeneratedColumn()\r\n  id: number;\r\n\r\n  @Column({ name: 'user_id' })\r\n  userId: number;\r\n\r\n  @Column({ name: 'oa_id' })\r\n  oaId: string;\r\n\r\n  @Column()\r\n  name: string;\r\n\r\n  @Column({ nullable: true })\r\n  description?: string;\r\n\r\n  @Column({ type: 'json' })\r\n  trigger: ZaloAutomationTriggerDto;\r\n\r\n  @Column({ type: 'json' })\r\n  actions: ZaloAutomationActionDto[];\r\n\r\n  @Column({\r\n    type: 'enum',\r\n    enum: ZaloAutomationStatus,\r\n    default: ZaloAutomationStatus.ACTIVE,\r\n  })\r\n  status: ZaloAutomationStatus;\r\n\r\n  @Column({ name: 'trigger_count', default: 0 })\r\n  triggerCount: number;\r\n\r\n  @Column({ name: 'created_at', type: 'bigint' })\r\n  createdAt: number;\r\n\r\n  @Column({ name: 'updated_at', type: 'bigint' })\r\n  updatedAt: number;\r\n}\r\n"], "names": ["ZaloAutomation", "name", "nullable", "type", "enum", "ZaloAutomationStatus", "default", "ACTIVE"], "mappings": ";;;;+BAWaA;;;eAAAA;;;yBAX0C;sBAKhD;;;;;;;;;;AAMA,IAAA,AAAMA,iBAAN,MAAMA;AAqCb;;;;;;;QAjCYC,MAAM;;;;;;QAGNA,MAAM;;;;;;;;;;QAMNC,UAAU;;;;;;QAGVC,MAAM;;;;;;QAGNA,MAAM;;;;;;QAIdA,MAAM;QACNC,MAAMC,0BAAoB;QAC1BC,SAASD,0BAAoB,CAACE,MAAM;;;;;;QAI5BN,MAAM;QAAiBK,SAAS;;;;;;QAGhCL,MAAM;QAAcE,MAAM;;;;;;QAG1BF,MAAM;QAAcE,MAAM"}