{"version": 3, "sources": ["../../../../src/modules/marketing/entities/user-audience-custom-field-definition.entity.ts"], "sourcesContent": ["import { Column, Entity, PrimaryGeneratedColumn } from 'typeorm';\r\n\r\n/**\r\n * Entity đại diện cho bảng audience_user_custom_fields trong cơ sở dữ liệu\r\n * Lưu thông tin các trường tùy chỉnh mà người dùng có thể định nghĩa động\r\n */\r\n@Entity('audience_user_custom_fields')\r\nexport class UserAudienceCustomFieldDefinition {\r\n  /**\r\n   * ID tự động tăng\r\n   */\r\n  @PrimaryGeneratedColumn({ name: 'id', type: 'bigint' })\r\n  id: number;\r\n\r\n  /**\r\n   * Định danh duy nhất cho trường tùy chỉnh (chữ thường, không dấu cách)\r\n   */\r\n  @Column({ name: 'field_key', length: 100 })\r\n  fieldKey: string;\r\n\r\n  /**\r\n   * ID của người dùng mà trường tùy chỉnh này thuộc về\r\n   */\r\n  @Column({ name: 'user_id', type: 'bigint' })\r\n  userId: number;\r\n\r\n  /**\r\n   * Tên hiển thị thân thiện với người dùng\r\n   */\r\n  @Column({ name: 'display_name', length: 255 })\r\n  displayName: string;\r\n\r\n  /**\r\n   * Ki<PERSON>u dữ liệu: string, integer, date, boolean, v.v.\r\n   */\r\n  @Column({ name: 'data_type', length: 50 })\r\n  dataType: string;\r\n\r\n  /**\r\n   * Mô tả chi tiết hoặc ghi chú về trường tùy chỉnh\r\n   */\r\n  @Column({ name: 'description', type: 'text', nullable: true })\r\n  description: string;\r\n}\r\n"], "names": ["UserAudienceCustomFieldDefinition", "name", "type", "length", "nullable"], "mappings": ";;;;+BAOaA;;;eAAAA;;;yBAP0C;;;;;;;;;;AAOhD,IAAA,AAAMA,oCAAN,MAAMA;AAoCb;;;QAhC4BC,MAAM;QAAMC,MAAM;;;;;;QAMlCD,MAAM;QAAaE,QAAQ;;;;;;QAM3BF,MAAM;QAAWC,MAAM;;;;;;QAMvBD,MAAM;QAAgBE,QAAQ;;;;;;QAM9BF,MAAM;QAAaE,QAAQ;;;;;;QAM3BF,MAAM;QAAeC,MAAM;QAAQE,UAAU"}