"use strict";
Object.defineProperty(exports, "__esModule", {
    value: true
});
function _export(target, all) {
    for(var name in all)Object.defineProperty(target, name, {
        enumerable: true,
        get: Object.getOwnPropertyDescriptor(all, name).get
    });
}
_export(exports, {
    get MessageRequestDto () {
        return MessageRequestDto;
    },
    get MessageType () {
        return MessageType;
    }
});
const _swagger = require("@nestjs/swagger");
const _classvalidator = require("class-validator");
function _ts_decorate(decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for(var i = decorators.length - 1; i >= 0; i--)if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
}
function _ts_metadata(k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
}
var MessageType = /*#__PURE__*/ function(MessageType) {
    MessageType["TEXT"] = "text";
    MessageType["IMAGE"] = "image";
    MessageType["FILE"] = "file";
    MessageType["TEMPLATE"] = "template";
    return MessageType;
}({});
let MessageRequestDto = class MessageRequestDto {
};
_ts_decorate([
    (0, _swagger.ApiProperty)({
        description: 'ID của người dùng Zalo',
        example: '123456789'
    }),
    (0, _classvalidator.IsString)(),
    (0, _classvalidator.IsNotEmpty)(),
    _ts_metadata("design:type", String)
], MessageRequestDto.prototype, "userId", void 0);
_ts_decorate([
    (0, _swagger.ApiProperty)({
        description: 'Loại tin nhắn',
        enum: MessageType,
        example: "text"
    }),
    (0, _classvalidator.IsEnum)(MessageType),
    (0, _classvalidator.IsNotEmpty)(),
    _ts_metadata("design:type", String)
], MessageRequestDto.prototype, "messageType", void 0);
_ts_decorate([
    (0, _swagger.ApiProperty)({
        description: 'Nội dung tin nhắn văn bản',
        example: 'Xin chào! Cảm ơn bạn đã quan tâm đến sản phẩm của chúng tôi.',
        required: false
    }),
    (0, _classvalidator.ValidateIf)((o)=>o.messageType === "text"),
    (0, _classvalidator.IsString)(),
    (0, _classvalidator.IsNotEmpty)(),
    _ts_metadata("design:type", String)
], MessageRequestDto.prototype, "message", void 0);
_ts_decorate([
    (0, _swagger.ApiProperty)({
        description: 'URL của hình ảnh',
        example: 'https://example.com/image.jpg',
        required: false
    }),
    (0, _classvalidator.ValidateIf)((o)=>o.messageType === "image"),
    (0, _classvalidator.IsString)(),
    (0, _classvalidator.IsNotEmpty)(),
    _ts_metadata("design:type", String)
], MessageRequestDto.prototype, "imageUrl", void 0);
_ts_decorate([
    (0, _swagger.ApiProperty)({
        description: 'URL của file',
        example: 'https://example.com/document.pdf',
        required: false
    }),
    (0, _classvalidator.ValidateIf)((o)=>o.messageType === "file"),
    (0, _classvalidator.IsString)(),
    (0, _classvalidator.IsNotEmpty)(),
    _ts_metadata("design:type", String)
], MessageRequestDto.prototype, "fileUrl", void 0);
_ts_decorate([
    (0, _swagger.ApiProperty)({
        description: 'ID của template',
        example: 'template123',
        required: false
    }),
    (0, _classvalidator.ValidateIf)((o)=>o.messageType === "template"),
    (0, _classvalidator.IsString)(),
    (0, _classvalidator.IsNotEmpty)(),
    _ts_metadata("design:type", String)
], MessageRequestDto.prototype, "templateId", void 0);
_ts_decorate([
    (0, _swagger.ApiProperty)({
        description: 'Dữ liệu cho template',
        example: {
            name: 'Nguyễn Văn A',
            product: 'RedAI Pro'
        },
        required: false
    }),
    (0, _classvalidator.ValidateIf)((o)=>o.messageType === "template"),
    (0, _classvalidator.IsOptional)(),
    _ts_metadata("design:type", typeof Record === "undefined" ? Object : Record)
], MessageRequestDto.prototype, "templateData", void 0);

//# sourceMappingURL=message-request.dto.js.map