{"version": 3, "sources": ["../../../../../src/modules/marketing/dto/zalo/zns-template-query.dto.ts"], "sourcesContent": ["import { ApiProperty } from '@nestjs/swagger';\r\nimport { IsEnum, IsOptional, IsString } from 'class-validator';\r\nimport { QueryDto, SortDirection } from '@common/dto';\r\n\r\n/**\r\n * Enum cho trạng thái template ZNS\r\n */\r\nexport enum ZnsTemplateStatus {\r\n  APPROVED = 'approved',\r\n  PENDING = 'pending',\r\n  REJECTED = 'rejected',\r\n  ALL = 'all',\r\n}\r\n\r\n/**\r\n * DTO cho việc truy vấn danh sách template ZNS\r\n */\r\nexport class ZnsTemplateQueryDto extends QueryDto {\r\n  @ApiProperty({\r\n    description: 'Tìm kiếm theo tên template',\r\n    example: 'đơn hàng',\r\n    required: false,\r\n  })\r\n  @IsOptional()\r\n  @IsString()\r\n  templateName?: string;\r\n\r\n  @ApiProperty({\r\n    description: 'Lọc theo trạng thái',\r\n    enum: ZnsTemplateStatus,\r\n    example: ZnsTemplateStatus.APPROVED,\r\n    required: false,\r\n  })\r\n  @IsOptional()\r\n  @IsEnum(ZnsTemplateStatus)\r\n  status?: ZnsTemplateStatus;\r\n\r\n  constructor() {\r\n    super();\r\n    this.sortBy = 'createdAt';\r\n    this.sortDirection = SortDirection.DESC;\r\n  }\r\n}\r\n"], "names": ["ZnsTemplateQueryDto", "ZnsTemplateStatus", "QueryDto", "constructor", "sortBy", "sortDirection", "SortDirection", "DESC", "description", "example", "required", "enum"], "mappings": ";;;;;;;;;;;QAiBaA;eAAAA;;QAVDC;eAAAA;;;yBAPgB;gCACiB;qBACL;;;;;;;;;;AAKjC,IAAA,AAAKA,2CAAAA;;;;;WAAAA;;AAUL,IAAA,AAAMD,sBAAN,MAAMA,4BAA4BE,aAAQ;IAoB/CC,aAAc;QACZ,KAAK;QACL,IAAI,CAACC,MAAM,GAAG;QACd,IAAI,CAACC,aAAa,GAAGC,kBAAa,CAACC,IAAI;IACzC;AACF;;;QAvBIC,aAAa;QACbC,SAAS;QACTC,UAAU;;;;;;;;QAOVF,aAAa;QACbG,MAAMV;QACNQ,OAAO;QACPC,UAAU"}