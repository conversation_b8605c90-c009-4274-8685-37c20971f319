"use strict";
Object.defineProperty(exports, "__esModule", {
    value: true
});
function _export(target, all) {
    for(var name in all)Object.defineProperty(target, name, {
        enumerable: true,
        get: Object.getOwnPropertyDescriptor(all, name).get
    });
}
_export(exports, {
    get CampaignServerDto () {
        return CampaignServerDto;
    },
    get ServerType () {
        return ServerType;
    }
});
const _classvalidator = require("class-validator");
const _swagger = require("@nestjs/swagger");
function _ts_decorate(decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for(var i = decorators.length - 1; i >= 0; i--)if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
}
function _ts_metadata(k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
}
var ServerType = /*#__PURE__*/ function(ServerType) {
    ServerType["SMTP"] = "smtp";
    ServerType["API"] = "api";
    ServerType["SMS_GATEWAY"] = "sms_gateway";
    ServerType["FIREBASE"] = "firebase";
    return ServerType;
}({});
let CampaignServerDto = class CampaignServerDto {
};
_ts_decorate([
    (0, _swagger.ApiProperty)({
        description: 'Loại máy chủ',
        enum: ServerType,
        example: "smtp"
    }),
    (0, _classvalidator.IsNotEmpty)({
        message: 'Loại máy chủ không được để trống'
    }),
    (0, _classvalidator.IsIn)(Object.values(ServerType), {
        message: `Loại máy chủ phải là một trong các giá trị: ${Object.values(ServerType).join(', ')}`
    }),
    _ts_metadata("design:type", String)
], CampaignServerDto.prototype, "type", void 0);
_ts_decorate([
    (0, _swagger.ApiProperty)({
        description: 'Tên máy chủ',
        example: 'Gmail SMTP'
    }),
    (0, _classvalidator.IsNotEmpty)({
        message: 'Tên máy chủ không được để trống'
    }),
    (0, _classvalidator.IsString)({
        message: 'Tên máy chủ phải là chuỗi'
    }),
    _ts_metadata("design:type", String)
], CampaignServerDto.prototype, "name", void 0);
_ts_decorate([
    (0, _swagger.ApiProperty)({
        description: 'Cấu hình máy chủ',
        example: {
            host: 'smtp.gmail.com',
            port: 587,
            secure: false,
            auth: {
                user: '<EMAIL>',
                pass: 'password'
            }
        }
    }),
    (0, _classvalidator.IsNotEmpty)({
        message: 'Cấu hình máy chủ không được để trống'
    }),
    _ts_metadata("design:type", Object)
], CampaignServerDto.prototype, "config", void 0);
_ts_decorate([
    (0, _swagger.ApiProperty)({
        description: 'Địa chỉ email người gửi (chỉ áp dụng cho máy chủ email)',
        example: '<EMAIL>',
        required: false
    }),
    (0, _classvalidator.IsOptional)(),
    (0, _classvalidator.IsString)({
        message: 'Địa chỉ email người gửi phải là chuỗi'
    }),
    _ts_metadata("design:type", String)
], CampaignServerDto.prototype, "fromEmail", void 0);
_ts_decorate([
    (0, _swagger.ApiProperty)({
        description: 'Tên người gửi (chỉ áp dụng cho máy chủ email)',
        example: 'Marketing Team',
        required: false
    }),
    (0, _classvalidator.IsOptional)(),
    (0, _classvalidator.IsString)({
        message: 'Tên người gửi phải là chuỗi'
    }),
    _ts_metadata("design:type", String)
], CampaignServerDto.prototype, "fromName", void 0);

//# sourceMappingURL=campaign-server.dto.js.map