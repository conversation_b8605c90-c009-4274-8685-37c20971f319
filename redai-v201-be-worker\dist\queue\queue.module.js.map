{"version": 3, "sources": ["../../src/queue/queue.module.ts"], "sourcesContent": ["import { Global, Module } from '@nestjs/common';\r\nimport { BullModule } from '@nestjs/bullmq';\r\nimport { QueueName } from './index';\r\nimport { BullBoardModule } from '@bull-board/nestjs';\r\nimport { ExpressAdapter } from '@bull-board/express';\r\nimport basicAuth from 'express-basic-auth';\r\nimport { BullMQAdapter } from '@bull-board/api/bullMQAdapter';\r\nimport { ExampleController } from './example/example.controller';\r\n\r\n@Global()\r\n@Module({\r\n  imports: [\r\n    BullModule.registerQueue({\r\n      name: QueueName.AGENT,\r\n    }),\r\n    BullModule.registerQueue({\r\n      name: QueueName.EMAIL_SYSTEM,\r\n    }),\r\n    BullModule.registerQueue({\r\n      name: QueueName.EMAIL_MARKETING,\r\n    }),\r\n    BullBoardModule.forRoot({\r\n      route: '/queues',\r\n      adapter: ExpressAdapter,\r\n      middleware: basicAuth({\r\n        challenge: true,\r\n        users: { admin: 'redai@123' },\r\n      }),\r\n    }),\r\n    BullBoardModule.forFeature({\r\n      name: QueueName.AGENT,\r\n      adapter: BullMQAdapter,\r\n    }),\r\n    BullBoardModule.forFeature({\r\n      name: QueueName.EMAIL_SYSTEM,\r\n      adapter: BullMQAdapter,\r\n    }),\r\n    BullBoardModule.forFeature({\r\n      name: QueueName.EMAIL_MARKETING,\r\n      adapter: BullMQAdapter,\r\n    }),\r\n  ],\r\n  controllers: [ExampleController],\r\n})\r\nexport class QueueModule {}\r\n"], "names": ["QueueModule", "imports", "BullModule", "registerQueue", "name", "QueueName", "AGENT", "EMAIL_SYSTEM", "EMAIL_MARKETING", "BullBoardModule", "forRoot", "route", "adapter", "ExpressAdapter", "middleware", "basicAuth", "challenge", "users", "admin", "forFeature", "BullMQAdapter", "controllers", "ExampleController"], "mappings": ";;;;+BA4CaA;;;eAAAA;;;wBA5CkB;wBACJ;uBACD;wBACM;yBACD;yEACT;+BACQ;mCACI;;;;;;;;;;;;AAqC3B,IAAA,AAAMA,cAAN,MAAMA;AAAa;;;;QAjCxBC,SAAS;YACPC,kBAAU,CAACC,aAAa,CAAC;gBACvBC,MAAMC,gBAAS,CAACC,KAAK;YACvB;YACAJ,kBAAU,CAACC,aAAa,CAAC;gBACvBC,MAAMC,gBAAS,CAACE,YAAY;YAC9B;YACAL,kBAAU,CAACC,aAAa,CAAC;gBACvBC,MAAMC,gBAAS,CAACG,eAAe;YACjC;YACAC,uBAAe,CAACC,OAAO,CAAC;gBACtBC,OAAO;gBACPC,SAASC,uBAAc;gBACvBC,YAAYC,IAAAA,yBAAS,EAAC;oBACpBC,WAAW;oBACXC,OAAO;wBAAEC,OAAO;oBAAY;gBAC9B;YACF;YACAT,uBAAe,CAACU,UAAU,CAAC;gBACzBf,MAAMC,gBAAS,CAACC,KAAK;gBACrBM,SAASQ,4BAAa;YACxB;YACAX,uBAAe,CAACU,UAAU,CAAC;gBACzBf,MAAMC,gBAAS,CAACE,YAAY;gBAC5BK,SAASQ,4BAAa;YACxB;YACAX,uBAAe,CAACU,UAAU,CAAC;gBACzBf,MAAMC,gBAAS,CAACG,eAAe;gBAC/BI,SAASQ,4BAAa;YACxB;SACD;QACDC,aAAa;YAACC,oCAAiB;SAAC"}