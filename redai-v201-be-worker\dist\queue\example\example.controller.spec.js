"use strict";
Object.defineProperty(exports, "__esModule", {
    value: true
});
const _testing = require("@nestjs/testing");
const _examplecontroller = require("./example.controller");
describe('ExampleController', ()=>{
    let controller;
    beforeEach(async ()=>{
        const module = await _testing.Test.createTestingModule({
            controllers: [
                _examplecontroller.ExampleController
            ]
        }).compile();
        controller = module.get(_examplecontroller.ExampleController);
    });
    it('should be defined', ()=>{
        expect(controller).toBeDefined();
    });
});

//# sourceMappingURL=example.controller.spec.js.map