"use strict";
Object.defineProperty(exports, "__esModule", {
    value: true
});
Object.defineProperty(exports, "ZaloSegment", {
    enumerable: true,
    get: function() {
        return ZaloSegment;
    }
});
const _typeorm = require("typeorm");
function _ts_decorate(decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for(var i = decorators.length - 1; i >= 0; i--)if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
}
function _ts_metadata(k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
}
let ZaloSegment = class ZaloSegment {
};
_ts_decorate([
    (0, _typeorm.PrimaryGeneratedColumn)(),
    _ts_metadata("design:type", Number)
], ZaloSegment.prototype, "id", void 0);
_ts_decorate([
    (0, _typeorm.Column)({
        name: 'user_id'
    }),
    _ts_metadata("design:type", Number)
], ZaloSegment.prototype, "userId", void 0);
_ts_decorate([
    (0, _typeorm.Column)({
        name: 'oa_id'
    }),
    _ts_metadata("design:type", String)
], ZaloSegment.prototype, "oaId", void 0);
_ts_decorate([
    (0, _typeorm.Column)(),
    _ts_metadata("design:type", String)
], ZaloSegment.prototype, "name", void 0);
_ts_decorate([
    (0, _typeorm.Column)({
        nullable: true
    }),
    _ts_metadata("design:type", String)
], ZaloSegment.prototype, "description", void 0);
_ts_decorate([
    (0, _typeorm.Column)({
        type: 'json'
    }),
    _ts_metadata("design:type", Array)
], ZaloSegment.prototype, "conditions", void 0);
_ts_decorate([
    (0, _typeorm.Column)({
        name: 'follower_count',
        default: 0
    }),
    _ts_metadata("design:type", Number)
], ZaloSegment.prototype, "followerCount", void 0);
_ts_decorate([
    (0, _typeorm.Column)({
        name: 'created_at',
        type: 'bigint'
    }),
    _ts_metadata("design:type", Number)
], ZaloSegment.prototype, "createdAt", void 0);
_ts_decorate([
    (0, _typeorm.Column)({
        name: 'updated_at',
        type: 'bigint'
    }),
    _ts_metadata("design:type", Number)
], ZaloSegment.prototype, "updatedAt", void 0);
ZaloSegment = _ts_decorate([
    (0, _typeorm.Entity)('zalo_segments')
], ZaloSegment);

//# sourceMappingURL=zalo-segment.entity.js.map