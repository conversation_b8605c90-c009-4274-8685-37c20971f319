"use strict";
Object.defineProperty(exports, "__esModule", {
    value: true
});
function _export(target, all) {
    for(var name in all)Object.defineProperty(target, name, {
        enumerable: true,
        get: Object.getOwnPropertyDescriptor(all, name).get
    });
}
_export(exports, {
    get CreateZaloCampaignDto () {
        return CreateZaloCampaignDto;
    },
    get ExecuteZaloCampaignDto () {
        return ExecuteZaloCampaignDto;
    },
    get UpdateZaloCampaignDto () {
        return UpdateZaloCampaignDto;
    },
    get ZaloCampaignMessageContentDto () {
        return ZaloCampaignMessageContentDto;
    },
    get ZaloCampaignQueryDto () {
        return ZaloCampaignQueryDto;
    },
    get ZaloCampaignResponseDto () {
        return ZaloCampaignResponseDto;
    },
    get ZaloCampaignStatus () {
        return ZaloCampaignStatus;
    },
    get ZaloCampaignType () {
        return ZaloCampaignType;
    },
    get ZaloCampaignZnsContentDto () {
        return ZaloCampaignZnsContentDto;
    }
});
const _swagger = require("@nestjs/swagger");
const _classvalidator = require("class-validator");
function _ts_decorate(decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for(var i = decorators.length - 1; i >= 0; i--)if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
}
function _ts_metadata(k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
}
var ZaloCampaignType = /*#__PURE__*/ function(ZaloCampaignType) {
    ZaloCampaignType["MESSAGE"] = "message";
    ZaloCampaignType["ZNS"] = "zns";
    return ZaloCampaignType;
}({});
var ZaloCampaignStatus = /*#__PURE__*/ function(ZaloCampaignStatus) {
    ZaloCampaignStatus["DRAFT"] = "draft";
    ZaloCampaignStatus["SCHEDULED"] = "scheduled";
    ZaloCampaignStatus["RUNNING"] = "running";
    ZaloCampaignStatus["COMPLETED"] = "completed";
    ZaloCampaignStatus["CANCELLED"] = "cancelled";
    return ZaloCampaignStatus;
}({});
let ZaloCampaignMessageContentDto = class ZaloCampaignMessageContentDto {
};
_ts_decorate([
    (0, _swagger.ApiProperty)({
        description: 'Loại tin nhắn (text, image, file, template)',
        example: 'text'
    }),
    (0, _classvalidator.IsString)(),
    (0, _classvalidator.IsNotEmpty)(),
    _ts_metadata("design:type", String)
], ZaloCampaignMessageContentDto.prototype, "type", void 0);
_ts_decorate([
    (0, _swagger.ApiProperty)({
        description: 'Nội dung tin nhắn văn bản',
        example: 'Xin chào! Cảm ơn bạn đã quan tâm đến sản phẩm của chúng tôi.',
        required: false
    }),
    (0, _classvalidator.IsString)(),
    (0, _classvalidator.IsOptional)(),
    _ts_metadata("design:type", String)
], ZaloCampaignMessageContentDto.prototype, "text", void 0);
_ts_decorate([
    (0, _swagger.ApiProperty)({
        description: 'URL của hình ảnh',
        example: 'https://example.com/image.jpg',
        required: false
    }),
    (0, _classvalidator.IsString)(),
    (0, _classvalidator.IsOptional)(),
    _ts_metadata("design:type", String)
], ZaloCampaignMessageContentDto.prototype, "imageUrl", void 0);
_ts_decorate([
    (0, _swagger.ApiProperty)({
        description: 'URL của file',
        example: 'https://example.com/document.pdf',
        required: false
    }),
    (0, _classvalidator.IsString)(),
    (0, _classvalidator.IsOptional)(),
    _ts_metadata("design:type", String)
], ZaloCampaignMessageContentDto.prototype, "fileUrl", void 0);
_ts_decorate([
    (0, _swagger.ApiProperty)({
        description: 'ID của template',
        example: 'template123',
        required: false
    }),
    (0, _classvalidator.IsString)(),
    (0, _classvalidator.IsOptional)(),
    _ts_metadata("design:type", String)
], ZaloCampaignMessageContentDto.prototype, "templateId", void 0);
_ts_decorate([
    (0, _swagger.ApiProperty)({
        description: 'Dữ liệu cho template',
        example: {
            name: '{name}',
            product: '{product}'
        },
        required: false
    }),
    (0, _classvalidator.IsObject)(),
    (0, _classvalidator.IsOptional)(),
    _ts_metadata("design:type", typeof Record === "undefined" ? Object : Record)
], ZaloCampaignMessageContentDto.prototype, "templateData", void 0);
let ZaloCampaignZnsContentDto = class ZaloCampaignZnsContentDto {
};
_ts_decorate([
    (0, _swagger.ApiProperty)({
        description: 'ID của template ZNS',
        example: 'template*********'
    }),
    (0, _classvalidator.IsString)(),
    (0, _classvalidator.IsNotEmpty)(),
    _ts_metadata("design:type", String)
], ZaloCampaignZnsContentDto.prototype, "templateId", void 0);
_ts_decorate([
    (0, _swagger.ApiProperty)({
        description: 'Dữ liệu cho template',
        example: {
            orderId: '{orderId}',
            shopName: '{shopName}'
        }
    }),
    (0, _classvalidator.IsObject)(),
    (0, _classvalidator.IsNotEmpty)(),
    _ts_metadata("design:type", typeof Record === "undefined" ? Object : Record)
], ZaloCampaignZnsContentDto.prototype, "templateData", void 0);
let CreateZaloCampaignDto = class CreateZaloCampaignDto {
};
_ts_decorate([
    (0, _swagger.ApiProperty)({
        description: 'ID của Official Account',
        example: '*********'
    }),
    (0, _classvalidator.IsString)(),
    (0, _classvalidator.IsNotEmpty)(),
    _ts_metadata("design:type", String)
], CreateZaloCampaignDto.prototype, "oaId", void 0);
_ts_decorate([
    (0, _swagger.ApiProperty)({
        description: 'Tên của chiến dịch',
        example: 'Chiến dịch khuyến mãi tháng 7'
    }),
    (0, _classvalidator.IsString)(),
    (0, _classvalidator.IsNotEmpty)(),
    _ts_metadata("design:type", String)
], CreateZaloCampaignDto.prototype, "name", void 0);
_ts_decorate([
    (0, _swagger.ApiProperty)({
        description: 'Mô tả của chiến dịch',
        example: 'Chiến dịch khuyến mãi dành cho khách hàng VIP',
        required: false
    }),
    (0, _classvalidator.IsString)(),
    (0, _classvalidator.IsOptional)(),
    _ts_metadata("design:type", String)
], CreateZaloCampaignDto.prototype, "description", void 0);
_ts_decorate([
    (0, _swagger.ApiProperty)({
        description: 'Loại chiến dịch',
        enum: ZaloCampaignType,
        example: "message"
    }),
    (0, _classvalidator.IsEnum)(ZaloCampaignType),
    (0, _classvalidator.IsNotEmpty)(),
    _ts_metadata("design:type", String)
], CreateZaloCampaignDto.prototype, "type", void 0);
_ts_decorate([
    (0, _swagger.ApiProperty)({
        description: 'ID của phân đoạn',
        example: 1
    }),
    (0, _classvalidator.IsNumber)(),
    (0, _classvalidator.IsNotEmpty)(),
    _ts_metadata("design:type", Number)
], CreateZaloCampaignDto.prototype, "segmentId", void 0);
_ts_decorate([
    (0, _swagger.ApiProperty)({
        description: 'Thời điểm bắt đầu (Unix timestamp)',
        example: 1625097600000,
        required: false
    }),
    (0, _classvalidator.IsNumber)(),
    (0, _classvalidator.IsOptional)(),
    _ts_metadata("design:type", Number)
], CreateZaloCampaignDto.prototype, "scheduledAt", void 0);
_ts_decorate([
    (0, _swagger.ApiProperty)({
        description: 'Nội dung tin nhắn (chỉ dùng khi type là message)',
        type: ZaloCampaignMessageContentDto,
        required: false
    }),
    (0, _classvalidator.IsObject)(),
    (0, _classvalidator.IsOptional)(),
    _ts_metadata("design:type", typeof ZaloCampaignMessageContentDto === "undefined" ? Object : ZaloCampaignMessageContentDto)
], CreateZaloCampaignDto.prototype, "messageContent", void 0);
_ts_decorate([
    (0, _swagger.ApiProperty)({
        description: 'Nội dung ZNS (chỉ dùng khi type là zns)',
        type: ZaloCampaignZnsContentDto,
        required: false
    }),
    (0, _classvalidator.IsObject)(),
    (0, _classvalidator.IsOptional)(),
    _ts_metadata("design:type", typeof ZaloCampaignZnsContentDto === "undefined" ? Object : ZaloCampaignZnsContentDto)
], CreateZaloCampaignDto.prototype, "znsContent", void 0);
let UpdateZaloCampaignDto = class UpdateZaloCampaignDto {
};
_ts_decorate([
    (0, _swagger.ApiProperty)({
        description: 'Tên của chiến dịch',
        example: 'Chiến dịch khuyến mãi tháng 7 - Cập nhật',
        required: false
    }),
    (0, _classvalidator.IsString)(),
    (0, _classvalidator.IsOptional)(),
    _ts_metadata("design:type", String)
], UpdateZaloCampaignDto.prototype, "name", void 0);
_ts_decorate([
    (0, _swagger.ApiProperty)({
        description: 'Mô tả của chiến dịch',
        example: 'Chiến dịch khuyến mãi dành cho khách hàng VIP - Cập nhật',
        required: false
    }),
    (0, _classvalidator.IsString)(),
    (0, _classvalidator.IsOptional)(),
    _ts_metadata("design:type", String)
], UpdateZaloCampaignDto.prototype, "description", void 0);
_ts_decorate([
    (0, _swagger.ApiProperty)({
        description: 'ID của phân đoạn',
        example: 2,
        required: false
    }),
    (0, _classvalidator.IsNumber)(),
    (0, _classvalidator.IsOptional)(),
    _ts_metadata("design:type", Number)
], UpdateZaloCampaignDto.prototype, "segmentId", void 0);
_ts_decorate([
    (0, _swagger.ApiProperty)({
        description: 'Thời điểm bắt đầu (Unix timestamp)',
        example: 1625097600000,
        required: false
    }),
    (0, _classvalidator.IsNumber)(),
    (0, _classvalidator.IsOptional)(),
    _ts_metadata("design:type", Number)
], UpdateZaloCampaignDto.prototype, "scheduledAt", void 0);
_ts_decorate([
    (0, _swagger.ApiProperty)({
        description: 'Nội dung tin nhắn (chỉ dùng khi type là message)',
        type: ZaloCampaignMessageContentDto,
        required: false
    }),
    (0, _classvalidator.IsObject)(),
    (0, _classvalidator.IsOptional)(),
    _ts_metadata("design:type", typeof ZaloCampaignMessageContentDto === "undefined" ? Object : ZaloCampaignMessageContentDto)
], UpdateZaloCampaignDto.prototype, "messageContent", void 0);
_ts_decorate([
    (0, _swagger.ApiProperty)({
        description: 'Nội dung ZNS (chỉ dùng khi type là zns)',
        type: ZaloCampaignZnsContentDto,
        required: false
    }),
    (0, _classvalidator.IsObject)(),
    (0, _classvalidator.IsOptional)(),
    _ts_metadata("design:type", typeof ZaloCampaignZnsContentDto === "undefined" ? Object : ZaloCampaignZnsContentDto)
], UpdateZaloCampaignDto.prototype, "znsContent", void 0);
let ZaloCampaignResponseDto = class ZaloCampaignResponseDto {
};
_ts_decorate([
    (0, _swagger.ApiProperty)({
        description: 'ID của chiến dịch',
        example: 1
    }),
    _ts_metadata("design:type", Number)
], ZaloCampaignResponseDto.prototype, "id", void 0);
_ts_decorate([
    (0, _swagger.ApiProperty)({
        description: 'ID của người dùng',
        example: 123
    }),
    _ts_metadata("design:type", Number)
], ZaloCampaignResponseDto.prototype, "userId", void 0);
_ts_decorate([
    (0, _swagger.ApiProperty)({
        description: 'ID của Official Account',
        example: '*********'
    }),
    _ts_metadata("design:type", String)
], ZaloCampaignResponseDto.prototype, "oaId", void 0);
_ts_decorate([
    (0, _swagger.ApiProperty)({
        description: 'Tên của chiến dịch',
        example: 'Chiến dịch khuyến mãi tháng 7'
    }),
    _ts_metadata("design:type", String)
], ZaloCampaignResponseDto.prototype, "name", void 0);
_ts_decorate([
    (0, _swagger.ApiProperty)({
        description: 'Mô tả của chiến dịch',
        example: 'Chiến dịch khuyến mãi dành cho khách hàng VIP',
        nullable: true
    }),
    _ts_metadata("design:type", String)
], ZaloCampaignResponseDto.prototype, "description", void 0);
_ts_decorate([
    (0, _swagger.ApiProperty)({
        description: 'Loại chiến dịch',
        enum: ZaloCampaignType,
        example: "message"
    }),
    _ts_metadata("design:type", String)
], ZaloCampaignResponseDto.prototype, "type", void 0);
_ts_decorate([
    (0, _swagger.ApiProperty)({
        description: 'ID của phân đoạn',
        example: 1
    }),
    _ts_metadata("design:type", Number)
], ZaloCampaignResponseDto.prototype, "segmentId", void 0);
_ts_decorate([
    (0, _swagger.ApiProperty)({
        description: 'Trạng thái chiến dịch',
        enum: ZaloCampaignStatus,
        example: "draft"
    }),
    _ts_metadata("design:type", String)
], ZaloCampaignResponseDto.prototype, "status", void 0);
_ts_decorate([
    (0, _swagger.ApiProperty)({
        description: 'Thời điểm bắt đầu (Unix timestamp)',
        example: 1625097600000,
        nullable: true
    }),
    _ts_metadata("design:type", Number)
], ZaloCampaignResponseDto.prototype, "scheduledAt", void 0);
_ts_decorate([
    (0, _swagger.ApiProperty)({
        description: 'Thời điểm bắt đầu thực tế (Unix timestamp)',
        example: 1625097600000,
        nullable: true
    }),
    _ts_metadata("design:type", Number)
], ZaloCampaignResponseDto.prototype, "startedAt", void 0);
_ts_decorate([
    (0, _swagger.ApiProperty)({
        description: 'Thời điểm kết thúc (Unix timestamp)',
        example: 1625097600000,
        nullable: true
    }),
    _ts_metadata("design:type", Number)
], ZaloCampaignResponseDto.prototype, "completedAt", void 0);
_ts_decorate([
    (0, _swagger.ApiProperty)({
        description: 'Nội dung tin nhắn (chỉ dùng khi type là message)',
        type: ZaloCampaignMessageContentDto,
        nullable: true
    }),
    _ts_metadata("design:type", typeof ZaloCampaignMessageContentDto === "undefined" ? Object : ZaloCampaignMessageContentDto)
], ZaloCampaignResponseDto.prototype, "messageContent", void 0);
_ts_decorate([
    (0, _swagger.ApiProperty)({
        description: 'Nội dung ZNS (chỉ dùng khi type là zns)',
        type: ZaloCampaignZnsContentDto,
        nullable: true
    }),
    _ts_metadata("design:type", typeof ZaloCampaignZnsContentDto === "undefined" ? Object : ZaloCampaignZnsContentDto)
], ZaloCampaignResponseDto.prototype, "znsContent", void 0);
_ts_decorate([
    (0, _swagger.ApiProperty)({
        description: 'Tổng số người nhận',
        example: 100
    }),
    _ts_metadata("design:type", Number)
], ZaloCampaignResponseDto.prototype, "totalRecipients", void 0);
_ts_decorate([
    (0, _swagger.ApiProperty)({
        description: 'Số người đã gửi thành công',
        example: 95
    }),
    _ts_metadata("design:type", Number)
], ZaloCampaignResponseDto.prototype, "successCount", void 0);
_ts_decorate([
    (0, _swagger.ApiProperty)({
        description: 'Số người gửi thất bại',
        example: 5
    }),
    _ts_metadata("design:type", Number)
], ZaloCampaignResponseDto.prototype, "failureCount", void 0);
_ts_decorate([
    (0, _swagger.ApiProperty)({
        description: 'Thời điểm tạo (Unix timestamp)',
        example: 1625097600000
    }),
    _ts_metadata("design:type", Number)
], ZaloCampaignResponseDto.prototype, "createdAt", void 0);
_ts_decorate([
    (0, _swagger.ApiProperty)({
        description: 'Thời điểm cập nhật (Unix timestamp)',
        example: 1625097600000
    }),
    _ts_metadata("design:type", Number)
], ZaloCampaignResponseDto.prototype, "updatedAt", void 0);
let ZaloCampaignQueryDto = class ZaloCampaignQueryDto {
    constructor(){
        this.page = 1;
        this.limit = 10;
    }
};
_ts_decorate([
    (0, _swagger.ApiProperty)({
        description: 'Tìm kiếm theo tên chiến dịch',
        example: 'khuyến mãi',
        required: false
    }),
    (0, _classvalidator.IsString)(),
    (0, _classvalidator.IsOptional)(),
    _ts_metadata("design:type", String)
], ZaloCampaignQueryDto.prototype, "name", void 0);
_ts_decorate([
    (0, _swagger.ApiProperty)({
        description: 'Lọc theo loại chiến dịch',
        enum: ZaloCampaignType,
        example: "message",
        required: false
    }),
    (0, _classvalidator.IsEnum)(ZaloCampaignType),
    (0, _classvalidator.IsOptional)(),
    _ts_metadata("design:type", String)
], ZaloCampaignQueryDto.prototype, "type", void 0);
_ts_decorate([
    (0, _swagger.ApiProperty)({
        description: 'Lọc theo trạng thái chiến dịch',
        enum: ZaloCampaignStatus,
        example: "running",
        required: false
    }),
    (0, _classvalidator.IsEnum)(ZaloCampaignStatus),
    (0, _classvalidator.IsOptional)(),
    _ts_metadata("design:type", String)
], ZaloCampaignQueryDto.prototype, "status", void 0);
_ts_decorate([
    (0, _swagger.ApiProperty)({
        description: 'Số trang',
        example: 1,
        default: 1,
        required: false
    }),
    (0, _classvalidator.IsOptional)(),
    _ts_metadata("design:type", Number)
], ZaloCampaignQueryDto.prototype, "page", void 0);
_ts_decorate([
    (0, _swagger.ApiProperty)({
        description: 'Số lượng chiến dịch trên mỗi trang',
        example: 10,
        default: 10,
        required: false
    }),
    (0, _classvalidator.IsOptional)(),
    _ts_metadata("design:type", Number)
], ZaloCampaignQueryDto.prototype, "limit", void 0);
let ExecuteZaloCampaignDto = class ExecuteZaloCampaignDto {
};
_ts_decorate([
    (0, _swagger.ApiProperty)({
        description: 'Hành động (start, cancel)',
        example: 'start'
    }),
    (0, _classvalidator.IsString)(),
    (0, _classvalidator.IsNotEmpty)(),
    _ts_metadata("design:type", String)
], ExecuteZaloCampaignDto.prototype, "action", void 0);

//# sourceMappingURL=zalo-campaign.dto.js.map