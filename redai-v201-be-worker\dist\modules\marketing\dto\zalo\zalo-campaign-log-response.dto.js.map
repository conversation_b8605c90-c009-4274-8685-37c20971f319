{"version": 3, "sources": ["../../../../../src/modules/marketing/dto/zalo/zalo-campaign-log-response.dto.ts"], "sourcesContent": ["import { ApiProperty } from '@nestjs/swagger';\r\n\r\n/**\r\n * DTO cho phản hồi thông tin log chiến dịch Zalo\r\n */\r\nexport class ZaloCampaignLogResponseDto {\r\n  @ApiProperty({\r\n    description: 'ID của log',\r\n    example: 1,\r\n  })\r\n  id: number;\r\n\r\n  @ApiProperty({\r\n    description: 'ID của chiến dịch',\r\n    example: 1,\r\n  })\r\n  campaignId: number;\r\n\r\n  @ApiProperty({\r\n    description: 'ID của người dùng',\r\n    example: 123,\r\n  })\r\n  userId: number;\r\n\r\n  @ApiProperty({\r\n    description: 'ID của Official Account',\r\n    example: '*********',\r\n  })\r\n  oaId: string;\r\n\r\n  @ApiProperty({\r\n    description: 'ID của người theo dõi',\r\n    example: 1,\r\n  })\r\n  followerId: number;\r\n\r\n  @ApiProperty({\r\n    description: 'ID người dùng của người theo dõi trên Zalo',\r\n    example: '*********',\r\n  })\r\n  followerUserId: string;\r\n\r\n  @ApiProperty({\r\n    description: 'ID của tin nhắn',\r\n    example: 'msg*********',\r\n    nullable: true,\r\n  })\r\n  messageId?: string;\r\n\r\n  @ApiProperty({\r\n    description: 'Trạng thái của log',\r\n    example: 'success',\r\n    enum: ['pending', 'success', 'failed', 'deleted'],\r\n  })\r\n  status: string;\r\n\r\n  @ApiProperty({\r\n    description: 'Thông báo lỗi (nếu có)',\r\n    example: 'Không thể gửi tin nhắn',\r\n    nullable: true,\r\n  })\r\n  error?: string;\r\n\r\n  @ApiProperty({\r\n    description: 'Thời điểm tạo (Unix timestamp)',\r\n    example: *************,\r\n  })\r\n  createdAt: number;\r\n\r\n  @ApiProperty({\r\n    description: 'Thông tin người theo dõi',\r\n    example: {\r\n      displayName: 'Nguyễn Văn A',\r\n      avatar: 'https://example.com/avatar.jpg',\r\n    },\r\n    nullable: true,\r\n  })\r\n  follower?: {\r\n    displayName?: string;\r\n    avatar?: string;\r\n  };\r\n}\r\n"], "names": ["ZaloCampaignLogResponseDto", "description", "example", "nullable", "enum", "displayName", "avatar"], "mappings": ";;;;+BAKaA;;;eAAAA;;;yBALe;;;;;;;;;;AAKrB,IAAA,AAAMA,6BAAN,MAAMA;AA4Eb;;;QA1EIC,aAAa;QACbC,SAAS;;;;;;QAKTD,aAAa;QACbC,SAAS;;;;;;QAKTD,aAAa;QACbC,SAAS;;;;;;QAKTD,aAAa;QACbC,SAAS;;;;;;QAKTD,aAAa;QACbC,SAAS;;;;;;QAKTD,aAAa;QACbC,SAAS;;;;;;QAKTD,aAAa;QACbC,SAAS;QACTC,UAAU;;;;;;QAKVF,aAAa;QACbC,SAAS;QACTE,MAAM;YAAC;YAAW;YAAW;YAAU;SAAU;;;;;;QAKjDH,aAAa;QACbC,SAAS;QACTC,UAAU;;;;;;QAKVF,aAAa;QACbC,SAAS;;;;;;QAKTD,aAAa;QACbC,SAAS;YACPG,aAAa;YACbC,QAAQ;QACV;QACAH,UAAU"}