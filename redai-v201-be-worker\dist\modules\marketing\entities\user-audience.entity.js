"use strict";
Object.defineProperty(exports, "__esModule", {
    value: true
});
Object.defineProperty(exports, "UserAudience", {
    enumerable: true,
    get: function() {
        return UserAudience;
    }
});
const _typeorm = require("typeorm");
function _ts_decorate(decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for(var i = decorators.length - 1; i >= 0; i--)if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
}
function _ts_metadata(k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
}
let UserAudience = class UserAudience {
};
_ts_decorate([
    (0, _typeorm.PrimaryGeneratedColumn)({
        name: 'id',
        type: 'bigint'
    }),
    _ts_metadata("design:type", Number)
], UserAudience.prototype, "id", void 0);
_ts_decorate([
    (0, _typeorm.Column)({
        name: 'user_id',
        nullable: true,
        comment: 'Mã khách hàng'
    }),
    _ts_metadata("design:type", Number)
], UserAudience.prototype, "userId", void 0);
_ts_decorate([
    (0, _typeorm.Column)({
        name: 'email',
        length: 255,
        nullable: true,
        comment: 'Email người dùng'
    }),
    _ts_metadata("design:type", String)
], UserAudience.prototype, "email", void 0);
_ts_decorate([
    (0, _typeorm.Column)({
        name: 'phone',
        length: 20,
        nullable: true,
        comment: 'Số điện thoại'
    }),
    _ts_metadata("design:type", String)
], UserAudience.prototype, "phone", void 0);
_ts_decorate([
    (0, _typeorm.Column)({
        name: 'created_at',
        type: 'bigint',
        comment: 'Ngày tạo'
    }),
    _ts_metadata("design:type", Number)
], UserAudience.prototype, "createdAt", void 0);
_ts_decorate([
    (0, _typeorm.Column)({
        name: 'updated_at',
        type: 'bigint',
        nullable: true,
        comment: 'Ngày cập nhật'
    }),
    _ts_metadata("design:type", Number)
], UserAudience.prototype, "updatedAt", void 0);
UserAudience = _ts_decorate([
    (0, _typeorm.Entity)('user_audience')
], UserAudience);

//# sourceMappingURL=user-audience.entity.js.map