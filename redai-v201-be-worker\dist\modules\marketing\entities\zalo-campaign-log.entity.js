"use strict";
Object.defineProperty(exports, "__esModule", {
    value: true
});
Object.defineProperty(exports, "ZaloCampaignLog", {
    enumerable: true,
    get: function() {
        return ZaloCampaignLog;
    }
});
const _typeorm = require("typeorm");
function _ts_decorate(decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for(var i = decorators.length - 1; i >= 0; i--)if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
}
function _ts_metadata(k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
}
let ZaloCampaignLog = class ZaloCampaignLog {
};
_ts_decorate([
    (0, _typeorm.PrimaryGeneratedColumn)(),
    _ts_metadata("design:type", Number)
], ZaloCampaignLog.prototype, "id", void 0);
_ts_decorate([
    (0, _typeorm.Column)({
        name: 'campaign_id'
    }),
    _ts_metadata("design:type", Number)
], ZaloCampaignLog.prototype, "campaignId", void 0);
_ts_decorate([
    (0, _typeorm.Column)({
        name: 'user_id'
    }),
    _ts_metadata("design:type", Number)
], ZaloCampaignLog.prototype, "userId", void 0);
_ts_decorate([
    (0, _typeorm.Column)({
        name: 'oa_id'
    }),
    _ts_metadata("design:type", String)
], ZaloCampaignLog.prototype, "oaId", void 0);
_ts_decorate([
    (0, _typeorm.Column)({
        name: 'follower_id'
    }),
    _ts_metadata("design:type", Number)
], ZaloCampaignLog.prototype, "followerId", void 0);
_ts_decorate([
    (0, _typeorm.Column)({
        name: 'follower_user_id'
    }),
    _ts_metadata("design:type", String)
], ZaloCampaignLog.prototype, "followerUserId", void 0);
_ts_decorate([
    (0, _typeorm.Column)({
        name: 'message_id',
        nullable: true
    }),
    _ts_metadata("design:type", String)
], ZaloCampaignLog.prototype, "messageId", void 0);
_ts_decorate([
    (0, _typeorm.Column)(),
    _ts_metadata("design:type", String)
], ZaloCampaignLog.prototype, "status", void 0);
_ts_decorate([
    (0, _typeorm.Column)({
        nullable: true
    }),
    _ts_metadata("design:type", String)
], ZaloCampaignLog.prototype, "error", void 0);
_ts_decorate([
    (0, _typeorm.Column)({
        name: 'created_at',
        type: 'bigint'
    }),
    _ts_metadata("design:type", Number)
], ZaloCampaignLog.prototype, "createdAt", void 0);
ZaloCampaignLog = _ts_decorate([
    (0, _typeorm.Entity)('zalo_campaign_logs')
], ZaloCampaignLog);

//# sourceMappingURL=zalo-campaign-log.entity.js.map