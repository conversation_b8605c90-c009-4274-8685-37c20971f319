{"version": 3, "sources": ["../../../../src/modules/marketing/entities/zalo-zns-template.entity.ts"], "sourcesContent": ["import { Column, Entity, PrimaryGeneratedColumn } from 'typeorm';\r\n\r\n/**\r\n * Entity đại diện cho bảng zalo_zns_templates trong cơ sở dữ liệu\r\n * Lưu trữ thông tin về các template ZNS (Zalo Notification Service) mà người dùng đã tạo hoặc được cấp quyền sử dụng\r\n */\r\n@Entity('zalo_zns_templates')\r\nexport class ZaloZnsTemplate {\r\n  /**\r\n   * ID tự động tăng\r\n   */\r\n  @PrimaryGeneratedColumn({ name: 'id' })\r\n  id: number;\r\n\r\n  /**\r\n   * ID người dùng sở hữu template\r\n   */\r\n  @Column({ name: 'user_id' })\r\n  userId: number;\r\n\r\n  /**\r\n   * ID của Official Account\r\n   */\r\n  @Column({ name: 'oa_id', length: 50 })\r\n  oaId: string;\r\n\r\n  /**\r\n   * ID của template trên Zalo\r\n   */\r\n  @Column({ name: 'template_id', length: 50 })\r\n  templateId: string;\r\n\r\n  /**\r\n   * Tên của template\r\n   */\r\n  @Column({ name: 'template_name', length: 255 })\r\n  templateName: string;\r\n\r\n  /**\r\n   * Nội dung của template\r\n   */\r\n  @Column({ name: 'template_content', type: 'text' })\r\n  templateContent: string;\r\n\r\n  /**\r\n   * Các tham số của template (JSON)\r\n   */\r\n  @Column({ name: 'params', type: 'jsonb', default: '[]' })\r\n  params: string[];\r\n\r\n  /**\r\n   * Trạng thái của template (approved, pending, rejected)\r\n   */\r\n  @Column({ name: 'status', length: 20 })\r\n  status: string;\r\n\r\n  /**\r\n   * Thời điểm tạo (Unix timestamp)\r\n   */\r\n  @Column({ name: 'created_at', type: 'bigint' })\r\n  createdAt: number;\r\n\r\n  /**\r\n   * Thời điểm cập nhật (Unix timestamp)\r\n   */\r\n  @Column({ name: 'updated_at', type: 'bigint' })\r\n  updatedAt: number;\r\n}\r\n"], "names": ["ZaloZnsTemplate", "name", "length", "type", "default"], "mappings": ";;;;+BAOaA;;;eAAAA;;;yBAP0C;;;;;;;;;;AAOhD,IAAA,AAAMA,kBAAN,MAAMA;AA4Db;;;QAxD4BC,MAAM;;;;;;QAMtBA,MAAM;;;;;;QAMNA,MAAM;QAASC,QAAQ;;;;;;QAMvBD,MAAM;QAAeC,QAAQ;;;;;;QAM7BD,MAAM;QAAiBC,QAAQ;;;;;;QAM/BD,MAAM;QAAoBE,MAAM;;;;;;QAMhCF,MAAM;QAAUE,MAAM;QAASC,SAAS;;;;;;QAMxCH,MAAM;QAAUC,QAAQ;;;;;;QAMxBD,MAAM;QAAcE,MAAM;;;;;;QAM1BF,MAAM;QAAcE,MAAM"}