"use strict";
Object.defineProperty(exports, "__esModule", {
    value: true
});
Object.defineProperty(exports, "TagResponseDto", {
    enumerable: true,
    get: function() {
        return TagResponseDto;
    }
});
const _swagger = require("@nestjs/swagger");
function _ts_decorate(decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for(var i = decorators.length - 1; i >= 0; i--)if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
}
function _ts_metadata(k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
}
let TagResponseDto = class TagResponseDto {
};
_ts_decorate([
    (0, _swagger.ApiProperty)({
        description: 'ID của tag',
        example: 1
    }),
    _ts_metadata("design:type", Number)
], TagResponseDto.prototype, "id", void 0);
_ts_decorate([
    (0, _swagger.ApiProperty)({
        description: 'Tên tag',
        example: 'Khách hàng VIP'
    }),
    _ts_metadata("design:type", String)
], TagResponseDto.prototype, "name", void 0);
_ts_decorate([
    (0, _swagger.ApiProperty)({
        description: 'Mã màu của tag (định dạng HEX)',
        example: '#FF5733'
    }),
    _ts_metadata("design:type", String)
], TagResponseDto.prototype, "color", void 0);
_ts_decorate([
    (0, _swagger.ApiProperty)({
        description: 'Thời gian tạo (Unix timestamp)',
        example: 1619171200
    }),
    _ts_metadata("design:type", Number)
], TagResponseDto.prototype, "createdAt", void 0);
_ts_decorate([
    (0, _swagger.ApiProperty)({
        description: 'Thời gian cập nhật (Unix timestamp)',
        example: 1619171200
    }),
    _ts_metadata("design:type", Number)
], TagResponseDto.prototype, "updatedAt", void 0);

//# sourceMappingURL=tag-response.dto.js.map