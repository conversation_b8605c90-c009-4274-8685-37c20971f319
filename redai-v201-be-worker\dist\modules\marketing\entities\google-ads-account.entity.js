"use strict";
Object.defineProperty(exports, "__esModule", {
    value: true
});
Object.defineProperty(exports, "GoogleAdsAccount", {
    enumerable: true,
    get: function() {
        return GoogleAdsAccount;
    }
});
const _typeorm = require("typeorm");
function _ts_decorate(decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for(var i = decorators.length - 1; i >= 0; i--)if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
}
function _ts_metadata(k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
}
let GoogleAdsAccount = class GoogleAdsAccount {
};
_ts_decorate([
    (0, _typeorm.PrimaryGeneratedColumn)({
        name: 'id',
        type: 'bigint'
    }),
    _ts_metadata("design:type", Number)
], GoogleAdsAccount.prototype, "id", void 0);
_ts_decorate([
    (0, _typeorm.Column)({
        name: 'user_id',
        nullable: false,
        comment: 'ID của người dùng'
    }),
    _ts_metadata("design:type", Number)
], GoogleAdsAccount.prototype, "userId", void 0);
_ts_decorate([
    (0, _typeorm.Column)({
        name: 'customer_id',
        nullable: false,
        comment: 'Customer ID của tài khoản Google Ads'
    }),
    _ts_metadata("design:type", String)
], GoogleAdsAccount.prototype, "customerId", void 0);
_ts_decorate([
    (0, _typeorm.Column)({
        name: 'refresh_token',
        nullable: false,
        comment: 'Refresh token để truy cập Google Ads API'
    }),
    _ts_metadata("design:type", String)
], GoogleAdsAccount.prototype, "refreshToken", void 0);
_ts_decorate([
    (0, _typeorm.Column)({
        name: 'name',
        length: 255,
        nullable: true,
        comment: 'Tên tài khoản'
    }),
    _ts_metadata("design:type", String)
], GoogleAdsAccount.prototype, "name", void 0);
_ts_decorate([
    (0, _typeorm.Column)({
        name: 'status',
        length: 20,
        nullable: false,
        default: 'active',
        comment: 'Trạng thái tài khoản'
    }),
    _ts_metadata("design:type", String)
], GoogleAdsAccount.prototype, "status", void 0);
_ts_decorate([
    (0, _typeorm.Column)({
        name: 'created_at',
        type: 'bigint',
        nullable: false,
        comment: 'Thời gian tạo'
    }),
    _ts_metadata("design:type", Number)
], GoogleAdsAccount.prototype, "createdAt", void 0);
_ts_decorate([
    (0, _typeorm.Column)({
        name: 'updated_at',
        type: 'bigint',
        nullable: true,
        comment: 'Thời gian cập nhật'
    }),
    _ts_metadata("design:type", Number)
], GoogleAdsAccount.prototype, "updatedAt", void 0);
GoogleAdsAccount = _ts_decorate([
    (0, _typeorm.Entity)('google_ads_accounts')
], GoogleAdsAccount);

//# sourceMappingURL=google-ads-account.entity.js.map