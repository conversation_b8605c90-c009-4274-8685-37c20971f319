{"version": 3, "sources": ["../../../../src/modules/marketing/entities/google-ads-ad-group.entity.ts"], "sourcesContent": ["import { Column, Entity, PrimaryGeneratedColumn } from 'typeorm';\r\n\r\n/**\r\n * Entity đại diện cho bảng google_ads_ad_groups trong cơ sở dữ liệu\r\n * Lưu trữ thông tin nhóm quảng cáo Google Ads\r\n */\r\n@Entity('google_ads_ad_groups')\r\nexport class GoogleAdsAdGroup {\r\n  /**\r\n   * ID của nhóm quảng cáo trong hệ thống\r\n   */\r\n  @PrimaryGeneratedColumn({ name: 'id', type: 'bigint' })\r\n  id: number;\r\n\r\n  /**\r\n   * ID của người dùng\r\n   */\r\n  @Column({ name: 'user_id', nullable: false, comment: 'ID của người dùng' })\r\n  userId: number;\r\n\r\n  /**\r\n   * ID của chiến dịch trong hệ thống\r\n   */\r\n  @Column({\r\n    name: 'campaign_id',\r\n    nullable: false,\r\n    comment: 'ID của chiến dịch trong hệ thống',\r\n  })\r\n  campaignId: number;\r\n\r\n  /**\r\n   * ID của nhóm quảng cáo trên Google Ads\r\n   */\r\n  @Column({\r\n    name: 'ad_group_id',\r\n    nullable: false,\r\n    comment: 'ID của nhóm quảng cáo trên Google Ads',\r\n  })\r\n  adGroupId: string;\r\n\r\n  /**\r\n   * Tên nhóm quảng cáo\r\n   */\r\n  @Column({\r\n    name: 'name',\r\n    length: 255,\r\n    nullable: false,\r\n    comment: 'Tên nhóm quảng cáo',\r\n  })\r\n  name: string;\r\n\r\n  /**\r\n   * Trạng thái nhóm quảng cáo\r\n   */\r\n  @Column({\r\n    name: 'status',\r\n    length: 20,\r\n    nullable: false,\r\n    comment: 'Trạng thái nhóm quảng cáo',\r\n  })\r\n  status: string;\r\n\r\n  /**\r\n   * CPC tối đa (micro amount)\r\n   */\r\n  @Column({\r\n    name: 'cpc_bid_micros',\r\n    type: 'bigint',\r\n    nullable: true,\r\n    comment: 'CPC tối đa (micro amount)',\r\n  })\r\n  cpcBidMicros: number;\r\n\r\n  /**\r\n   * Thời gian tạo (Unix timestamp)\r\n   */\r\n  @Column({\r\n    name: 'created_at',\r\n    type: 'bigint',\r\n    nullable: false,\r\n    comment: 'Thời gian tạo',\r\n  })\r\n  createdAt: number;\r\n\r\n  /**\r\n   * Thời gian cập nhật (Unix timestamp)\r\n   */\r\n  @Column({\r\n    name: 'updated_at',\r\n    type: 'bigint',\r\n    nullable: true,\r\n    comment: 'Thời gian cập nhật',\r\n  })\r\n  updatedAt: number;\r\n}\r\n"], "names": ["GoogleAdsAdGroup", "name", "type", "nullable", "comment", "length"], "mappings": ";;;;+BAOaA;;;eAAAA;;;yBAP0C;;;;;;;;;;AAOhD,IAAA,AAAMA,mBAAN,MAAMA;AAuFb;;;QAnF4BC,MAAM;QAAMC,MAAM;;;;;;QAMlCD,MAAM;QAAWE,UAAU;QAAOC,SAAS;;;;;;QAOnDH,MAAM;QACNE,UAAU;QACVC,SAAS;;;;;;QAQTH,MAAM;QACNE,UAAU;QACVC,SAAS;;;;;;QAQTH,MAAM;QACNI,QAAQ;QACRF,UAAU;QACVC,SAAS;;;;;;QAQTH,MAAM;QACNI,QAAQ;QACRF,UAAU;QACVC,SAAS;;;;;;QAQTH,MAAM;QACNC,MAAM;QACNC,UAAU;QACVC,SAAS;;;;;;QAQTH,MAAM;QACNC,MAAM;QACNC,UAAU;QACVC,SAAS;;;;;;QAQTH,MAAM;QACNC,MAAM;QACNC,UAAU;QACVC,SAAS"}