"use strict";
Object.defineProperty(exports, "__esModule", {
    value: true
});
Object.defineProperty(exports, "BaseSmsProvider", {
    enumerable: true,
    get: function() {
        return BaseSmsProvider;
    }
});
const _common = require("@nestjs/common");
const _smsproviderinterface = require("./sms-provider.interface");
let BaseSmsProvider = class BaseSmsProvider {
    /**
   * G<PERSON>i tin nhắn SMS đến nhiều số điện thoại
   * @param phoneNumbers Danh sách số điện thoại của người nhận
   * @param message Nội dung tin nhắn
   * @param options Các tùy chọn bổ sung dành riêng cho nhà cung cấp
   * @returns Promise chứa kết quả cho từng người nhận
   */ async sendBulkSms(phoneNumbers, message, options) {
        this.logger.debug(`<PERSON><PERSON><PERSON> hàng loạt SMS đến ${phoneNumbers.length} người nhận`); // G<PERSON>i hàng loạt SMS
        const results = [];
        let successCount = 0;
        let failureCount = 0;
        // Triển khai mặc định: gửi SMS đến từng người nhận riêng lẻ
        for (const phoneNumber of phoneNumbers){
            try {
                const response = await this.sendSms(phoneNumber, message, options);
                if (response.success) {
                    successCount++;
                    results.push({
                        phoneNumber,
                        success: true,
                        messageId: response.messageId
                    });
                } else {
                    failureCount++;
                    results.push({
                        phoneNumber,
                        success: false,
                        errorCode: response.errorCode,
                        errorMessage: response.errorMessage
                    });
                }
            } catch (error) {
                failureCount++;
                results.push({
                    phoneNumber,
                    success: false,
                    errorMessage: error.message || 'Lỗi không xác định'
                });
            }
        }
        return {
            successCount,
            failureCount,
            results
        };
    }
    /**
   * Gửi tin nhắn SMS OTP (One-Time Password)
   * @param phoneNumber Số điện thoại của người nhận
   * @param otpCode Mã OTP cần gửi
   * @param options Các tùy chọn bổ sung dành riêng cho nhà cung cấp
   * @returns Promise chứa ID tin nhắn hoặc lỗi
   */ async sendOtp(phoneNumber, otpCode, options) {
        this.logger.debug(`Gửi mã OTP ${otpCode} đến số điện thoại ${phoneNumber}`); // Gửi mã OTP
        // Mặc định, sử dụng một mẫu chuẩn cho tin nhắn OTP
        const message = options?.template ? options.template.replace('{code}', otpCode) : `Mã xác thực của bạn là: ${otpCode}`; // Mã xác thực
        return this.sendSms(phoneNumber, message, options);
    }
    /**
   * Định dạng số điện thoại theo chuẩn quốc tế
   * @param phoneNumber Số điện thoại cần định dạng
   * @returns Số điện thoại đã được định dạng
   */ formatPhoneNumber(phoneNumber) {
        // Loại bỏ tất cả các ký tự không phải số
        let cleaned = phoneNumber.replace(/\D/g, '');
        // Nếu số điện thoại bắt đầu bằng số 0, thay thế bằng mã quốc gia của Việt Nam (+84)
        if (cleaned.startsWith('0')) {
            cleaned = '84' + cleaned.substring(1);
        }
        // Thêm dấu + nếu cần thiết
        if (!cleaned.startsWith('+')) {
            cleaned = '+' + cleaned;
        }
        return cleaned;
    }
    /**
   * Chuyển đổi trạng thái cụ thể của nhà cung cấp thành trạng thái chuẩn
   * @param providerStatus Trạng thái cụ thể của nhà cung cấp
   * @returns Trạng thái chuẩn
   */ mapToStandardStatus(_providerStatus) {
        // Phương thức này phải được ghi đè bởi các lớp con
        return _smsproviderinterface.MessageStatus.UNKNOWN;
    }
    constructor(loggerName){
        this.logger = new _common.Logger(loggerName);
    }
};

//# sourceMappingURL=base-sms-provider.service.js.map