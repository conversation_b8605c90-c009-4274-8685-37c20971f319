"use strict";
Object.defineProperty(exports, "__esModule", {
    value: true
});
Object.defineProperty(exports, "CustomFieldResponseDto", {
    enumerable: true,
    get: function() {
        return CustomFieldResponseDto;
    }
});
const _swagger = require("@nestjs/swagger");
const _createcustomfielddto = require("./create-custom-field.dto");
function _ts_decorate(decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for(var i = decorators.length - 1; i >= 0; i--)if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
}
function _ts_metadata(k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
}
let CustomFieldResponseDto = class CustomFieldResponseDto {
};
_ts_decorate([
    (0, _swagger.ApiProperty)({
        description: 'ID của trường tùy chỉnh',
        example: 1
    }),
    _ts_metadata("design:type", Number)
], CustomFieldResponseDto.prototype, "id", void 0);
_ts_decorate([
    (0, _swagger.ApiProperty)({
        description: 'ID của audience',
        example: 1
    }),
    _ts_metadata("design:type", Number)
], CustomFieldResponseDto.prototype, "audienceId", void 0);
_ts_decorate([
    (0, _swagger.ApiProperty)({
        description: 'Tên trường',
        example: 'Địa chỉ'
    }),
    _ts_metadata("design:type", String)
], CustomFieldResponseDto.prototype, "fieldName", void 0);
_ts_decorate([
    (0, _swagger.ApiProperty)({
        description: 'Giá trị trường',
        example: 'Hà Nội, Việt Nam'
    }),
    _ts_metadata("design:type", Object)
], CustomFieldResponseDto.prototype, "fieldValue", void 0);
_ts_decorate([
    (0, _swagger.ApiProperty)({
        description: 'Kiểu dữ liệu của trường',
        enum: _createcustomfielddto.CustomFieldType,
        example: _createcustomfielddto.CustomFieldType.TEXT
    }),
    _ts_metadata("design:type", typeof _createcustomfielddto.CustomFieldType === "undefined" ? Object : _createcustomfielddto.CustomFieldType)
], CustomFieldResponseDto.prototype, "fieldType", void 0);
_ts_decorate([
    (0, _swagger.ApiProperty)({
        description: 'Thời gian tạo (Unix timestamp)',
        example: 1619171200
    }),
    _ts_metadata("design:type", Number)
], CustomFieldResponseDto.prototype, "createdAt", void 0);
_ts_decorate([
    (0, _swagger.ApiProperty)({
        description: 'Thời gian cập nhật (Unix timestamp)',
        example: 1619171200
    }),
    _ts_metadata("design:type", Number)
], CustomFieldResponseDto.prototype, "updatedAt", void 0);

//# sourceMappingURL=custom-field-response.dto.js.map