{"version": 3, "sources": ["../../../../src/shared/services/sms/speed-sms-provider.service.ts"], "sourcesContent": ["import { Injectable } from '@nestjs/common';\r\nimport { HttpService } from '@nestjs/axios';\r\nimport { ConfigService } from '@nestjs/config';\r\nimport { firstValueFrom } from 'rxjs';\r\nimport { catchError } from 'rxjs/operators';\r\nimport { BaseSmsProvider } from './base-sms-provider.service';\r\nimport {\r\n  SmsResponse,\r\n  BulkSmsResponse,\r\n  MessageStatusResponse,\r\n  ConnectionTestResponse,\r\n  MessageStatus,\r\n} from './sms-provider.interface';\r\n\r\n/**\r\n * Interface pour la configuration de SpeedSMS\r\n */\r\nexport interface SpeedSmsConfig {\r\n  /**\r\n   * Token d'accès à l'API SpeedSMS\r\n   */\r\n  apiToken: string;\r\n\r\n  /**\r\n   * URL de base de l'API SpeedSMS\r\n   */\r\n  apiUrl?: string;\r\n\r\n  /**\r\n   * Type de SMS (2: CSKH, 3: Brandname, 4: Notify, 5: App Android, 6: Numéro fixe)\r\n   */\r\n  smsType?: number;\r\n\r\n  /**\r\n   * Nom de l'expéditeur (obligatoire si smsType = 3 ou 5)\r\n   */\r\n  sender?: string;\r\n}\r\n\r\n/**\r\n * Service d'intégration avec l'API SpeedSMS\r\n */\r\n@Injectable()\r\nexport class SpeedSmsProvider extends BaseSmsProvider {\r\n  readonly providerName = 'SpeedSMS';\r\n\r\n  private readonly apiUrl: string;\r\n  private readonly apiToken: string;\r\n  private readonly defaultSmsType: number;\r\n  private readonly defaultSender: string;\r\n\r\n  constructor(\r\n    private readonly httpService: HttpService,\r\n    private readonly configService: ConfigService,\r\n  ) {\r\n    super('SpeedSmsProvider');\r\n\r\n    // Charger la configuration depuis les variables d'environnement ou utiliser des valeurs par défaut\r\n    this.apiToken = this.configService.get<string>('SPEED_SMS_API_TOKEN') || '';\r\n    this.apiUrl =\r\n      this.configService.get<string>('SPEED_SMS_API_URL') ||\r\n      'https://api.speedsms.vn/index.php';\r\n    this.defaultSmsType = this.configService.get<number>('SPEED_SMS_TYPE') || 2; // Par défaut: CSKH\r\n    this.defaultSender =\r\n      this.configService.get<string>('SPEED_SMS_SENDER') || '';\r\n  }\r\n\r\n  /**\r\n   * Envoie un SMS à un numéro de téléphone via SpeedSMS\r\n   * @param phoneNumber Numéro de téléphone du destinataire\r\n   * @param message Contenu du message\r\n   * @param options Options supplémentaires (smsType, sender)\r\n   * @returns Promesse contenant l'ID du message ou une erreur\r\n   */\r\n  async sendSms(\r\n    phoneNumber: string,\r\n    message: string,\r\n    options?: any,\r\n  ): Promise<SmsResponse> {\r\n    try {\r\n      this.logger.debug(`Envoi d'un SMS à ${phoneNumber} via SpeedSMS`);\r\n\r\n      const smsType = options?.smsType || this.defaultSmsType;\r\n      const sender = options?.sender || this.defaultSender;\r\n\r\n      // Vérifier si un sender est requis mais non fourni\r\n      if ((smsType === 3 || smsType === 5) && !sender) {\r\n        throw new Error(\r\n          'Le paramètre \"sender\" est obligatoire pour les SMS de type Brandname ou Android App',\r\n        );\r\n      }\r\n\r\n      const formattedPhoneNumber = this.formatPhoneNumber(phoneNumber);\r\n\r\n      const response = await firstValueFrom(\r\n        this.httpService\r\n          .post(\r\n            `${this.apiUrl}/sms/send`,\r\n            {\r\n              to: [formattedPhoneNumber],\r\n              content: message,\r\n              sms_type: smsType,\r\n              sender: sender,\r\n            },\r\n            {\r\n              headers: {\r\n                'Content-Type': 'application/json',\r\n                Authorization: `Basic ${Buffer.from(this.apiToken + ':x').toString('base64')}`,\r\n              },\r\n            },\r\n          )\r\n          .pipe(\r\n            catchError((error) => {\r\n              this.logger.error(\r\n                `Erreur lors de l'envoi du SMS via SpeedSMS: ${error.message}`,\r\n                error.stack,\r\n              );\r\n              throw error;\r\n            }),\r\n          ),\r\n      );\r\n\r\n      if (response.data.status === 'success') {\r\n        return {\r\n          success: true,\r\n          messageId: response.data.data.tranId.toString(),\r\n          rawResponse: response.data,\r\n        };\r\n      } else {\r\n        return {\r\n          success: false,\r\n          errorCode: response.data.code,\r\n          errorMessage: response.data.message || 'Erreur inconnue',\r\n          rawResponse: response.data,\r\n        };\r\n      }\r\n    } catch (error) {\r\n      this.logger.error(\r\n        `Exception lors de l'envoi du SMS via SpeedSMS: ${error.message}`,\r\n        error.stack,\r\n      );\r\n      return {\r\n        success: false,\r\n        errorMessage: error.message || 'Erreur inconnue',\r\n      };\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Envoie un SMS à plusieurs numéros de téléphone via SpeedSMS\r\n   * @param phoneNumbers Liste des numéros de téléphone des destinataires\r\n   * @param message Contenu du message\r\n   * @param options Options supplémentaires (smsType, sender)\r\n   * @returns Promesse contenant les résultats pour chaque destinataire\r\n   */\r\n  async sendBulkSms(\r\n    phoneNumbers: string[],\r\n    message: string,\r\n    options?: any,\r\n  ): Promise<BulkSmsResponse> {\r\n    try {\r\n      this.logger.debug(\r\n        `Envoi de SMS en masse à ${phoneNumbers.length} destinataires via SpeedSMS`,\r\n      );\r\n\r\n      const smsType = options?.smsType || this.defaultSmsType;\r\n      const sender = options?.sender || this.defaultSender;\r\n\r\n      // Vérifier si un sender est requis mais non fourni\r\n      if ((smsType === 3 || smsType === 5) && !sender) {\r\n        throw new Error(\r\n          'Le paramètre \"sender\" est obligatoire pour les SMS de type Brandname ou Android App',\r\n        );\r\n      }\r\n\r\n      // Formater tous les numéros de téléphone\r\n      const formattedPhoneNumbers = phoneNumbers.map((phone) =>\r\n        this.formatPhoneNumber(phone),\r\n      );\r\n\r\n      const response = await firstValueFrom(\r\n        this.httpService\r\n          .post(\r\n            `${this.apiUrl}/sms/send`,\r\n            {\r\n              to: formattedPhoneNumbers,\r\n              content: message,\r\n              sms_type: smsType,\r\n              sender: sender,\r\n            },\r\n            {\r\n              headers: {\r\n                'Content-Type': 'application/json',\r\n                Authorization: `Basic ${Buffer.from(this.apiToken + ':x').toString('base64')}`,\r\n              },\r\n            },\r\n          )\r\n          .pipe(\r\n            catchError((error) => {\r\n              this.logger.error(\r\n                `Erreur lors de l'envoi des SMS en masse via SpeedSMS: ${error.message}`,\r\n                error.stack,\r\n              );\r\n              throw error;\r\n            }),\r\n          ),\r\n      );\r\n\r\n      if (response.data.status === 'success') {\r\n        // Créer les résultats pour chaque numéro\r\n        const results = formattedPhoneNumbers.map((phone) => {\r\n          // Vérifier si le numéro est dans la liste des numéros invalides\r\n          const isInvalid =\r\n            response.data.data.invalidPhone &&\r\n            response.data.data.invalidPhone.includes(phone);\r\n\r\n          if (isInvalid) {\r\n            return {\r\n              phoneNumber: phone,\r\n              success: false,\r\n              errorMessage: 'Numéro de téléphone invalide',\r\n            };\r\n          } else {\r\n            return {\r\n              phoneNumber: phone,\r\n              success: true,\r\n              messageId: response.data.data.tranId.toString(),\r\n            };\r\n          }\r\n        });\r\n\r\n        // Compter les succès et les échecs\r\n        const successCount = results.filter((r) => r.success).length;\r\n        const failureCount = results.filter((r) => !r.success).length;\r\n\r\n        return {\r\n          successCount,\r\n          failureCount,\r\n          results,\r\n          transactionId: response.data.data.tranId.toString(),\r\n          totalCost: response.data.data.totalPrice,\r\n          rawResponse: response.data,\r\n        };\r\n      } else {\r\n        // En cas d'erreur globale, tous les messages sont considérés comme échoués\r\n        const results = formattedPhoneNumbers.map((phone) => ({\r\n          phoneNumber: phone,\r\n          success: false,\r\n          errorCode: response.data.code,\r\n          errorMessage: response.data.message || 'Erreur inconnue',\r\n        }));\r\n\r\n        return {\r\n          successCount: 0,\r\n          failureCount: formattedPhoneNumbers.length,\r\n          results,\r\n          rawResponse: response.data,\r\n        };\r\n      }\r\n    } catch (error) {\r\n      this.logger.error(\r\n        `Exception lors de l'envoi des SMS en masse via SpeedSMS: ${error.message}`,\r\n        error.stack,\r\n      );\r\n\r\n      // En cas d'exception, tous les messages sont considérés comme échoués\r\n      const results = phoneNumbers.map((phone) => ({\r\n        phoneNumber: phone,\r\n        success: false,\r\n        errorMessage: error.message || 'Erreur inconnue',\r\n      }));\r\n\r\n      return {\r\n        successCount: 0,\r\n        failureCount: phoneNumbers.length,\r\n        results,\r\n      };\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Vérifie le statut d'un message envoyé via SpeedSMS\r\n   * @param messageId ID du message à vérifier\r\n   * @returns Promesse contenant le statut du message\r\n   */\r\n  async checkMessageStatus(messageId: string): Promise<MessageStatusResponse> {\r\n    try {\r\n      this.logger.debug(\r\n        `Vérification du statut du message ${messageId} via SpeedSMS`,\r\n      );\r\n\r\n      const response = await firstValueFrom(\r\n        this.httpService\r\n          .get(`${this.apiUrl}/sms/status/${messageId}`, {\r\n            headers: {\r\n              Authorization: `Basic ${Buffer.from(this.apiToken + ':x').toString('base64')}`,\r\n            },\r\n          })\r\n          .pipe(\r\n            catchError((error) => {\r\n              this.logger.error(\r\n                `Erreur lors de la vérification du statut du message via SpeedSMS: ${error.message}`,\r\n                error.stack,\r\n              );\r\n              throw error;\r\n            }),\r\n          ),\r\n      );\r\n\r\n      if (response.data.status === 'success') {\r\n        // Créer un objet pour stocker les statuts par numéro\r\n        const statusByPhone = {};\r\n\r\n        // Parcourir les résultats et stocker les statuts\r\n        for (const result of response.data.data) {\r\n          statusByPhone[result.phone] = this.mapSpeedSmsStatus(result.status);\r\n        }\r\n\r\n        // Déterminer le statut global\r\n        let globalStatus = MessageStatus.UNKNOWN;\r\n        const statuses = Object.values(statusByPhone);\r\n\r\n        if (statuses.length === 0) {\r\n          globalStatus = MessageStatus.UNKNOWN;\r\n        } else if (statuses.every((s) => s === MessageStatus.DELIVERED)) {\r\n          globalStatus = MessageStatus.DELIVERED;\r\n        } else if (statuses.every((s) => s === MessageStatus.FAILED)) {\r\n          globalStatus = MessageStatus.FAILED;\r\n        } else if (statuses.some((s) => s === MessageStatus.SENDING)) {\r\n          globalStatus = MessageStatus.SENDING;\r\n        } else if (statuses.some((s) => s === MessageStatus.PENDING)) {\r\n          globalStatus = MessageStatus.PENDING;\r\n        } else {\r\n          // Statut mixte\r\n          globalStatus = MessageStatus.SENDING;\r\n        }\r\n\r\n        return {\r\n          messageId,\r\n          status: globalStatus,\r\n          updatedAt: new Date(),\r\n          details: JSON.stringify(statusByPhone),\r\n          rawResponse: response.data,\r\n        };\r\n      } else {\r\n        return {\r\n          messageId,\r\n          status: MessageStatus.UNKNOWN,\r\n          updatedAt: new Date(),\r\n          details: response.data.message || 'Erreur inconnue',\r\n          rawResponse: response.data,\r\n        };\r\n      }\r\n    } catch (error) {\r\n      this.logger.error(\r\n        `Exception lors de la vérification du statut du message via SpeedSMS: ${error.message}`,\r\n        error.stack,\r\n      );\r\n      return {\r\n        messageId,\r\n        status: MessageStatus.UNKNOWN,\r\n        updatedAt: new Date(),\r\n        details: error.message || 'Erreur inconnue',\r\n      };\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Envoie un SMS avec un brandname via SpeedSMS\r\n   * @param phoneNumber Numéro de téléphone du destinataire\r\n   * @param message Contenu du message\r\n   * @param brandname Nom de la marque à utiliser comme expéditeur\r\n   * @param options Options supplémentaires\r\n   * @returns Promesse contenant l'ID du message ou une erreur\r\n   */\r\n  async sendBrandnameSms(\r\n    phoneNumber: string,\r\n    message: string,\r\n    brandname: string,\r\n    options?: any,\r\n  ): Promise<SmsResponse> {\r\n    // Pour SpeedSMS, utiliser le type 3 pour les SMS brandname\r\n    return this.sendSms(phoneNumber, message, {\r\n      ...options,\r\n      smsType: 3,\r\n      sender: brandname,\r\n    });\r\n  }\r\n\r\n  /**\r\n   * Teste la connexion avec SpeedSMS\r\n   * @param config Configuration de SpeedSMS\r\n   * @returns Promesse indiquant si la connexion est réussie\r\n   */\r\n  async testConnection(\r\n    config: SpeedSmsConfig,\r\n  ): Promise<ConnectionTestResponse> {\r\n    try {\r\n      this.logger.debug('Test de connexion avec SpeedSMS');\r\n\r\n      const apiToken = config.apiToken || this.apiToken;\r\n      const apiUrl = config.apiUrl || this.apiUrl;\r\n\r\n      if (!apiToken) {\r\n        return {\r\n          success: false,\r\n          message: 'Token API manquant',\r\n        };\r\n      }\r\n\r\n      const response = await firstValueFrom(\r\n        this.httpService\r\n          .get(`${apiUrl}/user/info`, {\r\n            headers: {\r\n              Authorization: `Basic ${Buffer.from(apiToken + ':x').toString('base64')}`,\r\n            },\r\n          })\r\n          .pipe(\r\n            catchError((error) => {\r\n              this.logger.error(\r\n                `Erreur lors du test de connexion avec SpeedSMS: ${error.message}`,\r\n                error.stack,\r\n              );\r\n              throw error;\r\n            }),\r\n          ),\r\n      );\r\n\r\n      if (response.data.status === 'success') {\r\n        return {\r\n          success: true,\r\n          message: 'Connexion réussie',\r\n          details: {\r\n            email: response.data.data.email,\r\n            balance: response.data.data.balance,\r\n            currency: response.data.data.currency,\r\n          },\r\n        };\r\n      } else {\r\n        return {\r\n          success: false,\r\n          message: response.data.message || 'Erreur inconnue',\r\n          details: response.data,\r\n        };\r\n      }\r\n    } catch (error) {\r\n      this.logger.error(\r\n        `Exception lors du test de connexion avec SpeedSMS: ${error.message}`,\r\n        error.stack,\r\n      );\r\n      return {\r\n        success: false,\r\n        message: error.message || 'Erreur inconnue',\r\n      };\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Convertit un statut SpeedSMS en statut standard\r\n   * @param speedSmsStatus Statut SpeedSMS\r\n   * @returns Statut standard\r\n   */\r\n  private mapSpeedSmsStatus(speedSmsStatus: number): MessageStatus {\r\n    switch (speedSmsStatus) {\r\n      case 0:\r\n        return MessageStatus.PENDING; // En attente\r\n      case -1:\r\n        return MessageStatus.SENDING; // En cours d'envoi\r\n      case 1:\r\n        return MessageStatus.DELIVERED; // Livré\r\n      case 2:\r\n        return MessageStatus.FAILED; // Échec\r\n      default:\r\n        return MessageStatus.UNKNOWN; // Inconnu\r\n    }\r\n  }\r\n}\r\n"], "names": ["SpeedSmsProvider", "BaseSmsProvider", "sendSms", "phoneNumber", "message", "options", "logger", "debug", "smsType", "defaultSmsType", "sender", "defaultSender", "Error", "formattedPhoneNumber", "formatPhoneNumber", "response", "firstValueFrom", "httpService", "post", "apiUrl", "to", "content", "sms_type", "headers", "Authorization", "<PERSON><PERSON><PERSON>", "from", "apiToken", "toString", "pipe", "catchError", "error", "stack", "data", "status", "success", "messageId", "tranId", "rawResponse", "errorCode", "code", "errorMessage", "sendBulkSms", "phoneNumbers", "length", "formattedPhoneNumbers", "map", "phone", "results", "isInvalid", "invalidPhone", "includes", "successCount", "filter", "r", "failureCount", "transactionId", "totalCost", "totalPrice", "checkMessageStatus", "get", "statusByPhone", "result", "mapSpeedSmsStatus", "globalStatus", "MessageStatus", "UNKNOWN", "statuses", "Object", "values", "every", "s", "DELIVERED", "FAILED", "some", "SENDING", "PENDING", "updatedAt", "Date", "details", "JSON", "stringify", "sendBrandnameSms", "brandname", "testConnection", "config", "email", "balance", "currency", "speedSmsStatus", "constructor", "configService", "providerName"], "mappings": ";;;;+BA2CaA;;;eAAAA;;;wBA3Cc;uBACC;wBACE;sBACC;2BACJ;wCACK;sCAOzB;;;;;;;;;;AA+BA,IAAA,AAAMA,mBAAN,MAAMA,yBAAyBC,uCAAe;IAwBnD;;;;;;GAMC,GACD,MAAMC,QACJC,WAAmB,EACnBC,OAAe,EACfC,OAAa,EACS;QACtB,IAAI;YACF,IAAI,CAACC,MAAM,CAACC,KAAK,CAAC,CAAC,iBAAiB,EAAEJ,YAAY,aAAa,CAAC;YAEhE,MAAMK,UAAUH,SAASG,WAAW,IAAI,CAACC,cAAc;YACvD,MAAMC,SAASL,SAASK,UAAU,IAAI,CAACC,aAAa;YAEpD,mDAAmD;YACnD,IAAI,AAACH,CAAAA,YAAY,KAAKA,YAAY,CAAA,KAAM,CAACE,QAAQ;gBAC/C,MAAM,IAAIE,MACR;YAEJ;YAEA,MAAMC,uBAAuB,IAAI,CAACC,iBAAiB,CAACX;YAEpD,MAAMY,WAAW,MAAMC,IAAAA,oBAAc,EACnC,IAAI,CAACC,WAAW,CACbC,IAAI,CACH,GAAG,IAAI,CAACC,MAAM,CAAC,SAAS,CAAC,EACzB;gBACEC,IAAI;oBAACP;iBAAqB;gBAC1BQ,SAASjB;gBACTkB,UAAUd;gBACVE,QAAQA;YACV,GACA;gBACEa,SAAS;oBACP,gBAAgB;oBAChBC,eAAe,CAAC,MAAM,EAAEC,OAAOC,IAAI,CAAC,IAAI,CAACC,QAAQ,GAAG,MAAMC,QAAQ,CAAC,WAAW;gBAChF;YACF,GAEDC,IAAI,CACHC,IAAAA,qBAAU,EAAC,CAACC;gBACV,IAAI,CAACzB,MAAM,CAACyB,KAAK,CACf,CAAC,4CAA4C,EAAEA,MAAM3B,OAAO,EAAE,EAC9D2B,MAAMC,KAAK;gBAEb,MAAMD;YACR;YAIN,IAAIhB,SAASkB,IAAI,CAACC,MAAM,KAAK,WAAW;gBACtC,OAAO;oBACLC,SAAS;oBACTC,WAAWrB,SAASkB,IAAI,CAACA,IAAI,CAACI,MAAM,CAACT,QAAQ;oBAC7CU,aAAavB,SAASkB,IAAI;gBAC5B;YACF,OAAO;gBACL,OAAO;oBACLE,SAAS;oBACTI,WAAWxB,SAASkB,IAAI,CAACO,IAAI;oBAC7BC,cAAc1B,SAASkB,IAAI,CAAC7B,OAAO,IAAI;oBACvCkC,aAAavB,SAASkB,IAAI;gBAC5B;YACF;QACF,EAAE,OAAOF,OAAO;YACd,IAAI,CAACzB,MAAM,CAACyB,KAAK,CACf,CAAC,+CAA+C,EAAEA,MAAM3B,OAAO,EAAE,EACjE2B,MAAMC,KAAK;YAEb,OAAO;gBACLG,SAAS;gBACTM,cAAcV,MAAM3B,OAAO,IAAI;YACjC;QACF;IACF;IAEA;;;;;;GAMC,GACD,MAAMsC,YACJC,YAAsB,EACtBvC,OAAe,EACfC,OAAa,EACa;QAC1B,IAAI;YACF,IAAI,CAACC,MAAM,CAACC,KAAK,CACf,CAAC,wBAAwB,EAAEoC,aAAaC,MAAM,CAAC,2BAA2B,CAAC;YAG7E,MAAMpC,UAAUH,SAASG,WAAW,IAAI,CAACC,cAAc;YACvD,MAAMC,SAASL,SAASK,UAAU,IAAI,CAACC,aAAa;YAEpD,mDAAmD;YACnD,IAAI,AAACH,CAAAA,YAAY,KAAKA,YAAY,CAAA,KAAM,CAACE,QAAQ;gBAC/C,MAAM,IAAIE,MACR;YAEJ;YAEA,yCAAyC;YACzC,MAAMiC,wBAAwBF,aAAaG,GAAG,CAAC,CAACC,QAC9C,IAAI,CAACjC,iBAAiB,CAACiC;YAGzB,MAAMhC,WAAW,MAAMC,IAAAA,oBAAc,EACnC,IAAI,CAACC,WAAW,CACbC,IAAI,CACH,GAAG,IAAI,CAACC,MAAM,CAAC,SAAS,CAAC,EACzB;gBACEC,IAAIyB;gBACJxB,SAASjB;gBACTkB,UAAUd;gBACVE,QAAQA;YACV,GACA;gBACEa,SAAS;oBACP,gBAAgB;oBAChBC,eAAe,CAAC,MAAM,EAAEC,OAAOC,IAAI,CAAC,IAAI,CAACC,QAAQ,GAAG,MAAMC,QAAQ,CAAC,WAAW;gBAChF;YACF,GAEDC,IAAI,CACHC,IAAAA,qBAAU,EAAC,CAACC;gBACV,IAAI,CAACzB,MAAM,CAACyB,KAAK,CACf,CAAC,sDAAsD,EAAEA,MAAM3B,OAAO,EAAE,EACxE2B,MAAMC,KAAK;gBAEb,MAAMD;YACR;YAIN,IAAIhB,SAASkB,IAAI,CAACC,MAAM,KAAK,WAAW;gBACtC,yCAAyC;gBACzC,MAAMc,UAAUH,sBAAsBC,GAAG,CAAC,CAACC;oBACzC,gEAAgE;oBAChE,MAAME,YACJlC,SAASkB,IAAI,CAACA,IAAI,CAACiB,YAAY,IAC/BnC,SAASkB,IAAI,CAACA,IAAI,CAACiB,YAAY,CAACC,QAAQ,CAACJ;oBAE3C,IAAIE,WAAW;wBACb,OAAO;4BACL9C,aAAa4C;4BACbZ,SAAS;4BACTM,cAAc;wBAChB;oBACF,OAAO;wBACL,OAAO;4BACLtC,aAAa4C;4BACbZ,SAAS;4BACTC,WAAWrB,SAASkB,IAAI,CAACA,IAAI,CAACI,MAAM,CAACT,QAAQ;wBAC/C;oBACF;gBACF;gBAEA,mCAAmC;gBACnC,MAAMwB,eAAeJ,QAAQK,MAAM,CAAC,CAACC,IAAMA,EAAEnB,OAAO,EAAES,MAAM;gBAC5D,MAAMW,eAAeP,QAAQK,MAAM,CAAC,CAACC,IAAM,CAACA,EAAEnB,OAAO,EAAES,MAAM;gBAE7D,OAAO;oBACLQ;oBACAG;oBACAP;oBACAQ,eAAezC,SAASkB,IAAI,CAACA,IAAI,CAACI,MAAM,CAACT,QAAQ;oBACjD6B,WAAW1C,SAASkB,IAAI,CAACA,IAAI,CAACyB,UAAU;oBACxCpB,aAAavB,SAASkB,IAAI;gBAC5B;YACF,OAAO;gBACL,2EAA2E;gBAC3E,MAAMe,UAAUH,sBAAsBC,GAAG,CAAC,CAACC,QAAW,CAAA;wBACpD5C,aAAa4C;wBACbZ,SAAS;wBACTI,WAAWxB,SAASkB,IAAI,CAACO,IAAI;wBAC7BC,cAAc1B,SAASkB,IAAI,CAAC7B,OAAO,IAAI;oBACzC,CAAA;gBAEA,OAAO;oBACLgD,cAAc;oBACdG,cAAcV,sBAAsBD,MAAM;oBAC1CI;oBACAV,aAAavB,SAASkB,IAAI;gBAC5B;YACF;QACF,EAAE,OAAOF,OAAO;YACd,IAAI,CAACzB,MAAM,CAACyB,KAAK,CACf,CAAC,yDAAyD,EAAEA,MAAM3B,OAAO,EAAE,EAC3E2B,MAAMC,KAAK;YAGb,sEAAsE;YACtE,MAAMgB,UAAUL,aAAaG,GAAG,CAAC,CAACC,QAAW,CAAA;oBAC3C5C,aAAa4C;oBACbZ,SAAS;oBACTM,cAAcV,MAAM3B,OAAO,IAAI;gBACjC,CAAA;YAEA,OAAO;gBACLgD,cAAc;gBACdG,cAAcZ,aAAaC,MAAM;gBACjCI;YACF;QACF;IACF;IAEA;;;;GAIC,GACD,MAAMW,mBAAmBvB,SAAiB,EAAkC;QAC1E,IAAI;YACF,IAAI,CAAC9B,MAAM,CAACC,KAAK,CACf,CAAC,kCAAkC,EAAE6B,UAAU,aAAa,CAAC;YAG/D,MAAMrB,WAAW,MAAMC,IAAAA,oBAAc,EACnC,IAAI,CAACC,WAAW,CACb2C,GAAG,CAAC,GAAG,IAAI,CAACzC,MAAM,CAAC,YAAY,EAAEiB,WAAW,EAAE;gBAC7Cb,SAAS;oBACPC,eAAe,CAAC,MAAM,EAAEC,OAAOC,IAAI,CAAC,IAAI,CAACC,QAAQ,GAAG,MAAMC,QAAQ,CAAC,WAAW;gBAChF;YACF,GACCC,IAAI,CACHC,IAAAA,qBAAU,EAAC,CAACC;gBACV,IAAI,CAACzB,MAAM,CAACyB,KAAK,CACf,CAAC,kEAAkE,EAAEA,MAAM3B,OAAO,EAAE,EACpF2B,MAAMC,KAAK;gBAEb,MAAMD;YACR;YAIN,IAAIhB,SAASkB,IAAI,CAACC,MAAM,KAAK,WAAW;gBACtC,qDAAqD;gBACrD,MAAM2B,gBAAgB,CAAC;gBAEvB,iDAAiD;gBACjD,KAAK,MAAMC,UAAU/C,SAASkB,IAAI,CAACA,IAAI,CAAE;oBACvC4B,aAAa,CAACC,OAAOf,KAAK,CAAC,GAAG,IAAI,CAACgB,iBAAiB,CAACD,OAAO5B,MAAM;gBACpE;gBAEA,8BAA8B;gBAC9B,IAAI8B,eAAeC,mCAAa,CAACC,OAAO;gBACxC,MAAMC,WAAWC,OAAOC,MAAM,CAACR;gBAE/B,IAAIM,SAASvB,MAAM,KAAK,GAAG;oBACzBoB,eAAeC,mCAAa,CAACC,OAAO;gBACtC,OAAO,IAAIC,SAASG,KAAK,CAAC,CAACC,IAAMA,MAAMN,mCAAa,CAACO,SAAS,GAAG;oBAC/DR,eAAeC,mCAAa,CAACO,SAAS;gBACxC,OAAO,IAAIL,SAASG,KAAK,CAAC,CAACC,IAAMA,MAAMN,mCAAa,CAACQ,MAAM,GAAG;oBAC5DT,eAAeC,mCAAa,CAACQ,MAAM;gBACrC,OAAO,IAAIN,SAASO,IAAI,CAAC,CAACH,IAAMA,MAAMN,mCAAa,CAACU,OAAO,GAAG;oBAC5DX,eAAeC,mCAAa,CAACU,OAAO;gBACtC,OAAO,IAAIR,SAASO,IAAI,CAAC,CAACH,IAAMA,MAAMN,mCAAa,CAACW,OAAO,GAAG;oBAC5DZ,eAAeC,mCAAa,CAACW,OAAO;gBACtC,OAAO;oBACL,eAAe;oBACfZ,eAAeC,mCAAa,CAACU,OAAO;gBACtC;gBAEA,OAAO;oBACLvC;oBACAF,QAAQ8B;oBACRa,WAAW,IAAIC;oBACfC,SAASC,KAAKC,SAAS,CAACpB;oBACxBvB,aAAavB,SAASkB,IAAI;gBAC5B;YACF,OAAO;gBACL,OAAO;oBACLG;oBACAF,QAAQ+B,mCAAa,CAACC,OAAO;oBAC7BW,WAAW,IAAIC;oBACfC,SAAShE,SAASkB,IAAI,CAAC7B,OAAO,IAAI;oBAClCkC,aAAavB,SAASkB,IAAI;gBAC5B;YACF;QACF,EAAE,OAAOF,OAAO;YACd,IAAI,CAACzB,MAAM,CAACyB,KAAK,CACf,CAAC,qEAAqE,EAAEA,MAAM3B,OAAO,EAAE,EACvF2B,MAAMC,KAAK;YAEb,OAAO;gBACLI;gBACAF,QAAQ+B,mCAAa,CAACC,OAAO;gBAC7BW,WAAW,IAAIC;gBACfC,SAAShD,MAAM3B,OAAO,IAAI;YAC5B;QACF;IACF;IAEA;;;;;;;GAOC,GACD,MAAM8E,iBACJ/E,WAAmB,EACnBC,OAAe,EACf+E,SAAiB,EACjB9E,OAAa,EACS;QACtB,2DAA2D;QAC3D,OAAO,IAAI,CAACH,OAAO,CAACC,aAAaC,SAAS;YACxC,GAAGC,OAAO;YACVG,SAAS;YACTE,QAAQyE;QACV;IACF;IAEA;;;;GAIC,GACD,MAAMC,eACJC,MAAsB,EACW;QACjC,IAAI;YACF,IAAI,CAAC/E,MAAM,CAACC,KAAK,CAAC;YAElB,MAAMoB,WAAW0D,OAAO1D,QAAQ,IAAI,IAAI,CAACA,QAAQ;YACjD,MAAMR,SAASkE,OAAOlE,MAAM,IAAI,IAAI,CAACA,MAAM;YAE3C,IAAI,CAACQ,UAAU;gBACb,OAAO;oBACLQ,SAAS;oBACT/B,SAAS;gBACX;YACF;YAEA,MAAMW,WAAW,MAAMC,IAAAA,oBAAc,EACnC,IAAI,CAACC,WAAW,CACb2C,GAAG,CAAC,GAAGzC,OAAO,UAAU,CAAC,EAAE;gBAC1BI,SAAS;oBACPC,eAAe,CAAC,MAAM,EAAEC,OAAOC,IAAI,CAACC,WAAW,MAAMC,QAAQ,CAAC,WAAW;gBAC3E;YACF,GACCC,IAAI,CACHC,IAAAA,qBAAU,EAAC,CAACC;gBACV,IAAI,CAACzB,MAAM,CAACyB,KAAK,CACf,CAAC,gDAAgD,EAAEA,MAAM3B,OAAO,EAAE,EAClE2B,MAAMC,KAAK;gBAEb,MAAMD;YACR;YAIN,IAAIhB,SAASkB,IAAI,CAACC,MAAM,KAAK,WAAW;gBACtC,OAAO;oBACLC,SAAS;oBACT/B,SAAS;oBACT2E,SAAS;wBACPO,OAAOvE,SAASkB,IAAI,CAACA,IAAI,CAACqD,KAAK;wBAC/BC,SAASxE,SAASkB,IAAI,CAACA,IAAI,CAACsD,OAAO;wBACnCC,UAAUzE,SAASkB,IAAI,CAACA,IAAI,CAACuD,QAAQ;oBACvC;gBACF;YACF,OAAO;gBACL,OAAO;oBACLrD,SAAS;oBACT/B,SAASW,SAASkB,IAAI,CAAC7B,OAAO,IAAI;oBAClC2E,SAAShE,SAASkB,IAAI;gBACxB;YACF;QACF,EAAE,OAAOF,OAAO;YACd,IAAI,CAACzB,MAAM,CAACyB,KAAK,CACf,CAAC,mDAAmD,EAAEA,MAAM3B,OAAO,EAAE,EACrE2B,MAAMC,KAAK;YAEb,OAAO;gBACLG,SAAS;gBACT/B,SAAS2B,MAAM3B,OAAO,IAAI;YAC5B;QACF;IACF;IAEA;;;;GAIC,GACD,AAAQ2D,kBAAkB0B,cAAsB,EAAiB;QAC/D,OAAQA;YACN,KAAK;gBACH,OAAOxB,mCAAa,CAACW,OAAO,EAAE,aAAa;YAC7C,KAAK,CAAC;gBACJ,OAAOX,mCAAa,CAACU,OAAO,EAAE,mBAAmB;YACnD,KAAK;gBACH,OAAOV,mCAAa,CAACO,SAAS,EAAE,QAAQ;YAC1C,KAAK;gBACH,OAAOP,mCAAa,CAACQ,MAAM,EAAE,QAAQ;YACvC;gBACE,OAAOR,mCAAa,CAACC,OAAO,EAAE,UAAU;QAC5C;IACF;IAxaAwB,YACE,AAAiBzE,WAAwB,EACzC,AAAiB0E,aAA4B,CAC7C;QACA,KAAK,CAAC,0BAHW1E,cAAAA,kBACA0E,gBAAAA,oBATVC,eAAe;QAatB,mGAAmG;QACnG,IAAI,CAACjE,QAAQ,GAAG,IAAI,CAACgE,aAAa,CAAC/B,GAAG,CAAS,0BAA0B;QACzE,IAAI,CAACzC,MAAM,GACT,IAAI,CAACwE,aAAa,CAAC/B,GAAG,CAAS,wBAC/B;QACF,IAAI,CAACnD,cAAc,GAAG,IAAI,CAACkF,aAAa,CAAC/B,GAAG,CAAS,qBAAqB,GAAG,mBAAmB;QAChG,IAAI,CAACjD,aAAa,GAChB,IAAI,CAACgF,aAAa,CAAC/B,GAAG,CAAS,uBAAuB;IAC1D;AA2ZF"}