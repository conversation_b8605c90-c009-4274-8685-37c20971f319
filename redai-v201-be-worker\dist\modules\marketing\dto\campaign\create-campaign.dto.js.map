{"version": 3, "sources": ["../../../../../src/modules/marketing/dto/campaign/create-campaign.dto.ts"], "sourcesContent": ["import {\r\n  IsIn,\r\n  IsNotEmpty,\r\n  <PERSON><PERSON><PERSON>ber,\r\n  IsOptional,\r\n  IsString,\r\n  ValidateNested,\r\n} from 'class-validator';\r\nimport { ApiProperty } from '@nestjs/swagger';\r\nimport { Type } from 'class-transformer';\r\nimport { CampaignServerDto } from './campaign-server.dto';\r\n\r\n/**\r\n * Enum cho các nền tảng gửi\r\n */\r\nexport enum CampaignPlatform {\r\n  EMAIL = 'email',\r\n  SMS = 'sms',\r\n  PUSH = 'push',\r\n}\r\n\r\n/**\r\n * DTO cho việc tạo campaign mới\r\n */\r\nexport class CreateCampaignDto {\r\n  /**\r\n   * Tiêu đề chiến dịch\r\n   * @example \"Khuyến mãi tháng 5\"\r\n   */\r\n  @ApiProperty({\r\n    description: 'Tiêu đề chiến dịch',\r\n    example: 'Khuyến mãi tháng 5',\r\n  })\r\n  @IsNotEmpty({ message: 'Tiêu đề không được để trống' })\r\n  @IsString({ message: 'Tiêu đề phải là chuỗi' })\r\n  title: string;\r\n\r\n  /**\r\n   * <PERSON>ô tả chiến dịch\r\n   * @example \"Chiến dịch khuyến mãi dành cho khách hàng VIP\"\r\n   */\r\n  @ApiProperty({\r\n    description: 'Mô tả chiến dịch',\r\n    example: 'Chiến dịch khuyến mãi dành cho khách hàng VIP',\r\n    required: false,\r\n  })\r\n  @IsOptional()\r\n  @IsString({ message: 'Mô tả phải là chuỗi' })\r\n  description?: string;\r\n\r\n  /**\r\n   * Nền tảng gửi\r\n   * @example \"email\"\r\n   */\r\n  @ApiProperty({\r\n    description: 'Nền tảng gửi',\r\n    enum: CampaignPlatform,\r\n    example: CampaignPlatform.EMAIL,\r\n  })\r\n  @IsNotEmpty({ message: 'Nền tảng không được để trống' })\r\n  @IsIn(Object.values(CampaignPlatform), {\r\n    message: `Nền tảng phải là một trong các giá trị: ${Object.values(CampaignPlatform).join(', ')}`,\r\n  })\r\n  platform: CampaignPlatform;\r\n\r\n  /**\r\n   * Nội dung chiến dịch\r\n   * @example \"<p>Xin chào quý khách,</p><p>Chúng tôi xin gửi đến quý khách chương trình khuyến mãi...</p>\"\r\n   */\r\n  @ApiProperty({\r\n    description: 'Nội dung chiến dịch',\r\n    example:\r\n      '<p>Xin chào quý khách,</p><p>Chúng tôi xin gửi đến quý khách chương trình khuyến mãi...</p>',\r\n  })\r\n  @IsNotEmpty({ message: 'Nội dung không được để trống' })\r\n  @IsString({ message: 'Nội dung phải là chuỗi' })\r\n  content: string;\r\n\r\n  /**\r\n   * Thông tin máy chủ gửi\r\n   */\r\n  @ApiProperty({\r\n    description: 'Thông tin máy chủ gửi',\r\n    type: CampaignServerDto,\r\n  })\r\n  @IsNotEmpty({ message: 'Thông tin máy chủ không được để trống' })\r\n  @ValidateNested()\r\n  @Type(() => CampaignServerDto)\r\n  server: CampaignServerDto;\r\n\r\n  /**\r\n   * Thời gian dự kiến gửi chiến dịch (Unix timestamp)\r\n   * @example 1619171200\r\n   */\r\n  @ApiProperty({\r\n    description: 'Thời gian dự kiến gửi chiến dịch (Unix timestamp)',\r\n    example: 1619171200,\r\n    required: false,\r\n  })\r\n  @IsOptional()\r\n  @IsNumber({}, { message: 'Thời gian dự kiến phải là số' })\r\n  scheduledAt?: number;\r\n\r\n  /**\r\n   * Tiêu đề email (chỉ áp dụng cho chiến dịch email)\r\n   * @example \"Khuyến mãi đặc biệt dành cho bạn\"\r\n   */\r\n  @ApiProperty({\r\n    description: 'Tiêu đề email (chỉ áp dụng cho chiến dịch email)',\r\n    example: 'Khuyến mãi đặc biệt dành cho bạn',\r\n    required: false,\r\n  })\r\n  @IsOptional()\r\n  @IsString({ message: 'Tiêu đề email phải là chuỗi' })\r\n  subject?: string;\r\n\r\n  /**\r\n   * ID của segment hoặc danh sách ID của audience\r\n   */\r\n  @ApiProperty({\r\n    description: 'ID của segment',\r\n    example: 1,\r\n    required: false,\r\n  })\r\n  @IsOptional()\r\n  @IsNumber({}, { message: 'ID segment phải là số' })\r\n  segmentId?: number;\r\n\r\n  /**\r\n   * Danh sách ID của audience\r\n   * @example [1, 2, 3]\r\n   */\r\n  @ApiProperty({\r\n    description: 'Danh sách ID của audience',\r\n    example: [1, 2, 3],\r\n    required: false,\r\n    type: [Number],\r\n  })\r\n  @IsOptional()\r\n  audienceIds?: number[];\r\n}\r\n"], "names": ["CampaignPlatform", "CreateCampaignDto", "description", "example", "message", "required", "enum", "values", "Object", "join", "type", "CampaignServerDto", "Number"], "mappings": ";;;;;;;;;;;QAeYA;eAAAA;;QASCC;eAAAA;;;gCAjBN;yBACqB;kCACP;mCACa;;;;;;;;;;AAK3B,IAAA,AAAKD,0CAAAA;;;;WAAAA;;AASL,IAAA,AAAMC,oBAAN,MAAMA;AAoHb;;;QA9GIC,aAAa;QACbC,SAAS;;;QAEGC,SAAS;;;QACXA,SAAS;;;;;;QAQnBF,aAAa;QACbC,SAAS;QACTE,UAAU;;;;QAGAD,SAAS;;;;;;QAQnBF,aAAa;QACbI,MAAMN;QACNG,OAAO;;;QAEKC,SAAS;;qCACVG;QACXH,SAAS,CAAC,wCAAwC,EAAEI,OAAOD,MAAM,CAACP,kBAAkBS,IAAI,CAAC,OAAO;;;;;;QAShGP,aAAa;QACbC,SACE;;;QAEUC,SAAS;;;QACXA,SAAS;;;;;;QAOnBF,aAAa;QACbQ,MAAMC,oCAAiB;;;QAEXP,SAAS;;;oCAEXO,oCAAiB;;;;;QAQ3BT,aAAa;QACbC,SAAS;QACTE,UAAU;;;;QAGID,SAAS;;;;;;QAQvBF,aAAa;QACbC,SAAS;QACTE,UAAU;;;;QAGAD,SAAS;;;;;;QAOnBF,aAAa;QACbC,SAAS;QACTE,UAAU;;;;QAGID,SAAS;;;;;;QAQvBF,aAAa;QACbC,SAAS;YAAC;YAAG;YAAG;SAAE;QAClBE,UAAU;QACVK,MAAM;YAACE;SAAO"}