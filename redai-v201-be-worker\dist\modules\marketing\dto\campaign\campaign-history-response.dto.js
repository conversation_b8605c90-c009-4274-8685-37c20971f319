"use strict";
Object.defineProperty(exports, "__esModule", {
    value: true
});
function _export(target, all) {
    for(var name in all)Object.defineProperty(target, name, {
        enumerable: true,
        get: Object.getOwnPropertyDescriptor(all, name).get
    });
}
_export(exports, {
    get CampaignHistoryResponseDto () {
        return CampaignHistoryResponseDto;
    },
    get SendStatus () {
        return SendStatus;
    }
});
const _swagger = require("@nestjs/swagger");
function _ts_decorate(decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for(var i = decorators.length - 1; i >= 0; i--)if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
}
function _ts_metadata(k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
}
var SendStatus = /*#__PURE__*/ function(SendStatus) {
    SendStatus["PENDING"] = "pending";
    SendStatus["SENT"] = "sent";
    SendStatus["DELIVERED"] = "delivered";
    SendStatus["FAILED"] = "failed";
    SendStatus["OPENED"] = "opened";
    SendStatus["CLICKED"] = "clicked";
    return SendStatus;
}({});
let CampaignHistoryResponseDto = class CampaignHistoryResponseDto {
};
_ts_decorate([
    (0, _swagger.ApiProperty)({
        description: 'ID của lịch sử',
        example: 1
    }),
    _ts_metadata("design:type", Number)
], CampaignHistoryResponseDto.prototype, "id", void 0);
_ts_decorate([
    (0, _swagger.ApiProperty)({
        description: 'ID của campaign',
        example: 1
    }),
    _ts_metadata("design:type", Number)
], CampaignHistoryResponseDto.prototype, "campaignId", void 0);
_ts_decorate([
    (0, _swagger.ApiProperty)({
        description: 'ID của audience',
        example: 1
    }),
    _ts_metadata("design:type", Number)
], CampaignHistoryResponseDto.prototype, "audienceId", void 0);
_ts_decorate([
    (0, _swagger.ApiProperty)({
        description: 'Trạng thái gửi',
        enum: SendStatus,
        example: "sent"
    }),
    _ts_metadata("design:type", String)
], CampaignHistoryResponseDto.prototype, "status", void 0);
_ts_decorate([
    (0, _swagger.ApiProperty)({
        description: 'Thời gian gửi (Unix timestamp)',
        example: 1619171200
    }),
    _ts_metadata("design:type", Number)
], CampaignHistoryResponseDto.prototype, "sentAt", void 0);
_ts_decorate([
    (0, _swagger.ApiProperty)({
        description: 'Thời gian tạo (Unix timestamp)',
        example: 1619171200
    }),
    _ts_metadata("design:type", Number)
], CampaignHistoryResponseDto.prototype, "createdAt", void 0);

//# sourceMappingURL=campaign-history-response.dto.js.map