"use strict";
Object.defineProperty(exports, "__esModule", {
    value: true
});
Object.defineProperty(exports, "OfficialAccountResponseDto", {
    enumerable: true,
    get: function() {
        return OfficialAccountResponseDto;
    }
});
const _swagger = require("@nestjs/swagger");
function _ts_decorate(decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for(var i = decorators.length - 1; i >= 0; i--)if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
}
function _ts_metadata(k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
}
let OfficialAccountResponseDto = class OfficialAccountResponseDto {
};
_ts_decorate([
    (0, _swagger.ApiProperty)({
        description: 'ID của Official Account trong hệ thống',
        example: 1
    }),
    _ts_metadata("design:type", Number)
], OfficialAccountResponseDto.prototype, "id", void 0);
_ts_decorate([
    (0, _swagger.ApiProperty)({
        description: 'ID của Official Account trên Zalo',
        example: '*********'
    }),
    _ts_metadata("design:type", String)
], OfficialAccountResponseDto.prototype, "oaId", void 0);
_ts_decorate([
    (0, _swagger.ApiProperty)({
        description: 'Tên của Official Account',
        example: 'RedAI Official'
    }),
    _ts_metadata("design:type", String)
], OfficialAccountResponseDto.prototype, "name", void 0);
_ts_decorate([
    (0, _swagger.ApiProperty)({
        description: 'Mô tả của Official Account',
        example: 'Kênh chính thức của RedAI',
        nullable: true
    }),
    _ts_metadata("design:type", String)
], OfficialAccountResponseDto.prototype, "description", void 0);
_ts_decorate([
    (0, _swagger.ApiProperty)({
        description: 'URL avatar của Official Account',
        example: 'https://zalo.me/avatar/*********.jpg',
        nullable: true
    }),
    _ts_metadata("design:type", String)
], OfficialAccountResponseDto.prototype, "avatarUrl", void 0);
_ts_decorate([
    (0, _swagger.ApiProperty)({
        description: 'Trạng thái kết nối (active, inactive)',
        example: 'active'
    }),
    _ts_metadata("design:type", String)
], OfficialAccountResponseDto.prototype, "status", void 0);
_ts_decorate([
    (0, _swagger.ApiProperty)({
        description: 'Thời điểm tạo (Unix timestamp)',
        example: *************
    }),
    _ts_metadata("design:type", Number)
], OfficialAccountResponseDto.prototype, "createdAt", void 0);
_ts_decorate([
    (0, _swagger.ApiProperty)({
        description: 'Thời điểm cập nhật (Unix timestamp)',
        example: *************
    }),
    _ts_metadata("design:type", Number)
], OfficialAccountResponseDto.prototype, "updatedAt", void 0);

//# sourceMappingURL=official-account-response.dto.js.map