{"version": 3, "sources": ["../../../../src/shared/services/sms/sms.service.ts"], "sourcesContent": ["import { Injectable, Logger } from '@nestjs/common';\r\nimport { ConfigService } from '@nestjs/config';\r\nimport {\r\n  SmsProviderFactory,\r\n  SmsProviderType,\r\n} from './sms-provider-factory.service';\r\nimport {\r\n  SmsResponse,\r\n  BulkSmsResponse,\r\n  MessageStatusResponse,\r\n  ConnectionTestResponse,\r\n} from './sms-provider.interface';\r\n\r\n/**\r\n * Service principal pour l'envoi de SMS\r\n */\r\n@Injectable()\r\nexport class SmsService {\r\n  private readonly logger = new Logger(SmsService.name);\r\n  private readonly defaultProviderType: SmsProviderType;\r\n\r\n  constructor(\r\n    private readonly smsProviderFactory: SmsProviderFactory,\r\n    private readonly configService: ConfigService,\r\n  ) {\r\n    // Charger le type de fournisseur par défaut depuis la configuration\r\n    const defaultProvider =\r\n      this.configService.get<string>('SMS_DEFAULT_PROVIDER') || 'SPEED_SMS';\r\n    this.defaultProviderType =\r\n      SmsProviderType[defaultProvider] || SmsProviderType.SPEED_SMS;\r\n\r\n    this.logger.log(`Fournisseur SMS par défaut: ${this.defaultProviderType}`);\r\n  }\r\n\r\n  /**\r\n   * Envoie un SMS à un numéro de téléphone\r\n   * @param phoneNumber Numéro de téléphone du destinataire\r\n   * @param message Contenu du message\r\n   * @param options Options supplémentaires\r\n   * @returns Promesse contenant l'ID du message ou une erreur\r\n   */\r\n  async sendSms(\r\n    phoneNumber: string,\r\n    message: string,\r\n    options?: {\r\n      providerType?: SmsProviderType;\r\n      [key: string]: any;\r\n    },\r\n  ): Promise<SmsResponse> {\r\n    const providerType = options?.providerType || this.defaultProviderType;\r\n\r\n    try {\r\n      this.logger.debug(`Envoi d'un SMS à ${phoneNumber} via ${providerType}`);\r\n\r\n      const provider = this.smsProviderFactory.createProvider(providerType);\r\n\r\n      // Supprimer providerType des options pour éviter les conflits\r\n      const { providerType: _, ...providerOptions } = options || {};\r\n\r\n      return await provider.sendSms(phoneNumber, message, providerOptions);\r\n    } catch (error) {\r\n      this.logger.error(\r\n        `Erreur lors de l'envoi du SMS à ${phoneNumber}: ${error.message}`,\r\n        error.stack,\r\n      );\r\n      return {\r\n        success: false,\r\n        errorMessage: error.message || 'Erreur inconnue',\r\n      };\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Envoie un SMS à plusieurs numéros de téléphone\r\n   * @param phoneNumbers Liste des numéros de téléphone des destinataires\r\n   * @param message Contenu du message\r\n   * @param options Options supplémentaires\r\n   * @returns Promesse contenant les résultats pour chaque destinataire\r\n   */\r\n  async sendBulkSms(\r\n    phoneNumbers: string[],\r\n    message: string,\r\n    options?: {\r\n      providerType?: SmsProviderType;\r\n      [key: string]: any;\r\n    },\r\n  ): Promise<BulkSmsResponse> {\r\n    const providerType = options?.providerType || this.defaultProviderType;\r\n\r\n    try {\r\n      this.logger.debug(\r\n        `Envoi de SMS en masse à ${phoneNumbers.length} destinataires via ${providerType}`,\r\n      );\r\n\r\n      const provider = this.smsProviderFactory.createProvider(providerType);\r\n\r\n      // Supprimer providerType des options pour éviter les conflits\r\n      const { providerType: _, ...providerOptions } = options || {};\r\n\r\n      return await provider.sendBulkSms(phoneNumbers, message, providerOptions);\r\n    } catch (error) {\r\n      this.logger.error(\r\n        `Erreur lors de l'envoi des SMS en masse: ${error.message}`,\r\n        error.stack,\r\n      );\r\n\r\n      // En cas d'exception, tous les messages sont considérés comme échoués\r\n      const results = phoneNumbers.map((phone) => ({\r\n        phoneNumber: phone,\r\n        success: false,\r\n        errorMessage: error.message || 'Erreur inconnue',\r\n      }));\r\n\r\n      return {\r\n        successCount: 0,\r\n        failureCount: phoneNumbers.length,\r\n        results,\r\n      };\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Vérifie le statut d'un message envoyé\r\n   * @param messageId ID du message à vérifier\r\n   * @param providerType Type de fournisseur SMS\r\n   * @returns Promesse contenant le statut du message\r\n   */\r\n  async checkMessageStatus(\r\n    messageId: string,\r\n    providerType: SmsProviderType = this.defaultProviderType,\r\n  ): Promise<MessageStatusResponse> {\r\n    try {\r\n      this.logger.debug(\r\n        `Vérification du statut du message ${messageId} via ${providerType}`,\r\n      );\r\n\r\n      const provider = this.smsProviderFactory.createProvider(providerType);\r\n\r\n      return await provider.checkMessageStatus(messageId);\r\n    } catch (error) {\r\n      this.logger.error(\r\n        `Erreur lors de la vérification du statut du message ${messageId}: ${error.message}`,\r\n        error.stack,\r\n      );\r\n      throw error;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Envoie un SMS avec un brandname\r\n   * @param phoneNumber Numéro de téléphone du destinataire\r\n   * @param message Contenu du message\r\n   * @param brandname Nom de la marque à utiliser comme expéditeur\r\n   * @param options Options supplémentaires\r\n   * @returns Promesse contenant l'ID du message ou une erreur\r\n   */\r\n  async sendBrandnameSms(\r\n    phoneNumber: string,\r\n    message: string,\r\n    brandname: string,\r\n    options?: {\r\n      providerType?: SmsProviderType;\r\n      [key: string]: any;\r\n    },\r\n  ): Promise<SmsResponse> {\r\n    const providerType = options?.providerType || this.defaultProviderType;\r\n\r\n    try {\r\n      this.logger.debug(\r\n        `Envoi d'un SMS brandname à ${phoneNumber} via ${providerType}`,\r\n      );\r\n\r\n      const provider = this.smsProviderFactory.createProvider(providerType);\r\n\r\n      // Supprimer providerType des options pour éviter les conflits\r\n      const { providerType: _, ...providerOptions } = options || {};\r\n\r\n      return await provider.sendBrandnameSms(\r\n        phoneNumber,\r\n        message,\r\n        brandname,\r\n        providerOptions,\r\n      );\r\n    } catch (error) {\r\n      this.logger.error(\r\n        `Erreur lors de l'envoi du SMS brandname à ${phoneNumber}: ${error.message}`,\r\n        error.stack,\r\n      );\r\n      return {\r\n        success: false,\r\n        errorMessage: error.message || 'Erreur inconnue',\r\n      };\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Envoie un SMS OTP (One-Time Password)\r\n   * @param phoneNumber Numéro de téléphone du destinataire\r\n   * @param otpCode Code OTP à envoyer\r\n   * @param options Options supplémentaires\r\n   * @returns Promesse contenant l'ID du message ou une erreur\r\n   */\r\n  async sendOtp(\r\n    phoneNumber: string,\r\n    otpCode: string,\r\n    options?: {\r\n      providerType?: SmsProviderType;\r\n      template?: string;\r\n      [key: string]: any;\r\n    },\r\n  ): Promise<SmsResponse> {\r\n    const providerType = options?.providerType || this.defaultProviderType;\r\n\r\n    try {\r\n      this.logger.debug(\r\n        `Envoi d'un code OTP à ${phoneNumber} via ${providerType}`,\r\n      );\r\n\r\n      const provider = this.smsProviderFactory.createProvider(providerType);\r\n\r\n      // Supprimer providerType des options pour éviter les conflits\r\n      const { providerType: _, ...providerOptions } = options || {};\r\n\r\n      return await provider.sendOtp(phoneNumber, otpCode, providerOptions);\r\n    } catch (error) {\r\n      this.logger.error(\r\n        `Erreur lors de l'envoi du code OTP à ${phoneNumber}: ${error.message}`,\r\n        error.stack,\r\n      );\r\n      return {\r\n        success: false,\r\n        errorMessage: error.message || 'Erreur inconnue',\r\n      };\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Teste la connexion avec un fournisseur SMS\r\n   * @param providerType Type de fournisseur SMS\r\n   * @param config Configuration du fournisseur\r\n   * @returns Promesse indiquant si la connexion est réussie\r\n   */\r\n  async testConnection(\r\n    providerType: SmsProviderType = this.defaultProviderType,\r\n    config: any = {},\r\n  ): Promise<ConnectionTestResponse> {\r\n    try {\r\n      this.logger.debug(`Test de connexion avec ${providerType}`);\r\n\r\n      const provider = this.smsProviderFactory.createProvider(providerType);\r\n\r\n      return await provider.testConnection(config);\r\n    } catch (error) {\r\n      this.logger.error(\r\n        `Erreur lors du test de connexion avec ${providerType}: ${error.message}`,\r\n        error.stack,\r\n      );\r\n      return {\r\n        success: false,\r\n        message: error.message || 'Erreur inconnue',\r\n      };\r\n    }\r\n  }\r\n}\r\n"], "names": ["SmsService", "sendSms", "phoneNumber", "message", "options", "providerType", "defaultProviderType", "logger", "debug", "provider", "smsProviderFactory", "createProvider", "_", "providerOptions", "error", "stack", "success", "errorMessage", "sendBulkSms", "phoneNumbers", "length", "results", "map", "phone", "successCount", "failureCount", "checkMessageStatus", "messageId", "sendBrandnameSms", "brandname", "sendOtp", "otpCode", "testConnection", "config", "constructor", "configService", "<PERSON><PERSON>", "name", "defaultProvider", "get", "SmsProviderType", "SPEED_SMS", "log"], "mappings": ";;;;+BAiBaA;;;eAAAA;;;wBAjBsB;wBACL;2CAIvB;;;;;;;;;;AAYA,IAAA,AAAMA,aAAN,MAAMA;IAiBX;;;;;;GAMC,GACD,MAAMC,QACJC,WAAmB,EACnBC,OAAe,EACfC,OAGC,EACqB;QACtB,MAAMC,eAAeD,SAASC,gBAAgB,IAAI,CAACC,mBAAmB;QAEtE,IAAI;YACF,IAAI,CAACC,MAAM,CAACC,KAAK,CAAC,CAAC,iBAAiB,EAAEN,YAAY,KAAK,EAAEG,cAAc;YAEvE,MAAMI,WAAW,IAAI,CAACC,kBAAkB,CAACC,cAAc,CAACN;YAExD,8DAA8D;YAC9D,MAAM,EAAEA,cAAcO,CAAC,EAAE,GAAGC,iBAAiB,GAAGT,WAAW,CAAC;YAE5D,OAAO,MAAMK,SAASR,OAAO,CAACC,aAAaC,SAASU;QACtD,EAAE,OAAOC,OAAO;YACd,IAAI,CAACP,MAAM,CAACO,KAAK,CACf,CAAC,gCAAgC,EAAEZ,YAAY,EAAE,EAAEY,MAAMX,OAAO,EAAE,EAClEW,MAAMC,KAAK;YAEb,OAAO;gBACLC,SAAS;gBACTC,cAAcH,MAAMX,OAAO,IAAI;YACjC;QACF;IACF;IAEA;;;;;;GAMC,GACD,MAAMe,YACJC,YAAsB,EACtBhB,OAAe,EACfC,OAGC,EACyB;QAC1B,MAAMC,eAAeD,SAASC,gBAAgB,IAAI,CAACC,mBAAmB;QAEtE,IAAI;YACF,IAAI,CAACC,MAAM,CAACC,KAAK,CACf,CAAC,wBAAwB,EAAEW,aAAaC,MAAM,CAAC,mBAAmB,EAAEf,cAAc;YAGpF,MAAMI,WAAW,IAAI,CAACC,kBAAkB,CAACC,cAAc,CAACN;YAExD,8DAA8D;YAC9D,MAAM,EAAEA,cAAcO,CAAC,EAAE,GAAGC,iBAAiB,GAAGT,WAAW,CAAC;YAE5D,OAAO,MAAMK,SAASS,WAAW,CAACC,cAAchB,SAASU;QAC3D,EAAE,OAAOC,OAAO;YACd,IAAI,CAACP,MAAM,CAACO,KAAK,CACf,CAAC,yCAAyC,EAAEA,MAAMX,OAAO,EAAE,EAC3DW,MAAMC,KAAK;YAGb,sEAAsE;YACtE,MAAMM,UAAUF,aAAaG,GAAG,CAAC,CAACC,QAAW,CAAA;oBAC3CrB,aAAaqB;oBACbP,SAAS;oBACTC,cAAcH,MAAMX,OAAO,IAAI;gBACjC,CAAA;YAEA,OAAO;gBACLqB,cAAc;gBACdC,cAAcN,aAAaC,MAAM;gBACjCC;YACF;QACF;IACF;IAEA;;;;;GAKC,GACD,MAAMK,mBACJC,SAAiB,EACjBtB,eAAgC,IAAI,CAACC,mBAAmB,EACxB;QAChC,IAAI;YACF,IAAI,CAACC,MAAM,CAACC,KAAK,CACf,CAAC,kCAAkC,EAAEmB,UAAU,KAAK,EAAEtB,cAAc;YAGtE,MAAMI,WAAW,IAAI,CAACC,kBAAkB,CAACC,cAAc,CAACN;YAExD,OAAO,MAAMI,SAASiB,kBAAkB,CAACC;QAC3C,EAAE,OAAOb,OAAO;YACd,IAAI,CAACP,MAAM,CAACO,KAAK,CACf,CAAC,oDAAoD,EAAEa,UAAU,EAAE,EAAEb,MAAMX,OAAO,EAAE,EACpFW,MAAMC,KAAK;YAEb,MAAMD;QACR;IACF;IAEA;;;;;;;GAOC,GACD,MAAMc,iBACJ1B,WAAmB,EACnBC,OAAe,EACf0B,SAAiB,EACjBzB,OAGC,EACqB;QACtB,MAAMC,eAAeD,SAASC,gBAAgB,IAAI,CAACC,mBAAmB;QAEtE,IAAI;YACF,IAAI,CAACC,MAAM,CAACC,KAAK,CACf,CAAC,2BAA2B,EAAEN,YAAY,KAAK,EAAEG,cAAc;YAGjE,MAAMI,WAAW,IAAI,CAACC,kBAAkB,CAACC,cAAc,CAACN;YAExD,8DAA8D;YAC9D,MAAM,EAAEA,cAAcO,CAAC,EAAE,GAAGC,iBAAiB,GAAGT,WAAW,CAAC;YAE5D,OAAO,MAAMK,SAASmB,gBAAgB,CACpC1B,aACAC,SACA0B,WACAhB;QAEJ,EAAE,OAAOC,OAAO;YACd,IAAI,CAACP,MAAM,CAACO,KAAK,CACf,CAAC,0CAA0C,EAAEZ,YAAY,EAAE,EAAEY,MAAMX,OAAO,EAAE,EAC5EW,MAAMC,KAAK;YAEb,OAAO;gBACLC,SAAS;gBACTC,cAAcH,MAAMX,OAAO,IAAI;YACjC;QACF;IACF;IAEA;;;;;;GAMC,GACD,MAAM2B,QACJ5B,WAAmB,EACnB6B,OAAe,EACf3B,OAIC,EACqB;QACtB,MAAMC,eAAeD,SAASC,gBAAgB,IAAI,CAACC,mBAAmB;QAEtE,IAAI;YACF,IAAI,CAACC,MAAM,CAACC,KAAK,CACf,CAAC,sBAAsB,EAAEN,YAAY,KAAK,EAAEG,cAAc;YAG5D,MAAMI,WAAW,IAAI,CAACC,kBAAkB,CAACC,cAAc,CAACN;YAExD,8DAA8D;YAC9D,MAAM,EAAEA,cAAcO,CAAC,EAAE,GAAGC,iBAAiB,GAAGT,WAAW,CAAC;YAE5D,OAAO,MAAMK,SAASqB,OAAO,CAAC5B,aAAa6B,SAASlB;QACtD,EAAE,OAAOC,OAAO;YACd,IAAI,CAACP,MAAM,CAACO,KAAK,CACf,CAAC,qCAAqC,EAAEZ,YAAY,EAAE,EAAEY,MAAMX,OAAO,EAAE,EACvEW,MAAMC,KAAK;YAEb,OAAO;gBACLC,SAAS;gBACTC,cAAcH,MAAMX,OAAO,IAAI;YACjC;QACF;IACF;IAEA;;;;;GAKC,GACD,MAAM6B,eACJ3B,eAAgC,IAAI,CAACC,mBAAmB,EACxD2B,SAAc,CAAC,CAAC,EACiB;QACjC,IAAI;YACF,IAAI,CAAC1B,MAAM,CAACC,KAAK,CAAC,CAAC,uBAAuB,EAAEH,cAAc;YAE1D,MAAMI,WAAW,IAAI,CAACC,kBAAkB,CAACC,cAAc,CAACN;YAExD,OAAO,MAAMI,SAASuB,cAAc,CAACC;QACvC,EAAE,OAAOnB,OAAO;YACd,IAAI,CAACP,MAAM,CAACO,KAAK,CACf,CAAC,sCAAsC,EAAET,aAAa,EAAE,EAAES,MAAMX,OAAO,EAAE,EACzEW,MAAMC,KAAK;YAEb,OAAO;gBACLC,SAAS;gBACTb,SAASW,MAAMX,OAAO,IAAI;YAC5B;QACF;IACF;IAjPA+B,YACE,AAAiBxB,kBAAsC,EACvD,AAAiByB,aAA4B,CAC7C;aAFiBzB,qBAAAA;aACAyB,gBAAAA;aALF5B,SAAS,IAAI6B,cAAM,CAACpC,WAAWqC,IAAI;QAOlD,oEAAoE;QACpE,MAAMC,kBACJ,IAAI,CAACH,aAAa,CAACI,GAAG,CAAS,2BAA2B;QAC5D,IAAI,CAACjC,mBAAmB,GACtBkC,0CAAe,CAACF,gBAAgB,IAAIE,0CAAe,CAACC,SAAS;QAE/D,IAAI,CAAClC,MAAM,CAACmC,GAAG,CAAC,CAAC,4BAA4B,EAAE,IAAI,CAACpC,mBAAmB,EAAE;IAC3E;AAuOF"}