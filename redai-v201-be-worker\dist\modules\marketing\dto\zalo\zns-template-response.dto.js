"use strict";
Object.defineProperty(exports, "__esModule", {
    value: true
});
Object.defineProperty(exports, "ZnsTemplateResponseDto", {
    enumerable: true,
    get: function() {
        return ZnsTemplateResponseDto;
    }
});
const _swagger = require("@nestjs/swagger");
function _ts_decorate(decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for(var i = decorators.length - 1; i >= 0; i--)if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
}
function _ts_metadata(k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
}
let ZnsTemplateResponseDto = class ZnsTemplateResponseDto {
};
_ts_decorate([
    (0, _swagger.ApiProperty)({
        description: 'ID của template trong hệ thống',
        example: 1
    }),
    _ts_metadata("design:type", Number)
], ZnsTemplateResponseDto.prototype, "id", void 0);
_ts_decorate([
    (0, _swagger.ApiProperty)({
        description: 'ID của Official Account',
        example: '*********'
    }),
    _ts_metadata("design:type", String)
], ZnsTemplateResponseDto.prototype, "oaId", void 0);
_ts_decorate([
    (0, _swagger.ApiProperty)({
        description: 'ID của template trên Zalo',
        example: 'template*********'
    }),
    _ts_metadata("design:type", String)
], ZnsTemplateResponseDto.prototype, "templateId", void 0);
_ts_decorate([
    (0, _swagger.ApiProperty)({
        description: 'Tên của template',
        example: 'Thông báo đơn hàng'
    }),
    _ts_metadata("design:type", String)
], ZnsTemplateResponseDto.prototype, "templateName", void 0);
_ts_decorate([
    (0, _swagger.ApiProperty)({
        description: 'Nội dung của template',
        example: 'Đơn hàng #{orderId} của bạn đã được xác nhận. Cảm ơn bạn đã mua hàng tại {shopName}.'
    }),
    _ts_metadata("design:type", String)
], ZnsTemplateResponseDto.prototype, "templateContent", void 0);
_ts_decorate([
    (0, _swagger.ApiProperty)({
        description: 'Các tham số của template',
        example: [
            'orderId',
            'shopName'
        ],
        type: [
            String
        ]
    }),
    _ts_metadata("design:type", Array)
], ZnsTemplateResponseDto.prototype, "params", void 0);
_ts_decorate([
    (0, _swagger.ApiProperty)({
        description: 'Trạng thái của template (approved, pending, rejected)',
        example: 'approved'
    }),
    _ts_metadata("design:type", String)
], ZnsTemplateResponseDto.prototype, "status", void 0);
_ts_decorate([
    (0, _swagger.ApiProperty)({
        description: 'Thời điểm tạo (Unix timestamp)',
        example: 1625097600000
    }),
    _ts_metadata("design:type", Number)
], ZnsTemplateResponseDto.prototype, "createdAt", void 0);
_ts_decorate([
    (0, _swagger.ApiProperty)({
        description: 'Thời điểm cập nhật (Unix timestamp)',
        example: 1625097600000
    }),
    _ts_metadata("design:type", Number)
], ZnsTemplateResponseDto.prototype, "updatedAt", void 0);

//# sourceMappingURL=zns-template-response.dto.js.map