"use strict";
Object.defineProperty(exports, "__esModule", {
    value: true
});
Object.defineProperty(exports, "EmailMarketingProcessor", {
    enumerable: true,
    get: function() {
        return EmailMarketingProcessor;
    }
});
const _bullmq = require("@nestjs/bullmq");
const _common = require("@nestjs/common");
const _nodemailer = /*#__PURE__*/ _interop_require_wildcard(require("nodemailer"));
const _queue = require("../../../queue");
const _services = require("./services");
const _env = require("../../../config/env");
function _getRequireWildcardCache(nodeInterop) {
    if (typeof WeakMap !== "function") return null;
    var cacheBabelInterop = new WeakMap();
    var cacheNodeInterop = new WeakMap();
    return (_getRequireWildcardCache = function(nodeInterop) {
        return nodeInterop ? cacheNodeInterop : cacheBabelInterop;
    })(nodeInterop);
}
function _interop_require_wildcard(obj, nodeInterop) {
    if (!nodeInterop && obj && obj.__esModule) {
        return obj;
    }
    if (obj === null || typeof obj !== "object" && typeof obj !== "function") {
        return {
            default: obj
        };
    }
    var cache = _getRequireWildcardCache(nodeInterop);
    if (cache && cache.has(obj)) {
        return cache.get(obj);
    }
    var newObj = {
        __proto__: null
    };
    var hasPropertyDescriptor = Object.defineProperty && Object.getOwnPropertyDescriptor;
    for(var key in obj){
        if (key !== "default" && Object.prototype.hasOwnProperty.call(obj, key)) {
            var desc = hasPropertyDescriptor ? Object.getOwnPropertyDescriptor(obj, key) : null;
            if (desc && (desc.get || desc.set)) {
                Object.defineProperty(newObj, key, desc);
            } else {
                newObj[key] = obj[key];
            }
        }
    }
    newObj.default = obj;
    if (cache) {
        cache.set(obj, newObj);
    }
    return newObj;
}
function _ts_decorate(decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for(var i = decorators.length - 1; i >= 0; i--)if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
}
function _ts_metadata(k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
}
let EmailMarketingProcessor = class EmailMarketingProcessor extends _bullmq.WorkerHost {
    /**
   * Xử lý job gửi email marketing
   * @param job Job từ queue
   */ async process(job) {
        const jobData = job.data;
        this.logger.log(`Processing email marketing job: ${job.id} for campaign ${jobData.campaignId}`);
        try {
            // Validate job data
            if (!this.validateJobData(jobData)) {
                throw new Error('Invalid job data');
            }
            // Inject custom variables vào subject và content
            const processedSubject = this.emailTemplateService.injectVariables(jobData.subject, jobData.customFields);
            const processedContent = this.emailTemplateService.injectVariables(jobData.content, jobData.customFields);
            // Inject tracking pixel vào content
            const contentWithTracking = this.emailTemplateService.injectTrackingPixel(processedContent, jobData.trackingId, _env.env.app.BASE_URL || 'http://localhost:3000');
            // Tạo email transporter
            const transporter = this.createTransporter(jobData.server);
            // Chuẩn bị email options
            const mailOptions = {
                from: jobData.server?.from || _env.env.email.MAIL_DEFAULT_FROM || '<EMAIL>',
                to: jobData.email,
                subject: processedSubject,
                html: contentWithTracking
            };
            // Gửi email
            const info = await transporter.sendMail(mailOptions);
            // Track email sent
            await this.emailTrackingService.trackEmailSent(jobData.campaignId, jobData.audienceId, jobData.email, jobData.trackingId);
            this.logger.log(`Email sent successfully: ${info.messageId} to ${jobData.email}`);
            // Update job progress
            await job.updateProgress(100);
        } catch (error) {
            this.logger.error(`Error processing email job ${job.id}: ${error.message}`, error.stack);
            // Track email failed
            await this.emailTrackingService.trackEmailFailed(jobData.campaignId, jobData.audienceId, jobData.email, jobData.trackingId, error);
            throw error; // Re-throw để Bull có thể retry
        }
    }
    /**
   * Validate job data
   * @param jobData Dữ liệu job
   * @returns True nếu valid
   */ validateJobData(jobData) {
        if (!jobData.campaignId || !jobData.audienceId) {
            this.logger.error('Missing campaignId or audienceId');
            return false;
        }
        if (!jobData.email || !this.isValidEmail(jobData.email)) {
            this.logger.error(`Invalid email: ${jobData.email}`);
            return false;
        }
        if (!jobData.trackingId) {
            this.logger.error('Missing trackingId');
            return false;
        }
        return true;
    }
    /**
   * Validate email format
   * @param email Email cần validate
   * @returns True nếu email hợp lệ
   */ isValidEmail(email) {
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        return emailRegex.test(email);
    }
    /**
   * Tạo email transporter
   * @param serverConfig Cấu hình server
   * @returns Nodemailer transporter
   */ createTransporter(serverConfig) {
        // Sử dụng cấu hình từ job nếu có, nếu không dùng cấu hình mặc định
        const config = {
            host: serverConfig?.host || _env.env.email.MAIL_HOST,
            port: serverConfig?.port || Number(_env.env.email.MAIL_PORT),
            secure: serverConfig?.secure !== undefined ? serverConfig.secure : _env.env.email.MAIL_SECURE,
            auth: {
                user: serverConfig?.user || _env.env.email.MAIL_USERNAME,
                pass: serverConfig?.password || _env.env.email.MAIL_PASSWORD
            }
        };
        return _nodemailer.createTransport(config);
    }
    /**
   * Xử lý khi job failed
   * @param job Job bị failed
   * @param err Lỗi
   */ async onFailed(job, err) {
        this.logger.error(`Job ${job.id} failed: ${err.message}`, err.stack);
        const jobData = job.data;
        // Track email failed nếu chưa track
        try {
            await this.emailTrackingService.trackEmailFailed(jobData.campaignId, jobData.audienceId, jobData.email, jobData.trackingId, err);
        } catch (trackingError) {
            this.logger.error(`Error tracking failed email: ${trackingError.message}`);
        }
    }
    /**
   * Xử lý khi job completed
   * @param job Job đã hoàn thành
   */ async onCompleted(job) {
        this.logger.debug(`Job ${job.id} completed successfully`);
    }
    /**
   * Xử lý khi job active
   * @param job Job đang xử lý
   */ async onActive(job) {
        this.logger.debug(`Job ${job.id} started processing`);
    }
    constructor(emailTemplateService, emailTrackingService){
        super(), this.emailTemplateService = emailTemplateService, this.emailTrackingService = emailTrackingService, this.logger = new _common.Logger(EmailMarketingProcessor.name);
    }
};
EmailMarketingProcessor = _ts_decorate([
    (0, _common.Injectable)(),
    (0, _bullmq.Processor)(_queue.QueueName.EMAIL_MARKETING, {
        concurrency: 10
    }),
    _ts_metadata("design:type", Function),
    _ts_metadata("design:paramtypes", [
        typeof _services.EmailTemplateService === "undefined" ? Object : _services.EmailTemplateService,
        typeof _services.EmailTrackingService === "undefined" ? Object : _services.EmailTrackingService
    ])
], EmailMarketingProcessor);

//# sourceMappingURL=email-marketing.processor.js.map