"use strict";
Object.defineProperty(exports, "__esModule", {
    value: true
});
Object.defineProperty(exports, "ZaloCampaignLogResponseDto", {
    enumerable: true,
    get: function() {
        return ZaloCampaignLogResponseDto;
    }
});
const _swagger = require("@nestjs/swagger");
function _ts_decorate(decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for(var i = decorators.length - 1; i >= 0; i--)if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
}
function _ts_metadata(k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
}
let ZaloCampaignLogResponseDto = class ZaloCampaignLogResponseDto {
};
_ts_decorate([
    (0, _swagger.ApiProperty)({
        description: 'ID của log',
        example: 1
    }),
    _ts_metadata("design:type", Number)
], ZaloCampaignLogResponseDto.prototype, "id", void 0);
_ts_decorate([
    (0, _swagger.ApiProperty)({
        description: 'ID của chiến dịch',
        example: 1
    }),
    _ts_metadata("design:type", Number)
], ZaloCampaignLogResponseDto.prototype, "campaignId", void 0);
_ts_decorate([
    (0, _swagger.ApiProperty)({
        description: 'ID của người dùng',
        example: 123
    }),
    _ts_metadata("design:type", Number)
], ZaloCampaignLogResponseDto.prototype, "userId", void 0);
_ts_decorate([
    (0, _swagger.ApiProperty)({
        description: 'ID của Official Account',
        example: '*********'
    }),
    _ts_metadata("design:type", String)
], ZaloCampaignLogResponseDto.prototype, "oaId", void 0);
_ts_decorate([
    (0, _swagger.ApiProperty)({
        description: 'ID của người theo dõi',
        example: 1
    }),
    _ts_metadata("design:type", Number)
], ZaloCampaignLogResponseDto.prototype, "followerId", void 0);
_ts_decorate([
    (0, _swagger.ApiProperty)({
        description: 'ID người dùng của người theo dõi trên Zalo',
        example: '*********'
    }),
    _ts_metadata("design:type", String)
], ZaloCampaignLogResponseDto.prototype, "followerUserId", void 0);
_ts_decorate([
    (0, _swagger.ApiProperty)({
        description: 'ID của tin nhắn',
        example: 'msg*********',
        nullable: true
    }),
    _ts_metadata("design:type", String)
], ZaloCampaignLogResponseDto.prototype, "messageId", void 0);
_ts_decorate([
    (0, _swagger.ApiProperty)({
        description: 'Trạng thái của log',
        example: 'success',
        enum: [
            'pending',
            'success',
            'failed',
            'deleted'
        ]
    }),
    _ts_metadata("design:type", String)
], ZaloCampaignLogResponseDto.prototype, "status", void 0);
_ts_decorate([
    (0, _swagger.ApiProperty)({
        description: 'Thông báo lỗi (nếu có)',
        example: 'Không thể gửi tin nhắn',
        nullable: true
    }),
    _ts_metadata("design:type", String)
], ZaloCampaignLogResponseDto.prototype, "error", void 0);
_ts_decorate([
    (0, _swagger.ApiProperty)({
        description: 'Thời điểm tạo (Unix timestamp)',
        example: 1625097600000
    }),
    _ts_metadata("design:type", Number)
], ZaloCampaignLogResponseDto.prototype, "createdAt", void 0);
_ts_decorate([
    (0, _swagger.ApiProperty)({
        description: 'Thông tin người theo dõi',
        example: {
            displayName: 'Nguyễn Văn A',
            avatar: 'https://example.com/avatar.jpg'
        },
        nullable: true
    }),
    _ts_metadata("design:type", Object)
], ZaloCampaignLogResponseDto.prototype, "follower", void 0);

//# sourceMappingURL=zalo-campaign-log-response.dto.js.map