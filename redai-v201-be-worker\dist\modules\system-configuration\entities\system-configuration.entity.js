"use strict";
Object.defineProperty(exports, "__esModule", {
    value: true
});
Object.defineProperty(exports, "SystemConfiguration", {
    enumerable: true,
    get: function() {
        return SystemConfiguration;
    }
});
const _typeorm = require("typeorm");
function _ts_decorate(decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for(var i = decorators.length - 1; i >= 0; i--)if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
}
function _ts_metadata(k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
}
let SystemConfiguration = class SystemConfiguration {
};
_ts_decorate([
    (0, _typeorm.PrimaryColumn)({
        type: 'integer'
    }),
    _ts_metadata("design:type", Number)
], SystemConfiguration.prototype, "id", void 0);
_ts_decorate([
    (0, _typeorm.Column)({
        name: 'bank_code',
        length: 20,
        nullable: true
    }),
    _ts_metadata("design:type", String)
], SystemConfiguration.prototype, "bankCode", void 0);
_ts_decorate([
    (0, _typeorm.Column)({
        name: 'account_number',
        length: 255,
        nullable: true
    }),
    _ts_metadata("design:type", String)
], SystemConfiguration.prototype, "accountNumber", void 0);
_ts_decorate([
    (0, _typeorm.Column)({
        name: 'account_name',
        length: 255,
        nullable: true
    }),
    _ts_metadata("design:type", String)
], SystemConfiguration.prototype, "accountName", void 0);
_ts_decorate([
    (0, _typeorm.Column)({
        name: 'active',
        type: 'boolean',
        default: true
    }),
    _ts_metadata("design:type", Boolean)
], SystemConfiguration.prototype, "active", void 0);
_ts_decorate([
    (0, _typeorm.Column)({
        name: 'fee_percentage',
        type: 'double precision',
        nullable: true
    }),
    _ts_metadata("design:type", Number)
], SystemConfiguration.prototype, "feePercentage", void 0);
_ts_decorate([
    (0, _typeorm.Column)({
        name: 'purchase_invoice_template',
        length: 255,
        nullable: true
    }),
    _ts_metadata("design:type", String)
], SystemConfiguration.prototype, "purchaseInvoiceTemplate", void 0);
_ts_decorate([
    (0, _typeorm.Column)({
        name: 'email_notification_system_id',
        type: 'integer',
        nullable: true
    }),
    _ts_metadata("design:type", Number)
], SystemConfiguration.prototype, "emailNotificationSystemId", void 0);
_ts_decorate([
    (0, _typeorm.Column)({
        name: 'initial_affiliate_contract_business',
        length: 255,
        nullable: true
    }),
    _ts_metadata("design:type", String)
], SystemConfiguration.prototype, "initialAffiliateContractBusiness", void 0);
_ts_decorate([
    (0, _typeorm.Column)({
        name: 'initial_rule_contract_business',
        length: 255,
        nullable: true
    }),
    _ts_metadata("design:type", String)
], SystemConfiguration.prototype, "initialRuleContractBusiness", void 0);
_ts_decorate([
    (0, _typeorm.Column)({
        name: 'initial_affiliate_contract_customer',
        length: 255,
        nullable: true
    }),
    _ts_metadata("design:type", String)
], SystemConfiguration.prototype, "initialAffiliateContractCustomer", void 0);
_ts_decorate([
    (0, _typeorm.Column)({
        name: 'initial_rule_contract_customer',
        length: 255,
        nullable: true
    }),
    _ts_metadata("design:type", String)
], SystemConfiguration.prototype, "initialRuleContractCustomer", void 0);
_ts_decorate([
    (0, _typeorm.Column)({
        name: 'created_at',
        type: 'bigint',
        nullable: true
    }),
    _ts_metadata("design:type", Number)
], SystemConfiguration.prototype, "createdAt", void 0);
_ts_decorate([
    (0, _typeorm.Column)({
        name: 'updated_at',
        type: 'bigint',
        nullable: true
    }),
    _ts_metadata("design:type", Number)
], SystemConfiguration.prototype, "updatedAt", void 0);
SystemConfiguration = _ts_decorate([
    (0, _typeorm.Entity)('system_configuration')
], SystemConfiguration);

//# sourceMappingURL=system-configuration.entity.js.map