{"version": 3, "sources": ["../../../../src/modules/marketing/entities/zalo-message.entity.ts"], "sourcesContent": ["import { Column, Entity, PrimaryGeneratedColumn } from 'typeorm';\r\n\r\n/**\r\n * Entity đại diện cho bảng zalo_messages trong cơ sở dữ liệu\r\n * Lưu trữ lịch sử tin nhắn giữa người dùng Zalo và Official Account\r\n */\r\n@Entity('zalo_messages')\r\nexport class ZaloMessage {\r\n  /**\r\n   * ID tự động tăng\r\n   */\r\n  @PrimaryGeneratedColumn({ name: 'id' })\r\n  id: number;\r\n\r\n  /**\r\n   * ID của Official Account\r\n   */\r\n  @Column({ name: 'oa_id', length: 50 })\r\n  oaId: string;\r\n\r\n  /**\r\n   * ID của người dùng Zalo\r\n   */\r\n  @Column({ name: 'user_id', length: 50 })\r\n  userId: string;\r\n\r\n  /**\r\n   * ID của tin nhắn trên Zalo\r\n   */\r\n  @Column({ name: 'message_id', length: 50, nullable: true })\r\n  messageId: string;\r\n\r\n  /**\r\n   * <PERSON><PERSON><PERSON> tin nhắn (text, image, file, template)\r\n   */\r\n  @Column({ name: 'message_type', length: 20 })\r\n  messageType: string;\r\n\r\n  /**\r\n   * Nội dung tin nhắn\r\n   */\r\n  @Column({ name: 'content', type: 'text', nullable: true })\r\n  content: string | null;\r\n\r\n  /**\r\n   * Dữ liệu bổ sung của tin nhắn (JSON)\r\n   */\r\n  @Column({ name: 'data', type: 'jsonb', nullable: true })\r\n  data: any;\r\n\r\n  /**\r\n   * Hướng tin nhắn (incoming, outgoing)\r\n   */\r\n  @Column({ name: 'direction', length: 10 })\r\n  direction: string;\r\n\r\n  /**\r\n   * ID của agent xử lý tin nhắn\r\n   */\r\n  @Column({ name: 'agent_id', nullable: true })\r\n  agentId: number;\r\n\r\n  /**\r\n   * Thời điểm gửi/nhận (Unix timestamp)\r\n   */\r\n  @Column({ name: 'timestamp', type: 'bigint' })\r\n  timestamp: number;\r\n\r\n  /**\r\n   * Thời điểm tạo bản ghi (Unix timestamp)\r\n   */\r\n  @Column({ name: 'created_at', type: 'bigint' })\r\n  createdAt: number;\r\n}\r\n"], "names": ["ZaloMessage", "name", "length", "nullable", "type"], "mappings": ";;;;+BAOaA;;;eAAAA;;;yBAP0C;;;;;;;;;;AAOhD,IAAA,AAAMA,cAAN,MAAMA;AAkEb;;;QA9D4BC,MAAM;;;;;;QAMtBA,MAAM;QAASC,QAAQ;;;;;;QAMvBD,MAAM;QAAWC,QAAQ;;;;;;QAMzBD,MAAM;QAAcC,QAAQ;QAAIC,UAAU;;;;;;QAM1CF,MAAM;QAAgBC,QAAQ;;;;;;QAM9BD,MAAM;QAAWG,MAAM;QAAQD,UAAU;;;;;;QAMzCF,MAAM;QAAQG,MAAM;QAASD,UAAU;;;;;;QAMvCF,MAAM;QAAaC,QAAQ;;;;;;QAM3BD,MAAM;QAAYE,UAAU;;;;;;QAM5BF,MAAM;QAAaG,MAAM;;;;;;QAMzBH,MAAM;QAAcG,MAAM"}