"use strict";
Object.defineProperty(exports, "__esModule", {
    value: true
});
Object.defineProperty(exports, "EmailTemplateService", {
    enumerable: true,
    get: function() {
        return EmailTemplateService;
    }
});
const _common = require("@nestjs/common");
function _ts_decorate(decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for(var i = decorators.length - 1; i >= 0; i--)if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
}
let EmailTemplateService = class EmailTemplateService {
    /**
   * Inject biến tùy chỉnh vào template
   * @param template Template chứa biến dạng {{variable}}
   * @param variables Dữ liệu biến để thay thế
   * @returns Template đã được inject biến
   */ injectVariables(template, variables) {
        if (!template || !variables) {
            return template;
        }
        try {
            let result = template;
            // Tìm tất cả biến dạng {{variable}} trong template
            const variablePattern = /\{\{([^}]+)\}\}/g;
            let match;
            while((match = variablePattern.exec(template)) !== null){
                const variableName = match[1].trim();
                const variableValue = this.getNestedValue(variables, variableName);
                if (variableValue !== undefined && variableValue !== null) {
                    // Thay thế biến bằng giá trị thực
                    const placeholder = match[0]; // {{variable}}
                    result = result.replace(new RegExp(this.escapeRegExp(placeholder), 'g'), String(variableValue));
                } else {
                    // Nếu không tìm thấy biến, giữ nguyên hoặc thay bằng chuỗi rỗng
                    this.logger.warn(`Variable "${variableName}" not found in data`);
                    const placeholder = match[0];
                    result = result.replace(new RegExp(this.escapeRegExp(placeholder), 'g'), '');
                }
            }
            return result;
        } catch (error) {
            this.logger.error(`Error injecting variables into template: ${error.message}`, error.stack);
            return template; // Trả về template gốc nếu có lỗi
        }
    }
    /**
   * Inject pixel tracking vào nội dung email
   * @param content Nội dung email HTML
   * @param trackingId ID tracking duy nhất
   * @param baseUrl Base URL của server
   * @returns Nội dung email đã có pixel tracking
   */ injectTrackingPixel(content, trackingId, baseUrl = '') {
        if (!content || !trackingId) {
            return content;
        }
        try {
            const trackingPixel = `<img src="${baseUrl}/api/email-tracking/pixel/${trackingId}" width="1" height="1" style="display:none;" alt="" />`;
            // Thêm pixel tracking trước thẻ đóng </body> hoặc cuối nội dung
            if (content.includes('</body>')) {
                return content.replace('</body>', `${trackingPixel}</body>`);
            } else {
                return content + trackingPixel;
            }
        } catch (error) {
            this.logger.error(`Error injecting tracking pixel: ${error.message}`, error.stack);
            return content;
        }
    }
    /**
   * Lấy giá trị nested từ object bằng dot notation
   * @param obj Object chứa dữ liệu
   * @param path Đường dẫn đến giá trị (vd: user.name, profile.address.city)
   * @returns Giá trị tìm được hoặc undefined
   */ getNestedValue(obj, path) {
        try {
            return path.split('.').reduce((current, key)=>{
                return current && current[key] !== undefined ? current[key] : undefined;
            }, obj);
        } catch (error) {
            return undefined;
        }
    }
    /**
   * Escape special characters for RegExp
   * @param string Chuỗi cần escape
   * @returns Chuỗi đã escape
   */ escapeRegExp(string) {
        return string.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
    }
    /**
   * Validate template syntax
   * @param template Template cần validate
   * @returns True nếu template hợp lệ
   */ validateTemplate(template) {
        if (!template) {
            return true;
        }
        try {
            // Kiểm tra cặp ngoặc {{ }} có đúng không
            const openBraces = (template.match(/\{\{/g) || []).length;
            const closeBraces = (template.match(/\}\}/g) || []).length;
            return openBraces === closeBraces;
        } catch (error) {
            this.logger.error(`Error validating template: ${error.message}`);
            return false;
        }
    }
    /**
   * Lấy danh sách tất cả biến trong template
   * @param template Template cần phân tích
   * @returns Mảng tên biến
   */ extractVariables(template) {
        if (!template) {
            return [];
        }
        try {
            const variables = [];
            const variablePattern = /\{\{([^}]+)\}\}/g;
            let match;
            while((match = variablePattern.exec(template)) !== null){
                const variableName = match[1].trim();
                if (!variables.includes(variableName)) {
                    variables.push(variableName);
                }
            }
            return variables;
        } catch (error) {
            this.logger.error(`Error extracting variables: ${error.message}`);
            return [];
        }
    }
    constructor(){
        this.logger = new _common.Logger(EmailTemplateService.name);
    }
};
EmailTemplateService = _ts_decorate([
    (0, _common.Injectable)()
], EmailTemplateService);

//# sourceMappingURL=email-template.service.js.map