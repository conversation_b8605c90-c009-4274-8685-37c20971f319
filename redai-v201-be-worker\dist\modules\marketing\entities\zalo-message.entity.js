"use strict";
Object.defineProperty(exports, "__esModule", {
    value: true
});
Object.defineProperty(exports, "ZaloMessage", {
    enumerable: true,
    get: function() {
        return ZaloMessage;
    }
});
const _typeorm = require("typeorm");
function _ts_decorate(decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for(var i = decorators.length - 1; i >= 0; i--)if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
}
function _ts_metadata(k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
}
let ZaloMessage = class ZaloMessage {
};
_ts_decorate([
    (0, _typeorm.PrimaryGeneratedColumn)({
        name: 'id'
    }),
    _ts_metadata("design:type", Number)
], ZaloMessage.prototype, "id", void 0);
_ts_decorate([
    (0, _typeorm.Column)({
        name: 'oa_id',
        length: 50
    }),
    _ts_metadata("design:type", String)
], ZaloMessage.prototype, "oaId", void 0);
_ts_decorate([
    (0, _typeorm.Column)({
        name: 'user_id',
        length: 50
    }),
    _ts_metadata("design:type", String)
], ZaloMessage.prototype, "userId", void 0);
_ts_decorate([
    (0, _typeorm.Column)({
        name: 'message_id',
        length: 50,
        nullable: true
    }),
    _ts_metadata("design:type", String)
], ZaloMessage.prototype, "messageId", void 0);
_ts_decorate([
    (0, _typeorm.Column)({
        name: 'message_type',
        length: 20
    }),
    _ts_metadata("design:type", String)
], ZaloMessage.prototype, "messageType", void 0);
_ts_decorate([
    (0, _typeorm.Column)({
        name: 'content',
        type: 'text',
        nullable: true
    }),
    _ts_metadata("design:type", Object)
], ZaloMessage.prototype, "content", void 0);
_ts_decorate([
    (0, _typeorm.Column)({
        name: 'data',
        type: 'jsonb',
        nullable: true
    }),
    _ts_metadata("design:type", Object)
], ZaloMessage.prototype, "data", void 0);
_ts_decorate([
    (0, _typeorm.Column)({
        name: 'direction',
        length: 10
    }),
    _ts_metadata("design:type", String)
], ZaloMessage.prototype, "direction", void 0);
_ts_decorate([
    (0, _typeorm.Column)({
        name: 'agent_id',
        nullable: true
    }),
    _ts_metadata("design:type", Number)
], ZaloMessage.prototype, "agentId", void 0);
_ts_decorate([
    (0, _typeorm.Column)({
        name: 'timestamp',
        type: 'bigint'
    }),
    _ts_metadata("design:type", Number)
], ZaloMessage.prototype, "timestamp", void 0);
_ts_decorate([
    (0, _typeorm.Column)({
        name: 'created_at',
        type: 'bigint'
    }),
    _ts_metadata("design:type", Number)
], ZaloMessage.prototype, "createdAt", void 0);
ZaloMessage = _ts_decorate([
    (0, _typeorm.Entity)('zalo_messages')
], ZaloMessage);

//# sourceMappingURL=zalo-message.entity.js.map