{"version": 3, "sources": ["../../../../src/modules/email-system/entities/admin-template-email.entity.ts"], "sourcesContent": ["import { Column, Entity, PrimaryGeneratedColumn } from 'typeorm';\r\n\r\n/**\r\n * Entity đại diện cho bảng admin_template_email trong cơ sở dữ liệu\r\n * <PERSON>h sách template email bên admin\r\n */\r\n@Entity('admin_template_email')\r\nexport class AdminTemplateEmail {\r\n  /**\r\n   * ID của template\r\n   */\r\n  @PrimaryGeneratedColumn({ name: 'id' })\r\n  id: number;\r\n\r\n  /**\r\n   * Tên mẫu\r\n   */\r\n  @Column({ name: 'name', length: 100, nullable: true, comment: 'Tên mẫu' })\r\n  name: string;\r\n\r\n  /**\r\n   * Ti<PERSON>u đề email\r\n   */\r\n  @Column({ name: 'subject', length: 255, nullable: true, comment: 'Tiêu đề' })\r\n  subject: string;\r\n\r\n  /**\r\n   * <PERSON>h mục email\r\n   */\r\n  @Column({\r\n    name: 'category',\r\n    length: 100,\r\n    nullable: true,\r\n    unique: true,\r\n    comment: 'Danh mục email',\r\n  })\r\n  category: string;\r\n\r\n  /**\r\n   * Nội dung email\r\n   */\r\n  @Column({\r\n    name: 'content',\r\n    type: 'text',\r\n    nullable: true,\r\n    comment: 'Nội dung email',\r\n  })\r\n  content: string;\r\n\r\n  /**\r\n   * <PERSON>h sách các placeholder\r\n   */\r\n  @Column({\r\n    name: 'placeholders',\r\n    type: 'json',\r\n    nullable: true,\r\n    comment: '<PERSON>h sách các placeholder',\r\n  })\r\n  placeholders: any;\r\n\r\n  /**\r\n   * ID của người tạo\r\n   */\r\n  @Column({ name: 'created_by', type: 'bigint', nullable: true })\r\n  createdBy: number;\r\n\r\n  /**\r\n   * ID của người cập nhật\r\n   */\r\n  @Column({ name: 'updated_by', type: 'bigint', nullable: true })\r\n  updatedBy: number;\r\n\r\n  /**\r\n   * Thời gian tạo (Unix timestamp)\r\n   */\r\n  @Column({\r\n    name: 'created_at',\r\n    type: 'bigint',\r\n    nullable: true,\r\n    comment: 'Thời gian tạo',\r\n  })\r\n  createdAt: number;\r\n\r\n  /**\r\n   * Thời gian cập nhật (Unix timestamp)\r\n   */\r\n  @Column({\r\n    name: 'updated_at',\r\n    type: 'bigint',\r\n    nullable: true,\r\n    comment: 'Thời gian cập nhật',\r\n  })\r\n  updatedAt: number;\r\n}\r\n"], "names": ["AdminTemplateEmail", "name", "length", "nullable", "comment", "unique", "type"], "mappings": ";;;;+BAOaA;;;eAAAA;;;yBAP0C;;;;;;;;;;AAOhD,IAAA,AAAMA,qBAAN,MAAMA;AAsFb;;;QAlF4BC,MAAM;;;;;;QAMtBA,MAAM;QAAQC,QAAQ;QAAKC,UAAU;QAAMC,SAAS;;;;;;QAMpDH,MAAM;QAAWC,QAAQ;QAAKC,UAAU;QAAMC,SAAS;;;;;;QAO/DH,MAAM;QACNC,QAAQ;QACRC,UAAU;QACVE,QAAQ;QACRD,SAAS;;;;;;QAQTH,MAAM;QACNK,MAAM;QACNH,UAAU;QACVC,SAAS;;;;;;QAQTH,MAAM;QACNK,MAAM;QACNH,UAAU;QACVC,SAAS;;;;;;QAODH,MAAM;QAAcK,MAAM;QAAUH,UAAU;;;;;;QAM9CF,MAAM;QAAcK,MAAM;QAAUH,UAAU;;;;;;QAOtDF,MAAM;QACNK,MAAM;QACNH,UAAU;QACVC,SAAS;;;;;;QAQTH,MAAM;QACNK,MAAM;QACNH,UAAU;QACVC,SAAS"}