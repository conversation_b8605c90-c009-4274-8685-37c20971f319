"use strict";
Object.defineProperty(exports, "__esModule", {
    value: true
});
Object.defineProperty(exports, "UpdateTemplateEmailDto", {
    enumerable: true,
    get: function() {
        return UpdateTemplateEmailDto;
    }
});
const _swagger = require("@nestjs/swagger");
const _classvalidator = require("class-validator");
function _ts_decorate(decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for(var i = decorators.length - 1; i >= 0; i--)if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
}
function _ts_metadata(k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
}
let UpdateTemplateEmailDto = class UpdateTemplateEmailDto {
};
_ts_decorate([
    (0, _swagger.ApiProperty)({
        description: 'Nombre del template',
        example: 'Plantilla de bienvenida actualizada',
        maxLength: 255,
        required: false
    }),
    (0, _classvalidator.IsString)(),
    (0, _classvalidator.IsOptional)(),
    (0, _classvalidator.MaxLength)(255),
    _ts_metadata("design:type", String)
], UpdateTemplateEmailDto.prototype, "name", void 0);
_ts_decorate([
    (0, _swagger.ApiProperty)({
        description: 'Asunto del email',
        example: 'Bienvenido a nuestra plataforma - Actualizado',
        maxLength: 255,
        required: false
    }),
    (0, _classvalidator.IsString)(),
    (0, _classvalidator.IsOptional)(),
    (0, _classvalidator.MaxLength)(255),
    _ts_metadata("design:type", String)
], UpdateTemplateEmailDto.prototype, "subject", void 0);
_ts_decorate([
    (0, _swagger.ApiProperty)({
        description: 'Contenido HTML del email',
        example: '<h1>Bienvenido</h1><p>Gracias por registrarte en nuestra plataforma actualizada.</p>',
        required: false
    }),
    (0, _classvalidator.IsString)(),
    (0, _classvalidator.IsOptional)(),
    _ts_metadata("design:type", String)
], UpdateTemplateEmailDto.prototype, "content", void 0);
_ts_decorate([
    (0, _swagger.ApiProperty)({
        description: 'Tags asociados al template',
        example: [
            'bienvenida',
            'registro',
            'actualizado'
        ],
        required: false
    }),
    (0, _classvalidator.IsArray)(),
    (0, _classvalidator.IsOptional)(),
    _ts_metadata("design:type", Array)
], UpdateTemplateEmailDto.prototype, "tags", void 0);
_ts_decorate([
    (0, _swagger.ApiProperty)({
        description: 'Placeholders utilizados en el template',
        example: [
            'userName',
            'companyName',
            'date',
            'orderNumber'
        ],
        required: false,
        type: [
            String
        ]
    }),
    (0, _classvalidator.IsArray)(),
    (0, _classvalidator.IsString)({
        each: true
    }),
    (0, _classvalidator.IsOptional)(),
    _ts_metadata("design:type", Array)
], UpdateTemplateEmailDto.prototype, "placeholders", void 0);

//# sourceMappingURL=update-template-email.dto.js.map