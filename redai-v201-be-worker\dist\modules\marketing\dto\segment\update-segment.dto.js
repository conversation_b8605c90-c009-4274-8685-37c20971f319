"use strict";
Object.defineProperty(exports, "__esModule", {
    value: true
});
Object.defineProperty(exports, "UpdateSegmentDto", {
    enumerable: true,
    get: function() {
        return UpdateSegmentDto;
    }
});
const _classvalidator = require("class-validator");
const _swagger = require("@nestjs/swagger");
const _classtransformer = require("class-transformer");
const _segmentcriteriadto = require("./segment-criteria.dto");
function _ts_decorate(decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for(var i = decorators.length - 1; i >= 0; i--)if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
}
function _ts_metadata(k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
}
let UpdateSegmentDto = class UpdateSegmentDto {
};
_ts_decorate([
    (0, _swagger.ApiProperty)({
        description: 'Tên segment',
        example: 'Khách hàng tiềm năng',
        required: false
    }),
    (0, _classvalidator.IsOptional)(),
    (0, _classvalidator.IsString)({
        message: 'Tên segment phải là chuỗi'
    }),
    _ts_metadata("design:type", String)
], UpdateSegmentDto.prototype, "name", void 0);
_ts_decorate([
    (0, _swagger.ApiProperty)({
        description: 'Mô tả segment',
        example: 'Khách hàng có khả năng mua hàng cao',
        required: false
    }),
    (0, _classvalidator.IsOptional)(),
    (0, _classvalidator.IsString)({
        message: 'Mô tả phải là chuỗi'
    }),
    _ts_metadata("design:type", String)
], UpdateSegmentDto.prototype, "description", void 0);
_ts_decorate([
    (0, _swagger.ApiProperty)({
        description: 'Điều kiện lọc khách hàng',
        type: _segmentcriteriadto.SegmentCriteriaDto,
        required: false
    }),
    (0, _classvalidator.IsOptional)(),
    (0, _classvalidator.ValidateNested)(),
    (0, _classtransformer.Type)(()=>_segmentcriteriadto.SegmentCriteriaDto),
    _ts_metadata("design:type", typeof _segmentcriteriadto.SegmentCriteriaDto === "undefined" ? Object : _segmentcriteriadto.SegmentCriteriaDto)
], UpdateSegmentDto.prototype, "criteria", void 0);

//# sourceMappingURL=update-segment.dto.js.map