"use strict";
Object.defineProperty(exports, "__esModule", {
    value: true
});
Object.defineProperty(exports, "env", {
    enumerable: true,
    get: function() {
        return env;
    }
});
const _zod = require("zod");
const _dotenv = require("dotenv");
const _common = require("@nestjs/common");
(0, _dotenv.configDotenv)();
const logger = new _common.Logger('Config');
const requiredString = (name)=>_zod.z.string().trim().min(1, `${name} is required`);
const requiredURL = (name)=>_zod.z.string().url(`${name} must be a valid URL`);
const requiredNumber = (name)=>_zod.z.string().transform(Number).refine((v)=>!isNaN(v), {
        message: `${name} must be a number`
    });
const envSchema = _zod.z.object({
    misc: _zod.z.object({
        NODE_ENV: _zod.z.enum([
            'development',
            'test',
            'staging',
            'production'
        ]),
        PORT: requiredNumber('PORT')
    }),
    app: _zod.z.object({
        BASE_URL: _zod.z.string().url().optional().default('http://localhost:3000')
    }),
    database: _zod.z.object({
        DB_HOST: requiredString('DB_HOST'),
        DB_URL: requiredURL('DB_URL'),
        DB_PORT: requiredNumber('DB_PORT'),
        DB_USERNAME: requiredString('DB_USERNAME'),
        DB_PASSWORD: requiredString('DB_PASSWORD'),
        DB_DATABASE: requiredString('DB_DATABASE'),
        DB_SSL: _zod.z.string().transform((v)=>v === 'true')
    }),
    s3: _zod.z.object({
        S3_ACCESS_KEY: requiredString('S3_ACCESS_KEY'),
        S3_SECRET_KEY: requiredString('S3_SECRET_KEY'),
        S3_ENDPOINT: requiredURL('S3_ENDPOINT'),
        S3_BUCKET_NAME: requiredString('S3_BUCKET_NAME'),
        S3_REGION: requiredString('S3_REGION')
    }),
    cdn: _zod.z.object({
        CDN_URL: requiredURL('CDN_URL'),
        CDN_SECRET_KEY: requiredString('CDN_SECRET_KEY')
    }),
    openai: _zod.z.object({
        OPENAI_API_KEY: requiredString('OPENAI_API_KEY')
    }),
    external: _zod.z.object({
        EXTERNAL_EMAIL_API_URL: requiredURL('EXTERNAL_EMAIL_API_URL'),
        REDIS_URL: requiredURL('REDIS_URL'),
        PDF_EDIT_API_URL: requiredURL('PDF_EDIT_API_URL'),
        SERVICE_HOST_SMS: requiredURL('SERVICE_HOST_SMS'),
        SERVICE_SMS_API_KEY: requiredString('SERVICE_SMS_API_KEY'),
        RECAPTCHA_SECRET_KEY: requiredString('RECAPTCHA_SECRET_KEY')
    }),
    redis: _zod.z.object({
        REDIS_POOL_MIN: _zod.z.string().transform(Number).optional().default('2'),
        REDIS_POOL_MAX: _zod.z.string().transform(Number).optional().default('10'),
        REDIS_RETRY_ATTEMPTS: _zod.z.string().transform(Number).optional().default('3'),
        REDIS_RETRY_DELAY_MS: _zod.z.string().transform(Number).optional().default('1000'),
        REDIS_CONNECTION_TIMEOUT_MS: _zod.z.string().transform(Number).optional().default('5000'),
        REDIS_COMMAND_TIMEOUT_MS: _zod.z.string().transform(Number).optional().default('3000')
    }),
    sepay: _zod.z.object({
        SEPAY_HUB_API_KEY: requiredString('SEPAY_HUB_API_KEY'),
        SEPAY_HUB_CLIENT_ID: requiredString('SEPAY_HUB_CLIENT_ID'),
        SEPAY_HUB_CLIENT_SECRET: requiredString('SEPAY_HUB_CLIENT_SECRET'),
        SEPAY_HUB_API_URL: requiredURL('SEPAY_HUB_API_URL'),
        SEPAY_WEBHOOK_API_KEY: requiredString('SEPAY_WEBHOOK_API_KEY')
    }),
    swagger: _zod.z.object({
        SWAGGER_LOCAL_URL: requiredURL('SWAGGER_LOCAL_URL'),
        SWAGGER_DEV_URL: requiredURL('SWAGGER_DEV_URL'),
        SWAGGER_TEST_URL: requiredURL('SWAGGER_TEST_URL'),
        SWAGGER_STAGING_URL: requiredURL('SWAGGER_STAGING_URL'),
        SWAGGER_PROD_URL: requiredURL('SWAGGER_PROD_URL')
    }),
    zalo: _zod.z.object({
        ZALO_APP_ID: requiredString('ZALO_APP_ID'),
        ZALO_APP_SECRET: requiredString('ZALO_APP_SECRET'),
        ZALO_WEBHOOK_SECRET: requiredString('ZALO_WEBHOOK_SECRET'),
        ZALO_WEBHOOK_URL: requiredURL('ZALO_WEBHOOK_URL')
    }),
    google: _zod.z.object({
        GOOGLE_CLIENT_ID: requiredString('GOOGLE_CLIENT_ID'),
        GOOGLE_CLIENT_SECRET: requiredString('GOOGLE_CLIENT_SECRET'),
        GOOGLE_REDIRECT_URI: requiredURL('GOOGLE_REDIRECT_URI'),
        GOOGLE_APPLICATION_CREDENTIALS: requiredString('GOOGLE_APPLICATION_CREDENTIALS'),
        GOOGLE_CLOUD_PROJECT_ID: requiredString('GOOGLE_CLOUD_PROJECT_ID'),
        GOOGLE_CLOUD_STORAGE_BUCKET: requiredString('GOOGLE_CLOUD_STORAGE_BUCKET'),
        GOOGLE_ADS_CLIENT_ID: requiredString('GOOGLE_ADS_CLIENT_ID'),
        GOOGLE_ADS_CLIENT_SECRET: requiredString('GOOGLE_ADS_CLIENT_SECRET'),
        GOOGLE_ADS_DEVELOPER_TOKEN: requiredString('GOOGLE_ADS_DEVELOPER_TOKEN'),
        GOOGLE_ADS_REFRESH_TOKEN: requiredString('GOOGLE_ADS_REFRESH_TOKEN'),
        GOOGLE_ADS_LOGIN_CUSTOMER_ID: requiredString('GOOGLE_ADS_LOGIN_CUSTOMER_ID')
    }),
    facebook: _zod.z.object({
        FACEBOOK_APP_ID: requiredString('FACEBOOK_APP_ID'),
        FACEBOOK_APP_SECRET: requiredString('FACEBOOK_APP_SECRET'),
        FACEBOOK_REDIRECT_URI: requiredURL('FACEBOOK_REDIRECT_URI'),
        FACEBOOK_GRAPH_API_VERSION: requiredString('FACEBOOK_GRAPH_API_VERSION')
    }),
    agent: _zod.z.object({
        AGENT_API_KEY: requiredString('AGENT_API_KEY'),
        ENCRYPTION_SECRET_KEY: requiredString('ENCRYPTION_SECRET_KEY'),
        API_PREFIX_KEY: requiredString('API_PREFIX_KEY'),
        USER_SECRET_MODEL: requiredString('USER_SECRET_MODEL'),
        ADMIN_SECRET_MODEL: requiredString('ADMIN_SECRET_MODEL'),
        API_SECRET_KEY: requiredString('API_SECRET_KEY'),
        MCP_HOST: requiredString('MCP_HOST'),
        MCP_PORT: requiredNumber('MCP_PORT')
    }),
    email: _zod.z.object({
        MAIL_HOST: requiredString('MAIL_HOST'),
        MAIL_PORT: requiredNumber('MAIL_PORT'),
        MAIL_SECURE: _zod.z.string().transform((v)=>v === 'true'),
        MAIL_USERNAME: requiredString('MAIL_USERNAME'),
        MAIL_PASSWORD: requiredString('MAIL_PASSWORD'),
        MAIL_DEFAULT_FROM: requiredString('MAIL_DEFAULT_FROM')
    }),
    fpt: _zod.z.object({
        FPT_SMS_CLIENT_ID: requiredString('FPT_SMS_CLIENT_ID'),
        FPT_SMS_CLIENT_SECRET: requiredString('FPT_SMS_CLIENT_SECRET'),
        FPT_SMS_SCOPE: requiredString('FPT_SMS_SCOPE'),
        FPT_SMS_API_URL: requiredURL('FPT_SMS_API_URL'),
        FPT_SMS_BRANDNAME: requiredString('FPT_SMS_BRANDNAME')
    }),
    llmSystemEncryptionKey: _zod.z.object({
        USER_SECRECT_MODEL: requiredString('USER_SECRECT_MODEL'),
        ADMIN_SECRECT_MODEL: requiredString('ADMIN_SECRECT_MODEL')
    })
});
let env;
try {
    env = envSchema.parse({
        misc: process.env,
        app: process.env,
        database: process.env,
        s3: process.env,
        cdn: process.env,
        openai: process.env,
        external: process.env,
        redis: process.env,
        sepay: process.env,
        swagger: process.env,
        zalo: process.env,
        google: process.env,
        facebook: process.env,
        agent: process.env,
        email: process.env,
        fpt: process.env,
        llmSystemEncryptionKey: process.env
    });
} catch (err) {
    if (err instanceof _zod.ZodError) {
        logger.error('❌ Invalid environment variables:', JSON.stringify(err.format(), null, 2));
        process.exit(1);
    }
    throw err;
}

//# sourceMappingURL=env.js.map