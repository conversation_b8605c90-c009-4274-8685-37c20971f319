{"version": 3, "sources": ["../../../../src/modules/marketing/entities/zalo-webhook-log.entity.ts"], "sourcesContent": ["import { Column, Entity, PrimaryGeneratedColumn } from 'typeorm';\r\n\r\n/**\r\n * Entity đại diện cho bảng zalo_webhook_logs trong cơ sở dữ liệu\r\n * Lưu trữ lịch sử các sự kiện webhook từ Zalo\r\n */\r\n@Entity('zalo_webhook_logs')\r\nexport class ZaloWebhookLog {\r\n  /**\r\n   * ID tự động tăng\r\n   */\r\n  @PrimaryGeneratedColumn({ name: 'id' })\r\n  id: number;\r\n\r\n  /**\r\n   * ID của Official Account\r\n   */\r\n  @Column({ name: 'oa_id', length: 50 })\r\n  oaId: string;\r\n\r\n  /**\r\n   * Tên sự kiện\r\n   */\r\n  @Column({ name: 'event_name', length: 50 })\r\n  eventName: string;\r\n\r\n  /**\r\n   * ID của sự kiện\r\n   */\r\n  @Column({ name: 'event_id', length: 50 })\r\n  eventId: string;\r\n\r\n  /**\r\n   * Dữ liệu của sự kiện (JSON)\r\n   */\r\n  @Column({ name: 'data', type: 'jsonb' })\r\n  data: any;\r\n\r\n  /**\r\n   * Đã xử lý chưa\r\n   */\r\n  @Column({ name: 'processed', default: false })\r\n  processed: boolean;\r\n\r\n  /**\r\n   * Thời điểm x<PERSON>y ra sự kiện (Unix timestamp)\r\n   */\r\n  @Column({ name: 'timestamp', type: 'bigint' })\r\n  timestamp: number;\r\n\r\n  /**\r\n   * Thời điểm tạo bản ghi (Unix timestamp)\r\n   */\r\n  @Column({ name: 'created_at', type: 'bigint' })\r\n  createdAt: number;\r\n}\r\n"], "names": ["ZaloWebhookLog", "name", "length", "type", "default"], "mappings": ";;;;+BAOaA;;;eAAAA;;;yBAP0C;;;;;;;;;;AAOhD,IAAA,AAAMA,iBAAN,MAAMA;AAgDb;;;QA5C4BC,MAAM;;;;;;QAMtBA,MAAM;QAASC,QAAQ;;;;;;QAMvBD,MAAM;QAAcC,QAAQ;;;;;;QAM5BD,MAAM;QAAYC,QAAQ;;;;;;QAM1BD,MAAM;QAAQE,MAAM;;;;;;QAMpBF,MAAM;QAAaG,SAAS;;;;;;QAM5BH,MAAM;QAAaE,MAAM;;;;;;QAMzBF,MAAM;QAAcE,MAAM"}