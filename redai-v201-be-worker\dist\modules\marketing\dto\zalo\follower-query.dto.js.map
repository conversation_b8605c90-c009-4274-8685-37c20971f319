{"version": 3, "sources": ["../../../../../src/modules/marketing/dto/zalo/follower-query.dto.ts"], "sourcesContent": ["import { ApiProperty } from '@nestjs/swagger';\r\nimport { IsEnum, IsOptional, IsString } from 'class-validator';\r\nimport { QueryDto, SortDirection } from '@common/dto';\r\n\r\n/**\r\n * Enum cho trạng thái người theo dõi\r\n */\r\nexport enum FollowerStatus {\r\n  ACTIVE = 'active',\r\n  UNFOLLOWED = 'unfollowed',\r\n  ALL = 'all',\r\n}\r\n\r\n/**\r\n * DTO cho việc truy vấn danh sách người theo dõi\r\n */\r\nexport class FollowerQueryDto extends QueryDto {\r\n  @ApiProperty({\r\n    description: 'Tìm kiếm theo tên hiển thị',\r\n    example: 'Nguyễn',\r\n    required: false,\r\n  })\r\n  @IsOptional()\r\n  @IsString()\r\n  displayName?: string;\r\n\r\n  @ApiProperty({\r\n    description: 'Tìm kiếm theo tag',\r\n    example: 'vip',\r\n    required: false,\r\n  })\r\n  @IsOptional()\r\n  @IsString()\r\n  tag?: string;\r\n\r\n  @ApiProperty({\r\n    description: 'Lọc theo trạng thái',\r\n    enum: FollowerStatus,\r\n    example: FollowerStatus.ACTIVE,\r\n    required: false,\r\n  })\r\n  @IsOptional()\r\n  @IsEnum(FollowerStatus)\r\n  status?: FollowerStatus;\r\n\r\n  constructor() {\r\n    super();\r\n    this.sortBy = 'followedAt';\r\n    this.sortDirection = SortDirection.DESC;\r\n  }\r\n}\r\n"], "names": ["Follower<PERSON><PERSON><PERSON><PERSON><PERSON>", "FollowerS<PERSON>us", "QueryDto", "constructor", "sortBy", "sortDirection", "SortDirection", "DESC", "description", "example", "required", "enum"], "mappings": ";;;;;;;;;;;QAgBaA;eAAAA;;QATDC;eAAAA;;;yBAPgB;gCACiB;qBACL;;;;;;;;;;AAKjC,IAAA,AAAKA,wCAAAA;;;;WAAAA;;AASL,IAAA,AAAMD,mBAAN,MAAMA,yBAAyBE,aAAQ;IA6B5CC,aAAc;QACZ,KAAK;QACL,IAAI,CAACC,MAAM,GAAG;QACd,IAAI,CAACC,aAAa,GAAGC,kBAAa,CAACC,IAAI;IACzC;AACF;;;QAhCIC,aAAa;QACbC,SAAS;QACTC,UAAU;;;;;;;;QAOVF,aAAa;QACbC,SAAS;QACTC,UAAU;;;;;;;;QAOVF,aAAa;QACbG,MAAMV;QACNQ,OAAO;QACPC,UAAU"}