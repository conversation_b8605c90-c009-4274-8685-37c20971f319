{"version": 3, "sources": ["../../../../../src/modules/marketing/dto/segment/segment-response.dto.ts"], "sourcesContent": ["import { ApiProperty } from '@nestjs/swagger';\r\nimport { SegmentCriteriaDto } from './segment-criteria.dto';\r\n\r\n/**\r\n * DTO cho phản hồi thông tin segment\r\n */\r\nexport class SegmentResponseDto {\r\n  /**\r\n   * ID của segment\r\n   * @example 1\r\n   */\r\n  @ApiProperty({\r\n    description: 'ID của segment',\r\n    example: 1,\r\n  })\r\n  id: number;\r\n\r\n  /**\r\n   * Tên segment\r\n   * @example \"Khách hàng tiềm năng\"\r\n   */\r\n  @ApiProperty({\r\n    description: 'Tên segment',\r\n    example: 'Khách hàng tiềm năng',\r\n  })\r\n  name: string;\r\n\r\n  /**\r\n   * Mô tả segment\r\n   * @example \"Khách hàng có khả năng mua hàng cao\"\r\n   */\r\n  @ApiProperty({\r\n    description: 'Mô tả segment',\r\n    example: 'Khách hàng có khả năng mua hàng cao',\r\n  })\r\n  description: string;\r\n\r\n  /**\r\n   * Điều kiện lọc khách hàng\r\n   */\r\n  @ApiProperty({\r\n    description: 'Đi<PERSON><PERSON> kiện lọc kh<PERSON>ch hàng',\r\n    type: SegmentCriteriaDto,\r\n  })\r\n  criteria: SegmentCriteriaDto;\r\n\r\n  /**\r\n   * Thời gian tạo (Unix timestamp)\r\n   * @example 1619171200\r\n   */\r\n  @ApiProperty({\r\n    description: 'Thời gian tạo (Unix timestamp)',\r\n    example: 1619171200,\r\n  })\r\n  createdAt: number;\r\n\r\n  /**\r\n   * Thời gian cập nhật (Unix timestamp)\r\n   * @example 1619171200\r\n   */\r\n  @ApiProperty({\r\n    description: 'Thời gian cập nhật (Unix timestamp)',\r\n    example: 1619171200,\r\n  })\r\n  updatedAt: number;\r\n}\r\n"], "names": ["SegmentResponseDto", "description", "example", "type", "SegmentCriteriaDto"], "mappings": ";;;;+BAMaA;;;eAAAA;;;yBANe;oCACO;;;;;;;;;;AAK5B,IAAA,AAAMA,qBAAN,MAAMA;AA2Db;;;QArDIC,aAAa;QACbC,SAAS;;;;;;QASTD,aAAa;QACbC,SAAS;;;;;;QASTD,aAAa;QACbC,SAAS;;;;;;QAQTD,aAAa;QACbE,MAAMC,sCAAkB;;;;;;QASxBH,aAAa;QACbC,SAAS;;;;;;QASTD,aAAa;QACbC,SAAS"}