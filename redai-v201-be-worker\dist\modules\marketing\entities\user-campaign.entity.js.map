{"version": 3, "sources": ["../../../../src/modules/marketing/entities/user-campaign.entity.ts"], "sourcesContent": ["import { Column, Entity, PrimaryGeneratedColumn } from 'typeorm';\r\n\r\n/**\r\n * Entity đại diện cho bảng user_campaigns trong cơ sở dữ liệu\r\n * Bảng chiến dịch của người dùng\r\n */\r\n@Entity('user_campaigns')\r\nexport class UserCampaign {\r\n  /**\r\n   * ID của campaign\r\n   */\r\n  @PrimaryGeneratedColumn({ name: 'id', type: 'bigint' })\r\n  id: number;\r\n\r\n  /**\r\n   * ID của người dùng\r\n   */\r\n  @Column({ name: 'user_id', nullable: true, comment: 'Mã người dùng' })\r\n  userId: number;\r\n\r\n  // Không sử dụng quan hệ với bảng User, chỉ lưu ID\r\n\r\n  /**\r\n   * Tiêu đề chiến dịch\r\n   */\r\n  @Column({ name: 'title', length: 255, nullable: true, comment: 'Tiêu đề' })\r\n  title: string;\r\n\r\n  /**\r\n   * <PERSON>ô tả chiến dịch\r\n   */\r\n  @Column({\r\n    name: 'description',\r\n    type: 'text',\r\n    nullable: true,\r\n    comment: '<PERSON><PERSON> tả',\r\n  })\r\n  description: string;\r\n\r\n  /**\r\n   * <PERSON><PERSON><PERSON> tảng gử<PERSON> (email, sms, ...)\r\n   */\r\n  @Column({\r\n    name: 'platform',\r\n    length: 255,\r\n    nullable: true,\r\n    comment: 'Nền tảng',\r\n  })\r\n  platform: string;\r\n\r\n  /**\r\n   * Nội dung chiến dịch\r\n   */\r\n  @Column({\r\n    name: 'content',\r\n    type: 'text',\r\n    nullable: true,\r\n    comment: 'Nội dung',\r\n  })\r\n  content: string;\r\n\r\n  /**\r\n   * Thông tin máy chủ gửi\r\n   */\r\n  @Column({\r\n    name: 'server',\r\n    type: 'jsonb',\r\n    nullable: true,\r\n    comment: 'Thông tin máy chủ gửi',\r\n  })\r\n  server: any;\r\n\r\n  /**\r\n   * Thời gian dự kiến gửi chiến dịch (Unix timestamp)\r\n   */\r\n  @Column({\r\n    name: 'scheduled_at',\r\n    type: 'bigint',\r\n    nullable: true,\r\n    comment: 'Thời gian dự kiến gửi chiến dịch',\r\n  })\r\n  scheduledAt: number;\r\n\r\n  /**\r\n   * Tiêu đề email (chỉ áp dụng cho chiến dịch email)\r\n   */\r\n  @Column({\r\n    name: 'subject',\r\n    length: 255,\r\n    nullable: true,\r\n    comment: 'Nội dung tiêu đề với chiến dịch là email',\r\n  })\r\n  subject: string;\r\n\r\n  /**\r\n   * Trạng thái chiến dịch\r\n   */\r\n  @Column({ name: 'status', length: 20, nullable: true, comment: 'Trạng thái' })\r\n  status: string;\r\n\r\n  /**\r\n   * Thời gian tạo (Unix timestamp)\r\n   */\r\n  @Column({\r\n    name: 'created_at',\r\n    type: 'bigint',\r\n    nullable: true,\r\n    comment: 'Ngày tạo',\r\n  })\r\n  createdAt: number;\r\n\r\n  /**\r\n   * Thời gian cập nhật (Unix timestamp)\r\n   */\r\n  @Column({\r\n    name: 'updated_at',\r\n    type: 'bigint',\r\n    nullable: true,\r\n    comment: 'Ngày cập nhật',\r\n  })\r\n  updatedAt: number;\r\n\r\n  // Không sử dụng quan hệ với bảng UserCampaignHistory, chỉ lưu ID\r\n\r\n  /**\r\n   * ID của segment (nếu có)\r\n   */\r\n  @Column({\r\n    name: 'segment_id',\r\n    type: 'bigint',\r\n    nullable: true,\r\n    comment: 'ID của segment',\r\n  })\r\n  segmentId: number | null;\r\n\r\n  /**\r\n   * Danh sách ID của audience (nếu có)\r\n   */\r\n  @Column({\r\n    name: 'audience_ids',\r\n    type: 'jsonb',\r\n    nullable: true,\r\n    comment: 'Danh sách ID của audience',\r\n  })\r\n  audienceIds: number[] | null;\r\n}\r\n"], "names": ["UserCampaign", "name", "type", "nullable", "comment", "length"], "mappings": ";;;;+BAOaA;;;eAAAA;;;yBAP0C;;;;;;;;;;AAOhD,IAAA,AAAMA,eAAN,MAAMA;AA0Ib;;;QAtI4BC,MAAM;QAAMC,MAAM;;;;;;QAMlCD,MAAM;QAAWE,UAAU;QAAMC,SAAS;;;;;;QAQ1CH,MAAM;QAASI,QAAQ;QAAKF,UAAU;QAAMC,SAAS;;;;;;QAO7DH,MAAM;QACNC,MAAM;QACNC,UAAU;QACVC,SAAS;;;;;;QAQTH,MAAM;QACNI,QAAQ;QACRF,UAAU;QACVC,SAAS;;;;;;QAQTH,MAAM;QACNC,MAAM;QACNC,UAAU;QACVC,SAAS;;;;;;QAQTH,MAAM;QACNC,MAAM;QACNC,UAAU;QACVC,SAAS;;;;;;QAQTH,MAAM;QACNC,MAAM;QACNC,UAAU;QACVC,SAAS;;;;;;QAQTH,MAAM;QACNI,QAAQ;QACRF,UAAU;QACVC,SAAS;;;;;;QAODH,MAAM;QAAUI,QAAQ;QAAIF,UAAU;QAAMC,SAAS;;;;;;QAO7DH,MAAM;QACNC,MAAM;QACNC,UAAU;QACVC,SAAS;;;;;;QAQTH,MAAM;QACNC,MAAM;QACNC,UAAU;QACVC,SAAS;;;;;;QAUTH,MAAM;QACNC,MAAM;QACNC,UAAU;QACVC,SAAS;;;;;;QAQTH,MAAM;QACNC,MAAM;QACNC,UAAU;QACVC,SAAS"}