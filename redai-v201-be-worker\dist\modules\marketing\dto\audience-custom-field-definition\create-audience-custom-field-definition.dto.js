"use strict";
Object.defineProperty(exports, "__esModule", {
    value: true
});
function _export(target, all) {
    for(var name in all)Object.defineProperty(target, name, {
        enumerable: true,
        get: Object.getOwnPropertyDescriptor(all, name).get
    });
}
_export(exports, {
    get CreateAudienceCustomFieldDefinitionDto () {
        return CreateAudienceCustomFieldDefinitionDto;
    },
    get CustomFieldDataType () {
        return CustomFieldDataType;
    }
});
const _swagger = require("@nestjs/swagger");
const _classvalidator = require("class-validator");
function _ts_decorate(decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for(var i = decorators.length - 1; i >= 0; i--)if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
}
function _ts_metadata(k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
}
var CustomFieldDataType = /*#__PURE__*/ function(CustomFieldDataType) {
    CustomFieldDataType["STRING"] = "string";
    CustomFieldDataType["INTEGER"] = "integer";
    CustomFieldDataType["DATE"] = "date";
    CustomFieldDataType["BOOLEAN"] = "boolean";
    CustomFieldDataType["JSON"] = "json";
    return CustomFieldDataType;
}({});
let CreateAudienceCustomFieldDefinitionDto = class CreateAudienceCustomFieldDefinitionDto {
};
_ts_decorate([
    (0, _swagger.ApiProperty)({
        description: 'Định danh duy nhất cho trường tùy chỉnh (chữ thường, không dấu cách)',
        example: 'customer_address'
    }),
    (0, _classvalidator.IsNotEmpty)({
        message: 'Định danh không được để trống'
    }),
    (0, _classvalidator.IsString)({
        message: 'Định danh phải là chuỗi'
    }),
    (0, _classvalidator.Matches)(/^[a-z0-9_]+$/, {
        message: 'Định danh chỉ được chứa chữ thường, số và dấu gạch dưới'
    }),
    _ts_metadata("design:type", String)
], CreateAudienceCustomFieldDefinitionDto.prototype, "fieldKey", void 0);
_ts_decorate([
    (0, _swagger.ApiProperty)({
        description: 'Tên hiển thị thân thiện với người dùng',
        example: 'Địa chỉ khách hàng'
    }),
    (0, _classvalidator.IsNotEmpty)({
        message: 'Tên hiển thị không được để trống'
    }),
    (0, _classvalidator.IsString)({
        message: 'Tên hiển thị phải là chuỗi'
    }),
    _ts_metadata("design:type", String)
], CreateAudienceCustomFieldDefinitionDto.prototype, "displayName", void 0);
_ts_decorate([
    (0, _swagger.ApiProperty)({
        description: 'Kiểu dữ liệu',
        enum: CustomFieldDataType,
        example: "string"
    }),
    (0, _classvalidator.IsNotEmpty)({
        message: 'Kiểu dữ liệu không được để trống'
    }),
    (0, _classvalidator.IsEnum)(CustomFieldDataType, {
        message: `Kiểu dữ liệu phải là một trong các giá trị: ${Object.values(CustomFieldDataType).join(', ')}`
    }),
    _ts_metadata("design:type", String)
], CreateAudienceCustomFieldDefinitionDto.prototype, "dataType", void 0);
_ts_decorate([
    (0, _swagger.ApiProperty)({
        description: 'Mô tả chi tiết hoặc ghi chú về trường tùy chỉnh',
        example: 'Địa chỉ liên hệ của khách hàng',
        required: false
    }),
    (0, _classvalidator.IsOptional)(),
    (0, _classvalidator.IsString)({
        message: 'Mô tả phải là chuỗi'
    }),
    _ts_metadata("design:type", String)
], CreateAudienceCustomFieldDefinitionDto.prototype, "description", void 0);

//# sourceMappingURL=create-audience-custom-field-definition.dto.js.map