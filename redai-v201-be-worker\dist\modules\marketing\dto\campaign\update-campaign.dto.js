"use strict";
Object.defineProperty(exports, "__esModule", {
    value: true
});
function _export(target, all) {
    for(var name in all)Object.defineProperty(target, name, {
        enumerable: true,
        get: Object.getOwnPropertyDescriptor(all, name).get
    });
}
_export(exports, {
    get CampaignStatus () {
        return CampaignStatus;
    },
    get UpdateCampaignDto () {
        return UpdateCampaignDto;
    }
});
const _classvalidator = require("class-validator");
const _swagger = require("@nestjs/swagger");
const _classtransformer = require("class-transformer");
const _createcampaigndto = require("./create-campaign.dto");
const _campaignserverdto = require("./campaign-server.dto");
function _ts_decorate(decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for(var i = decorators.length - 1; i >= 0; i--)if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
}
function _ts_metadata(k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
}
var CampaignStatus = /*#__PURE__*/ function(CampaignStatus) {
    CampaignStatus["DRAFT"] = "draft";
    CampaignStatus["SCHEDULED"] = "scheduled";
    CampaignStatus["RUNNING"] = "running";
    CampaignStatus["COMPLETED"] = "completed";
    CampaignStatus["CANCELLED"] = "cancelled";
    CampaignStatus["FAILED"] = "failed";
    return CampaignStatus;
}({});
let UpdateCampaignDto = class UpdateCampaignDto {
};
_ts_decorate([
    (0, _swagger.ApiProperty)({
        description: 'Tiêu đề chiến dịch',
        example: 'Khuyến mãi tháng 5',
        required: false
    }),
    (0, _classvalidator.IsOptional)(),
    (0, _classvalidator.IsString)({
        message: 'Tiêu đề phải là chuỗi'
    }),
    _ts_metadata("design:type", String)
], UpdateCampaignDto.prototype, "title", void 0);
_ts_decorate([
    (0, _swagger.ApiProperty)({
        description: 'Mô tả chiến dịch',
        example: 'Chiến dịch khuyến mãi dành cho khách hàng VIP',
        required: false
    }),
    (0, _classvalidator.IsOptional)(),
    (0, _classvalidator.IsString)({
        message: 'Mô tả phải là chuỗi'
    }),
    _ts_metadata("design:type", String)
], UpdateCampaignDto.prototype, "description", void 0);
_ts_decorate([
    (0, _swagger.ApiProperty)({
        description: 'Nền tảng gửi',
        enum: _createcampaigndto.CampaignPlatform,
        example: _createcampaigndto.CampaignPlatform.EMAIL,
        required: false
    }),
    (0, _classvalidator.IsOptional)(),
    (0, _classvalidator.IsIn)(Object.values(_createcampaigndto.CampaignPlatform), {
        message: `Nền tảng phải là một trong các giá trị: ${Object.values(_createcampaigndto.CampaignPlatform).join(', ')}`
    }),
    _ts_metadata("design:type", typeof _createcampaigndto.CampaignPlatform === "undefined" ? Object : _createcampaigndto.CampaignPlatform)
], UpdateCampaignDto.prototype, "platform", void 0);
_ts_decorate([
    (0, _swagger.ApiProperty)({
        description: 'Nội dung chiến dịch',
        example: '<p>Xin chào quý khách,</p><p>Chúng tôi xin gửi đến quý khách chương trình khuyến mãi...</p>',
        required: false
    }),
    (0, _classvalidator.IsOptional)(),
    (0, _classvalidator.IsString)({
        message: 'Nội dung phải là chuỗi'
    }),
    _ts_metadata("design:type", String)
], UpdateCampaignDto.prototype, "content", void 0);
_ts_decorate([
    (0, _swagger.ApiProperty)({
        description: 'Thông tin máy chủ gửi',
        type: _campaignserverdto.CampaignServerDto,
        required: false
    }),
    (0, _classvalidator.IsOptional)(),
    (0, _classvalidator.ValidateNested)(),
    (0, _classtransformer.Type)(()=>_campaignserverdto.CampaignServerDto),
    _ts_metadata("design:type", typeof _campaignserverdto.CampaignServerDto === "undefined" ? Object : _campaignserverdto.CampaignServerDto)
], UpdateCampaignDto.prototype, "server", void 0);
_ts_decorate([
    (0, _swagger.ApiProperty)({
        description: 'Thời gian dự kiến gửi chiến dịch (Unix timestamp)',
        example: 1619171200,
        required: false
    }),
    (0, _classvalidator.IsOptional)(),
    (0, _classvalidator.IsNumber)({}, {
        message: 'Thời gian dự kiến phải là số'
    }),
    _ts_metadata("design:type", Number)
], UpdateCampaignDto.prototype, "scheduledAt", void 0);
_ts_decorate([
    (0, _swagger.ApiProperty)({
        description: 'Tiêu đề email (chỉ áp dụng cho chiến dịch email)',
        example: 'Khuyến mãi đặc biệt dành cho bạn',
        required: false
    }),
    (0, _classvalidator.IsOptional)(),
    (0, _classvalidator.IsString)({
        message: 'Tiêu đề email phải là chuỗi'
    }),
    _ts_metadata("design:type", String)
], UpdateCampaignDto.prototype, "subject", void 0);
_ts_decorate([
    (0, _swagger.ApiProperty)({
        description: 'Trạng thái chiến dịch',
        enum: CampaignStatus,
        example: "draft",
        required: false
    }),
    (0, _classvalidator.IsOptional)(),
    (0, _classvalidator.IsIn)(Object.values(CampaignStatus), {
        message: `Trạng thái phải là một trong các giá trị: ${Object.values(CampaignStatus).join(', ')}`
    }),
    _ts_metadata("design:type", String)
], UpdateCampaignDto.prototype, "status", void 0);
_ts_decorate([
    (0, _swagger.ApiProperty)({
        description: 'ID của segment',
        example: 1,
        required: false
    }),
    (0, _classvalidator.IsOptional)(),
    (0, _classvalidator.IsNumber)({}, {
        message: 'ID segment phải là số'
    }),
    _ts_metadata("design:type", Number)
], UpdateCampaignDto.prototype, "segmentId", void 0);
_ts_decorate([
    (0, _swagger.ApiProperty)({
        description: 'Danh sách ID của audience',
        example: [
            1,
            2,
            3
        ],
        required: false,
        type: [
            Number
        ]
    }),
    (0, _classvalidator.IsOptional)(),
    _ts_metadata("design:type", Array)
], UpdateCampaignDto.prototype, "audienceIds", void 0);

//# sourceMappingURL=update-campaign.dto.js.map