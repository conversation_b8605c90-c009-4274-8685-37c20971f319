"use strict";
Object.defineProperty(exports, "__esModule", {
    value: true
});
Object.defineProperty(exports, "AdminTemplateEmail", {
    enumerable: true,
    get: function() {
        return AdminTemplateEmail;
    }
});
const _typeorm = require("typeorm");
function _ts_decorate(decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for(var i = decorators.length - 1; i >= 0; i--)if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
}
function _ts_metadata(k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
}
let AdminTemplateEmail = class AdminTemplateEmail {
};
_ts_decorate([
    (0, _typeorm.PrimaryGeneratedColumn)({
        name: 'id'
    }),
    _ts_metadata("design:type", Number)
], AdminTemplateEmail.prototype, "id", void 0);
_ts_decorate([
    (0, _typeorm.Column)({
        name: 'name',
        length: 100,
        nullable: true,
        comment: 'Tên mẫu'
    }),
    _ts_metadata("design:type", String)
], AdminTemplateEmail.prototype, "name", void 0);
_ts_decorate([
    (0, _typeorm.Column)({
        name: 'subject',
        length: 255,
        nullable: true,
        comment: 'Tiêu đề'
    }),
    _ts_metadata("design:type", String)
], AdminTemplateEmail.prototype, "subject", void 0);
_ts_decorate([
    (0, _typeorm.Column)({
        name: 'category',
        length: 100,
        nullable: true,
        unique: true,
        comment: 'Danh mục email'
    }),
    _ts_metadata("design:type", String)
], AdminTemplateEmail.prototype, "category", void 0);
_ts_decorate([
    (0, _typeorm.Column)({
        name: 'content',
        type: 'text',
        nullable: true,
        comment: 'Nội dung email'
    }),
    _ts_metadata("design:type", String)
], AdminTemplateEmail.prototype, "content", void 0);
_ts_decorate([
    (0, _typeorm.Column)({
        name: 'placeholders',
        type: 'json',
        nullable: true,
        comment: 'Danh sách các placeholder'
    }),
    _ts_metadata("design:type", Object)
], AdminTemplateEmail.prototype, "placeholders", void 0);
_ts_decorate([
    (0, _typeorm.Column)({
        name: 'created_by',
        type: 'bigint',
        nullable: true
    }),
    _ts_metadata("design:type", Number)
], AdminTemplateEmail.prototype, "createdBy", void 0);
_ts_decorate([
    (0, _typeorm.Column)({
        name: 'updated_by',
        type: 'bigint',
        nullable: true
    }),
    _ts_metadata("design:type", Number)
], AdminTemplateEmail.prototype, "updatedBy", void 0);
_ts_decorate([
    (0, _typeorm.Column)({
        name: 'created_at',
        type: 'bigint',
        nullable: true,
        comment: 'Thời gian tạo'
    }),
    _ts_metadata("design:type", Number)
], AdminTemplateEmail.prototype, "createdAt", void 0);
_ts_decorate([
    (0, _typeorm.Column)({
        name: 'updated_at',
        type: 'bigint',
        nullable: true,
        comment: 'Thời gian cập nhật'
    }),
    _ts_metadata("design:type", Number)
], AdminTemplateEmail.prototype, "updatedAt", void 0);
AdminTemplateEmail = _ts_decorate([
    (0, _typeorm.Entity)('admin_template_email')
], AdminTemplateEmail);

//# sourceMappingURL=admin-template-email.entity.js.map