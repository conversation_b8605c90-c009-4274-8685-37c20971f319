"use strict";
Object.defineProperty(exports, "__esModule", {
    value: true
});
Object.defineProperty(exports, "FptSmsProvider", {
    enumerable: true,
    get: function() {
        return FptSmsProvider;
    }
});
const _common = require("@nestjs/common");
const _axios = require("@nestjs/axios");
const _config = require("@nestjs/config");
const _rxjs = require("rxjs");
const _operators = require("rxjs/operators");
const _basesmsproviderservice = require("./base-sms-provider.service");
const _smsproviderinterface = require("./sms-provider.interface");
const _env = require("../../../config/env");
function _ts_decorate(decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for(var i = decorators.length - 1; i >= 0; i--)if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
}
function _ts_metadata(k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
}
let FptSmsProvider = class FptSmsProvider extends _basesmsproviderservice.BaseSmsProvider {
    /**
   * Lấy token truy cập hợp lệ cho API FPT SMS
   * @param forceRefresh Buộc làm mới token ngay cả khi nó vẫn còn hợp lệ
   * @returns Token truy cập
   */ async getAccessToken(forceRefresh = false) {
        // Kiểm tra xem token có còn hợp lệ không
        const now = new Date();
        if (!forceRefresh && this.accessToken && this.tokenExpiration && this.tokenExpiration > now) {
            // Token vẫn còn hợp lệ, trả về token
            return this.accessToken;
        }
        try {
            this.logger.debug('Lấy token truy cập mới cho FPT SMS');
            const response = await (0, _rxjs.firstValueFrom)(this.httpService.post(`${this.apiUrl}/oauth2/token`, {
                grant_type: 'client_credentials',
                client_id: this.clientId,
                client_secret: this.clientSecret,
                scope: this.scope,
                session_id: Date.now().toString()
            }, {
                headers: {
                    'Content-Type': 'application/json'
                }
            }).pipe((0, _operators.catchError)((error)=>{
                this.logger.error(`Erreur lors de l'obtention du token d'accès FPT SMS: ${error.message}`, error.stack);
                throw error;
            })));
            if (response.data.error) {
                throw new Error(`Erreur FPT SMS: ${response.data.error_description}`);
            }
            // Lưu token và tính toán thời gian hết hạn của nó
            this.accessToken = response.data.access_token;
            this.tokenExpiration = new Date(now.getTime() + response.data.expires_in * 1000);
            // Kiểm tra token không phải là null
            if (!this.accessToken) {
                throw new Error('Token truy cập FPT SMS là null');
            }
            return this.accessToken;
        } catch (error) {
            this.logger.error(`Lỗi khi lấy token truy cập FPT SMS: ${error.message}`, error.stack);
            throw error;
        }
    }
    /**
   * Gửi tin nhắn SMS đến một số điện thoại qua FPT SMS
   * @param phoneNumber Số điện thoại của người nhận
   * @param message Nội dung tin nhắn
   * @param options Các tùy chọn bổ sung (brandName)
   * @returns Promise chứa ID tin nhắn hoặc lỗi
   */ async sendSms(phoneNumber, message, options) {
        try {
            this.logger.debug(`Gửi SMS đến ${phoneNumber} qua FPT SMS`);
            const brandName = options?.brandName || this.defaultBrandName;
            if (!brandName) {
                throw new Error('Tham số "brandName" là bắt buộc đối với FPT SMS');
            }
            // Lấy token truy cập hợp lệ
            const accessToken = await this.getAccessToken();
            // Định dạng số điện thoại
            const formattedPhoneNumber = this.formatPhoneNumber(phoneNumber);
            // Xóa dấu + cho FPT SMS
            const cleanedPhoneNumber = formattedPhoneNumber.startsWith('+') ? formattedPhoneNumber.substring(1) : formattedPhoneNumber;
            // Gửi SMS qua FPT SMS
            const response = await (0, _rxjs.firstValueFrom)(this.httpService.post(`${this.apiUrl}/push-brandname-otp`, {
                access_token: accessToken,
                session_id: Date.now().toString(),
                BrandName: brandName,
                Phone: cleanedPhoneNumber,
                Message: message,
                Quota: options?.quota || 1
            }, {
                headers: {
                    'Content-Type': 'application/json'
                }
            }).pipe((0, _operators.catchError)((error)=>{
                this.logger.error(`Erreur lors de l'envoi du SMS via FPT SMS: ${error.message}`, error.stack);
                throw error;
            })));
            if (response.data.errorid === 0) {
                return {
                    success: true,
                    messageId: response.data.requestid,
                    rawResponse: response.data
                };
            } else {
                return {
                    success: false,
                    errorCode: response.data.errorid.toString(),
                    errorMessage: response.data.errordes || 'Lỗi không xác định',
                    rawResponse: response.data
                };
            }
        } catch (error) {
            this.logger.error(`Lỗi khi gửi SMS qua FPT SMS: ${error.message}`, error.stack);
            return {
                success: false,
                errorMessage: error.message || 'Lỗi không xác định'
            };
        }
    }
    /**
   * Kiểm tra trạng thái của tin nhắn đã gửi qua FPT SMS
   * @param messageId ID của tin nhắn cần kiểm tra
   * @returns Promise chứa trạng thái của tin nhắn
   */ async checkMessageStatus(messageId) {
        try {
            this.logger.debug(`Kiểm tra trạng thái tin nhắn ${messageId} qua FPT SMS`);
            // Lấy token truy cập hợp lệ
            const accessToken = await this.getAccessToken();
            // Kiểm tra trạng thái tin nhắn qua FPT SMS
            const response = await (0, _rxjs.firstValueFrom)(this.httpService.post(`${this.apiUrl}/dlr-otp-recheck`, {
                access_token: accessToken,
                session_id: Date.now().toString(),
                RequestCode: messageId
            }, {
                headers: {
                    'Content-Type': 'application/json'
                }
            }).pipe((0, _operators.catchError)((error)=>{
                this.logger.error(`Erreur lors de la vérification du statut du message via FPT SMS: ${error.message}`, error.stack);
                throw error;
            })));
            // Xác định trạng thái dựa trên phản hồi
            let status = _smsproviderinterface.MessageStatus.UNKNOWN;
            let details = '';
            if (response.data.errorid === 0) {
                status = _smsproviderinterface.MessageStatus.DELIVERED;
            } else {
                status = _smsproviderinterface.MessageStatus.FAILED;
                details = response.data.errordes || 'Lỗi không xác định';
            }
            return {
                messageId,
                status,
                updatedAt: new Date(),
                details,
                rawResponse: response.data
            };
        } catch (error) {
            this.logger.error(`Lỗi khi kiểm tra trạng thái tin nhắn qua FPT SMS: ${error.message}`, error.stack);
            return {
                messageId,
                status: _smsproviderinterface.MessageStatus.UNKNOWN,
                updatedAt: new Date(),
                details: error.message || 'Lỗi không xác định'
            };
        }
    }
    /**
   * Gửi tin nhắn SMS với brandname qua FPT SMS
   * @param phoneNumber Số điện thoại của người nhận
   * @param message Nội dung tin nhắn
   * @param brandname Tên thương hiệu sử dụng làm người gửi
   * @param options Các tùy chọn bổ sung
   * @returns Promise chứa ID tin nhắn hoặc lỗi
   */ async sendBrandnameSms(phoneNumber, message, brandname, options) {
        try {
            this.logger.debug(`Gửi SMS brandname đến ${phoneNumber} qua FPT SMS`);
            // Lấy token truy cập hợp lệ
            const accessToken = await this.getAccessToken();
            // Định dạng số điện thoại
            const formattedPhoneNumber = this.formatPhoneNumber(phoneNumber);
            // Xóa dấu + cho FPT SMS
            const cleanedPhoneNumber = formattedPhoneNumber.startsWith('+') ? formattedPhoneNumber.substring(1) : formattedPhoneNumber;
            // Tạo chiến dịch cho SMS brandname
            const campaignResponse = await (0, _rxjs.firstValueFrom)(this.httpService.post(`${this.apiUrl}/create-campaign`, {
                access_token: accessToken,
                session_id: Date.now().toString(),
                CampaignName: options?.campaignName || `Campaign_${Date.now()}`,
                BrandName: brandname,
                Message: message,
                ScheduleTime: options?.scheduleTime || this.formatScheduleTime(new Date()),
                Quota: options?.quota || 1
            }, {
                headers: {
                    'Content-Type': 'application/json'
                }
            }).pipe((0, _operators.catchError)((error)=>{
                this.logger.error(`Erreur lors de la création de la campagne FPT SMS: ${error.message}`, error.stack);
                throw error;
            })));
            if (campaignResponse.data.errorid !== 0) {
                return {
                    success: false,
                    errorCode: campaignResponse.data.errorid.toString(),
                    errorMessage: campaignResponse.data.errordes || 'Lỗi khi tạo chiến dịch',
                    rawResponse: campaignResponse.data
                };
            }
            // Gửi SMS brandname
            const adsResponse = await (0, _rxjs.firstValueFrom)(this.httpService.post(`${this.apiUrl}/push-ads`, {
                access_token: accessToken,
                session_id: Date.now().toString(),
                CampaignCode: campaignResponse.data.campaigncode,
                PhoneList: cleanedPhoneNumber
            }, {
                headers: {
                    'Content-Type': 'application/json'
                }
            }).pipe((0, _operators.catchError)((error)=>{
                this.logger.error(`Erreur lors de l'envoi du SMS brandname via FPT SMS: ${error.message}`, error.stack);
                throw error;
            })));
            if (adsResponse.data.errorid === 0) {
                return {
                    success: true,
                    messageId: adsResponse.data.requestid,
                    rawResponse: {
                        campaign: campaignResponse.data,
                        ads: adsResponse.data
                    }
                };
            } else {
                return {
                    success: false,
                    errorCode: adsResponse.data.errorid.toString(),
                    errorMessage: adsResponse.data.errordes || 'Lỗi không xác định',
                    rawResponse: {
                        campaign: campaignResponse.data,
                        ads: adsResponse.data
                    }
                };
            }
        } catch (error) {
            this.logger.error(`Lỗi khi gửi SMS brandname qua FPT SMS: ${error.message}`, error.stack);
            return {
                success: false,
                errorMessage: error.message || 'Lỗi không xác định'
            };
        }
    }
    /**
   * Gửi tin nhắn SMS OTP qua FPT SMS
   * @param phoneNumber Số điện thoại của người nhận
   * @param otpCode Mã OTP cần gửi
   * @param options Các tùy chọn bổ sung (brandName, template)
   * @returns Promise chứa ID tin nhắn hoặc lỗi
   */ async sendOtp(phoneNumber, otpCode, options) {
        this.logger.debug(`Gửi mã OTP ${otpCode} đến số điện thoại ${phoneNumber} qua FPT SMS`);
        // Xây dựng tin nhắn OTP
        const message = options?.template ? options.template.replace('{code}', otpCode) : `Mã xác thực của bạn là: ${otpCode}`;
        // Sử dụng API push-brandname-otp để gửi OTP
        return this.sendSms(phoneNumber, message, options);
    }
    /**
   * Khởi tạo campaign cho SMS brandname qua FPT SMS
   * @param request Thông tin campaign cần tạo
   * @returns Promise chứa mã campaign hoặc lỗi
   */ async createCampaign(request) {
        try {
            this.logger.debug(`Khởi tạo campaign "${request.campaignName}" qua FPT SMS`);
            // Lấy token truy cập hợp lệ
            const accessToken = await this.getAccessToken();
            // Chuẩn bị payload theo tài liệu API
            const payload = {
                access_token: accessToken,
                session_id: Date.now().toString(),
                CampaignName: request.campaignName,
                BrandName: request.brandName,
                Message: request.message,
                ScheduleTime: request.scheduleTime,
                Quota: request.quota
            };
            this.logger.debug('Payload gửi tạo campaign:', payload);
            // Gửi request tạo campaign
            const response = await (0, _rxjs.firstValueFrom)(this.httpService.post(`${this.apiUrl}/create-campaign`, payload, {
                headers: {
                    'Content-Type': 'application/json'
                }
            }).pipe((0, _operators.catchError)((error)=>{
                this.logger.error(`Lỗi khi tạo campaign qua FPT SMS: ${error.message}`, error.stack);
                throw error;
            })));
            // Kiểm tra response thành công (HTTP 200 và có CampaignCode)
            if (response.status === 200 && response.data.CampaignCode) {
                const responseData = response.data;
                this.logger.debug(`Campaign "${request.campaignName}" đã được tạo thành công với mã: ${responseData.CampaignCode}`);
                return {
                    success: true,
                    campaignCode: responseData.CampaignCode,
                    rawResponse: responseData
                };
            } else if (response.data.error) {
                // Trường hợp lỗi với error code
                this.logger.warn(`Lỗi tạo campaign: ${response.data.error} - ${response.data.error_description}`);
                return {
                    success: false,
                    errorCode: response.data.error.toString(),
                    errorMessage: response.data.error_description || 'Lỗi không xác định',
                    rawResponse: response.data
                };
            } else {
                // Trường hợp khác
                return {
                    success: false,
                    errorMessage: 'Phản hồi không hợp lệ từ FPT SMS',
                    rawResponse: response.data
                };
            }
        } catch (error) {
            this.logger.error(`Lỗi khi tạo campaign qua FPT SMS: ${error.message}`, error.stack);
            return {
                success: false,
                errorMessage: error.message || 'Lỗi không xác định'
            };
        }
    }
    /**
   * Gửi tin nhắn CSKH quốc tế qua FPT SMS
   * @param phoneNumber Số điện thoại quốc tế của người nhận (ví dụ: 1749123456)
   * @param message Nội dung tin nhắn (sẽ được mã hóa Base64 tự động)
   * @param options Các tùy chọn bổ sung (brandName, requestId)
   * @returns Promise chứa thông tin tin nhắn đã gửi hoặc lỗi
   */ async sendInternationalSms(phoneNumber, message, options) {
        try {
            this.logger.debug(`Gửi SMS CSKH quốc tế đến ${phoneNumber} qua FPT SMS`);
            const brandName = options?.brandName || this.defaultBrandName;
            if (!brandName) {
                throw new Error('Tham số "brandName" là bắt buộc đối với FPT SMS quốc tế');
            }
            // Lấy token truy cập hợp lệ
            const accessToken = await this.getAccessToken();
            // Mã hóa nội dung tin nhắn thành Base64
            const encodedMessage = Buffer.from(message, 'utf8').toString('base64');
            // Chuẩn bị payload
            const payload = {
                access_token: accessToken,
                session_id: Date.now().toString(),
                BrandName: brandName,
                Phone: phoneNumber,
                Message: encodedMessage,
                ...options?.requestId && {
                    RequestId: options.requestId
                }
            };
            // Gửi SMS CSKH quốc tế qua FPT SMS
            const response = await (0, _rxjs.firstValueFrom)(this.httpService.post(`${this.apiUrl}/push-brandname-international`, payload, {
                headers: {
                    'Content-Type': 'application/json'
                }
            }).pipe((0, _operators.catchError)((error)=>{
                this.logger.error(`Lỗi khi gửi SMS CSKH quốc tế qua FPT SMS: ${error.message}`, error.stack);
                throw error;
            })));
            // Kiểm tra response thành công (HTTP 200 và có MessageId)
            if (response.status === 200 && response.data.MessageId) {
                const responseData = response.data;
                return {
                    success: true,
                    messageId: responseData.MessageId.toString(),
                    rawResponse: responseData
                };
            } else if (response.data.error) {
                // Trường hợp lỗi với error code
                return {
                    success: false,
                    errorCode: response.data.error.toString(),
                    errorMessage: response.data.error_description || 'Lỗi không xác định',
                    rawResponse: response.data
                };
            } else {
                // Trường hợp khác
                return {
                    success: false,
                    errorMessage: 'Phản hồi không hợp lệ từ FPT SMS',
                    rawResponse: response.data
                };
            }
        } catch (error) {
            this.logger.error(`Lỗi khi gửi SMS CSKH quốc tế qua FPT SMS: ${error.message}`, error.stack);
            return {
                success: false,
                errorMessage: error.message || 'Lỗi không xác định'
            };
        }
    }
    /**
   * Kiểm tra kết nối với FPT SMS
   * @param config Cấu hình của FPT SMS
   * @returns Promise chỉ ra liệu kết nối có thành công hay không
   */ async testConnection(config) {
        try {
            this.logger.debug('Kiểm tra kết nối với FPT SMS');
            const clientId = config.clientId || this.clientId;
            const clientSecret = config.clientSecret || this.clientSecret;
            const scope = config.scope || this.scope;
            const apiUrl = config.apiUrl || this.apiUrl;
            if (!clientId || !clientSecret) {
                return {
                    success: false,
                    message: 'Thiếu thông tin xác thực FPT SMS'
                };
            }
            // Kiểm tra việc lấy token truy cập
            const response = await (0, _rxjs.firstValueFrom)(this.httpService.post(`${apiUrl}/oauth2/token`, {
                grant_type: 'client_credentials',
                client_id: clientId,
                client_secret: clientSecret,
                scope: scope,
                session_id: Date.now().toString()
            }, {
                headers: {
                    'Content-Type': 'application/json'
                }
            }).pipe((0, _operators.catchError)((error)=>{
                this.logger.error(`Erreur lors du test de connexion avec FPT SMS: ${error.message}`, error.stack);
                throw error;
            })));
            if (response.data.error) {
                return {
                    success: false,
                    message: response.data.error_description || 'Lỗi không xác định',
                    details: response.data
                };
            }
            return {
                success: true,
                message: 'Kết nối thành công',
                details: {
                    accessToken: response.data.access_token,
                    expiresIn: response.data.expires_in,
                    tokenType: response.data.token_type,
                    scope: response.data.scope
                }
            };
        } catch (error) {
            this.logger.error(`Lỗi khi kiểm tra kết nối với FPT SMS: ${error.message}`, error.stack);
            return {
                success: false,
                message: error.message || 'Lỗi không xác định'
            };
        }
    }
    /**
   * Định dạng ngày tháng theo định dạng yêu cầu của FPT SMS (yyyy-MM-dd HH:mm)
   * @param date Ngày cần định dạng
   * @returns Chuỗi ngày đã định dạng
   */ formatScheduleTime(date) {
        const year = date.getFullYear();
        const month = String(date.getMonth() + 1).padStart(2, '0');
        const day = String(date.getDate()).padStart(2, '0');
        const hours = String(date.getHours()).padStart(2, '0');
        const minutes = String(date.getMinutes()).padStart(2, '0');
        return `${year}-${month}-${day} ${hours}:${minutes}`;
    }
    /**
   * Tạo tên campaign duy nhất
   * @param prefix Tiền tố cho tên campaign (tùy chọn)
   * @returns Tên campaign duy nhất
   */ static generateUniqueCampaignName(prefix = 'Campaign') {
        const timestamp = Date.now();
        const random = Math.random().toString(36).substring(2, 8);
        return `${prefix}_${timestamp}_${random}`;
    }
    /**
   * Validate định dạng thời gian schedule
   * @param scheduleTime Thời gian cần validate (yyyy-mm-dd HH:ii)
   * @returns true nếu định dạng hợp lệ
   */ static validateScheduleTime(scheduleTime) {
        const regex = /^\d{4}-\d{2}-\d{2} \d{2}:\d{2}$/;
        if (!regex.test(scheduleTime)) {
            return false;
        }
        // Kiểm tra thời gian có hợp lệ không
        const [datePart, timePart] = scheduleTime.split(' ');
        const [year, month, day] = datePart.split('-').map(Number);
        const [hour, minute] = timePart.split(':').map(Number);
        const date = new Date(year, month - 1, day, hour, minute);
        return date.getFullYear() === year && date.getMonth() === month - 1 && date.getDate() === day && date.getHours() === hour && date.getMinutes() === minute;
    }
    /**
   * Tính toán quota cần thiết cho campaign
   * @param messageLength Độ dài tin nhắn (ký tự)
   * @param phoneCount Số lượng số điện thoại
   * @returns Quota cần thiết
   */ static calculateQuota(messageLength, phoneCount) {
        // Mỗi tin nhắn SMS có thể chứa tối đa 160 ký tự
        // Nếu vượt quá sẽ được chia thành nhiều tin nhắn
        const smsPerMessage = Math.ceil(messageLength / 160);
        return smsPerMessage * phoneCount;
    }
    constructor(httpService, configService){
        super('FptSmsProvider'), this.httpService = httpService, this.configService = configService, this.providerName = 'FPT SMS', this.accessToken = null, this.tokenExpiration = null;
        // Tải cấu hình từ biến môi trường hoặc sử dụng giá trị mặc định
        this.clientId = _env.env.fpt.FPT_SMS_CLIENT_ID || '';
        this.clientSecret = _env.env.fpt.FPT_SMS_CLIENT_SECRET || '';
        this.scope = _env.env.fpt.FPT_SMS_SCOPE || 'send_brandname_otp send_brandname';
        this.apiUrl = _env.env.fpt.FPT_SMS_API_URL || 'http://api.fpt.net/api';
        this.defaultBrandName = _env.env.fpt.FPT_SMS_BRANDNAME || '';
    }
};
FptSmsProvider = _ts_decorate([
    (0, _common.Injectable)(),
    _ts_metadata("design:type", Function),
    _ts_metadata("design:paramtypes", [
        typeof _axios.HttpService === "undefined" ? Object : _axios.HttpService,
        typeof _config.ConfigService === "undefined" ? Object : _config.ConfigService
    ])
], FptSmsProvider);

//# sourceMappingURL=fpt-sms-provider.service.js.map