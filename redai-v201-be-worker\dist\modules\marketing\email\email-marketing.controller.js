"use strict";
Object.defineProperty(exports, "__esModule", {
    value: true
});
Object.defineProperty(exports, "EmailMarketingController", {
    enumerable: true,
    get: function() {
        return EmailMarketingController;
    }
});
const _common = require("@nestjs/common");
const _services = require("./services");
function _ts_decorate(decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for(var i = decorators.length - 1; i >= 0; i--)if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
}
function _ts_metadata(k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
}
function _ts_param(paramIndex, decorator) {
    return function(target, key) {
        decorator(target, key, paramIndex);
    };
}
let EmailMarketingController = class EmailMarketingController {
    /**
   * Tạo jobs email marketing cho một campaign
   * @param campaignId ID của campaign
   * @returns Kết quả tạo jobs
   */ async createCampaignJobs(campaignId) {
        try {
            const id = parseInt(campaignId);
            if (isNaN(id)) {
                return {
                    success: false,
                    message: 'Invalid campaign ID'
                };
            }
            const jobCount = await this.emailMarketingService.createEmailMarketingJobs(id);
            return {
                success: true,
                message: `Created ${jobCount} email marketing jobs`,
                data: {
                    campaignId: id,
                    jobCount
                }
            };
        } catch (error) {
            this.logger.error(`Error creating campaign jobs: ${error.message}`, error.stack);
            return {
                success: false,
                message: error.message
            };
        }
    }
    /**
   * Hủy tất cả jobs của một campaign
   * @param campaignId ID của campaign
   * @returns Kết quả hủy jobs
   */ async cancelCampaignJobs(campaignId) {
        try {
            const id = parseInt(campaignId);
            if (isNaN(id)) {
                return {
                    success: false,
                    message: 'Invalid campaign ID'
                };
            }
            const canceledCount = await this.emailMarketingService.cancelCampaignJobs(id);
            return {
                success: true,
                message: `Canceled ${canceledCount} jobs`,
                data: {
                    campaignId: id,
                    canceledCount
                }
            };
        } catch (error) {
            this.logger.error(`Error canceling campaign jobs: ${error.message}`, error.stack);
            return {
                success: false,
                message: error.message
            };
        }
    }
    /**
   * Lấy thống kê campaign
   * @param campaignId ID của campaign
   * @returns Thống kê campaign
   */ async getCampaignStats(campaignId) {
        try {
            const id = parseInt(campaignId);
            if (isNaN(id)) {
                return {
                    success: false,
                    message: 'Invalid campaign ID'
                };
            }
            const stats = await this.emailMarketingService.getCampaignStats(id);
            return {
                success: true,
                data: stats
            };
        } catch (error) {
            this.logger.error(`Error getting campaign stats: ${error.message}`, error.stack);
            return {
                success: false,
                message: error.message
            };
        }
    }
    /**
   * Lấy trạng thái queue
   * @returns Thông tin trạng thái queue
   */ async getQueueStatus() {
        try {
            const status = await this.emailMarketingService.getQueueStatus();
            return {
                success: true,
                data: status
            };
        } catch (error) {
            this.logger.error(`Error getting queue status: ${error.message}`, error.stack);
            return {
                success: false,
                message: error.message
            };
        }
    }
    /**
   * Test endpoint để tạo campaign mẫu
   * @param body Dữ liệu test
   * @returns Kết quả test
   */ async createSampleJobs(body) {
        try {
            const { campaignId, count = 1 } = body;
            if (!campaignId) {
                return {
                    success: false,
                    message: 'Campaign ID is required'
                };
            }
            const jobCount = await this.emailMarketingService.createEmailMarketingJobs(campaignId);
            return {
                success: true,
                message: `Test completed. Created ${jobCount} jobs for campaign ${campaignId}`,
                data: {
                    campaignId,
                    jobCount
                }
            };
        } catch (error) {
            this.logger.error(`Error creating sample jobs: ${error.message}`, error.stack);
            return {
                success: false,
                message: error.message
            };
        }
    }
    constructor(emailMarketingService){
        this.emailMarketingService = emailMarketingService;
        this.logger = new _common.Logger(EmailMarketingController.name);
    }
};
_ts_decorate([
    (0, _common.Post)('campaigns/:campaignId/jobs'),
    _ts_param(0, (0, _common.Param)('campaignId')),
    _ts_metadata("design:type", Function),
    _ts_metadata("design:paramtypes", [
        String
    ]),
    _ts_metadata("design:returntype", Promise)
], EmailMarketingController.prototype, "createCampaignJobs", null);
_ts_decorate([
    (0, _common.Delete)('campaigns/:campaignId/jobs'),
    _ts_param(0, (0, _common.Param)('campaignId')),
    _ts_metadata("design:type", Function),
    _ts_metadata("design:paramtypes", [
        String
    ]),
    _ts_metadata("design:returntype", Promise)
], EmailMarketingController.prototype, "cancelCampaignJobs", null);
_ts_decorate([
    (0, _common.Get)('campaigns/:campaignId/stats'),
    _ts_param(0, (0, _common.Param)('campaignId')),
    _ts_metadata("design:type", Function),
    _ts_metadata("design:paramtypes", [
        String
    ]),
    _ts_metadata("design:returntype", Promise)
], EmailMarketingController.prototype, "getCampaignStats", null);
_ts_decorate([
    (0, _common.Get)('queue/status'),
    _ts_metadata("design:type", Function),
    _ts_metadata("design:paramtypes", []),
    _ts_metadata("design:returntype", Promise)
], EmailMarketingController.prototype, "getQueueStatus", null);
_ts_decorate([
    (0, _common.Post)('test/create-sample-jobs'),
    _ts_param(0, (0, _common.Body)()),
    _ts_metadata("design:type", Function),
    _ts_metadata("design:paramtypes", [
        Object
    ]),
    _ts_metadata("design:returntype", Promise)
], EmailMarketingController.prototype, "createSampleJobs", null);
EmailMarketingController = _ts_decorate([
    (0, _common.Controller)('api/email-marketing'),
    _ts_metadata("design:type", Function),
    _ts_metadata("design:paramtypes", [
        typeof _services.EmailMarketingService === "undefined" ? Object : _services.EmailMarketingService
    ])
], EmailMarketingController);

//# sourceMappingURL=email-marketing.controller.js.map