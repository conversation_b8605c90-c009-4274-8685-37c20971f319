{"version": 3, "sources": ["../../../../../src/modules/marketing/dto/zalo/message-query.dto.ts"], "sourcesContent": ["import { ApiProperty } from '@nestjs/swagger';\r\nimport { IsEnum, IsOptional, IsString } from 'class-validator';\r\nimport { QueryDto, SortDirection } from '@common/dto';\r\n\r\n/**\r\n * Enum cho hướng tin nhắn\r\n */\r\nexport enum MessageDirection {\r\n  INCOMING = 'incoming',\r\n  OUTGOING = 'outgoing',\r\n  ALL = 'all',\r\n}\r\n\r\n/**\r\n * Enum cho loại tin nhắn\r\n */\r\nexport enum MessageTypeFilter {\r\n  TEXT = 'text',\r\n  IMAGE = 'image',\r\n  FILE = 'file',\r\n  TEMPLATE = 'template',\r\n  ALL = 'all',\r\n}\r\n\r\n/**\r\n * DTO cho việc truy vấn danh sách tin nhắn\r\n */\r\nexport class MessageQueryDto extends QueryDto {\r\n  @ApiProperty({\r\n    description: 'Lọc theo loại tin nhắn',\r\n    enum: MessageTypeFilter,\r\n    example: MessageTypeFilter.ALL,\r\n    required: false,\r\n  })\r\n  @IsOptional()\r\n  @IsEnum(MessageTypeFilter)\r\n  messageType?: MessageTypeFilter;\r\n\r\n  @ApiProperty({\r\n    description: '<PERSON>ọ<PERSON> theo hướng tin nhắn',\r\n    enum: MessageDirection,\r\n    example: MessageDirection.ALL,\r\n    required: false,\r\n  })\r\n  @IsOptional()\r\n  @IsEnum(MessageDirection)\r\n  direction?: MessageDirection;\r\n\r\n  @ApiProperty({\r\n    description: 'Tìm kiếm theo nội dung tin nhắn',\r\n    example: 'xin chào',\r\n    required: false,\r\n  })\r\n  @IsOptional()\r\n  @IsString()\r\n  content?: string;\r\n\r\n  constructor() {\r\n    super();\r\n    this.sortBy = 'timestamp';\r\n    this.sortDirection = SortDirection.DESC;\r\n  }\r\n}\r\n"], "names": ["MessageDirection", "MessageQueryDto", "MessageTypeFilter", "QueryDto", "constructor", "sortBy", "sortDirection", "SortDirection", "DESC", "description", "enum", "example", "required"], "mappings": ";;;;;;;;;;;QAOYA;eAAAA;;QAoBCC;eAAAA;;QAXDC;eAAAA;;;yBAhBgB;gCACiB;qBACL;;;;;;;;;;AAKjC,IAAA,AAAKF,0CAAAA;;;;WAAAA;;AASL,IAAA,AAAKE,2CAAAA;;;;;;WAAAA;;AAWL,IAAA,AAAMD,kBAAN,MAAMA,wBAAwBE,aAAQ;IA8B3CC,aAAc;QACZ,KAAK;QACL,IAAI,CAACC,MAAM,GAAG;QACd,IAAI,CAACC,aAAa,GAAGC,kBAAa,CAACC,IAAI;IACzC;AACF;;;QAjCIC,aAAa;QACbC,MAAMR;QACNS,OAAO;QACPC,UAAU;;;;;;;;QAOVH,aAAa;QACbC,MAAMV;QACNW,OAAO;QACPC,UAAU;;;;;;;;QAOVH,aAAa;QACbE,SAAS;QACTC,UAAU"}