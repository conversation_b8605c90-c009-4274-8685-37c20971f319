{"version": 3, "sources": ["../../../../src/modules/marketing/entities/zalo-follower.entity.ts"], "sourcesContent": ["import { Column, Entity, PrimaryGeneratedColumn } from 'typeorm';\r\n\r\n/**\r\n * Entity đại diện cho bảng zalo_followers trong cơ sở dữ liệu\r\n * Lưu trữ thông tin về người dùng Zalo đã theo dõi Official Account\r\n */\r\n@Entity('zalo_followers')\r\nexport class ZaloFollower {\r\n  /**\r\n   * ID tự động tăng\r\n   */\r\n  @PrimaryGeneratedColumn({ name: 'id' })\r\n  id: number;\r\n\r\n  /**\r\n   * ID của Official Account\r\n   */\r\n  @Column({ name: 'oa_id', length: 50 })\r\n  oaId: string;\r\n\r\n  /**\r\n   * ID của người dùng Zalo\r\n   */\r\n  @Column({ name: 'user_id', length: 50 })\r\n  userId: string;\r\n\r\n  /**\r\n   * Tên hiển thị của người dùng\r\n   */\r\n  @Column({ name: 'display_name', length: 255, nullable: true })\r\n  displayName: string;\r\n\r\n  /**\r\n   * URL avatar của người dùng\r\n   */\r\n  @Column({ name: 'avatar_url', length: 500, nullable: true })\r\n  avatarUrl: string;\r\n\r\n  /**\r\n   * <PERSON><PERSON> điện thoại của người dùng (nếu đư<PERSON> cấp quyền)\r\n   */\r\n  @Column({ name: 'phone', length: 20, nullable: true })\r\n  phone: string;\r\n\r\n  /**\r\n   * Giới tính của người dùng (1: Nam, 2: Nữ)\r\n   */\r\n  @Column({ name: 'gender', type: 'smallint', nullable: true })\r\n  gender: number;\r\n\r\n  /**\r\n   * Ngày sinh của người dùng (định dạng dd/mm/yyyy)\r\n   */\r\n  @Column({ name: 'birth_date', length: 20, nullable: true })\r\n  birthDate: string;\r\n\r\n  /**\r\n   * Các tag gán cho người dùng (JSON)\r\n   */\r\n  @Column({ name: 'tags', type: 'jsonb', default: '[]' })\r\n  tags: string[];\r\n\r\n  /**\r\n   * Thời điểm theo dõi (Unix timestamp)\r\n   */\r\n  @Column({ name: 'followed_at', type: 'bigint' })\r\n  followedAt: number;\r\n\r\n  /**\r\n   * Thời điểm hủy theo dõi (Unix timestamp)\r\n   */\r\n  @Column({ name: 'unfollowed_at', type: 'bigint', nullable: true })\r\n  unfollowedAt: number;\r\n\r\n  /**\r\n   * Trạng thái (active, unfollowed)\r\n   */\r\n  @Column({ name: 'status', length: 20, default: 'active' })\r\n  status: string;\r\n\r\n  /**\r\n   * Thời điểm tạo (Unix timestamp)\r\n   */\r\n  @Column({ name: 'created_at', type: 'bigint' })\r\n  createdAt: number;\r\n\r\n  /**\r\n   * Thời điểm cập nhật (Unix timestamp)\r\n   */\r\n  @Column({ name: 'updated_at', type: 'bigint' })\r\n  updatedAt: number;\r\n}\r\n"], "names": ["<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "name", "length", "nullable", "type", "default"], "mappings": ";;;;+BAOaA;;;eAAAA;;;yBAP0C;;;;;;;;;;AAOhD,IAAA,AAAMA,eAAN,MAAMA;AAoFb;;;QAhF4BC,MAAM;;;;;;QAMtBA,MAAM;QAASC,QAAQ;;;;;;QAMvBD,MAAM;QAAWC,QAAQ;;;;;;QAMzBD,MAAM;QAAgBC,QAAQ;QAAKC,UAAU;;;;;;QAM7CF,MAAM;QAAcC,QAAQ;QAAKC,UAAU;;;;;;QAM3CF,MAAM;QAASC,QAAQ;QAAIC,UAAU;;;;;;QAMrCF,MAAM;QAAUG,MAAM;QAAYD,UAAU;;;;;;QAM5CF,MAAM;QAAcC,QAAQ;QAAIC,UAAU;;;;;;QAM1CF,MAAM;QAAQG,MAAM;QAASC,SAAS;;;;;;QAMtCJ,MAAM;QAAeG,MAAM;;;;;;QAM3BH,MAAM;QAAiBG,MAAM;QAAUD,UAAU;;;;;;QAMjDF,MAAM;QAAUC,QAAQ;QAAIG,SAAS;;;;;;QAMrCJ,MAAM;QAAcG,MAAM;;;;;;QAM1BH,MAAM;QAAcG,MAAM"}