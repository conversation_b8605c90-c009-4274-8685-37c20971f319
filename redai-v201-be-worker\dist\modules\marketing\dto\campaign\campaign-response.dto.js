"use strict";
Object.defineProperty(exports, "__esModule", {
    value: true
});
function _export(target, all) {
    for(var name in all)Object.defineProperty(target, name, {
        enumerable: true,
        get: Object.getOwnPropertyDescriptor(all, name).get
    });
}
_export(exports, {
    get CampaignResponseDto () {
        return CampaignResponseDto;
    },
    get CampaignStatsDto () {
        return CampaignStatsDto;
    }
});
const _swagger = require("@nestjs/swagger");
const _createcampaigndto = require("./create-campaign.dto");
const _campaignserverdto = require("./campaign-server.dto");
const _updatecampaigndto = require("./update-campaign.dto");
function _ts_decorate(decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for(var i = decorators.length - 1; i >= 0; i--)if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
}
function _ts_metadata(k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
}
let CampaignStatsDto = class CampaignStatsDto {
};
_ts_decorate([
    (0, _swagger.ApiProperty)({
        description: 'Tổng số đối tượng',
        example: 100
    }),
    _ts_metadata("design:type", Number)
], CampaignStatsDto.prototype, "totalRecipients", void 0);
_ts_decorate([
    (0, _swagger.ApiProperty)({
        description: 'Số lượng đã gửi',
        example: 80
    }),
    _ts_metadata("design:type", Number)
], CampaignStatsDto.prototype, "sent", void 0);
_ts_decorate([
    (0, _swagger.ApiProperty)({
        description: 'Số lượng đã nhận',
        example: 75
    }),
    _ts_metadata("design:type", Number)
], CampaignStatsDto.prototype, "delivered", void 0);
_ts_decorate([
    (0, _swagger.ApiProperty)({
        description: 'Số lượng đã mở (chỉ áp dụng cho email)',
        example: 50
    }),
    _ts_metadata("design:type", Number)
], CampaignStatsDto.prototype, "opened", void 0);
_ts_decorate([
    (0, _swagger.ApiProperty)({
        description: 'Số lượng đã nhấp (chỉ áp dụng cho email)',
        example: 20
    }),
    _ts_metadata("design:type", Number)
], CampaignStatsDto.prototype, "clicked", void 0);
_ts_decorate([
    (0, _swagger.ApiProperty)({
        description: 'Số lượng thất bại',
        example: 5
    }),
    _ts_metadata("design:type", Number)
], CampaignStatsDto.prototype, "failed", void 0);
let CampaignResponseDto = class CampaignResponseDto {
};
_ts_decorate([
    (0, _swagger.ApiProperty)({
        description: 'ID của campaign',
        example: 1
    }),
    _ts_metadata("design:type", Number)
], CampaignResponseDto.prototype, "id", void 0);
_ts_decorate([
    (0, _swagger.ApiProperty)({
        description: 'Tiêu đề chiến dịch',
        example: 'Khuyến mãi tháng 5'
    }),
    _ts_metadata("design:type", String)
], CampaignResponseDto.prototype, "title", void 0);
_ts_decorate([
    (0, _swagger.ApiProperty)({
        description: 'Mô tả chiến dịch',
        example: 'Chiến dịch khuyến mãi dành cho khách hàng VIP'
    }),
    _ts_metadata("design:type", String)
], CampaignResponseDto.prototype, "description", void 0);
_ts_decorate([
    (0, _swagger.ApiProperty)({
        description: 'Nền tảng gửi',
        enum: _createcampaigndto.CampaignPlatform,
        example: _createcampaigndto.CampaignPlatform.EMAIL
    }),
    _ts_metadata("design:type", typeof _createcampaigndto.CampaignPlatform === "undefined" ? Object : _createcampaigndto.CampaignPlatform)
], CampaignResponseDto.prototype, "platform", void 0);
_ts_decorate([
    (0, _swagger.ApiProperty)({
        description: 'Nội dung chiến dịch',
        example: '<p>Xin chào quý khách,</p><p>Chúng tôi xin gửi đến quý khách chương trình khuyến mãi...</p>'
    }),
    _ts_metadata("design:type", String)
], CampaignResponseDto.prototype, "content", void 0);
_ts_decorate([
    (0, _swagger.ApiProperty)({
        description: 'Thông tin máy chủ gửi',
        type: _campaignserverdto.CampaignServerDto
    }),
    _ts_metadata("design:type", typeof _campaignserverdto.CampaignServerDto === "undefined" ? Object : _campaignserverdto.CampaignServerDto)
], CampaignResponseDto.prototype, "server", void 0);
_ts_decorate([
    (0, _swagger.ApiProperty)({
        description: 'Thời gian dự kiến gửi chiến dịch (Unix timestamp)',
        example: 1619171200
    }),
    _ts_metadata("design:type", Number)
], CampaignResponseDto.prototype, "scheduledAt", void 0);
_ts_decorate([
    (0, _swagger.ApiProperty)({
        description: 'Tiêu đề email (chỉ áp dụng cho chiến dịch email)',
        example: 'Khuyến mãi đặc biệt dành cho bạn'
    }),
    _ts_metadata("design:type", String)
], CampaignResponseDto.prototype, "subject", void 0);
_ts_decorate([
    (0, _swagger.ApiProperty)({
        description: 'Trạng thái chiến dịch',
        enum: _updatecampaigndto.CampaignStatus,
        example: _updatecampaigndto.CampaignStatus.DRAFT
    }),
    _ts_metadata("design:type", typeof _updatecampaigndto.CampaignStatus === "undefined" ? Object : _updatecampaigndto.CampaignStatus)
], CampaignResponseDto.prototype, "status", void 0);
_ts_decorate([
    (0, _swagger.ApiProperty)({
        description: 'Thời gian tạo (Unix timestamp)',
        example: 1619171200
    }),
    _ts_metadata("design:type", Number)
], CampaignResponseDto.prototype, "createdAt", void 0);
_ts_decorate([
    (0, _swagger.ApiProperty)({
        description: 'Thời gian cập nhật (Unix timestamp)',
        example: 1619171200
    }),
    _ts_metadata("design:type", Number)
], CampaignResponseDto.prototype, "updatedAt", void 0);
_ts_decorate([
    (0, _swagger.ApiProperty)({
        description: 'Thống kê chiến dịch',
        type: CampaignStatsDto,
        required: false
    }),
    _ts_metadata("design:type", typeof CampaignStatsDto === "undefined" ? Object : CampaignStatsDto)
], CampaignResponseDto.prototype, "stats", void 0);

//# sourceMappingURL=campaign-response.dto.js.map