"use strict";
Object.defineProperty(exports, "__esModule", {
    value: true
});
Object.defineProperty(exports, "UserAudienceCustomField", {
    enumerable: true,
    get: function() {
        return UserAudienceCustomField;
    }
});
const _typeorm = require("typeorm");
function _ts_decorate(decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for(var i = decorators.length - 1; i >= 0; i--)if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
}
function _ts_metadata(k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
}
let UserAudienceCustomField = class UserAudienceCustomField {
};
_ts_decorate([
    (0, _typeorm.PrimaryGeneratedColumn)({
        name: 'id',
        type: 'bigint'
    }),
    _ts_metadata("design:type", Number)
], UserAudienceCustomField.prototype, "id", void 0);
_ts_decorate([
    (0, _typeorm.Column)({
        name: 'audience_id',
        nullable: true
    }),
    _ts_metadata("design:type", Number)
], UserAudienceCustomField.prototype, "audienceId", void 0);
_ts_decorate([
    (0, _typeorm.Column)({
        name: 'field_name',
        length: 255,
        nullable: true,
        comment: 'Tên trường'
    }),
    _ts_metadata("design:type", String)
], UserAudienceCustomField.prototype, "fieldName", void 0);
_ts_decorate([
    (0, _typeorm.Column)({
        name: 'field_value',
        type: 'jsonb',
        nullable: true,
        comment: 'Giá trị'
    }),
    _ts_metadata("design:type", Object)
], UserAudienceCustomField.prototype, "fieldValue", void 0);
_ts_decorate([
    (0, _typeorm.Column)({
        name: 'field_type',
        length: 20,
        nullable: true,
        comment: 'Kiểu giá trị'
    }),
    _ts_metadata("design:type", String)
], UserAudienceCustomField.prototype, "fieldType", void 0);
_ts_decorate([
    (0, _typeorm.Column)({
        name: 'created_at',
        type: 'bigint',
        nullable: true,
        comment: 'Thời gian tạo'
    }),
    _ts_metadata("design:type", Number)
], UserAudienceCustomField.prototype, "createdAt", void 0);
_ts_decorate([
    (0, _typeorm.Column)({
        name: 'updated_at',
        type: 'bigint',
        nullable: true,
        comment: 'Thời gian cập nhật'
    }),
    _ts_metadata("design:type", Number)
], UserAudienceCustomField.prototype, "updatedAt", void 0);
UserAudienceCustomField = _ts_decorate([
    (0, _typeorm.Entity)('user_audience_custom_fields')
], UserAudienceCustomField);

//# sourceMappingURL=user-audience-custom-field.entity.js.map