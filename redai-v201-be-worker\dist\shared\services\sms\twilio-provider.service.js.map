{"version": 3, "sources": ["../../../../src/shared/services/sms/twilio-provider.service.ts"], "sourcesContent": ["import { Injectable } from '@nestjs/common';\r\nimport { ConfigService } from '@nestjs/config';\r\nimport { BaseSmsProvider } from './base-sms-provider.service';\r\nimport {\r\n  SmsResponse,\r\n  BulkSmsResponse,\r\n  MessageStatusResponse,\r\n  ConnectionTestResponse,\r\n  MessageStatus,\r\n} from './sms-provider.interface';\r\n\r\n// Importer le client Twilio\r\nlet twilio;\r\ntry {\r\n  twilio = require('twilio');\r\n} catch (e) {\r\n  // Twilio n'est pas installé, nous le gérerons dans le constructeur\r\n}\r\n\r\n/**\r\n * Interface pour la configuration de Twilio\r\n */\r\nexport interface TwilioConfig {\r\n  /**\r\n   * SID du compte Twilio\r\n   */\r\n  accountSid: string;\r\n\r\n  /**\r\n   * Token d'authentification Twilio\r\n   */\r\n  authToken: string;\r\n\r\n  /**\r\n   * Numéro de téléphone ou service de messagerie Twilio à utiliser comme expéditeur\r\n   */\r\n  from?: string;\r\n\r\n  /**\r\n   * SID du service de messagerie Twilio (alternative à from)\r\n   */\r\n  messagingServiceSid?: string;\r\n}\r\n\r\n/**\r\n * Service d'intégration avec l'API Twilio\r\n */\r\n@Injectable()\r\nexport class TwilioProvider extends BaseSmsProvider {\r\n  readonly providerName = 'Twilio';\r\n\r\n  private client: any;\r\n  private readonly accountSid: string;\r\n  private readonly authToken: string;\r\n  private readonly defaultFrom: string;\r\n  private readonly defaultMessagingServiceSid: string;\r\n\r\n  constructor(private readonly configService: ConfigService) {\r\n    super('TwilioProvider');\r\n\r\n    // Charger la configuration depuis les variables d'environnement ou utiliser des valeurs par défaut\r\n    this.accountSid =\r\n      this.configService.get<string>('TWILIO_ACCOUNT_SID') || '';\r\n    this.authToken = this.configService.get<string>('TWILIO_AUTH_TOKEN') || '';\r\n    this.defaultFrom =\r\n      this.configService.get<string>('TWILIO_PHONE_NUMBER') || '';\r\n    this.defaultMessagingServiceSid =\r\n      this.configService.get<string>('TWILIO_MESSAGING_SERVICE_SID') || '';\r\n\r\n    // Vérifier si Twilio est installé\r\n    if (!twilio) {\r\n      this.logger.warn(\r\n        'Le package \"twilio\" n\\'est pas installé. Veuillez l\\'installer avec \"npm install twilio\"',\r\n      );\r\n    } else {\r\n      // Initialiser le client Twilio\r\n      this.initClient(this.accountSid, this.authToken);\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Initialise le client Twilio avec les identifiants fournis\r\n   * @param accountSid SID du compte Twilio\r\n   * @param authToken Token d'authentification Twilio\r\n   */\r\n  private initClient(accountSid: string, authToken: string): void {\r\n    if (twilio && accountSid && authToken) {\r\n      try {\r\n        this.client = twilio(accountSid, authToken);\r\n        this.logger.log('Client Twilio initialisé avec succès');\r\n      } catch (error) {\r\n        this.logger.error(\r\n          `Erreur lors de l'initialisation du client Twilio: ${error.message}`,\r\n          error.stack,\r\n        );\r\n      }\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Envoie un SMS à un numéro de téléphone via Twilio\r\n   * @param phoneNumber Numéro de téléphone du destinataire\r\n   * @param message Contenu du message\r\n   * @param options Options supplémentaires (from, messagingServiceSid)\r\n   * @returns Promesse contenant l'ID du message ou une erreur\r\n   */\r\n  async sendSms(\r\n    phoneNumber: string,\r\n    message: string,\r\n    options?: any,\r\n  ): Promise<SmsResponse> {\r\n    try {\r\n      this.logger.debug(`Envoi d'un SMS à ${phoneNumber} via Twilio`);\r\n\r\n      // Vérifier si le client Twilio est initialisé\r\n      if (!this.client) {\r\n        throw new Error(\"Le client Twilio n'est pas initialisé\");\r\n      }\r\n\r\n      const formattedPhoneNumber = this.formatPhoneNumber(phoneNumber);\r\n\r\n      // Préparer les paramètres pour l'envoi du SMS\r\n      const params: any = {\r\n        to: formattedPhoneNumber,\r\n        body: message,\r\n      };\r\n\r\n      // Utiliser le service de messagerie ou le numéro d'expéditeur\r\n      if (options?.messagingServiceSid) {\r\n        params.messagingServiceSid = options.messagingServiceSid;\r\n      } else if (options?.from) {\r\n        params.from = options.from;\r\n      } else if (this.defaultMessagingServiceSid) {\r\n        params.messagingServiceSid = this.defaultMessagingServiceSid;\r\n      } else if (this.defaultFrom) {\r\n        params.from = this.defaultFrom;\r\n      } else {\r\n        throw new Error(\r\n          'Aucun expéditeur spécifié (from ou messagingServiceSid)',\r\n        );\r\n      }\r\n\r\n      // Ajouter les options supplémentaires\r\n      if (options?.statusCallback) {\r\n        params.statusCallback = options.statusCallback;\r\n      }\r\n\r\n      // Envoyer le SMS via Twilio\r\n      const twilioMessage = await this.client.messages.create(params);\r\n\r\n      return {\r\n        success: true,\r\n        messageId: twilioMessage.sid,\r\n        rawResponse: twilioMessage,\r\n      };\r\n    } catch (error) {\r\n      this.logger.error(\r\n        `Exception lors de l'envoi du SMS via Twilio: ${error.message}`,\r\n        error.stack,\r\n      );\r\n      return {\r\n        success: false,\r\n        errorCode: error.code,\r\n        errorMessage: error.message || 'Erreur inconnue',\r\n      };\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Vérifie le statut d'un message envoyé via Twilio\r\n   * @param messageId SID du message Twilio à vérifier\r\n   * @returns Promesse contenant le statut du message\r\n   */\r\n  async checkMessageStatus(messageId: string): Promise<MessageStatusResponse> {\r\n    try {\r\n      this.logger.debug(\r\n        `Vérification du statut du message ${messageId} via Twilio`,\r\n      );\r\n\r\n      // Vérifier si le client Twilio est initialisé\r\n      if (!this.client) {\r\n        throw new Error(\"Le client Twilio n'est pas initialisé\");\r\n      }\r\n\r\n      // Récupérer le message depuis Twilio\r\n      const twilioMessage = await this.client.messages(messageId).fetch();\r\n\r\n      return {\r\n        messageId,\r\n        status: this.mapTwilioStatus(twilioMessage.status),\r\n        updatedAt: new Date(twilioMessage.dateUpdated),\r\n        details: twilioMessage.errorMessage || '',\r\n        rawResponse: twilioMessage,\r\n      };\r\n    } catch (error) {\r\n      this.logger.error(\r\n        `Exception lors de la vérification du statut du message via Twilio: ${error.message}`,\r\n        error.stack,\r\n      );\r\n      return {\r\n        messageId,\r\n        status: MessageStatus.UNKNOWN,\r\n        updatedAt: new Date(),\r\n        details: error.message || 'Erreur inconnue',\r\n      };\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Envoie un SMS avec un brandname via Twilio\r\n   * @param phoneNumber Numéro de téléphone du destinataire\r\n   * @param message Contenu du message\r\n   * @param brandname Nom de la marque à utiliser comme expéditeur (doit être un numéro Twilio valide)\r\n   * @param options Options supplémentaires\r\n   * @returns Promesse contenant l'ID du message ou une erreur\r\n   */\r\n  async sendBrandnameSms(\r\n    phoneNumber: string,\r\n    message: string,\r\n    brandname: string,\r\n    options?: any,\r\n  ): Promise<SmsResponse> {\r\n    // Pour Twilio, le brandname doit être un numéro de téléphone valide ou un alphasender\r\n    return this.sendSms(phoneNumber, message, {\r\n      ...options,\r\n      from: brandname,\r\n    });\r\n  }\r\n\r\n  /**\r\n   * Teste la connexion avec Twilio\r\n   * @param config Configuration de Twilio\r\n   * @returns Promesse indiquant si la connexion est réussie\r\n   */\r\n  async testConnection(config: TwilioConfig): Promise<ConnectionTestResponse> {\r\n    try {\r\n      this.logger.debug('Test de connexion avec Twilio');\r\n\r\n      const accountSid = config.accountSid || this.accountSid;\r\n      const authToken = config.authToken || this.authToken;\r\n\r\n      if (!accountSid || !authToken) {\r\n        return {\r\n          success: false,\r\n          message: 'Identifiants Twilio manquants',\r\n        };\r\n      }\r\n\r\n      // Créer un client temporaire pour le test\r\n      const testClient = twilio(accountSid, authToken);\r\n\r\n      // Tester la connexion en récupérant les informations du compte\r\n      const account = await testClient.api.accounts(accountSid).fetch();\r\n\r\n      return {\r\n        success: true,\r\n        message: 'Connexion réussie',\r\n        details: {\r\n          friendlyName: account.friendlyName,\r\n          status: account.status,\r\n          type: account.type,\r\n        },\r\n      };\r\n    } catch (error) {\r\n      this.logger.error(\r\n        `Exception lors du test de connexion avec Twilio: ${error.message}`,\r\n        error.stack,\r\n      );\r\n      return {\r\n        success: false,\r\n        message: error.message || 'Erreur inconnue',\r\n      };\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Convertit un statut Twilio en statut standard\r\n   * @param twilioStatus Statut Twilio\r\n   * @returns Statut standard\r\n   */\r\n  private mapTwilioStatus(twilioStatus: string): MessageStatus {\r\n    switch (twilioStatus) {\r\n      case 'queued':\r\n        return MessageStatus.PENDING;\r\n      case 'sending':\r\n      case 'sent':\r\n        return MessageStatus.SENDING;\r\n      case 'delivered':\r\n        return MessageStatus.DELIVERED;\r\n      case 'undelivered':\r\n      case 'failed':\r\n        return MessageStatus.FAILED;\r\n      default:\r\n        return MessageStatus.UNKNOWN;\r\n    }\r\n  }\r\n}\r\n"], "names": ["<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "twi<PERSON>", "require", "e", "BaseSmsProvider", "initClient", "accountSid", "authToken", "client", "logger", "log", "error", "message", "stack", "sendSms", "phoneNumber", "options", "debug", "Error", "formattedPhoneNumber", "formatPhoneNumber", "params", "to", "body", "messagingServiceSid", "from", "defaultMessagingServiceSid", "defaultFrom", "statusCallback", "twilioMessage", "messages", "create", "success", "messageId", "sid", "rawResponse", "errorCode", "code", "errorMessage", "checkMessageStatus", "fetch", "status", "mapTwilioStatus", "updatedAt", "Date", "dateUpdated", "details", "MessageStatus", "UNKNOWN", "sendBrandnameSms", "brandname", "testConnection", "config", "testClient", "account", "api", "accounts", "friendlyName", "type", "twi<PERSON><PERSON><PERSON><PERSON>", "PENDING", "SENDING", "DELIVERED", "FAILED", "constructor", "configService", "providerName", "get", "warn"], "mappings": ";;;;+BAgDaA;;;eAAAA;;;wBAhDc;wBACG;wCACE;sCAOzB;;;;;;;;;;AAEP,4BAA4B;AAC5B,IAAIC;AACJ,IAAI;IACFA,SAASC,QAAQ;AACnB,EAAE,OAAOC,GAAG;AACV,mEAAmE;AACrE;AA+BO,IAAA,AAAMH,iBAAN,MAAMA,uBAAuBI,uCAAe;IAgCjD;;;;GAIC,GACD,AAAQC,WAAWC,UAAkB,EAAEC,SAAiB,EAAQ;QAC9D,IAAIN,UAAUK,cAAcC,WAAW;YACrC,IAAI;gBACF,IAAI,CAACC,MAAM,GAAGP,OAAOK,YAAYC;gBACjC,IAAI,CAACE,MAAM,CAACC,GAAG,CAAC;YAClB,EAAE,OAAOC,OAAO;gBACd,IAAI,CAACF,MAAM,CAACE,KAAK,CACf,CAAC,kDAAkD,EAAEA,MAAMC,OAAO,EAAE,EACpED,MAAME,KAAK;YAEf;QACF;IACF;IAEA;;;;;;GAMC,GACD,MAAMC,QACJC,WAAmB,EACnBH,OAAe,EACfI,OAAa,EACS;QACtB,IAAI;YACF,IAAI,CAACP,MAAM,CAACQ,KAAK,CAAC,CAAC,iBAAiB,EAAEF,YAAY,WAAW,CAAC;YAE9D,8CAA8C;YAC9C,IAAI,CAAC,IAAI,CAACP,MAAM,EAAE;gBAChB,MAAM,IAAIU,MAAM;YAClB;YAEA,MAAMC,uBAAuB,IAAI,CAACC,iBAAiB,CAACL;YAEpD,8CAA8C;YAC9C,MAAMM,SAAc;gBAClBC,IAAIH;gBACJI,MAAMX;YACR;YAEA,8DAA8D;YAC9D,IAAII,SAASQ,qBAAqB;gBAChCH,OAAOG,mBAAmB,GAAGR,QAAQQ,mBAAmB;YAC1D,OAAO,IAAIR,SAASS,MAAM;gBACxBJ,OAAOI,IAAI,GAAGT,QAAQS,IAAI;YAC5B,OAAO,IAAI,IAAI,CAACC,0BAA0B,EAAE;gBAC1CL,OAAOG,mBAAmB,GAAG,IAAI,CAACE,0BAA0B;YAC9D,OAAO,IAAI,IAAI,CAACC,WAAW,EAAE;gBAC3BN,OAAOI,IAAI,GAAG,IAAI,CAACE,WAAW;YAChC,OAAO;gBACL,MAAM,IAAIT,MACR;YAEJ;YAEA,sCAAsC;YACtC,IAAIF,SAASY,gBAAgB;gBAC3BP,OAAOO,cAAc,GAAGZ,QAAQY,cAAc;YAChD;YAEA,4BAA4B;YAC5B,MAAMC,gBAAgB,MAAM,IAAI,CAACrB,MAAM,CAACsB,QAAQ,CAACC,MAAM,CAACV;YAExD,OAAO;gBACLW,SAAS;gBACTC,WAAWJ,cAAcK,GAAG;gBAC5BC,aAAaN;YACf;QACF,EAAE,OAAOlB,OAAO;YACd,IAAI,CAACF,MAAM,CAACE,KAAK,CACf,CAAC,6CAA6C,EAAEA,MAAMC,OAAO,EAAE,EAC/DD,MAAME,KAAK;YAEb,OAAO;gBACLmB,SAAS;gBACTI,WAAWzB,MAAM0B,IAAI;gBACrBC,cAAc3B,MAAMC,OAAO,IAAI;YACjC;QACF;IACF;IAEA;;;;GAIC,GACD,MAAM2B,mBAAmBN,SAAiB,EAAkC;QAC1E,IAAI;YACF,IAAI,CAACxB,MAAM,CAACQ,KAAK,CACf,CAAC,kCAAkC,EAAEgB,UAAU,WAAW,CAAC;YAG7D,8CAA8C;YAC9C,IAAI,CAAC,IAAI,CAACzB,MAAM,EAAE;gBAChB,MAAM,IAAIU,MAAM;YAClB;YAEA,qCAAqC;YACrC,MAAMW,gBAAgB,MAAM,IAAI,CAACrB,MAAM,CAACsB,QAAQ,CAACG,WAAWO,KAAK;YAEjE,OAAO;gBACLP;gBACAQ,QAAQ,IAAI,CAACC,eAAe,CAACb,cAAcY,MAAM;gBACjDE,WAAW,IAAIC,KAAKf,cAAcgB,WAAW;gBAC7CC,SAASjB,cAAcS,YAAY,IAAI;gBACvCH,aAAaN;YACf;QACF,EAAE,OAAOlB,OAAO;YACd,IAAI,CAACF,MAAM,CAACE,KAAK,CACf,CAAC,mEAAmE,EAAEA,MAAMC,OAAO,EAAE,EACrFD,MAAME,KAAK;YAEb,OAAO;gBACLoB;gBACAQ,QAAQM,mCAAa,CAACC,OAAO;gBAC7BL,WAAW,IAAIC;gBACfE,SAASnC,MAAMC,OAAO,IAAI;YAC5B;QACF;IACF;IAEA;;;;;;;GAOC,GACD,MAAMqC,iBACJlC,WAAmB,EACnBH,OAAe,EACfsC,SAAiB,EACjBlC,OAAa,EACS;QACtB,sFAAsF;QACtF,OAAO,IAAI,CAACF,OAAO,CAACC,aAAaH,SAAS;YACxC,GAAGI,OAAO;YACVS,MAAMyB;QACR;IACF;IAEA;;;;GAIC,GACD,MAAMC,eAAeC,MAAoB,EAAmC;QAC1E,IAAI;YACF,IAAI,CAAC3C,MAAM,CAACQ,KAAK,CAAC;YAElB,MAAMX,aAAa8C,OAAO9C,UAAU,IAAI,IAAI,CAACA,UAAU;YACvD,MAAMC,YAAY6C,OAAO7C,SAAS,IAAI,IAAI,CAACA,SAAS;YAEpD,IAAI,CAACD,cAAc,CAACC,WAAW;gBAC7B,OAAO;oBACLyB,SAAS;oBACTpB,SAAS;gBACX;YACF;YAEA,0CAA0C;YAC1C,MAAMyC,aAAapD,OAAOK,YAAYC;YAEtC,+DAA+D;YAC/D,MAAM+C,UAAU,MAAMD,WAAWE,GAAG,CAACC,QAAQ,CAAClD,YAAYkC,KAAK;YAE/D,OAAO;gBACLR,SAAS;gBACTpB,SAAS;gBACTkC,SAAS;oBACPW,cAAcH,QAAQG,YAAY;oBAClChB,QAAQa,QAAQb,MAAM;oBACtBiB,MAAMJ,QAAQI,IAAI;gBACpB;YACF;QACF,EAAE,OAAO/C,OAAO;YACd,IAAI,CAACF,MAAM,CAACE,KAAK,CACf,CAAC,iDAAiD,EAAEA,MAAMC,OAAO,EAAE,EACnED,MAAME,KAAK;YAEb,OAAO;gBACLmB,SAAS;gBACTpB,SAASD,MAAMC,OAAO,IAAI;YAC5B;QACF;IACF;IAEA;;;;GAIC,GACD,AAAQ8B,gBAAgBiB,YAAoB,EAAiB;QAC3D,OAAQA;YACN,KAAK;gBACH,OAAOZ,mCAAa,CAACa,OAAO;YAC9B,KAAK;YACL,KAAK;gBACH,OAAOb,mCAAa,CAACc,OAAO;YAC9B,KAAK;gBACH,OAAOd,mCAAa,CAACe,SAAS;YAChC,KAAK;YACL,KAAK;gBACH,OAAOf,mCAAa,CAACgB,MAAM;YAC7B;gBACE,OAAOhB,mCAAa,CAACC,OAAO;QAChC;IACF;IA9OAgB,YAAY,AAAiBC,aAA4B,CAAE;QACzD,KAAK,CAAC,wBADqBA,gBAAAA,oBARpBC,eAAe;QAWtB,mGAAmG;QACnG,IAAI,CAAC5D,UAAU,GACb,IAAI,CAAC2D,aAAa,CAACE,GAAG,CAAS,yBAAyB;QAC1D,IAAI,CAAC5D,SAAS,GAAG,IAAI,CAAC0D,aAAa,CAACE,GAAG,CAAS,wBAAwB;QACxE,IAAI,CAACxC,WAAW,GACd,IAAI,CAACsC,aAAa,CAACE,GAAG,CAAS,0BAA0B;QAC3D,IAAI,CAACzC,0BAA0B,GAC7B,IAAI,CAACuC,aAAa,CAACE,GAAG,CAAS,mCAAmC;QAEpE,kCAAkC;QAClC,IAAI,CAAClE,QAAQ;YACX,IAAI,CAACQ,MAAM,CAAC2D,IAAI,CACd;QAEJ,OAAO;YACL,+BAA+B;YAC/B,IAAI,CAAC/D,UAAU,CAAC,IAAI,CAACC,UAAU,EAAE,IAAI,CAACC,SAAS;QACjD;IACF;AA0NF"}