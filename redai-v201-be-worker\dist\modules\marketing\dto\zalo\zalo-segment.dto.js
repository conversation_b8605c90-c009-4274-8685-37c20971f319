"use strict";
Object.defineProperty(exports, "__esModule", {
    value: true
});
function _export(target, all) {
    for(var name in all)Object.defineProperty(target, name, {
        enumerable: true,
        get: Object.getOwnPropertyDescriptor(all, name).get
    });
}
_export(exports, {
    get CreateZaloSegmentDto () {
        return CreateZaloSegmentDto;
    },
    get UpdateZaloSegmentDto () {
        return UpdateZaloSegmentDto;
    },
    get ZaloSegmentConditionDto () {
        return ZaloSegmentConditionDto;
    },
    get ZaloSegmentConditionType () {
        return ZaloSegmentConditionType;
    },
    get ZaloSegmentOperator () {
        return ZaloSegmentOperator;
    },
    get ZaloSegmentQueryDto () {
        return ZaloSegmentQueryDto;
    },
    get ZaloSegmentResponseDto () {
        return ZaloSegmentResponseDto;
    }
});
const _swagger = require("@nestjs/swagger");
const _classvalidator = require("class-validator");
function _ts_decorate(decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for(var i = decorators.length - 1; i >= 0; i--)if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
}
function _ts_metadata(k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
}
var ZaloSegmentConditionType = /*#__PURE__*/ function(ZaloSegmentConditionType) {
    ZaloSegmentConditionType["TAG"] = "tag";
    ZaloSegmentConditionType["GENDER"] = "gender";
    ZaloSegmentConditionType["FOLLOW_DATE"] = "follow_date";
    ZaloSegmentConditionType["INTERACTION"] = "interaction";
    return ZaloSegmentConditionType;
}({});
var ZaloSegmentOperator = /*#__PURE__*/ function(ZaloSegmentOperator) {
    ZaloSegmentOperator["EQUAL"] = "equal";
    ZaloSegmentOperator["NOT_EQUAL"] = "not_equal";
    ZaloSegmentOperator["CONTAINS"] = "contains";
    ZaloSegmentOperator["NOT_CONTAINS"] = "not_contains";
    ZaloSegmentOperator["GREATER_THAN"] = "greater_than";
    ZaloSegmentOperator["LESS_THAN"] = "less_than";
    ZaloSegmentOperator["BETWEEN"] = "between";
    return ZaloSegmentOperator;
}({});
let ZaloSegmentConditionDto = class ZaloSegmentConditionDto {
};
_ts_decorate([
    (0, _swagger.ApiProperty)({
        description: 'Loại điều kiện',
        enum: ZaloSegmentConditionType,
        example: "tag"
    }),
    (0, _classvalidator.IsEnum)(ZaloSegmentConditionType),
    (0, _classvalidator.IsNotEmpty)(),
    _ts_metadata("design:type", String)
], ZaloSegmentConditionDto.prototype, "type", void 0);
_ts_decorate([
    (0, _swagger.ApiProperty)({
        description: 'Toán tử so sánh',
        enum: ZaloSegmentOperator,
        example: "contains"
    }),
    (0, _classvalidator.IsEnum)(ZaloSegmentOperator),
    (0, _classvalidator.IsNotEmpty)(),
    _ts_metadata("design:type", String)
], ZaloSegmentConditionDto.prototype, "operator", void 0);
_ts_decorate([
    (0, _swagger.ApiProperty)({
        description: 'Giá trị điều kiện',
        example: [
            'vip',
            'new-customer'
        ],
        type: [
            String
        ]
    }),
    (0, _classvalidator.IsArray)(),
    (0, _classvalidator.IsString)({
        each: true
    }),
    _ts_metadata("design:type", Array)
], ZaloSegmentConditionDto.prototype, "values", void 0);
let CreateZaloSegmentDto = class CreateZaloSegmentDto {
};
_ts_decorate([
    (0, _swagger.ApiProperty)({
        description: 'ID của Official Account',
        example: '*********'
    }),
    (0, _classvalidator.IsString)(),
    (0, _classvalidator.IsNotEmpty)(),
    _ts_metadata("design:type", String)
], CreateZaloSegmentDto.prototype, "oaId", void 0);
_ts_decorate([
    (0, _swagger.ApiProperty)({
        description: 'Tên của phân đoạn',
        example: 'Khách hàng VIP'
    }),
    (0, _classvalidator.IsString)(),
    (0, _classvalidator.IsNotEmpty)(),
    _ts_metadata("design:type", String)
], CreateZaloSegmentDto.prototype, "name", void 0);
_ts_decorate([
    (0, _swagger.ApiProperty)({
        description: 'Mô tả của phân đoạn',
        example: 'Phân đoạn dành cho khách hàng VIP',
        required: false
    }),
    (0, _classvalidator.IsString)(),
    (0, _classvalidator.IsOptional)(),
    _ts_metadata("design:type", String)
], CreateZaloSegmentDto.prototype, "description", void 0);
_ts_decorate([
    (0, _swagger.ApiProperty)({
        description: 'Danh sách điều kiện phân đoạn',
        type: [
            ZaloSegmentConditionDto
        ]
    }),
    (0, _classvalidator.IsArray)(),
    (0, _classvalidator.IsNotEmpty)(),
    _ts_metadata("design:type", Array)
], CreateZaloSegmentDto.prototype, "conditions", void 0);
let UpdateZaloSegmentDto = class UpdateZaloSegmentDto {
};
_ts_decorate([
    (0, _swagger.ApiProperty)({
        description: 'Tên của phân đoạn',
        example: 'Khách hàng VIP - Cập nhật',
        required: false
    }),
    (0, _classvalidator.IsString)(),
    (0, _classvalidator.IsOptional)(),
    _ts_metadata("design:type", String)
], UpdateZaloSegmentDto.prototype, "name", void 0);
_ts_decorate([
    (0, _swagger.ApiProperty)({
        description: 'Mô tả của phân đoạn',
        example: 'Phân đoạn dành cho khách hàng VIP - Cập nhật',
        required: false
    }),
    (0, _classvalidator.IsString)(),
    (0, _classvalidator.IsOptional)(),
    _ts_metadata("design:type", String)
], UpdateZaloSegmentDto.prototype, "description", void 0);
_ts_decorate([
    (0, _swagger.ApiProperty)({
        description: 'Danh sách điều kiện phân đoạn',
        type: [
            ZaloSegmentConditionDto
        ],
        required: false
    }),
    (0, _classvalidator.IsArray)(),
    (0, _classvalidator.IsOptional)(),
    _ts_metadata("design:type", Array)
], UpdateZaloSegmentDto.prototype, "conditions", void 0);
let ZaloSegmentResponseDto = class ZaloSegmentResponseDto {
};
_ts_decorate([
    (0, _swagger.ApiProperty)({
        description: 'ID của phân đoạn',
        example: 1
    }),
    _ts_metadata("design:type", Number)
], ZaloSegmentResponseDto.prototype, "id", void 0);
_ts_decorate([
    (0, _swagger.ApiProperty)({
        description: 'ID của người dùng',
        example: 123
    }),
    _ts_metadata("design:type", Number)
], ZaloSegmentResponseDto.prototype, "userId", void 0);
_ts_decorate([
    (0, _swagger.ApiProperty)({
        description: 'ID của Official Account',
        example: '*********'
    }),
    _ts_metadata("design:type", String)
], ZaloSegmentResponseDto.prototype, "oaId", void 0);
_ts_decorate([
    (0, _swagger.ApiProperty)({
        description: 'Tên của phân đoạn',
        example: 'Khách hàng VIP'
    }),
    _ts_metadata("design:type", String)
], ZaloSegmentResponseDto.prototype, "name", void 0);
_ts_decorate([
    (0, _swagger.ApiProperty)({
        description: 'Mô tả của phân đoạn',
        example: 'Phân đoạn dành cho khách hàng VIP',
        nullable: true
    }),
    _ts_metadata("design:type", String)
], ZaloSegmentResponseDto.prototype, "description", void 0);
_ts_decorate([
    (0, _swagger.ApiProperty)({
        description: 'Danh sách điều kiện phân đoạn',
        type: [
            ZaloSegmentConditionDto
        ]
    }),
    _ts_metadata("design:type", Array)
], ZaloSegmentResponseDto.prototype, "conditions", void 0);
_ts_decorate([
    (0, _swagger.ApiProperty)({
        description: 'Số lượng người theo dõi trong phân đoạn',
        example: 100
    }),
    _ts_metadata("design:type", Number)
], ZaloSegmentResponseDto.prototype, "followerCount", void 0);
_ts_decorate([
    (0, _swagger.ApiProperty)({
        description: 'Thời điểm tạo (Unix timestamp)',
        example: 1625097600000
    }),
    _ts_metadata("design:type", Number)
], ZaloSegmentResponseDto.prototype, "createdAt", void 0);
_ts_decorate([
    (0, _swagger.ApiProperty)({
        description: 'Thời điểm cập nhật (Unix timestamp)',
        example: 1625097600000
    }),
    _ts_metadata("design:type", Number)
], ZaloSegmentResponseDto.prototype, "updatedAt", void 0);
let ZaloSegmentQueryDto = class ZaloSegmentQueryDto {
    constructor(){
        this.page = 1;
        this.limit = 10;
    }
};
_ts_decorate([
    (0, _swagger.ApiProperty)({
        description: 'Tìm kiếm theo tên phân đoạn',
        example: 'VIP',
        required: false
    }),
    (0, _classvalidator.IsString)(),
    (0, _classvalidator.IsOptional)(),
    _ts_metadata("design:type", String)
], ZaloSegmentQueryDto.prototype, "name", void 0);
_ts_decorate([
    (0, _swagger.ApiProperty)({
        description: 'Số trang',
        example: 1,
        default: 1,
        required: false
    }),
    (0, _classvalidator.IsOptional)(),
    _ts_metadata("design:type", Number)
], ZaloSegmentQueryDto.prototype, "page", void 0);
_ts_decorate([
    (0, _swagger.ApiProperty)({
        description: 'Số lượng phân đoạn trên mỗi trang',
        example: 10,
        default: 10,
        required: false
    }),
    (0, _classvalidator.IsOptional)(),
    _ts_metadata("design:type", Number)
], ZaloSegmentQueryDto.prototype, "limit", void 0);

//# sourceMappingURL=zalo-segment.dto.js.map