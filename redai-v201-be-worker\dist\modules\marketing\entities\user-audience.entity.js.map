{"version": 3, "sources": ["../../../../src/modules/marketing/entities/user-audience.entity.ts"], "sourcesContent": ["import { Column, Entity, PrimaryGeneratedColumn } from 'typeorm';\r\n\r\n/**\r\n * Entity đại diện cho bảng user_audience trong cơ sở dữ liệu\r\n * Bảng khách hàng của người dùng\r\n */\r\n@Entity('user_audience')\r\nexport class UserAudience {\r\n  /**\r\n   * ID của audience\r\n   */\r\n  @PrimaryGeneratedColumn({ name: 'id', type: 'bigint' })\r\n  id: number;\r\n\r\n  /**\r\n   * ID của người dùng\r\n   */\r\n  @Column({ name: 'user_id', nullable: true, comment: 'Mã khách hàng' })\r\n  userId: number;\r\n\r\n  // Không sử dụng quan hệ với bảng User, chỉ lưu ID\r\n\r\n  /**\r\n   * Email của khách hàng\r\n   */\r\n  @Column({\r\n    name: 'email',\r\n    length: 255,\r\n    nullable: true,\r\n    comment: 'Email người dùng',\r\n  })\r\n  email: string;\r\n\r\n  /**\r\n   * Số điện thoại của khách hàng\r\n   */\r\n  @Column({\r\n    name: 'phone',\r\n    length: 20,\r\n    nullable: true,\r\n    comment: '<PERSON><PERSON> điện thoại',\r\n  })\r\n  phone: string;\r\n\r\n  /**\r\n   * Thời gian tạo (Unix timestamp)\r\n   */\r\n  @Column({ name: 'created_at', type: 'bigint', comment: 'Ngày tạo' })\r\n  createdAt: number;\r\n\r\n  /**\r\n   * Thời gian cập nhật (Unix timestamp)\r\n   */\r\n  @Column({\r\n    name: 'updated_at',\r\n    type: 'bigint',\r\n    nullable: true,\r\n    comment: 'Ngày cập nhật',\r\n  })\r\n  updatedAt: number;\r\n\r\n  // Không sử dụng quan hệ với các bảng khác, chỉ lưu ID\r\n}\r\n"], "names": ["UserAudience", "name", "type", "nullable", "comment", "length"], "mappings": ";;;;+BAOaA;;;eAAAA;;;yBAP0C;;;;;;;;;;AAOhD,IAAA,AAAMA,eAAN,MAAMA;AAuDb;;;QAnD4BC,MAAM;QAAMC,MAAM;;;;;;QAMlCD,MAAM;QAAWE,UAAU;QAAMC,SAAS;;;;;;QASlDH,MAAM;QACNI,QAAQ;QACRF,UAAU;QACVC,SAAS;;;;;;QAQTH,MAAM;QACNI,QAAQ;QACRF,UAAU;QACVC,SAAS;;;;;;QAODH,MAAM;QAAcC,MAAM;QAAUE,SAAS;;;;;;QAOrDH,MAAM;QACNC,MAAM;QACNC,UAAU;QACVC,SAAS"}