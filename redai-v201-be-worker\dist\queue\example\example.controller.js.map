{"version": 3, "sources": ["../../../src/queue/example/example.controller.ts"], "sourcesContent": ["import { Body, Controller, Logger, Post } from '@nestjs/common';\r\nimport { InjectQueue } from '@nestjs/bullmq';\r\nimport { QueueName } from '../queue-name.enum';\r\nimport { Queue } from 'bullmq';\r\nimport { workflow } from '../../agent';\r\nimport { RedisService } from '../../infra';\r\nimport { AIMessage } from '@langchain/core/messages';\r\n\r\n@Controller('example')\r\nexport class ExampleController {\r\n  private readonly logger = new Logger(ExampleController.name);\r\n  private agentQueue: Queue;\r\n\r\n  constructor(\r\n    @InjectQueue(QueueName.AGENT) agentQueue: Queue,\r\n    private readonly redisService: RedisService,\r\n  ) {\r\n    this.agentQueue = agentQueue;\r\n  }\r\n\r\n  @Post() async enqueue(@Body() data: any) {\r\n    const client = this.redisService.getRawClient();\r\n    await client.publish(`cancel:${data?.threadId}`, 'interrupted');\r\n    // 2. Lấy checkpointId (g<PERSON>i getState)\r\n    const state = await workflow.getState({\r\n      configurable: { thread_id: data?.threadId },\r\n    });\r\n    this.logger.debug(`state = ${JSON.stringify(state, null, 2)}`);\r\n    const checkpointId = state?.config?.configurable?.checkpoint_id;\r\n\r\n    this.logger.debug(`checkpointId = ${checkpointId}`);\r\n    const isToolCallMessage =\r\n      state?.values?.messages?.at(-1) instanceof AIMessage &&\r\n      state?.values?.messages?.at(-1)?.tool_calls?.length > 0;\r\n    await this.agentQueue.add('example', {\r\n      ...data,\r\n      checkpointId,\r\n      lastToolMessageIds: isToolCallMessage\r\n        ? state?.values?.messages?.at(-1)?.tool_calls?.map((tc) => tc.id)\r\n        : null,\r\n    });\r\n    this.logger.log('Job added to queue');\r\n  }\r\n}\r\n"], "names": ["ExampleController", "enqueue", "data", "client", "redisService", "getRawClient", "publish", "threadId", "state", "workflow", "getState", "configurable", "thread_id", "logger", "debug", "JSON", "stringify", "checkpointId", "config", "checkpoint_id", "isToolCallMessage", "values", "messages", "at", "AIMessage", "tool_calls", "length", "<PERSON><PERSON><PERSON><PERSON>", "add", "lastToolMessageIds", "map", "tc", "id", "log", "constructor", "<PERSON><PERSON>", "name", "AGENT"], "mappings": ";;;;+BASaA;;;eAAAA;;;wBATkC;wBACnB;+BACF;yBACJ;uBACG;uBACI;0BACH;;;;;;;;;;;;;;;AAGnB,IAAA,AAAMA,oBAAN,MAAMA;IAWX,MAAcC,QAAQ,AAAQC,IAAS,EAAE;QACvC,MAAMC,SAAS,IAAI,CAACC,YAAY,CAACC,YAAY;QAC7C,MAAMF,OAAOG,OAAO,CAAC,CAAC,OAAO,EAAEJ,MAAMK,UAAU,EAAE;QACjD,qCAAqC;QACrC,MAAMC,QAAQ,MAAMC,eAAQ,CAACC,QAAQ,CAAC;YACpCC,cAAc;gBAAEC,WAAWV,MAAMK;YAAS;QAC5C;QACA,IAAI,CAACM,MAAM,CAACC,KAAK,CAAC,CAAC,QAAQ,EAAEC,KAAKC,SAAS,CAACR,OAAO,MAAM,IAAI;QAC7D,MAAMS,eAAeT,OAAOU,QAAQP,cAAcQ;QAElD,IAAI,CAACN,MAAM,CAACC,KAAK,CAAC,CAAC,eAAe,EAAEG,cAAc;QAClD,MAAMG,oBACJZ,OAAOa,QAAQC,UAAUC,GAAG,CAAC,cAAcC,mBAAS,IACpDhB,OAAOa,QAAQC,UAAUC,GAAG,CAAC,IAAIE,YAAYC,SAAS;QACxD,MAAM,IAAI,CAACC,UAAU,CAACC,GAAG,CAAC,WAAW;YACnC,GAAG1B,IAAI;YACPe;YACAY,oBAAoBT,oBAChBZ,OAAOa,QAAQC,UAAUC,GAAG,CAAC,IAAIE,YAAYK,IAAI,CAACC,KAAOA,GAAGC,EAAE,IAC9D;QACN;QACA,IAAI,CAACnB,MAAM,CAACoB,GAAG,CAAC;IAClB;IA7BAC,YACE,AAA8BP,UAAiB,EAC/C,AAAiBvB,YAA0B,CAC3C;aADiBA,eAAAA;aALFS,SAAS,IAAIsB,cAAM,CAACnC,kBAAkBoC,IAAI;QAOzD,IAAI,CAACT,UAAU,GAAGA;IACpB;AAyBF;;;;;;;;;;;;mEA7B2BU"}