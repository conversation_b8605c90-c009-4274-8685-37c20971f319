{"version": 3, "sources": ["../../../src/modules/email-system/email-system.processor.ts"], "sourcesContent": ["import { Processor, WorkerHost } from '@nestjs/bullmq';\r\nimport { Logger, Injectable } from '@nestjs/common';\r\nimport { Job } from 'bullmq';\r\nimport { QueueName } from '../../queue';\r\nimport { EmailSystemService } from './email-system.service';\r\nimport { EmailService } from '../../shared/services/email.service';\r\nimport { EmailSystemJobDto } from './dto/email-system-job.dto';\r\n\r\n/**\r\n * Processor xử lý queue gửi email\r\n */\r\n@Injectable()\r\n@Processor(QueueName.EMAIL_SYSTEM)\r\nexport class EmailSystemProcessor extends WorkerHost {\r\n  private readonly logger = new Logger(EmailSystemProcessor.name);\r\n\r\n  constructor(\r\n    private readonly emailSystemService: EmailSystemService,\r\n    private readonly emailService: EmailService,\r\n  ) {\r\n    super();\r\n  }\r\n\r\n  /**\r\n   * Xử lý job gửi email\r\n   * @param job Job từ queue\r\n   */\r\n  async process(job: Job<EmailSystemJobDto>): Promise<any> {\r\n    try {\r\n      this.logger.log(\r\n        `Đang xử lý job email ${job.id} với category: ${job.data.category}`,\r\n      );\r\n\r\n      const { category, data, to } = job.data;\r\n\r\n      // Lấy template email từ database\r\n      const template =\r\n        await this.emailSystemService.getTemplateByCategory(category);\r\n      if (!template) {\r\n        throw new Error(\r\n          `Không tìm thấy template email với category: ${category}`,\r\n        );\r\n      }\r\n\r\n      // Thay thế các placeholder trong tiêu đề và nội dung\r\n      const subject = this.emailSystemService.replacePlaceholders(\r\n        template.subject,\r\n        data,\r\n      );\r\n      const content = this.emailSystemService.replacePlaceholders(\r\n        template.content,\r\n        data,\r\n      );\r\n\r\n      // Gửi email\r\n      const result = await this.emailService.sendEmail(to, subject, content);\r\n\r\n      if (result) {\r\n        this.logger.log(\r\n          `Đã gửi email thành công đến ${to} với category: ${category}`,\r\n        );\r\n        return { success: true, message: 'Email đã được gửi thành công' };\r\n      } else {\r\n        throw new Error('Không thể gửi email');\r\n      }\r\n    } catch (error) {\r\n      this.logger.error(\r\n        `Lỗi khi xử lý job email: ${error.message}`,\r\n        error.stack,\r\n      );\r\n      throw error;\r\n    }\r\n  }\r\n}\r\n"], "names": ["EmailSystemProcessor", "WorkerHost", "process", "job", "logger", "log", "id", "data", "category", "to", "template", "emailSystemService", "getTemplateByCategory", "Error", "subject", "replacePlaceholders", "content", "result", "emailService", "sendEmail", "success", "message", "error", "stack", "constructor", "<PERSON><PERSON>", "name", "EMAIL_SYSTEM"], "mappings": ";;;;+BAaaA;;;eAAAA;;;wBAbyB;wBACH;uBAET;oCACS;8BACN;;;;;;;;;;AAQtB,IAAA,AAAMA,uBAAN,MAAMA,6BAA6BC,kBAAU;IAUlD;;;GAGC,GACD,MAAMC,QAAQC,GAA2B,EAAgB;QACvD,IAAI;YACF,IAAI,CAACC,MAAM,CAACC,GAAG,CACb,CAAC,qBAAqB,EAAEF,IAAIG,EAAE,CAAC,eAAe,EAAEH,IAAII,IAAI,CAACC,QAAQ,EAAE;YAGrE,MAAM,EAAEA,QAAQ,EAAED,IAAI,EAAEE,EAAE,EAAE,GAAGN,IAAII,IAAI;YAEvC,iCAAiC;YACjC,MAAMG,WACJ,MAAM,IAAI,CAACC,kBAAkB,CAACC,qBAAqB,CAACJ;YACtD,IAAI,CAACE,UAAU;gBACb,MAAM,IAAIG,MACR,CAAC,4CAA4C,EAAEL,UAAU;YAE7D;YAEA,qDAAqD;YACrD,MAAMM,UAAU,IAAI,CAACH,kBAAkB,CAACI,mBAAmB,CACzDL,SAASI,OAAO,EAChBP;YAEF,MAAMS,UAAU,IAAI,CAACL,kBAAkB,CAACI,mBAAmB,CACzDL,SAASM,OAAO,EAChBT;YAGF,YAAY;YACZ,MAAMU,SAAS,MAAM,IAAI,CAACC,YAAY,CAACC,SAAS,CAACV,IAAIK,SAASE;YAE9D,IAAIC,QAAQ;gBACV,IAAI,CAACb,MAAM,CAACC,GAAG,CACb,CAAC,4BAA4B,EAAEI,GAAG,eAAe,EAAED,UAAU;gBAE/D,OAAO;oBAAEY,SAAS;oBAAMC,SAAS;gBAA+B;YAClE,OAAO;gBACL,MAAM,IAAIR,MAAM;YAClB;QACF,EAAE,OAAOS,OAAO;YACd,IAAI,CAAClB,MAAM,CAACkB,KAAK,CACf,CAAC,yBAAyB,EAAEA,MAAMD,OAAO,EAAE,EAC3CC,MAAMC,KAAK;YAEb,MAAMD;QACR;IACF;IAxDAE,YACE,AAAiBb,kBAAsC,EACvD,AAAiBO,YAA0B,CAC3C;QACA,KAAK,SAHYP,qBAAAA,yBACAO,eAAAA,mBAJFd,SAAS,IAAIqB,cAAM,CAACzB,qBAAqB0B,IAAI;IAO9D;AAoDF;;;4CA7DqBC"}