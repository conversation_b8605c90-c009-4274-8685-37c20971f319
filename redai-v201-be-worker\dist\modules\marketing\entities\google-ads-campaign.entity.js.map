{"version": 3, "sources": ["../../../../src/modules/marketing/entities/google-ads-campaign.entity.ts"], "sourcesContent": ["import { Column, Entity, PrimaryGeneratedColumn } from 'typeorm';\r\n\r\n/**\r\n * Entity đại diện cho bảng google_ads_campaigns trong cơ sở dữ liệu\r\n * Lưu trữ thông tin chiến dịch Google Ads\r\n */\r\n@Entity('google_ads_campaigns')\r\nexport class GoogleAdsCampaign {\r\n  /**\r\n   * ID của chiến dịch Google Ads trong hệ thống\r\n   */\r\n  @PrimaryGeneratedColumn({ name: 'id', type: 'bigint' })\r\n  id: number;\r\n\r\n  /**\r\n   * ID của người dùng\r\n   */\r\n  @Column({ name: 'user_id', nullable: false, comment: 'ID của người dùng' })\r\n  userId: number;\r\n\r\n  /**\r\n   * ID của tài khoản Google Ads trong hệ thống\r\n   */\r\n  @Column({\r\n    name: 'account_id',\r\n    nullable: false,\r\n    comment: 'ID của tài khoản Google Ads',\r\n  })\r\n  accountId: number;\r\n\r\n  /**\r\n   * ID của chiến dịch trên Google Ads\r\n   */\r\n  @Column({\r\n    name: 'campaign_id',\r\n    nullable: false,\r\n    comment: 'ID của chiến dịch trên Google Ads',\r\n  })\r\n  campaignId: string;\r\n\r\n  /**\r\n   * Tên chiến dịch\r\n   */\r\n  @Column({\r\n    name: 'name',\r\n    length: 255,\r\n    nullable: false,\r\n    comment: 'Tên chiến dịch',\r\n  })\r\n  name: string;\r\n\r\n  /**\r\n   * Trạng thái chiến dịch\r\n   */\r\n  @Column({\r\n    name: 'status',\r\n    length: 20,\r\n    nullable: false,\r\n    comment: 'Trạng thái chiến dịch',\r\n  })\r\n  status: string;\r\n\r\n  /**\r\n   * Loại chiến dịch (SEARCH, DISPLAY, ...)\r\n   */\r\n  @Column({\r\n    name: 'type',\r\n    length: 50,\r\n    nullable: false,\r\n    comment: 'Loại chiến dịch (SEARCH, DISPLAY, ...)',\r\n  })\r\n  type: string;\r\n\r\n  /**\r\n   * Ngân sách hàng ngày (micro amount)\r\n   */\r\n  @Column({\r\n    name: 'budget',\r\n    type: 'bigint',\r\n    nullable: false,\r\n    comment: 'Ngân sách hàng ngày (micro amount)',\r\n  })\r\n  budget: number;\r\n\r\n  /**\r\n   * Ngày bắt đầu (YYYY-MM-DD)\r\n   */\r\n  @Column({\r\n    name: 'start_date',\r\n    length: 10,\r\n    nullable: true,\r\n    comment: 'Ngày bắt đầu (YYYY-MM-DD)',\r\n  })\r\n  startDate: string;\r\n\r\n  /**\r\n   * Ngày kết thúc (YYYY-MM-DD)\r\n   */\r\n  @Column({\r\n    name: 'end_date',\r\n    length: 10,\r\n    nullable: true,\r\n    comment: 'Ngày kết thúc (YYYY-MM-DD)',\r\n  })\r\n  endDate: string;\r\n\r\n  /**\r\n   * ID của chiến dịch marketing\r\n   */\r\n  @Column({\r\n    name: 'user_campaign_id',\r\n    type: 'bigint',\r\n    nullable: true,\r\n    comment: 'ID của chiến dịch marketing',\r\n  })\r\n  userCampaignId: number;\r\n\r\n  /**\r\n   * Thời gian tạo (Unix timestamp)\r\n   */\r\n  @Column({\r\n    name: 'created_at',\r\n    type: 'bigint',\r\n    nullable: false,\r\n    comment: 'Thời gian tạo',\r\n  })\r\n  createdAt: number;\r\n\r\n  /**\r\n   * Thời gian cập nhật (Unix timestamp)\r\n   */\r\n  @Column({\r\n    name: 'updated_at',\r\n    type: 'bigint',\r\n    nullable: true,\r\n    comment: 'Thời gian cập nhật',\r\n  })\r\n  updatedAt: number;\r\n}\r\n"], "names": ["GoogleAdsCampaign", "name", "type", "nullable", "comment", "length"], "mappings": ";;;;+BAOaA;;;eAAAA;;;yBAP0C;;;;;;;;;;AAOhD,IAAA,AAAMA,oBAAN,MAAMA;AAmIb;;;QA/H4BC,MAAM;QAAMC,MAAM;;;;;;QAMlCD,MAAM;QAAWE,UAAU;QAAOC,SAAS;;;;;;QAOnDH,MAAM;QACNE,UAAU;QACVC,SAAS;;;;;;QAQTH,MAAM;QACNE,UAAU;QACVC,SAAS;;;;;;QAQTH,MAAM;QACNI,QAAQ;QACRF,UAAU;QACVC,SAAS;;;;;;QAQTH,MAAM;QACNI,QAAQ;QACRF,UAAU;QACVC,SAAS;;;;;;QAQTH,MAAM;QACNI,QAAQ;QACRF,UAAU;QACVC,SAAS;;;;;;QAQTH,MAAM;QACNC,MAAM;QACNC,UAAU;QACVC,SAAS;;;;;;QAQTH,MAAM;QACNI,QAAQ;QACRF,UAAU;QACVC,SAAS;;;;;;QAQTH,MAAM;QACNI,QAAQ;QACRF,UAAU;QACVC,SAAS;;;;;;QAQTH,MAAM;QACNC,MAAM;QACNC,UAAU;QACVC,SAAS;;;;;;QAQTH,MAAM;QACNC,MAAM;QACNC,UAAU;QACVC,SAAS;;;;;;QAQTH,MAAM;QACNC,MAAM;QACNC,UAAU;QACVC,SAAS"}