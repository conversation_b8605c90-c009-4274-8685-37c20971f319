{"version": 3, "sources": ["../../../../../src/modules/marketing/dto/zalo/zns-message-query.dto.ts"], "sourcesContent": ["import { ApiProperty } from '@nestjs/swagger';\r\nimport { IsEnum, IsOptional, IsString } from 'class-validator';\r\nimport { QueryDto, SortDirection } from '@common/dto';\r\n\r\n/**\r\n * Enum cho trạng thái tin nhắn ZNS\r\n */\r\nexport enum ZnsMessageStatus {\r\n  PENDING = 'pending',\r\n  DELIVERED = 'delivered',\r\n  FAILED = 'failed',\r\n  ALL = 'all',\r\n}\r\n\r\n/**\r\n * DTO cho việc truy vấn danh sách tin nhắn ZNS\r\n */\r\nexport class ZnsMessageQueryDto extends QueryDto {\r\n  @ApiProperty({\r\n    description: 'Tìm kiếm theo số điện thoại',\r\n    example: '0912345678',\r\n    required: false,\r\n  })\r\n  @IsOptional()\r\n  @IsString()\r\n  phone?: string;\r\n\r\n  @ApiProperty({\r\n    description: 'Lọc theo ID template',\r\n    example: 'template123456789',\r\n    required: false,\r\n  })\r\n  @IsOptional()\r\n  @IsString()\r\n  templateId?: string;\r\n\r\n  @ApiProperty({\r\n    description: 'Lọc theo trạng thái',\r\n    enum: ZnsMessageStatus,\r\n    example: ZnsMessageStatus.DELIVERED,\r\n    required: false,\r\n  })\r\n  @IsOptional()\r\n  @IsEnum(ZnsMessageStatus)\r\n  status?: ZnsMessageStatus;\r\n\r\n  constructor() {\r\n    super();\r\n    this.sortBy = 'createdAt';\r\n    this.sortDirection = SortDirection.DESC;\r\n  }\r\n}\r\n"], "names": ["ZnsMessageQueryDto", "ZnsMessageStatus", "QueryDto", "constructor", "sortBy", "sortDirection", "SortDirection", "DESC", "description", "example", "required", "enum"], "mappings": ";;;;;;;;;;;QAiBaA;eAAAA;;QAVDC;eAAAA;;;yBAPgB;gCACiB;qBACL;;;;;;;;;;AAKjC,IAAA,AAAKA,0CAAAA;;;;;WAAAA;;AAUL,IAAA,AAAMD,qBAAN,MAAMA,2BAA2BE,aAAQ;IA6B9CC,aAAc;QACZ,KAAK;QACL,IAAI,CAACC,MAAM,GAAG;QACd,IAAI,CAACC,aAAa,GAAGC,kBAAa,CAACC,IAAI;IACzC;AACF;;;QAhCIC,aAAa;QACbC,SAAS;QACTC,UAAU;;;;;;;;QAOVF,aAAa;QACbC,SAAS;QACTC,UAAU;;;;;;;;QAOVF,aAAa;QACbG,MAAMV;QACNQ,OAAO;QACPC,UAAU"}