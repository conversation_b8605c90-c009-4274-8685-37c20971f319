{"version": 3, "sources": ["../../../../../src/modules/marketing/dto/common/paginated-response.dto.ts"], "sourcesContent": ["import { ApiProperty } from '@nestjs/swagger';\r\n\r\n/**\r\n * DTO cho thông tin phân trang\r\n */\r\nexport class PaginationMetaDto {\r\n  /**\r\n   * Tổng số item\r\n   * @example 100\r\n   */\r\n  @ApiProperty({\r\n    description: 'Tổng số item',\r\n    example: 100,\r\n  })\r\n  total: number;\r\n\r\n  /**\r\n   * Trang hiện tại\r\n   * @example 1\r\n   */\r\n  @ApiProperty({\r\n    description: 'Trang hiện tại',\r\n    example: 1,\r\n  })\r\n  page: number;\r\n\r\n  /**\r\n   * Số lượng item trên mỗi trang\r\n   * @example 10\r\n   */\r\n  @ApiProperty({\r\n    description: 'Số lượng item trên mỗi trang',\r\n    example: 10,\r\n  })\r\n  limit: number;\r\n\r\n  /**\r\n   * Tổng số trang\r\n   * @example 10\r\n   */\r\n  @ApiProperty({\r\n    description: 'Tổng số trang',\r\n    example: 10,\r\n  })\r\n  totalPages: number;\r\n\r\n  /**\r\n   * Có trang trước không\r\n   * @example false\r\n   */\r\n  @ApiProperty({\r\n    description: 'Có trang trước không',\r\n    example: false,\r\n  })\r\n  hasPreviousPage: boolean;\r\n\r\n  /**\r\n   * <PERSON><PERSON> trang sau không\r\n   * @example true\r\n   */\r\n  @ApiProperty({\r\n    description: 'Có trang sau không',\r\n    example: true,\r\n  })\r\n  hasNextPage: boolean;\r\n}\r\n\r\n/**\r\n * DTO cho phản hồi phân trang\r\n */\r\nexport class PaginatedResponseDto<T> {\r\n  /**\r\n   * Dữ liệu\r\n   */\r\n  @ApiProperty({\r\n    description: 'Dữ liệu',\r\n    isArray: true,\r\n  })\r\n  data: T[];\r\n\r\n  /**\r\n   * Thông tin phân trang\r\n   */\r\n  @ApiProperty({\r\n    description: 'Thông tin phân trang',\r\n    type: () => PaginationMetaDto,\r\n  })\r\n  meta: PaginationMetaDto;\r\n}\r\n"], "names": ["PaginatedResponseDto", "PaginationMetaDto", "description", "example", "isArray", "type"], "mappings": ";;;;;;;;;;;QAsEaA;eAAAA;;QAjEAC;eAAAA;;;yBALe;;;;;;;;;;AAKrB,IAAA,AAAMA,oBAAN,MAAMA;AA4Db;;;QAtDIC,aAAa;QACbC,SAAS;;;;;;QASTD,aAAa;QACbC,SAAS;;;;;;QASTD,aAAa;QACbC,SAAS;;;;;;QASTD,aAAa;QACbC,SAAS;;;;;;QASTD,aAAa;QACbC,SAAS;;;;;;QASTD,aAAa;QACbC,SAAS;;;;AAQN,IAAA,AAAMH,uBAAN,MAAMA;AAkBb;;;QAbIE,aAAa;QACbE,SAAS;;;;;;QAQTF,aAAa;QACbG,MAAM,IAAMJ"}