{"version": 3, "sources": ["../../../../src/modules/marketing/entities/zalo-official-account.entity.ts"], "sourcesContent": ["import { Column, Entity, PrimaryGeneratedColumn } from 'typeorm';\r\n\r\n/**\r\n * Entity đại diện cho bảng zalo_official_accounts trong cơ sở dữ liệu\r\n * Lưu trữ thông tin về các Official Account của Zalo mà người dùng đã kết nối với hệ thống\r\n */\r\n@Entity('zalo_official_accounts')\r\nexport class ZaloOfficialAccount {\r\n  /**\r\n   * ID tự động tăng\r\n   */\r\n  @PrimaryGeneratedColumn({ name: 'id' })\r\n  id: number;\r\n\r\n  /**\r\n   * ID người dùng sở hữu Official Account\r\n   */\r\n  @Column({ name: 'user_id' })\r\n  userId: number;\r\n\r\n  /**\r\n   * ID của Official Account trên Zalo\r\n   */\r\n  @Column({ name: 'oa_id', length: 50 })\r\n  oaId: string;\r\n\r\n  /**\r\n   * Tên của Official Account\r\n   */\r\n  @Column({ name: 'name', length: 255 })\r\n  name: string;\r\n\r\n  /**\r\n   * <PERSON>ô tả của Official Account\r\n   */\r\n  @Column({ name: 'description', length: 500, nullable: true })\r\n  description: string;\r\n\r\n  /**\r\n   * URL avatar của Official Account\r\n   */\r\n  @Column({ name: 'avatar_url', length: 500, nullable: true })\r\n  avatarUrl: string;\r\n\r\n  /**\r\n   * Access token của Official Account\r\n   */\r\n  @Column({ name: 'access_token', length: 500 })\r\n  accessToken: string;\r\n\r\n  /**\r\n   * Refresh token của Official Account\r\n   */\r\n  @Column({ name: 'refresh_token', length: 500, nullable: true })\r\n  refreshToken: string;\r\n\r\n  /**\r\n   * Thời gian hết hạn của access token (Unix timestamp)\r\n   */\r\n  @Column({ name: 'expires_at', type: 'bigint' })\r\n  expiresAt: number;\r\n\r\n  /**\r\n   * ID của agent được kết nối với Official Account\r\n   */\r\n  @Column({ name: 'agent_id', nullable: true })\r\n  agentId: number;\r\n\r\n  /**\r\n   * Trạng thái kết nối (active, inactive)\r\n   */\r\n  @Column({ name: 'status', length: 20, default: 'active' })\r\n  status: string;\r\n\r\n  /**\r\n   * Thời điểm tạo (Unix timestamp)\r\n   */\r\n  @Column({ name: 'created_at', type: 'bigint' })\r\n  createdAt: number;\r\n\r\n  /**\r\n   * Thời điểm cập nhật (Unix timestamp)\r\n   */\r\n  @Column({ name: 'updated_at', type: 'bigint' })\r\n  updatedAt: number;\r\n}\r\n"], "names": ["ZaloOfficialAccount", "name", "length", "nullable", "type", "default"], "mappings": ";;;;+BAOaA;;;eAAAA;;;yBAP0C;;;;;;;;;;AAOhD,IAAA,AAAMA,sBAAN,MAAMA;AA8Eb;;;QA1E4BC,MAAM;;;;;;QAMtBA,MAAM;;;;;;QAMNA,MAAM;QAASC,QAAQ;;;;;;QAMvBD,MAAM;QAAQC,QAAQ;;;;;;QAMtBD,MAAM;QAAeC,QAAQ;QAAKC,UAAU;;;;;;QAM5CF,MAAM;QAAcC,QAAQ;QAAKC,UAAU;;;;;;QAM3CF,MAAM;QAAgBC,QAAQ;;;;;;QAM9BD,MAAM;QAAiBC,QAAQ;QAAKC,UAAU;;;;;;QAM9CF,MAAM;QAAcG,MAAM;;;;;;QAM1BH,MAAM;QAAYE,UAAU;;;;;;QAM5BF,MAAM;QAAUC,QAAQ;QAAIG,SAAS;;;;;;QAMrCJ,MAAM;QAAcG,MAAM;;;;;;QAM1BH,MAAM;QAAcG,MAAM"}