"use strict";
Object.defineProperty(exports, "__esModule", {
    value: true
});
Object.defineProperty(exports, "CreateSegmentDto", {
    enumerable: true,
    get: function() {
        return CreateSegmentDto;
    }
});
const _classvalidator = require("class-validator");
const _swagger = require("@nestjs/swagger");
const _classtransformer = require("class-transformer");
const _segmentcriteriadto = require("./segment-criteria.dto");
function _ts_decorate(decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for(var i = decorators.length - 1; i >= 0; i--)if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
}
function _ts_metadata(k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
}
let CreateSegmentDto = class CreateSegmentDto {
};
_ts_decorate([
    (0, _swagger.ApiProperty)({
        description: 'Tên segment',
        example: 'Khách hàng tiềm năng'
    }),
    (0, _classvalidator.IsNotEmpty)({
        message: 'Tên segment không được để trống'
    }),
    (0, _classvalidator.IsString)({
        message: 'Tên segment phải là chuỗi'
    }),
    _ts_metadata("design:type", String)
], CreateSegmentDto.prototype, "name", void 0);
_ts_decorate([
    (0, _swagger.ApiProperty)({
        description: 'Mô tả segment',
        example: 'Khách hàng có khả năng mua hàng cao',
        required: false
    }),
    (0, _classvalidator.IsOptional)(),
    (0, _classvalidator.IsString)({
        message: 'Mô tả phải là chuỗi'
    }),
    _ts_metadata("design:type", String)
], CreateSegmentDto.prototype, "description", void 0);
_ts_decorate([
    (0, _swagger.ApiProperty)({
        description: 'Điều kiện lọc khách hàng',
        type: _segmentcriteriadto.SegmentCriteriaDto
    }),
    (0, _classvalidator.IsNotEmpty)({
        message: 'Điều kiện lọc không được để trống'
    }),
    (0, _classvalidator.ValidateNested)(),
    (0, _classtransformer.Type)(()=>_segmentcriteriadto.SegmentCriteriaDto),
    _ts_metadata("design:type", typeof _segmentcriteriadto.SegmentCriteriaDto === "undefined" ? Object : _segmentcriteriadto.SegmentCriteriaDto)
], CreateSegmentDto.prototype, "criteria", void 0);

//# sourceMappingURL=create-segment.dto.js.map