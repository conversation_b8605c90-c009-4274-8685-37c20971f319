"use strict";
Object.defineProperty(exports, "__esModule", {
    value: true
});
Object.defineProperty(exports, "ConnectOfficialAccountDto", {
    enumerable: true,
    get: function() {
        return ConnectOfficialAccountDto;
    }
});
const _swagger = require("@nestjs/swagger");
const _classvalidator = require("class-validator");
function _ts_decorate(decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for(var i = decorators.length - 1; i >= 0; i--)if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
}
function _ts_metadata(k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
}
let ConnectOfficialAccountDto = class ConnectOfficialAccountDto {
};
_ts_decorate([
    (0, _swagger.ApiProperty)({
        description: 'Access token của Official Account',
        example: 'abcdef123456789'
    }),
    (0, _classvalidator.IsString)(),
    (0, _classvalidator.IsNotEmpty)(),
    _ts_metadata("design:type", String)
], ConnectOfficialAccountDto.prototype, "accessToken", void 0);
_ts_decorate([
    (0, _swagger.ApiProperty)({
        description: 'Refresh token của Official Account',
        example: 'refresh123456789'
    }),
    (0, _classvalidator.IsString)(),
    (0, _classvalidator.IsNotEmpty)(),
    _ts_metadata("design:type", String)
], ConnectOfficialAccountDto.prototype, "refreshToken", void 0);
_ts_decorate([
    (0, _swagger.ApiProperty)({
        description: 'Thời gian hết hạn của access token (Unix timestamp)',
        example: *************
    }),
    (0, _classvalidator.IsNumber)(),
    (0, _classvalidator.IsNotEmpty)(),
    _ts_metadata("design:type", Number)
], ConnectOfficialAccountDto.prototype, "expiresAt", void 0);

//# sourceMappingURL=connect-official-account.dto.js.map