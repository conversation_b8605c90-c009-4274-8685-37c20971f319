"use strict";
Object.defineProperty(exports, "__esModule", {
    value: true
});
function _export(target, all) {
    for(var name in all)Object.defineProperty(target, name, {
        enumerable: true,
        get: Object.getOwnPropertyDescriptor(all, name).get
    });
}
_export(exports, {
    get AudienceCustomFieldDefinitionQueryDto () {
        return AudienceCustomFieldDefinitionQueryDto;
    },
    get CustomFieldDefinitionSortField () {
        return CustomFieldDefinitionSortField;
    }
});
const _swagger = require("@nestjs/swagger");
const _classvalidator = require("class-validator");
const _dto = require("../../../../common/dto");
const _createaudiencecustomfielddefinitiondto = require("./create-audience-custom-field-definition.dto");
function _ts_decorate(decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for(var i = decorators.length - 1; i >= 0; i--)if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
}
function _ts_metadata(k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
}
var CustomFieldDefinitionSortField = /*#__PURE__*/ function(CustomFieldDefinitionSortField) {
    CustomFieldDefinitionSortField["ID"] = "id";
    CustomFieldDefinitionSortField["FIELD_KEY"] = "fieldKey";
    CustomFieldDefinitionSortField["DISPLAY_NAME"] = "displayName";
    CustomFieldDefinitionSortField["DATA_TYPE"] = "dataType";
    return CustomFieldDefinitionSortField;
}({});
let AudienceCustomFieldDefinitionQueryDto = class AudienceCustomFieldDefinitionQueryDto extends _dto.QueryDto {
    constructor(...args){
        super(...args), /**
   * Sắp xếp theo trường
   * @example "displayName"
   */ this.sortBy = "displayName", /**
   * Ghi đè thuộc tính sortDirection từ QueryDto để thay đổi giá trị mặc định
   * @example "ASC"
   */ this.sortDirection = _dto.SortDirection.ASC;
    }
};
_ts_decorate([
    (0, _swagger.ApiProperty)({
        description: 'Tìm kiếm theo định danh',
        example: 'customer',
        required: false
    }),
    (0, _classvalidator.IsOptional)(),
    (0, _classvalidator.IsString)({
        message: 'Định danh phải là chuỗi'
    }),
    _ts_metadata("design:type", String)
], AudienceCustomFieldDefinitionQueryDto.prototype, "fieldKey", void 0);
_ts_decorate([
    (0, _swagger.ApiProperty)({
        description: 'Tìm kiếm theo tên hiển thị',
        example: 'Khách hàng',
        required: false
    }),
    (0, _classvalidator.IsOptional)(),
    (0, _classvalidator.IsString)({
        message: 'Tên hiển thị phải là chuỗi'
    }),
    _ts_metadata("design:type", String)
], AudienceCustomFieldDefinitionQueryDto.prototype, "displayName", void 0);
_ts_decorate([
    (0, _swagger.ApiProperty)({
        description: 'Tìm kiếm theo kiểu dữ liệu',
        enum: _createaudiencecustomfielddefinitiondto.CustomFieldDataType,
        example: _createaudiencecustomfielddefinitiondto.CustomFieldDataType.STRING,
        required: false
    }),
    (0, _classvalidator.IsOptional)(),
    (0, _classvalidator.IsEnum)(_createaudiencecustomfielddefinitiondto.CustomFieldDataType, {
        message: `Kiểu dữ liệu phải là một trong các giá trị: ${Object.values(_createaudiencecustomfielddefinitiondto.CustomFieldDataType).join(', ')}`
    }),
    _ts_metadata("design:type", typeof _createaudiencecustomfielddefinitiondto.CustomFieldDataType === "undefined" ? Object : _createaudiencecustomfielddefinitiondto.CustomFieldDataType)
], AudienceCustomFieldDefinitionQueryDto.prototype, "dataType", void 0);
_ts_decorate([
    (0, _swagger.ApiProperty)({
        description: 'Sắp xếp theo trường',
        enum: CustomFieldDefinitionSortField,
        example: "displayName",
        default: "displayName",
        required: false
    }),
    (0, _classvalidator.IsOptional)(),
    (0, _classvalidator.IsEnum)(CustomFieldDefinitionSortField, {
        message: `Trường sắp xếp phải là một trong các giá trị: ${Object.values(CustomFieldDefinitionSortField).join(', ')}`
    }),
    _ts_metadata("design:type", String)
], AudienceCustomFieldDefinitionQueryDto.prototype, "sortBy", void 0);
_ts_decorate([
    (0, _swagger.ApiProperty)({
        description: 'Thứ tự sắp xếp',
        enum: _dto.SortDirection,
        example: _dto.SortDirection.ASC,
        default: _dto.SortDirection.ASC,
        required: false
    }),
    (0, _classvalidator.IsOptional)(),
    (0, _classvalidator.IsEnum)(_dto.SortDirection, {
        message: `Thứ tự sắp xếp phải là một trong các giá trị: ${Object.values(_dto.SortDirection).join(', ')}`
    }),
    _ts_metadata("design:type", typeof _dto.SortDirection === "undefined" ? Object : _dto.SortDirection)
], AudienceCustomFieldDefinitionQueryDto.prototype, "sortDirection", void 0);

//# sourceMappingURL=audience-custom-field-definition-query.dto.js.map