{"version": 3, "sources": ["../../../../src/modules/marketing/entities/user-template-email.entity.ts"], "sourcesContent": ["import { Column, Entity, PrimaryGeneratedColumn } from 'typeorm';\r\n\r\n/**\r\n * Entity đại diện cho bảng user_template_email trong cơ sở dữ liệu\r\n * Bảng template email của người dùng\r\n */\r\n@Entity('user_template_email')\r\nexport class UserTemplateEmail {\r\n  /**\r\n   * ID của template\r\n   */\r\n  @PrimaryGeneratedColumn({ name: 'id', type: 'bigint' })\r\n  id: number;\r\n\r\n  /**\r\n   * ID của người dùng\r\n   */\r\n  @Column({ name: 'user_id', nullable: true })\r\n  userId: number;\r\n\r\n  // Không sử dụng quan hệ với bảng User, chỉ lưu ID\r\n\r\n  /**\r\n   * Tên template\r\n   */\r\n  @Column({ name: 'name', length: 255, nullable: true, comment: 'Tên email' })\r\n  name: string;\r\n\r\n  /**\r\n   * Tiêu đề email\r\n   */\r\n  @Column({\r\n    name: 'subject',\r\n    length: 255,\r\n    nullable: true,\r\n    comment: 'Tiêu đề email',\r\n  })\r\n  subject: string;\r\n\r\n  /**\r\n   * Nội dung email\r\n   */\r\n  @Column({\r\n    name: 'content',\r\n    type: 'text',\r\n    nullable: true,\r\n    comment: 'Nội dung email',\r\n  })\r\n  content: string;\r\n\r\n  /**\r\n   * <PERSON><PERSON>c tag của template\r\n   */\r\n  @Column({\r\n    name: 'tags',\r\n    type: 'jsonb',\r\n    nullable: true,\r\n    comment: 'Nhãn cho email',\r\n  })\r\n  tags: any;\r\n\r\n  /**\r\n   * Các placeholder trong template\r\n   */\r\n  @Column({\r\n    name: 'placeholders',\r\n    type: 'json',\r\n    nullable: true,\r\n    comment: 'Biến truyền vào',\r\n  })\r\n  placeholders: any;\r\n\r\n  /**\r\n   * Thời gian tạo (Unix timestamp)\r\n   */\r\n  @Column({\r\n    name: 'created_at',\r\n    type: 'bigint',\r\n    nullable: true,\r\n    comment: 'Thời gian tạo',\r\n  })\r\n  createdAt: number;\r\n\r\n  /**\r\n   * Thời gian cập nhật (Unix timestamp)\r\n   */\r\n  @Column({\r\n    name: 'updated_at',\r\n    type: 'bigint',\r\n    nullable: true,\r\n    comment: 'Thời gian cập nhật',\r\n  })\r\n  updatedAt: number;\r\n}\r\n"], "names": ["UserTemplateEmail", "name", "type", "nullable", "length", "comment"], "mappings": ";;;;+BAOaA;;;eAAAA;;;yBAP0C;;;;;;;;;;AAOhD,IAAA,AAAMA,oBAAN,MAAMA;AAsFb;;;QAlF4BC,MAAM;QAAMC,MAAM;;;;;;QAMlCD,MAAM;QAAWE,UAAU;;;;;;QAQ3BF,MAAM;QAAQG,QAAQ;QAAKD,UAAU;QAAME,SAAS;;;;;;QAO5DJ,MAAM;QACNG,QAAQ;QACRD,UAAU;QACVE,SAAS;;;;;;QAQTJ,MAAM;QACNC,MAAM;QACNC,UAAU;QACVE,SAAS;;;;;;QAQTJ,MAAM;QACNC,MAAM;QACNC,UAAU;QACVE,SAAS;;;;;;QAQTJ,MAAM;QACNC,MAAM;QACNC,UAAU;QACVE,SAAS;;;;;;QAQTJ,MAAM;QACNC,MAAM;QACNC,UAAU;QACVE,SAAS;;;;;;QAQTJ,MAAM;QACNC,MAAM;QACNC,UAAU;QACVE,SAAS"}