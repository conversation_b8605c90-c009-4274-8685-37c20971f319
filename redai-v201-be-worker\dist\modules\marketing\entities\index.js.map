{"version": 3, "sources": ["../../../../src/modules/marketing/entities/index.ts"], "sourcesContent": ["export * from './user-tag.entity';\r\nexport * from './user-segment.entity';\r\nexport * from './user-audience.entity';\r\nexport * from './user-audience-custom-field.entity';\r\nexport * from './user-audience-custom-field-definition.entity';\r\nexport * from './user-campaign.entity';\r\nexport * from './user-campaign-history.entity';\r\nexport * from './user-template-email.entity';\r\n\r\n// Zalo entities\r\nexport * from './zalo-official-account.entity';\r\nexport * from './zalo-zns-template.entity';\r\nexport * from './zalo-message.entity';\r\nexport * from './zalo-zns-message.entity';\r\nexport * from './zalo-follower.entity';\r\nexport * from './zalo-webhook-log.entity';\r\n\r\n// Zalo Marketing entities\r\nexport * from './zalo-segment.entity';\r\nexport * from './zalo-campaign.entity';\r\nexport * from './zalo-campaign-log.entity';\r\nexport * from './zalo-automation.entity';\r\nexport * from './zalo-automation-log.entity';\r\n\r\n// Google Ads entities\r\nexport * from './google-ads-account.entity';\r\nexport * from './google-ads-campaign.entity';\r\nexport * from './google-ads-ad-group.entity';\r\nexport * from './google-ads-keyword.entity';\r\nexport * from './google-ads-performance.entity';\r\n"], "names": [], "mappings": ";;;;qBAAc;qBACA;qBACA;qBACA;qBACA;qBACA;qBACA;qBACA;qBAGA;qBACA;qBACA;qBACA;qBACA;qBACA;qBAGA;qBACA;qBACA;qBACA;qBACA;qBAGA;qBACA;qBACA;qBACA;qBACA"}