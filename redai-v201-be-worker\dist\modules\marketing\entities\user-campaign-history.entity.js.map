{"version": 3, "sources": ["../../../../src/modules/marketing/entities/user-campaign-history.entity.ts"], "sourcesContent": ["import { Column, Entity, PrimaryGeneratedColumn } from 'typeorm';\r\n\r\n/**\r\n * Entity đại diện cho bảng user_campaign_history trong cơ sở dữ liệu\r\n * Bảng lịch sử chăm sóc khách hàng\r\n */\r\n@Entity('user_campaign_history')\r\nexport class UserCampaignHistory {\r\n  /**\r\n   * ID của lịch sử\r\n   */\r\n  @PrimaryGeneratedColumn({ name: 'id', type: 'bigint' })\r\n  id: number;\r\n\r\n  /**\r\n   * ID của campaign\r\n   */\r\n  @Column({\r\n    name: 'campaign_id',\r\n    type: 'bigint',\r\n    nullable: true,\r\n    comment: 'Mã chiến dịch',\r\n  })\r\n  campaignId: number;\r\n\r\n  // Không sử dụng quan hệ với bảng UserCampaign, chỉ lưu ID\r\n\r\n  /**\r\n   * ID của audience\r\n   */\r\n  @Column({\r\n    name: 'audience_id',\r\n    type: 'bigint',\r\n    nullable: true,\r\n    comment: 'Mã khách hàng',\r\n  })\r\n  audienceId: number;\r\n\r\n  // Không sử dụng quan hệ với bảng UserAudience, chỉ lưu ID\r\n\r\n  /**\r\n   * Trạng thái gửi\r\n   */\r\n  @Column({ name: 'status', length: 20, nullable: true, comment: 'Trạng thái' })\r\n  status: string;\r\n\r\n  /**\r\n   * Thời gian gửi (Unix timestamp)\r\n   */\r\n  @Column({\r\n    name: 'sent_at',\r\n    type: 'bigint',\r\n    nullable: true,\r\n    comment: 'Thời gian gửi',\r\n  })\r\n  sentAt: number;\r\n\r\n  /**\r\n   * Thời gian tạo (Unix timestamp)\r\n   */\r\n  @Column({\r\n    name: 'created_at',\r\n    type: 'bigint',\r\n    nullable: true,\r\n    comment: 'Thời gian tạo',\r\n  })\r\n  createdAt: number;\r\n}\r\n"], "names": ["UserCampaignHistory", "name", "type", "nullable", "comment", "length"], "mappings": ";;;;+BAOaA;;;eAAAA;;;yBAP0C;;;;;;;;;;AAOhD,IAAA,AAAMA,sBAAN,MAAMA;AA4Db;;;QAxD4BC,MAAM;QAAMC,MAAM;;;;;;QAO1CD,MAAM;QACNC,MAAM;QACNC,UAAU;QACVC,SAAS;;;;;;QAUTH,MAAM;QACNC,MAAM;QACNC,UAAU;QACVC,SAAS;;;;;;QASDH,MAAM;QAAUI,QAAQ;QAAIF,UAAU;QAAMC,SAAS;;;;;;QAO7DH,MAAM;QACNC,MAAM;QACNC,UAAU;QACVC,SAAS;;;;;;QAQTH,MAAM;QACNC,MAAM;QACNC,UAAU;QACVC,SAAS"}