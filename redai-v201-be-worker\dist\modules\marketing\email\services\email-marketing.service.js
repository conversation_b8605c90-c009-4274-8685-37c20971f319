"use strict";
Object.defineProperty(exports, "__esModule", {
    value: true
});
Object.defineProperty(exports, "EmailMarketingService", {
    enumerable: true,
    get: function() {
        return EmailMarketingService;
    }
});
const _common = require("@nestjs/common");
const _typeorm = require("@nestjs/typeorm");
const _typeorm1 = require("typeorm");
const _bullmq = require("@nestjs/bullmq");
const _bullmq1 = require("bullmq");
const _usercampaignentity = require("../../entities/user-campaign.entity");
const _useraudienceentity = require("../../entities/user-audience.entity");
const _useraudiencecustomfieldentity = require("../../entities/user-audience-custom-field.entity");
const _emailtrackingservice = require("./email-tracking.service");
const _queue = require("../../../../queue");
function _ts_decorate(decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for(var i = decorators.length - 1; i >= 0; i--)if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
}
function _ts_metadata(k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
}
function _ts_param(paramIndex, decorator) {
    return function(target, key) {
        decorator(target, key, paramIndex);
    };
}
let EmailMarketingService = class EmailMarketingService {
    /**
   * Tạo jobs email marketing cho một campaign
   * @param campaignId ID của campaign
   * @returns Số lượng jobs đã tạo
   */ async createEmailMarketingJobs(campaignId) {
        try {
            this.logger.log(`Creating email marketing jobs for campaign: ${campaignId}`);
            // Lấy thông tin campaign
            const campaign = await this.campaignRepository.findOne({
                where: {
                    id: campaignId
                }
            });
            if (!campaign) {
                throw new Error(`Campaign not found: ${campaignId}`);
            }
            if (campaign.platform !== 'email') {
                throw new Error(`Campaign ${campaignId} is not an email campaign`);
            }
            // Lấy danh sách audience IDs
            let audienceIds = [];
            if (campaign.audienceIds && Array.isArray(campaign.audienceIds)) {
                audienceIds = campaign.audienceIds;
            } else if (campaign.segmentId) {
                // TODO: Implement segment logic if needed
                this.logger.warn(`Segment logic not implemented for campaign ${campaignId}`);
                return 0;
            } else {
                this.logger.warn(`No audience or segment found for campaign ${campaignId}`);
                return 0;
            }
            // Lấy thông tin audiences
            const audiences = await this.audienceRepository.findByIds(audienceIds);
            if (audiences.length === 0) {
                this.logger.warn(`No valid audiences found for campaign ${campaignId}`);
                return 0;
            }
            let jobCount = 0;
            // Tạo job cho từng audience
            for (const audience of audiences){
                if (!audience.email) {
                    this.logger.warn(`Audience ${audience.id} has no email, skipping`);
                    continue;
                }
                try {
                    // Lấy custom fields cho audience này
                    const customFields = await this.getAudienceCustomFields(audience.id);
                    // Tạo tracking ID
                    const trackingId = this.emailTrackingService.generateTrackingId(campaignId, audience.id);
                    // Tạo job data
                    const jobData = {
                        campaignId: campaign.id,
                        audienceId: audience.id,
                        email: audience.email,
                        subject: campaign.subject || '',
                        content: campaign.content || '',
                        customFields,
                        server: campaign.server || undefined,
                        trackingId,
                        createdAt: Date.now()
                    };
                    // Thêm job vào queue
                    await this.emailMarketingQueue.add('send-email', jobData, {
                        delay: campaign.scheduledAt ? Math.max(0, campaign.scheduledAt - Date.now()) : 0,
                        attempts: 3,
                        backoff: {
                            type: 'exponential',
                            delay: 2000
                        }
                    });
                    jobCount++;
                    this.logger.debug(`Created job for audience ${audience.id} (${audience.email})`);
                } catch (error) {
                    this.logger.error(`Error creating job for audience ${audience.id}: ${error.message}`);
                }
            }
            this.logger.log(`Created ${jobCount} email marketing jobs for campaign ${campaignId}`);
            return jobCount;
        } catch (error) {
            this.logger.error(`Error creating email marketing jobs: ${error.message}`, error.stack);
            throw error;
        }
    }
    /**
   * Lấy custom fields cho một audience
   * @param audienceId ID của audience
   * @returns Object chứa custom fields
   */ async getAudienceCustomFields(audienceId) {
        try {
            const customFields = await this.customFieldRepository.find({
                where: {
                    audienceId
                }
            });
            const result = {};
            for (const field of customFields){
                if (field.fieldName) {
                    result[field.fieldName] = field.fieldValue;
                }
            }
            return result;
        } catch (error) {
            this.logger.error(`Error getting custom fields for audience ${audienceId}: ${error.message}`);
            return {};
        }
    }
    /**
   * Lấy thống kê campaign
   * @param campaignId ID của campaign
   * @returns Thống kê campaign
   */ async getCampaignStats(campaignId) {
        try {
            // TODO: Implement campaign statistics
            // Có thể lấy từ UserCampaignHistory để tính toán các metrics
            return {
                campaignId,
                totalSent: 0,
                totalOpened: 0,
                totalClicked: 0,
                totalBounced: 0,
                totalFailed: 0
            };
        } catch (error) {
            this.logger.error(`Error getting campaign stats: ${error.message}`, error.stack);
            throw error;
        }
    }
    /**
   * Hủy tất cả jobs của một campaign
   * @param campaignId ID của campaign
   * @returns Số lượng jobs đã hủy
   */ async cancelCampaignJobs(campaignId) {
        try {
            this.logger.log(`Canceling jobs for campaign: ${campaignId}`);
            // Lấy tất cả jobs đang chờ
            const jobs = await this.emailMarketingQueue.getJobs([
                'waiting',
                'delayed'
            ]);
            let canceledCount = 0;
            for (const job of jobs){
                const jobData = job.data;
                if (jobData.campaignId === campaignId) {
                    await job.remove();
                    canceledCount++;
                }
            }
            this.logger.log(`Canceled ${canceledCount} jobs for campaign ${campaignId}`);
            return canceledCount;
        } catch (error) {
            this.logger.error(`Error canceling campaign jobs: ${error.message}`, error.stack);
            throw error;
        }
    }
    /**
   * Lấy trạng thái queue
   * @returns Thông tin trạng thái queue
   */ async getQueueStatus() {
        try {
            const waiting = await this.emailMarketingQueue.getWaiting();
            const active = await this.emailMarketingQueue.getActive();
            const completed = await this.emailMarketingQueue.getCompleted();
            const failed = await this.emailMarketingQueue.getFailed();
            const delayed = await this.emailMarketingQueue.getDelayed();
            return {
                waiting: waiting.length,
                active: active.length,
                completed: completed.length,
                failed: failed.length,
                delayed: delayed.length
            };
        } catch (error) {
            this.logger.error(`Error getting queue status: ${error.message}`, error.stack);
            throw error;
        }
    }
    constructor(campaignRepository, audienceRepository, customFieldRepository, emailMarketingQueue, emailTrackingService){
        this.campaignRepository = campaignRepository;
        this.audienceRepository = audienceRepository;
        this.customFieldRepository = customFieldRepository;
        this.emailMarketingQueue = emailMarketingQueue;
        this.emailTrackingService = emailTrackingService;
        this.logger = new _common.Logger(EmailMarketingService.name);
    }
};
EmailMarketingService = _ts_decorate([
    (0, _common.Injectable)(),
    _ts_param(0, (0, _typeorm.InjectRepository)(_usercampaignentity.UserCampaign)),
    _ts_param(1, (0, _typeorm.InjectRepository)(_useraudienceentity.UserAudience)),
    _ts_param(2, (0, _typeorm.InjectRepository)(_useraudiencecustomfieldentity.UserAudienceCustomField)),
    _ts_param(3, (0, _bullmq.InjectQueue)(_queue.QueueName.EMAIL_MARKETING)),
    _ts_metadata("design:type", Function),
    _ts_metadata("design:paramtypes", [
        typeof _typeorm1.Repository === "undefined" ? Object : _typeorm1.Repository,
        typeof _typeorm1.Repository === "undefined" ? Object : _typeorm1.Repository,
        typeof _typeorm1.Repository === "undefined" ? Object : _typeorm1.Repository,
        typeof _bullmq1.Queue === "undefined" ? Object : _bullmq1.Queue,
        typeof _emailtrackingservice.EmailTrackingService === "undefined" ? Object : _emailtrackingservice.EmailTrackingService
    ])
], EmailMarketingService);

//# sourceMappingURL=email-marketing.service.js.map