{"version": 3, "sources": ["../../../../src/modules/marketing/entities/zalo-automation-log.entity.ts"], "sourcesContent": ["import { Column, Entity, PrimaryGeneratedColumn } from 'typeorm';\r\n\r\n/**\r\n * Entity cho log tự động hóa Zalo\r\n */\r\n@Entity('zalo_automation_logs')\r\nexport class ZaloAutomationLog {\r\n  @PrimaryGeneratedColumn()\r\n  id: number;\r\n\r\n  @Column({ name: 'automation_id' })\r\n  automationId: number;\r\n\r\n  @Column({ name: 'user_id' })\r\n  userId: number;\r\n\r\n  @Column({ name: 'oa_id' })\r\n  oaId: string;\r\n\r\n  @Column({ name: 'follower_id' })\r\n  followerId: number;\r\n\r\n  @Column({ name: 'follower_user_id' })\r\n  followerUserId: string;\r\n\r\n  @Column({ name: 'trigger_type' })\r\n  triggerType: string;\r\n\r\n  @Column({ name: 'action_type' })\r\n  actionType: string;\r\n\r\n  @Column()\r\n  status: string;\r\n\r\n  @Column({ nullable: true })\r\n  error?: string;\r\n\r\n  @Column({ name: 'created_at', type: 'bigint' })\r\n  createdAt: number;\r\n}\r\n"], "names": ["ZaloAutomationLog", "name", "nullable", "type"], "mappings": ";;;;+BAMaA;;;eAAAA;;;yBAN0C;;;;;;;;;;AAMhD,IAAA,AAAMA,oBAAN,MAAMA;AAiCb;;;;;;;QA7BYC,MAAM;;;;;;QAGNA,MAAM;;;;;;QAGNA,MAAM;;;;;;QAGNA,MAAM;;;;;;QAGNA,MAAM;;;;;;QAGNA,MAAM;;;;;;QAGNA,MAAM;;;;;;;;;;QAMNC,UAAU;;;;;;QAGVD,MAAM;QAAcE,MAAM"}