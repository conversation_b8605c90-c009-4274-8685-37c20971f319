"use strict";
Object.defineProperty(exports, "__esModule", {
    value: true
});
Object.defineProperty(exports, "RedisEventController", {
    enumerable: true,
    get: function() {
        return RedisEventController;
    }
});
const _common = require("@nestjs/common");
const _microservices = require("@nestjs/microservices");
const _database = require("../database");
const _constants = require("../constants");
const _redis = require("../../infra/redis");
const _messages = require("@langchain/core/messages");
const _multiagent = require("../system/core/multi-agent");
const _constants1 = require("../system/core/constants");
const _apikeyencryptionhelper = require("../helper/api-key-encryption.helper");
function _ts_decorate(decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for(var i = decorators.length - 1; i >= 0; i--)if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
}
function _ts_metadata(k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
}
function _ts_param(paramIndex, decorator) {
    return function(target, key) {
        decorator(target, key, paramIndex);
    };
}
// Role tags for event transformation
const ROLE_TAGS = [
    _constants1.SUPERVISOR_TAG,
    _constants1.WORKER_TAG
];
const TOOL_CALL_TAGS = [
    _constants1.SUPERVISOR_TOOL_CALL_TAG,
    _constants1.WORKER_TOOL_CALL_TAG
];
let RedisEventController = class RedisEventController {
    /**
   * Handle run trigger events from backend
   * @param data Run trigger event payload
   */ async handleRunTrigger(data) {
        try {
            this.logger.log(`Received run trigger event for thread ${data.threadId}, run ${data.runId}`);
            // Validate event payload
            if (!this.validateRunTriggerEvent(data)) {
                this.logger.error(`Invalid run trigger event payload for thread ${data?.['threadId']}`);
                return;
            }
            // Check if thread is already being processed
            if (this.activeThreads.has(data.threadId)) {
                this.logger.warn(`Thread ${data.threadId} is already being processed, ignoring duplicate trigger`);
                return;
            }
            // Create AbortController for this thread (LangGraph pattern)
            const abortController = new AbortController();
            this.activeThreads.set(data.threadId, abortController);
            this.threadToRun.set(data.threadId, data.runId);
            // Fetch run data from database
            const runData = await this.userAgentRunsQueries.getRunById(data.runId);
            if (!runData) {
                this.logger.error(`Run ${data.runId} not found in database for thread ${data.threadId}`);
                this.cleanupThread(data.threadId);
                return;
            }
            // Extract sessionId from event data or run payload for stream management
            const sessionId = data.sessionId || runData.payload?.message?.sessionId || data.threadId;
            this.logger.log(`Processing thread ${data.threadId} for agent ${data.agentId} (run ${data.runId}) with sessionId ${sessionId}`);
            await this.processAgentThread(data.threadId, runData, abortController, data.jwt, sessionId);
        } catch (error) {
            this.logger.error(`Error handling run trigger for thread ${data.threadId}:`, {
                message: error.message,
                stack: error.stack,
                name: error.name,
                threadId: data.threadId,
                runId: data.runId,
                error: error
            });
            this.cleanupThread(data.threadId);
        }
    }
    /**
   * Handle run cancel events from backend
   * @param data Run cancel event payload
   */ async handleRunCancel(data) {
        try {
            this.logger.log(`Received run cancel event for thread ${data.threadId}: ${data.reason}`);
            // Validate event payload
            if (!this.validateRunCancelEvent(data)) {
                this.logger.error(`Invalid run cancel event payload for thread ${data?.['threadId']}`);
                return;
            }
            // LangGraph pattern: Cancel by threadId, ignore if not active
            const abortController = this.activeThreads.get(data.threadId);
            if (!abortController) {
                this.logger.log(`Thread ${data.threadId} is not active, ignoring cancel request (this is normal)`);
                return;
            }
            // Abort the thread using LangGraph AbortController pattern
            abortController.abort();
            this.logger.log(`Cancelled thread ${data.threadId}: ${data.reason}`);
            // Emit cancellation event to Redis Streams
            try {
                // Get sessionId from run data if available
                const runId = this.threadToRun.get(data.threadId);
                let sessionId = data.threadId; // fallback to threadId
                if (runId) {
                    try {
                        const runData = await this.userAgentRunsQueries.getRunById(runId);
                        sessionId = runData?.payload?.message?.sessionId || data.threadId;
                    } catch (error) {
                        this.logger.warn(`Failed to get sessionId for cancellation, using threadId: ${error.message}`);
                    }
                }
                await this.emitStream({
                    sessionId,
                    event: 'stream_cancelled',
                    data: {
                        reason: data.reason,
                        timestamp: Date.now(),
                        threadId: data.threadId
                    }
                });
            } catch (error) {
                this.logger.warn(`Failed to emit cancellation event for thread ${data.threadId}:`, error);
            }
            // Cleanup thread resources
            this.cleanupThread(data.threadId);
        } catch (error) {
            this.logger.error(`Error handling run cancel for thread ${data.threadId}:`, error);
        }
    }
    /**
   * Validate run trigger event payload
   * @param data Event data to validate
   * @returns True if valid
   */ validateRunTriggerEvent(data) {
        return data && typeof data.runId === 'string' && typeof data.threadId === 'string' && typeof data.sessionId === 'string' && typeof data.agentId === 'string' && typeof data.userId === 'number' && typeof data.jwt === 'string' && typeof data.timestamp === 'number' && data.eventType === _constants.REDIS_EVENTS.RUN_TRIGGER;
    }
    /**
   * Validate run cancel event payload
   * @param data Event data to validate
   * @returns True if valid
   */ validateRunCancelEvent(data) {
        return data && typeof data.threadId === 'string' && typeof data.reason === 'string' && typeof data.timestamp === 'number' && data.eventType === _constants.REDIS_EVENTS.RUN_CANCEL;
    }
    /**
   * Process agent thread with LangGraph (placeholder for Task 19)
   * @param threadId LangGraph thread ID
   * @param runData Run data from database
   * @param abortController AbortController for cancellation
   * @param jwt JWT token for authenticated API calls
   * @param sessionId Session ID for stream management
   */ async processAgentThread(threadId, runData, abortController, jwt, sessionId) {
        this.logger.debug(`Processing LangGraph thread ${threadId} with run ${runData.id}:`, {
            threadId,
            runId: runData.id,
            status: runData.status,
            payloadSize: JSON.stringify(runData.payload).length,
            aborted: abortController.signal.aborted,
            hasJwt: !!jwt,
            jwtLength: jwt.length
        });
        // Redis streaming events will use the optimized RedisService directly
        // Build CustomConfigurableType from payload data (includes API key decryption)
        const customConfig = this.buildCustomConfigurable(runData, threadId);
        // Get decrypted payload for message extraction
        const userId = runData.created_by;
        const decryptedPayload = this.decryptApiKeysInPayload(runData.payload, userId);
        this.logger.log(`Starting LangGraph streaming for thread ${threadId} (run ${runData.id})`);
        this.logger.debug(`CustomConfigurableType built:`, {
            alwaysApproveToolCall: customConfig.alwaysApproveToolCall,
            thread_id: customConfig.thread_id,
            supervisorAgentId: customConfig.supervisorAgentId,
            agentConfigCount: Object.keys(customConfig.agentConfigMap || {}).length
        });
        // Prepare to accumulate partial tokens
        const partialTokens = [];
        try {
            // Check if already aborted before starting
            if (abortController.signal.aborted) {
                this.logger.log(`Thread ${threadId} was cancelled before processing started`);
                return;
            }
            // Extract message data from decrypted payload
            const messageContent = decryptedPayload?.message?.content || '';
            // Validate message content
            if (!messageContent || messageContent.trim() === '') {
                this.logger.error(`Empty message content for thread ${threadId}`, {
                    hasPayload: !!decryptedPayload,
                    hasMessage: !!decryptedPayload?.message,
                    messageContent: messageContent,
                    payloadStructure: {
                        keys: decryptedPayload ? Object.keys(decryptedPayload) : [],
                        messageKeys: decryptedPayload?.message ? Object.keys(decryptedPayload.message) : [],
                        messageType: typeof decryptedPayload?.message,
                        contentType: typeof decryptedPayload?.message?.content
                    },
                    rawPayloadSample: JSON.stringify(decryptedPayload).substring(0, 500)
                });
                throw new Error('Message content is empty or invalid');
            }
            // Build input for multi-agent LangGraph workflow
            const humanMessage = new _messages.HumanMessage(messageContent);
            const input = {
                messages: [
                    humanMessage
                ],
                activeAgent: customConfig.supervisorAgentId || 'supervisor'
            };
            this.logger.debug(`Starting LangGraph streamEvents with config:`, {
                threadId,
                activeAgent: input.activeAgent,
                messageLength: messageContent.length,
                messagePreview: messageContent.substring(0, 100),
                alwaysApproveToolCall: customConfig.alwaysApproveToolCall,
                humanMessageType: humanMessage.constructor.name,
                hasValidMessage: !!humanMessage.content
            });
            // Start multi-agent LangGraph streaming
            const streaming = _multiagent.workflow.streamEvents(input, {
                configurable: customConfig,
                subgraphs: true,
                recursionLimit: 1000,
                version: 'v2',
                signal: abortController.signal
            });
            const streamingIterator = streaming[Symbol.asyncIterator]();
            // Set up abort signal listener for graceful cancellation
            const abortListener = async ()=>{
                this.logger.log(`Thread ${threadId} processing was cancelled`);
                // Force-close the LLM stream generator
                await streamingIterator?.return?.();
            };
            abortController.signal.addEventListener('abort', abortListener);
            // Process LangGraph events and transform to frontend events
            for await (const { event, data, tags = [] } of streamingIterator){
                if (abortController.signal.aborted) {
                    break;
                }
                // Chat model streaming events
                if (event === 'on_chat_model_stream' && tags.some((t)=>ROLE_TAGS.includes(t))) {
                    const role = this.getRoleFromTags(tags, ROLE_TAGS);
                    if (data.chunk?.content) {
                        const text = data.chunk.content;
                        partialTokens.push(text); // Accumulate tokens for potential persistence
                        // Log actual text content with smart truncation
                        const displayText = text.length > 50 ? `${text.substring(0, 50)}...` : text;
                        const safeText = displayText.replace(/\n/g, '\\n').replace(/\r/g, '\\r');
                        this.logger.debug(`📝 Streaming text token [${role}]: "${safeText}" (${text.length} chars)`, {
                            threadId,
                            role,
                            textLength: text.length,
                            fullText: text.length <= 20 ? text : undefined
                        });
                        await this.emitStream({
                            sessionId,
                            event: 'stream_text_token',
                            data: {
                                role,
                                text
                            }
                        });
                    } else {
                        this.logger.debug(`🔧 Streaming tool token [${role}]`, {
                            threadId,
                            role
                        });
                        await this.emitStream({
                            sessionId,
                            event: 'stream_tool_token',
                            data: {
                                role
                            }
                        });
                    }
                // Tool start/end events
                } else if ([
                    'on_tool_start',
                    'on_tool_end'
                ].includes(event)) {
                    const role = this.getRoleFromTags(tags, TOOL_CALL_TAGS);
                    const eventType = event === 'on_tool_start' ? 'tool_call_start' : 'tool_call_end';
                    // Enhanced tool logging with tool name if available
                    let toolInfo = '';
                    let toolName;
                    if (data && typeof data === 'object' && 'name' in data) {
                        toolName = data.name;
                        toolInfo = ` (${toolName})`;
                    }
                    this.logger.debug(`🔧 Tool ${eventType} [${role}]${toolInfo}`, {
                        threadId,
                        role,
                        toolName,
                        eventType
                    });
                    await this.emitStream({
                        sessionId,
                        event: eventType,
                        data: {
                            role,
                            toolName
                        }
                    });
                // Interrupt events (user decisions)
                } else if (event === 'on_chain_stream' && data.chunk?.[2]?.['__interrupt__']) {
                    const [{ value }] = data.chunk[2]['__interrupt__'];
                    const interruptValue = JSON.parse(value);
                    // Enhanced interrupt logging
                    const displayValue = JSON.stringify(interruptValue).length > 100 ? `${JSON.stringify(interruptValue).substring(0, 100)}...` : JSON.stringify(interruptValue);
                    this.logger.debug(`⚠️ Tool call interrupt: ${displayValue}`, {
                        threadId,
                        interruptType: interruptValue.role || 'unknown',
                        hasPrompt: !!interruptValue.prompt
                    });
                    await this.emitStream({
                        sessionId,
                        event: 'tool_call_interrupt',
                        data: interruptValue
                    });
                }
            }
            // If not cancelled, emit the "end of stream" marker
            if (!abortController.signal.aborted) {
                // Log complete response summary
                const completeResponse = partialTokens.join('');
                const responsePreview = completeResponse.length > 200 ? `${completeResponse.substring(0, 200)}...` : completeResponse;
                const safePreview = responsePreview.replace(/\n/g, '\\n').replace(/\r/g, '\\r');
                this.logger.log(`✅ LangGraph thread ${threadId} completed: "${safePreview}"`, {
                    threadId,
                    runId: runData.id,
                    totalTokens: partialTokens.length,
                    totalChars: completeResponse.length,
                    responsePreview: safePreview
                });
                await this.emitStream({
                    sessionId,
                    event: 'llm_stream_end',
                    data: {
                        threadId,
                        runId: runData.id,
                        sessionId
                    }
                });
                // Save complete assistant response to database
                if (partialTokens.length > 0) {
                    await this.saveCompleteResponse(threadId, partialTokens.join(''), userId);
                }
                this.cleanupThread(threadId);
            }
        } catch (error) {
            const isAbortError = abortController.signal.aborted || error instanceof TypeError && error.message.includes('Invalid state: The reader is not attached to a stream');
            if (isAbortError) {
                // Swallow it: this is expected on cancel
                this.logger.log('LangGraph stream aborted cleanly.');
            } else {
                // Unexpected error: log with full stack trace, emit error event, and cleanup
                this.logger.error(`Error in LangGraph streaming for ${threadId}:`, {
                    message: error.message,
                    stack: error.stack,
                    name: error.name,
                    threadId,
                    runId: runData.id,
                    error: error
                });
                // Emit error event to SSE stream so frontend knows about the failure
                try {
                    await this.emitStream({
                        sessionId,
                        event: 'stream_error',
                        data: {
                            error: error.message || 'Unknown streaming error',
                            errorName: error.name,
                            stack: error.stack,
                            threadId,
                            runId: runData.id,
                            sessionId,
                            timestamp: Date.now()
                        }
                    });
                    // Also emit stream end to properly close the SSE connection
                    await this.emitStream({
                        sessionId,
                        event: 'llm_stream_end',
                        data: {
                            threadId,
                            runId: runData.id,
                            sessionId,
                            error: true
                        }
                    });
                } catch (emitError) {
                    this.logger.error(`Failed to emit error event for thread ${threadId}:`, emitError);
                }
                this.cleanupThread(threadId);
            // Don't re-throw the error to prevent it from bubbling up to the event handler
            }
        } finally{
            // On cancellation, persist the partial tokens
            if (abortController.signal.aborted && partialTokens.length > 0) {
                const partialText = partialTokens.join('');
                const displayText = partialText.length > 100 ? `${partialText.substring(0, 100)}...` : partialText;
                const safeText = displayText.replace(/\n/g, '\\n').replace(/\r/g, '\\r');
                this.logger.log(`💾 Saving partial response for ${threadId}: "${safeText}"`, {
                    tokenCount: partialTokens.length,
                    textLength: partialText.length,
                    preview: safeText
                });
                await this.savePartialResponse(threadId, partialText);
            }
        }
    }
    /**
   * Cleanup thread resources
   * @param threadId Thread ID to cleanup
   */ cleanupThread(threadId) {
        const runId = this.threadToRun.get(threadId);
        this.activeThreads.delete(threadId);
        this.threadToRun.delete(threadId);
        this.logger.debug(`Cleaned up thread ${threadId}${runId ? ` (run ${runId})` : ''}`);
    }
    /**
   * Get active threads count for monitoring
   * @returns Number of active threads
   */ getActiveThreadsCount() {
        return this.activeThreads.size;
    }
    /**
   * Get list of active thread IDs for debugging
   * @returns Array of active thread IDs
   */ getActiveThreadIds() {
        return Array.from(this.activeThreads.keys());
    }
    /**
   * Check if a specific thread is active
   * @param threadId Thread ID to check
   * @returns True if thread is active
   */ isThreadActive(threadId) {
        return this.activeThreads.has(threadId);
    }
    /**
   * Get thread-to-run mapping for debugging
   * @returns Map of threadId to runId
   */ getThreadToRunMapping() {
        return Object.fromEntries(this.threadToRun.entries());
    }
    /**
   * Extract role from LangGraph event tags
   * @param tags Array of tags from LangGraph event
   * @param roleTags Array of role tags to match against
   * @returns Role string or null if not found
   */ getRoleFromTags(tags, roleTags) {
        for (const tag of tags){
            if (roleTags.includes(tag)) {
                return tag === _constants1.SUPERVISOR_TAG || tag === _constants1.SUPERVISOR_TOOL_CALL_TAG ? 'supervisor' : 'worker';
            }
        }
        return null;
    }
    /**
   * Save complete assistant response when streaming finishes successfully
   * @param threadId Thread ID for the response
   * @param completeText Complete accumulated text
   * @param userId User ID who created the run
   */ async saveCompleteResponse(threadId, completeText, userId) {
        try {
            this.logger.log(`💾 Saving complete assistant response for thread ${threadId}`, {
                threadId,
                textLength: completeText.length,
                userId,
                preview: completeText.substring(0, 100) + (completeText.length > 100 ? '...' : '')
            });
            await this.userMessagesQueries.createMessage({
                thread_id: threadId,
                role: 'assistant',
                content: {
                    contentBlocks: [
                        {
                            type: 'text',
                            content: completeText
                        }
                    ],
                    complete: true,
                    tokenCount: completeText.length,
                    timestamp: Date.now()
                },
                created_by: userId
            });
            this.logger.log(`✅ Successfully saved complete assistant response for thread ${threadId}`);
        } catch (error) {
            this.logger.error(`Failed to save complete response for thread ${threadId}:`, error);
        // Don't throw - message saving should not fail the streaming completion
        }
    }
    /**
   * Save partial response when streaming is cancelled
   * @param threadId Thread ID for the partial response
   * @param partialText Accumulated partial text
   */ async savePartialResponse(threadId, partialText) {
        try {
            // Get userId from the run data
            const runId = this.threadToRun.get(threadId);
            if (!runId) {
                this.logger.warn(`No runId found for thread ${threadId}, cannot save partial response`);
                return;
            }
            const runData = await this.userAgentRunsQueries.getRunById(runId);
            if (!runData) {
                this.logger.warn(`No run data found for runId ${runId}, cannot save partial response`);
                return;
            }
            const userId = runData.created_by;
            this.logger.log(`💾 Saving partial assistant response for thread ${threadId}`, {
                threadId,
                partialLength: partialText.length,
                userId,
                preview: partialText.substring(0, 100) + (partialText.length > 100 ? '...' : '')
            });
            await this.userMessagesQueries.createMessage({
                thread_id: threadId,
                role: 'assistant',
                content: {
                    contentBlocks: [
                        {
                            type: 'text',
                            content: partialText
                        }
                    ],
                    partial: true,
                    cancelled: true,
                    tokenCount: partialText.length,
                    timestamp: Date.now()
                },
                created_by: userId
            });
            this.logger.log(`✅ Successfully saved partial assistant response for thread ${threadId}`);
        } catch (error) {
            this.logger.error(`Failed to save partial response for thread ${threadId}:`, error);
        // Don't throw - partial response saving should not fail the cancellation
        }
    }
    /**
   * Decrypt API keys in the payload based on user type
   * @param payload The payload containing encrypted API keys
   * @param userId User ID for decryption (from created_by field)
   * @returns Payload with decrypted API keys
   */ decryptApiKeysInPayload(payload, userId) {
        try {
            if (!payload) {
                return payload;
            }
            // Deep clone payload to avoid modifying original
            const decryptedPayload = JSON.parse(JSON.stringify(payload));
            // Decrypt API keys in agentConfigMap
            if (decryptedPayload.agentConfigMap) {
                for(const agentId in decryptedPayload.agentConfigMap){
                    const agentConfig = decryptedPayload.agentConfigMap[agentId];
                    if (agentConfig?.model?.apiKeys && Array.isArray(agentConfig.model.apiKeys)) {
                        this.logger.debug(`Decrypting ${agentConfig.model.apiKeys.length} API keys for agent ${agentId}`);
                        agentConfig.model.apiKeys = agentConfig.model.apiKeys.map((encryptedKey)=>{
                            try {
                                // Determine if this is admin or user key based on agent type
                                if (agentConfig.model.type === 'SYSTEM') {
                                    // System agents use admin keys
                                    return this.apiKeyEncryptionHelper.decryptAdminApiKey(encryptedKey);
                                } else {
                                    // User agents use user-specific keys
                                    return this.apiKeyEncryptionHelper.decryptUserApiKey(encryptedKey, userId);
                                }
                            } catch (error) {
                                this.logger.error(`Failed to decrypt API key for agent ${agentId}:`, error);
                                // Return original key if decryption fails (might already be decrypted)
                                return encryptedKey;
                            }
                        });
                    }
                }
            }
            this.logger.debug(`Successfully decrypted API keys in payload for user ${userId}`);
            return decryptedPayload;
        } catch (error) {
            this.logger.error(`Failed to decrypt API keys in payload for user ${userId}:`, error);
            // Return original payload if decryption fails
            return payload;
        }
    }
    /**
   * Map payload data to CustomConfigurableType for LangGraph configuration
   * @param runData Run data from database containing payload
   * @param threadId Thread ID for the configuration
   * @returns CustomConfigurableType configuration object
   */ buildCustomConfigurable(runData, threadId) {
        try {
            // Decrypt API keys in payload before processing
            const userId = runData.created_by; // Get user ID from database record
            const decryptedPayload = this.decryptApiKeysInPayload(runData.payload, userId);
            // Extract configuration values from decrypted payload with fallbacks
            const alwaysApproveToolCall = decryptedPayload?.processing?.alwaysApproveToolCall || false;
            const agentConfigMap = decryptedPayload?.agentConfigMap || {};
            const supervisorAgentId = decryptedPayload?.primaryAgentId || '';
            const config = {
                alwaysApproveToolCall,
                thread_id: threadId,
                agentConfigMap,
                supervisorAgentId,
                multiMcpClients: undefined
            };
            this.logger.debug(`Built CustomConfigurableType for thread ${threadId}:`, {
                alwaysApproveToolCall,
                thread_id: threadId,
                supervisorAgentId,
                agentConfigCount: Object.keys(agentConfigMap).length,
                hasMultiMcp: false,
                userId: userId,
                apiKeysDecrypted: true
            });
            return config;
        } catch (error) {
            this.logger.error(`Failed to build CustomConfigurableType for thread ${threadId}:`, error);
            // Return minimal fallback configuration
            return {
                alwaysApproveToolCall: false,
                thread_id: threadId,
                agentConfigMap: {},
                supervisorAgentId: '',
                multiMcpClients: undefined
            };
        }
    }
    /**
   * Emit streaming event to Redis Streams for backend SSE consumption
   * Uses optimized RedisService with circuit breaker, retry logic, and error handling
   * @param event Streaming event data
   */ async emitStream(event) {
        try {
            const streamKey = `agent_stream:${event.sessionId}`;
            const streamData = {
                event: event.event,
                data: JSON.stringify(event.data),
                timestamp: Date.now().toString()
            };
            // Publish to Redis Streams using optimized RedisService with circuit breaker and retry
            await this.redisService.xadd(streamKey, streamData);
            // Set TTL on stream (24 hours) to prevent indefinite storage
            // Only set TTL on first message to avoid resetting it
            if (event.event === 'stream_text_token' || event.event === 'tool_call_start') {
                try {
                    const client = this.redisService.getRawClient();
                    await client.expire(streamKey, 24 * 60 * 60); // 24 hours TTL
                    this.logger.debug(`Set TTL on stream ${streamKey}`);
                } catch (error) {
                    this.logger.warn(`Failed to set TTL on stream ${streamKey}:`, error.message);
                }
            }
            // Also publish notification for real-time updates (following working pattern)
            const notificationData = JSON.stringify({
                event: event.event,
                timestamp: Date.now()
            });
            await this.redisService.publish(streamKey, notificationData);
            this.logger.debug(`Emitted stream event ${event.event} for session ${event.sessionId}`, {
                streamKey,
                eventType: event.event,
                sessionId: event.sessionId,
                dataSize: JSON.stringify(event.data).length
            });
        } catch (error) {
            this.logger.error(`Failed to emit stream event ${event.event} for session ${event.sessionId}:`, {
                error: error.message,
                streamKey: `agent_stream:${event.sessionId}`,
                eventType: event.event,
                sessionId: event.sessionId,
                isRedisError: error.name?.includes('Redis')
            });
            throw error;
        }
    }
    constructor(userAgentRunsQueries, userMessagesQueries, redisService, apiKeyEncryptionHelper){
        this.userAgentRunsQueries = userAgentRunsQueries;
        this.userMessagesQueries = userMessagesQueries;
        this.redisService = redisService;
        this.apiKeyEncryptionHelper = apiKeyEncryptionHelper;
        this.logger = new _common.Logger(RedisEventController.name);
        this.activeThreads = new Map();
        this.threadToRun = new Map();
    }
};
_ts_decorate([
    (0, _microservices.EventPattern)(_constants.REDIS_EVENTS.RUN_TRIGGER),
    _ts_param(0, (0, _microservices.Payload)()),
    _ts_metadata("design:type", Function),
    _ts_metadata("design:paramtypes", [
        typeof _constants.RunTriggerEvent === "undefined" ? Object : _constants.RunTriggerEvent
    ]),
    _ts_metadata("design:returntype", Promise)
], RedisEventController.prototype, "handleRunTrigger", null);
_ts_decorate([
    (0, _microservices.EventPattern)(_constants.REDIS_EVENTS.RUN_CANCEL),
    _ts_param(0, (0, _microservices.Payload)()),
    _ts_metadata("design:type", Function),
    _ts_metadata("design:paramtypes", [
        typeof _constants.RunCancelEvent === "undefined" ? Object : _constants.RunCancelEvent
    ]),
    _ts_metadata("design:returntype", Promise)
], RedisEventController.prototype, "handleRunCancel", null);
RedisEventController = _ts_decorate([
    (0, _common.Controller)(),
    _ts_metadata("design:type", Function),
    _ts_metadata("design:paramtypes", [
        typeof _database.UserAgentRunsQueries === "undefined" ? Object : _database.UserAgentRunsQueries,
        typeof _database.UserMessagesQueries === "undefined" ? Object : _database.UserMessagesQueries,
        typeof _redis.RedisService === "undefined" ? Object : _redis.RedisService,
        typeof _apikeyencryptionhelper.ApiKeyEncryptionHelper === "undefined" ? Object : _apikeyencryptionhelper.ApiKeyEncryptionHelper
    ])
], RedisEventController);

//# sourceMappingURL=redis-event.controller.js.map