{"version": 3, "sources": ["../../../../../src/modules/marketing/dto/segment/segment-stats.dto.ts"], "sourcesContent": ["import { ApiProperty } from '@nestjs/swagger';\r\n\r\n/**\r\n * DTO cho thống kê segment\r\n */\r\nexport class SegmentStatsDto {\r\n  /**\r\n   * ID của segment\r\n   * @example 1\r\n   */\r\n  @ApiProperty({\r\n    description: 'ID của segment',\r\n    example: 1,\r\n  })\r\n  segmentId: number;\r\n\r\n  /**\r\n   * Tên segment\r\n   * @example \"Khách hàng tiềm năng\"\r\n   */\r\n  @ApiProperty({\r\n    description: 'Tên segment',\r\n    example: 'Khách hàng tiềm năng',\r\n  })\r\n  segmentName: string;\r\n\r\n  /**\r\n   * Tổng số khách hàng trong segment\r\n   * @example 150\r\n   */\r\n  @ApiProperty({\r\n    description: 'Tổng số khách hàng trong segment',\r\n    example: 150,\r\n  })\r\n  totalAudiences: number;\r\n\r\n  /**\r\n   * Tỷ lệ khách hàng trong segment so với tổng số khách hàng\r\n   * @example 0.25\r\n   */\r\n  @ApiProperty({\r\n    description: 'Tỷ lệ khách hàng trong segment so với tổng số khách hàng',\r\n    example: 0.25,\r\n  })\r\n  percentageOfTotal: number;\r\n\r\n  /**\r\n   * Thời gian cập nhật thống kê (Unix timestamp)\r\n   * @example 1619171200\r\n   */\r\n  @ApiProperty({\r\n    description: 'Thời gian cập nhật thống kê (Unix timestamp)',\r\n    example: 1619171200,\r\n  })\r\n  updatedAt: number;\r\n}\r\n"], "names": ["SegmentStatsDto", "description", "example"], "mappings": ";;;;+BAKaA;;;eAAAA;;;yBALe;;;;;;;;;;AAKrB,IAAA,AAAMA,kBAAN,MAAMA;AAkDb;;;QA5CIC,aAAa;QACbC,SAAS;;;;;;QASTD,aAAa;QACbC,SAAS;;;;;;QASTD,aAAa;QACbC,SAAS;;;;;;QASTD,aAAa;QACbC,SAAS;;;;;;QASTD,aAAa;QACbC,SAAS"}