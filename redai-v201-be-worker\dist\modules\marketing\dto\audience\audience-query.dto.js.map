{"version": 3, "sources": ["../../../../../src/modules/marketing/dto/audience/audience-query.dto.ts"], "sourcesContent": ["import { ApiProperty } from '@nestjs/swagger';\r\nimport {\r\n  IsEmail,\r\n  IsEnum,\r\n  IsInt,\r\n  IsOptional,\r\n  IsPhoneNumber,\r\n  IsString,\r\n  Min,\r\n} from 'class-validator';\r\nimport { Type } from 'class-transformer';\r\n\r\n/**\r\n * Enum cho các trường sắp xếp\r\n */\r\nexport enum AudienceSortField {\r\n  ID = 'id',\r\n  EMAIL = 'email',\r\n  PHONE = 'phone',\r\n  CREATED_AT = 'createdAt',\r\n  UPDATED_AT = 'updatedAt',\r\n}\r\n\r\n/**\r\n * Enum cho thứ tự sắp xếp\r\n */\r\nexport enum SortOrder {\r\n  ASC = 'ASC',\r\n  DESC = 'DESC',\r\n}\r\n\r\n/**\r\n * DTO cho query parameters khi lấy danh sách audience\r\n */\r\nexport class AudienceQueryDto {\r\n  /**\r\n   * Trang hiện tại (bắt đầu từ 1)\r\n   * @example 1\r\n   */\r\n  @ApiProperty({\r\n    description: 'Trang hiện tại (bắt đầu từ 1)',\r\n    example: 1,\r\n    default: 1,\r\n    required: false,\r\n  })\r\n  @IsOptional()\r\n  @IsInt({ message: 'Trang phải là số nguyên' })\r\n  @Min(1, { message: 'Trang phải lớn hơn hoặc bằng 1' })\r\n  @Type(() => Number)\r\n  page?: number = 1;\r\n\r\n  /**\r\n   * Số lượng item trên mỗi trang\r\n   * @example 10\r\n   */\r\n  @ApiProperty({\r\n    description: 'Số lượng item trên mỗi trang',\r\n    example: 10,\r\n    default: 10,\r\n    required: false,\r\n  })\r\n  @IsOptional()\r\n  @IsInt({ message: 'Số lượng item phải là số nguyên' })\r\n  @Min(1, { message: 'Số lượng item phải lớn hơn hoặc bằng 1' })\r\n  @Type(() => Number)\r\n  limit?: number = 10;\r\n\r\n  /**\r\n   * Tìm kiếm theo email\r\n   * @example \"example.com\"\r\n   */\r\n  @ApiProperty({\r\n    description: 'Tìm kiếm theo email',\r\n    example: 'example.com',\r\n    required: false,\r\n  })\r\n  @IsOptional()\r\n  @IsString({ message: 'Email phải là chuỗi' })\r\n  email?: string;\r\n\r\n  /**\r\n   * Tìm kiếm theo số điện thoại\r\n   * @example \"+84\"\r\n   */\r\n  @ApiProperty({\r\n    description: 'Tìm kiếm theo số điện thoại',\r\n    example: '+84',\r\n    required: false,\r\n  })\r\n  @IsOptional()\r\n  @IsString({ message: 'Số điện thoại phải là chuỗi' })\r\n  phone?: string;\r\n\r\n  /**\r\n   * Tìm kiếm theo tag ID\r\n   * @example 1\r\n   */\r\n  @ApiProperty({\r\n    description: 'Tìm kiếm theo tag ID',\r\n    example: 1,\r\n    required: false,\r\n  })\r\n  @IsOptional()\r\n  @IsInt({ message: 'Tag ID phải là số nguyên' })\r\n  @Type(() => Number)\r\n  tagId?: number;\r\n\r\n  /**\r\n   * Tìm kiếm theo tên trường tùy chỉnh\r\n   * @example \"address\"\r\n   */\r\n  @ApiProperty({\r\n    description: 'Tìm kiếm theo tên trường tùy chỉnh',\r\n    example: 'address',\r\n    required: false,\r\n  })\r\n  @IsOptional()\r\n  @IsString({ message: 'Tên trường tùy chỉnh phải là chuỗi' })\r\n  customFieldName?: string;\r\n\r\n  /**\r\n   * Tìm kiếm theo giá trị trường tùy chỉnh\r\n   * @example \"Hanoi\"\r\n   */\r\n  @ApiProperty({\r\n    description: 'Tìm kiếm theo giá trị trường tùy chỉnh',\r\n    example: 'Hanoi',\r\n    required: false,\r\n  })\r\n  @IsOptional()\r\n  @IsString({ message: 'Giá trị trường tùy chỉnh phải là chuỗi' })\r\n  customFieldValue?: string;\r\n\r\n  /**\r\n   * Sắp xếp theo trường\r\n   * @example \"createdAt\"\r\n   */\r\n  @ApiProperty({\r\n    description: 'Sắp xếp theo trường',\r\n    enum: AudienceSortField,\r\n    example: AudienceSortField.CREATED_AT,\r\n    default: AudienceSortField.CREATED_AT,\r\n    required: false,\r\n  })\r\n  @IsOptional()\r\n  @IsEnum(AudienceSortField, {\r\n    message: `Trường sắp xếp phải là một trong các giá trị: ${Object.values(AudienceSortField).join(', ')}`,\r\n  })\r\n  sortBy?: AudienceSortField = AudienceSortField.CREATED_AT;\r\n\r\n  /**\r\n   * Thứ tự sắp xếp\r\n   * @example \"DESC\"\r\n   */\r\n  @ApiProperty({\r\n    description: 'Thứ tự sắp xếp',\r\n    enum: SortOrder,\r\n    example: SortOrder.DESC,\r\n    default: SortOrder.DESC,\r\n    required: false,\r\n  })\r\n  @IsOptional()\r\n  @IsEnum(SortOrder, {\r\n    message: `Thứ tự sắp xếp phải là một trong các giá trị: ${Object.values(SortOrder).join(', ')}`,\r\n  })\r\n  sortOrder?: SortOrder = SortOrder.DESC;\r\n}\r\n"], "names": ["AudienceQueryDto", "AudienceSortField", "SortOrder", "page", "limit", "sortBy", "sortOrder", "description", "example", "default", "required", "message", "Number", "enum", "Object", "values", "join"], "mappings": ";;;;;;;;;;;QAkCaA;eAAAA;;QAnBDC;eAAAA;;QAWAC;eAAAA;;;yBA1BgB;gCASrB;kCACc;;;;;;;;;;AAKd,IAAA,AAAKD,2CAAAA;;;;;;WAAAA;;AAWL,IAAA,AAAKC,mCAAAA;;;WAAAA;;AAQL,IAAA,AAAMF,mBAAN,MAAMA;;QACX;;;GAGC,QAWDG,OAAgB;QAEhB;;;GAGC,QAWDC,QAAiB;QAoEjB;;;GAGC,QAYDC;QAEA;;;GAGC,QAYDC;;AACF;;;QA9HIC,aAAa;QACbC,SAAS;QACTC,SAAS;QACTC,UAAU;;;;QAGHC,SAAS;;;QACRA,SAAS;;oCACPC;;;;;QAQVL,aAAa;QACbC,SAAS;QACTC,SAAS;QACTC,UAAU;;;;QAGHC,SAAS;;;QACRA,SAAS;;oCACPC;;;;;QAQVL,aAAa;QACbC,SAAS;QACTE,UAAU;;;;QAGAC,SAAS;;;;;;QAQnBJ,aAAa;QACbC,SAAS;QACTE,UAAU;;;;QAGAC,SAAS;;;;;;QAQnBJ,aAAa;QACbC,SAAS;QACTE,UAAU;;;;QAGHC,SAAS;;oCACNC;;;;;QAQVL,aAAa;QACbC,SAAS;QACTE,UAAU;;;;QAGAC,SAAS;;;;;;QAQnBJ,aAAa;QACbC,SAAS;QACTE,UAAU;;;;QAGAC,SAAS;;;;;;QAQnBJ,aAAa;QACbM,MAAMZ;QACNO,OAAO;QACPC,OAAO;QACPC,UAAU;;;;QAIVC,SAAS,CAAC,8CAA8C,EAAEG,OAAOC,MAAM,CAACd,mBAAmBe,IAAI,CAAC,OAAO;;;;;;QASvGT,aAAa;QACbM,MAAMX;QACNM,OAAO;QACPC,OAAO;QACPC,UAAU;;;;QAIVC,SAAS,CAAC,8CAA8C,EAAEG,OAAOC,MAAM,CAACb,WAAWc,IAAI,CAAC,OAAO"}