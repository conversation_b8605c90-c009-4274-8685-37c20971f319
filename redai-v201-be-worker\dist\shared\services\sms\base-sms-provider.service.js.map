{"version": 3, "sources": ["../../../../src/shared/services/sms/base-sms-provider.service.ts"], "sourcesContent": ["import { Logger } from '@nestjs/common';\r\nimport {\r\n  ISmsProvider,\r\n  SmsResponse,\r\n  BulkSmsResponse,\r\n  MessageStatusResponse,\r\n  ConnectionTestResponse,\r\n  MessageStatus,\r\n} from './sms-provider.interface';\r\n\r\n/**\r\n * Lớp cơ sở trừu tượng cho các nhà cung cấp SMS\r\n * Triển khai các chức năng chung cho tất cả các nhà cung cấp\r\n */\r\nexport abstract class BaseSmsProvider implements ISmsProvider {\r\n  protected readonly logger: Logger;\r\n\r\n  /**\r\n   * Tên nhà cung cấp SMS\r\n   */\r\n  abstract readonly providerName: string;\r\n\r\n  constructor(loggerName: string) {\r\n    this.logger = new Logger(loggerName);\r\n  }\r\n\r\n  /**\r\n   * G<PERSON>i tin nhắn SMS đến một số điện thoại\r\n   * @param phoneNumber Số điện thoại của người nhận\r\n   * @param message Nội dung tin nhắn\r\n   * @param options Cá<PERSON> tùy chọn bổ sung dành riêng cho nhà cung cấp\r\n   * @returns Promise chứa ID tin nhắn hoặc lỗi\r\n   */\r\n  abstract sendSms(\r\n    phoneNumber: string,\r\n    message: string,\r\n    options?: any,\r\n  ): Promise<SmsResponse>;\r\n\r\n  /**\r\n   * Gửi tin nhắn SMS đến nhiều số điện thoại\r\n   * @param phoneNumbers Danh sách số điện thoại của người nhận\r\n   * @param message Nội dung tin nhắn\r\n   * @param options Các tùy chọn bổ sung dành riêng cho nhà cung cấp\r\n   * @returns Promise chứa kết quả cho từng người nhận\r\n   */\r\n  async sendBulkSms(\r\n    phoneNumbers: string[],\r\n    message: string,\r\n    options?: any,\r\n  ): Promise<BulkSmsResponse> {\r\n    this.logger.debug(\r\n      `Gửi hàng loạt SMS đến ${phoneNumbers.length} người nhận`,\r\n    ); // Gửi hàng loạt SMS\r\n\r\n    const results: {\r\n      phoneNumber: string;\r\n      success: boolean;\r\n      messageId?: string;\r\n      errorCode?: string;\r\n      errorMessage?: string;\r\n    }[] = [];\r\n    let successCount = 0;\r\n    let failureCount = 0;\r\n\r\n    // Triển khai mặc định: gửi SMS đến từng người nhận riêng lẻ\r\n    for (const phoneNumber of phoneNumbers) {\r\n      try {\r\n        const response = await this.sendSms(phoneNumber, message, options);\r\n\r\n        if (response.success) {\r\n          successCount++;\r\n          results.push({\r\n            phoneNumber,\r\n            success: true,\r\n            messageId: response.messageId,\r\n          });\r\n        } else {\r\n          failureCount++;\r\n          results.push({\r\n            phoneNumber,\r\n            success: false,\r\n            errorCode: response.errorCode,\r\n            errorMessage: response.errorMessage,\r\n          });\r\n        }\r\n      } catch (error) {\r\n        failureCount++;\r\n        results.push({\r\n          phoneNumber,\r\n          success: false,\r\n          errorMessage: error.message || 'Lỗi không xác định', // Lỗi không xác định\r\n        });\r\n      }\r\n    }\r\n\r\n    return {\r\n      successCount,\r\n      failureCount,\r\n      results,\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Kiểm tra trạng thái của tin nhắn đã gửi\r\n   * @param messageId ID của tin nhắn cần kiểm tra\r\n   * @returns Promise chứa trạng thái của tin nhắn\r\n   */\r\n  abstract checkMessageStatus(\r\n    messageId: string,\r\n  ): Promise<MessageStatusResponse>;\r\n\r\n  /**\r\n   * Gửi tin nhắn SMS với brandname\r\n   * @param phoneNumber Số điện thoại của người nhận\r\n   * @param message Nội dung tin nhắn\r\n   * @param brandname Tên thương hiệu sử dụng làm người gửi\r\n   * @param options Các tùy chọn bổ sung dành riêng cho nhà cung cấp\r\n   * @returns Promise chứa ID tin nhắn hoặc lỗi\r\n   */\r\n  abstract sendBrandnameSms(\r\n    phoneNumber: string,\r\n    message: string,\r\n    brandname: string,\r\n    options?: any,\r\n  ): Promise<SmsResponse>;\r\n\r\n  /**\r\n   * Gửi tin nhắn SMS OTP (One-Time Password)\r\n   * @param phoneNumber Số điện thoại của người nhận\r\n   * @param otpCode Mã OTP cần gửi\r\n   * @param options Các tùy chọn bổ sung dành riêng cho nhà cung cấp\r\n   * @returns Promise chứa ID tin nhắn hoặc lỗi\r\n   */\r\n  async sendOtp(\r\n    phoneNumber: string,\r\n    otpCode: string,\r\n    options?: any,\r\n  ): Promise<SmsResponse> {\r\n    this.logger.debug(`Gửi mã OTP ${otpCode} đến số điện thoại ${phoneNumber}`); // Gửi mã OTP\r\n\r\n    // Mặc định, sử dụng một mẫu chuẩn cho tin nhắn OTP\r\n    const message = options?.template\r\n      ? options.template.replace('{code}', otpCode)\r\n      : `Mã xác thực của bạn là: ${otpCode}`; // Mã xác thực\r\n\r\n    return this.sendSms(phoneNumber, message, options);\r\n  }\r\n\r\n  /**\r\n   * Kiểm tra kết nối với nhà cung cấp SMS\r\n   * @param config Cấu hình của nhà cung cấp\r\n   * @returns Promise chỉ ra liệu kết nối có thành công hay không\r\n   */\r\n  abstract testConnection(config: any): Promise<ConnectionTestResponse>;\r\n\r\n  /**\r\n   * Định dạng số điện thoại theo chuẩn quốc tế\r\n   * @param phoneNumber Số điện thoại cần định dạng\r\n   * @returns Số điện thoại đã được định dạng\r\n   */\r\n  protected formatPhoneNumber(phoneNumber: string): string {\r\n    // Loại bỏ tất cả các ký tự không phải số\r\n    let cleaned = phoneNumber.replace(/\\D/g, '');\r\n\r\n    // Nếu số điện thoại bắt đầu bằng số 0, thay thế bằng mã quốc gia của Việt Nam (+84)\r\n    if (cleaned.startsWith('0')) {\r\n      cleaned = '84' + cleaned.substring(1);\r\n    }\r\n\r\n    // Thêm dấu + nếu cần thiết\r\n    if (!cleaned.startsWith('+')) {\r\n      cleaned = '+' + cleaned;\r\n    }\r\n\r\n    return cleaned;\r\n  }\r\n\r\n  /**\r\n   * Chuyển đổi trạng thái cụ thể của nhà cung cấp thành trạng thái chuẩn\r\n   * @param providerStatus Trạng thái cụ thể của nhà cung cấp\r\n   * @returns Trạng thái chuẩn\r\n   */\r\n  protected mapToStandardStatus(_providerStatus: string): MessageStatus {\r\n    // Phương thức này phải được ghi đè bởi các lớp con\r\n    return MessageStatus.UNKNOWN;\r\n  }\r\n}\r\n"], "names": ["BaseSmsProvider", "sendBulkSms", "phoneNumbers", "message", "options", "logger", "debug", "length", "results", "successCount", "failureCount", "phoneNumber", "response", "sendSms", "success", "push", "messageId", "errorCode", "errorMessage", "error", "sendOtp", "otpCode", "template", "replace", "formatPhoneNumber", "cleaned", "startsWith", "substring", "mapToStandardStatus", "_providerStatus", "MessageStatus", "UNKNOWN", "constructor", "loggerName", "<PERSON><PERSON>"], "mappings": ";;;;+BAcsBA;;;eAAAA;;;wBAdC;sCAQhB;AAMA,IAAA,AAAeA,kBAAf,MAAeA;IAyBpB;;;;;;GAMC,GACD,MAAMC,YACJC,YAAsB,EACtBC,OAAe,EACfC,OAAa,EACa;QAC1B,IAAI,CAACC,MAAM,CAACC,KAAK,CACf,CAAC,sBAAsB,EAAEJ,aAAaK,MAAM,CAAC,WAAW,CAAC,GACxD,oBAAoB;QAEvB,MAAMC,UAMA,EAAE;QACR,IAAIC,eAAe;QACnB,IAAIC,eAAe;QAEnB,4DAA4D;QAC5D,KAAK,MAAMC,eAAeT,aAAc;YACtC,IAAI;gBACF,MAAMU,WAAW,MAAM,IAAI,CAACC,OAAO,CAACF,aAAaR,SAASC;gBAE1D,IAAIQ,SAASE,OAAO,EAAE;oBACpBL;oBACAD,QAAQO,IAAI,CAAC;wBACXJ;wBACAG,SAAS;wBACTE,WAAWJ,SAASI,SAAS;oBAC/B;gBACF,OAAO;oBACLN;oBACAF,QAAQO,IAAI,CAAC;wBACXJ;wBACAG,SAAS;wBACTG,WAAWL,SAASK,SAAS;wBAC7BC,cAAcN,SAASM,YAAY;oBACrC;gBACF;YACF,EAAE,OAAOC,OAAO;gBACdT;gBACAF,QAAQO,IAAI,CAAC;oBACXJ;oBACAG,SAAS;oBACTI,cAAcC,MAAMhB,OAAO,IAAI;gBACjC;YACF;QACF;QAEA,OAAO;YACLM;YACAC;YACAF;QACF;IACF;IA0BA;;;;;;GAMC,GACD,MAAMY,QACJT,WAAmB,EACnBU,OAAe,EACfjB,OAAa,EACS;QACtB,IAAI,CAACC,MAAM,CAACC,KAAK,CAAC,CAAC,WAAW,EAAEe,QAAQ,mBAAmB,EAAEV,aAAa,GAAG,aAAa;QAE1F,mDAAmD;QACnD,MAAMR,UAAUC,SAASkB,WACrBlB,QAAQkB,QAAQ,CAACC,OAAO,CAAC,UAAUF,WACnC,CAAC,wBAAwB,EAAEA,SAAS,EAAE,cAAc;QAExD,OAAO,IAAI,CAACR,OAAO,CAACF,aAAaR,SAASC;IAC5C;IASA;;;;GAIC,GACD,AAAUoB,kBAAkBb,WAAmB,EAAU;QACvD,yCAAyC;QACzC,IAAIc,UAAUd,YAAYY,OAAO,CAAC,OAAO;QAEzC,oFAAoF;QACpF,IAAIE,QAAQC,UAAU,CAAC,MAAM;YAC3BD,UAAU,OAAOA,QAAQE,SAAS,CAAC;QACrC;QAEA,2BAA2B;QAC3B,IAAI,CAACF,QAAQC,UAAU,CAAC,MAAM;YAC5BD,UAAU,MAAMA;QAClB;QAEA,OAAOA;IACT;IAEA;;;;GAIC,GACD,AAAUG,oBAAoBC,eAAuB,EAAiB;QACpE,mDAAmD;QACnD,OAAOC,mCAAa,CAACC,OAAO;IAC9B;IApKAC,YAAYC,UAAkB,CAAE;QAC9B,IAAI,CAAC5B,MAAM,GAAG,IAAI6B,cAAM,CAACD;IAC3B;AAmKF"}