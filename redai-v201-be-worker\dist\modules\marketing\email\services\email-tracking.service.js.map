{"version": 3, "sources": ["../../../../../src/modules/marketing/email/services/email-tracking.service.ts"], "sourcesContent": ["import { Injectable, Logger } from '@nestjs/common';\r\nimport { InjectRepository } from '@nestjs/typeorm';\r\nimport { Repository } from 'typeorm';\r\nimport { RedisService } from '../../../../infra';\r\nimport { UserCampaignHistory } from '../../entities/user-campaign-history.entity';\r\nimport { EmailTrackingDto } from '../dto';\r\n\r\n/**\r\n * Service xử lý tracking email\r\n */\r\n@Injectable()\r\nexport class EmailTrackingService {\r\n  private readonly logger = new Logger(EmailTrackingService.name);\r\n  private readonly REDIS_TRACKING_KEY = 'email_tracking';\r\n  private readonly BATCH_SIZE = 100;\r\n  private readonly BATCH_INTERVAL = 30000; // 30 seconds\r\n\r\n  constructor(\r\n    @InjectRepository(UserCampaignHistory)\r\n    private readonly campaignHistoryRepository: Repository<UserCampaignHistory>,\r\n    private readonly redisService: RedisService,\r\n  ) {\r\n    // Khởi động batch processor\r\n    this.startBatchProcessor();\r\n  }\r\n\r\n  /**\r\n   * Lưu tracking event vào Redis\r\n   * @param trackingData Dữ liệu tracking\r\n   */\r\n  async saveTrackingToRedis(trackingData: EmailTrackingDto): Promise<void> {\r\n    try {\r\n      const redisKey = `${this.REDIS_TRACKING_KEY}:${Date.now()}:${Math.random()}`;\r\n      const data = JSON.stringify(trackingData);\r\n\r\n      await this.redisService.getRawClient().setex(redisKey, 3600, data); // TTL 1 hour\r\n\r\n      this.logger.debug(\r\n        `Tracking data saved to Redis: ${trackingData.trackingId}`,\r\n      );\r\n    } catch (error) {\r\n      this.logger.error(\r\n        `Error saving tracking to Redis: ${error.message}`,\r\n        error.stack,\r\n      );\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Tạo tracking ID duy nhất\r\n   * @param campaignId ID campaign\r\n   * @param audienceId ID audience\r\n   * @returns Tracking ID\r\n   */\r\n  generateTrackingId(campaignId: number, audienceId: number): string {\r\n    const timestamp = Date.now();\r\n    const random = Math.random().toString(36).substring(2);\r\n    return `${campaignId}_${audienceId}_${timestamp}_${random}`;\r\n  }\r\n\r\n  /**\r\n   * Xử lý tracking event khi email được gửi\r\n   * @param campaignId ID campaign\r\n   * @param audienceId ID audience\r\n   * @param email Email người nhận\r\n   * @param trackingId ID tracking\r\n   */\r\n  async trackEmailSent(\r\n    campaignId: number,\r\n    audienceId: number,\r\n    email: string,\r\n    trackingId: string,\r\n  ): Promise<void> {\r\n    const trackingData: EmailTrackingDto = {\r\n      trackingId,\r\n      campaignId,\r\n      audienceId,\r\n      email,\r\n      eventType: 'sent',\r\n      timestamp: Date.now(),\r\n    };\r\n\r\n    await this.saveTrackingToRedis(trackingData);\r\n  }\r\n\r\n  /**\r\n   * Xử lý tracking event khi email được mở (pixel tracking)\r\n   * @param trackingId ID tracking\r\n   * @param metadata Thông tin bổ sung (IP, User-Agent, etc.)\r\n   */\r\n  async trackEmailOpened(trackingId: string, metadata?: any): Promise<void> {\r\n    try {\r\n      // Lấy thông tin campaign và audience từ tracking ID\r\n      const trackingInfo = this.parseTrackingId(trackingId);\r\n      if (!trackingInfo) {\r\n        this.logger.warn(`Invalid tracking ID: ${trackingId}`);\r\n        return;\r\n      }\r\n\r\n      const trackingData: EmailTrackingDto = {\r\n        trackingId,\r\n        campaignId: trackingInfo.campaignId,\r\n        audienceId: trackingInfo.audienceId,\r\n        email: '', // Sẽ được lấy từ database nếu cần\r\n        eventType: 'opened',\r\n        timestamp: Date.now(),\r\n        metadata,\r\n      };\r\n\r\n      await this.saveTrackingToRedis(trackingData);\r\n    } catch (error) {\r\n      this.logger.error(\r\n        `Error tracking email opened: ${error.message}`,\r\n        error.stack,\r\n      );\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Xử lý tracking event khi email gửi thất bại\r\n   * @param campaignId ID campaign\r\n   * @param audienceId ID audience\r\n   * @param email Email người nhận\r\n   * @param trackingId ID tracking\r\n   * @param error Thông tin lỗi\r\n   */\r\n  async trackEmailFailed(\r\n    campaignId: number,\r\n    audienceId: number,\r\n    email: string,\r\n    trackingId: string,\r\n    error?: any,\r\n  ): Promise<void> {\r\n    const trackingData: EmailTrackingDto = {\r\n      trackingId,\r\n      campaignId,\r\n      audienceId,\r\n      email,\r\n      eventType: 'failed',\r\n      timestamp: Date.now(),\r\n      metadata: { error: error?.message || 'Unknown error' },\r\n    };\r\n\r\n    await this.saveTrackingToRedis(trackingData);\r\n  }\r\n\r\n  /**\r\n   * Parse tracking ID để lấy thông tin campaign và audience\r\n   * @param trackingId ID tracking\r\n   * @returns Thông tin parsed hoặc null\r\n   */\r\n  private parseTrackingId(\r\n    trackingId: string,\r\n  ): { campaignId: number; audienceId: number } | null {\r\n    try {\r\n      const parts = trackingId.split('_');\r\n      if (parts.length >= 2) {\r\n        return {\r\n          campaignId: parseInt(parts[0]),\r\n          audienceId: parseInt(parts[1]),\r\n        };\r\n      }\r\n      return null;\r\n    } catch (error) {\r\n      return null;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Khởi động batch processor để lưu dữ liệu từ Redis vào database\r\n   */\r\n  private startBatchProcessor(): void {\r\n    setInterval(async () => {\r\n      await this.processBatchTracking();\r\n    }, this.BATCH_INTERVAL);\r\n\r\n    this.logger.log('Email tracking batch processor started');\r\n  }\r\n\r\n  /**\r\n   * Xử lý batch tracking data từ Redis vào database\r\n   */\r\n  private async processBatchTracking(): Promise<void> {\r\n    try {\r\n      const redis = this.redisService.getRawClient();\r\n      const pattern = `${this.REDIS_TRACKING_KEY}:*`;\r\n      const keys = await redis.keys(pattern);\r\n\r\n      if (keys.length === 0) {\r\n        return;\r\n      }\r\n\r\n      // Lấy dữ liệu từ Redis\r\n      const trackingEvents: EmailTrackingDto[] = [];\r\n      const pipeline = redis.pipeline();\r\n\r\n      for (const key of keys.slice(0, this.BATCH_SIZE)) {\r\n        pipeline.get(key);\r\n        pipeline.del(key); // Xóa key sau khi lấy\r\n      }\r\n\r\n      const results = await pipeline.exec();\r\n\r\n      // Parse dữ liệu\r\n      if (results && results.length > 0) {\r\n        for (let i = 0; i < results.length; i += 2) {\r\n          const [getError, data] = results[i];\r\n          if (!getError && data) {\r\n            try {\r\n              const trackingEvent = JSON.parse(data as string);\r\n              trackingEvents.push(trackingEvent);\r\n            } catch (parseError) {\r\n              this.logger.error(\r\n                `Error parsing tracking data: ${parseError.message}`,\r\n              );\r\n            }\r\n          }\r\n        }\r\n      }\r\n\r\n      if (trackingEvents.length > 0) {\r\n        await this.saveBatchToDatabase(trackingEvents);\r\n        this.logger.debug(`Processed ${trackingEvents.length} tracking events`);\r\n      }\r\n    } catch (error) {\r\n      this.logger.error(\r\n        `Error processing batch tracking: ${error.message}`,\r\n        error.stack,\r\n      );\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Lưu batch tracking events vào database\r\n   * @param events Danh sách tracking events\r\n   */\r\n  private async saveBatchToDatabase(events: EmailTrackingDto[]): Promise<void> {\r\n    try {\r\n      const historyEntries = events.map((event) => {\r\n        const history = new UserCampaignHistory();\r\n        history.campaignId = event.campaignId;\r\n        history.audienceId = event.audienceId;\r\n        history.status = event.eventType;\r\n        history.sentAt = event.timestamp;\r\n        history.createdAt = Date.now();\r\n        return history;\r\n      });\r\n\r\n      await this.campaignHistoryRepository.save(historyEntries);\r\n      this.logger.debug(\r\n        `Saved ${historyEntries.length} tracking events to database`,\r\n      );\r\n    } catch (error) {\r\n      this.logger.error(\r\n        `Error saving batch to database: ${error.message}`,\r\n        error.stack,\r\n      );\r\n    }\r\n  }\r\n}\r\n"], "names": ["EmailTrackingService", "saveTrackingToRedis", "trackingData", "redisKey", "REDIS_TRACKING_KEY", "Date", "now", "Math", "random", "data", "JSON", "stringify", "redisService", "getRawClient", "setex", "logger", "debug", "trackingId", "error", "message", "stack", "generateTrackingId", "campaignId", "audienceId", "timestamp", "toString", "substring", "trackEmailSent", "email", "eventType", "trackEmailOpened", "metadata", "trackingInfo", "parseTrackingId", "warn", "trackEmailFailed", "parts", "split", "length", "parseInt", "startBatchProcessor", "setInterval", "processBatchTracking", "BATCH_INTERVAL", "log", "redis", "pattern", "keys", "trackingEvents", "pipeline", "key", "slice", "BATCH_SIZE", "get", "del", "results", "exec", "i", "getError", "trackingEvent", "parse", "push", "parseError", "saveBatchToDatabase", "events", "historyEntries", "map", "event", "history", "UserCampaignHistory", "status", "sentAt", "createdAt", "campaignHistoryRepository", "save", "constructor", "<PERSON><PERSON>", "name"], "mappings": ";;;;+BAWaA;;;eAAAA;;;wBAXsB;yBACF;0BACN;uBACE;2CACO;;;;;;;;;;;;;;;AAO7B,IAAA,AAAMA,uBAAN,MAAMA;IAeX;;;GAGC,GACD,MAAMC,oBAAoBC,YAA8B,EAAiB;QACvE,IAAI;YACF,MAAMC,WAAW,GAAG,IAAI,CAACC,kBAAkB,CAAC,CAAC,EAAEC,KAAKC,GAAG,GAAG,CAAC,EAAEC,KAAKC,MAAM,IAAI;YAC5E,MAAMC,OAAOC,KAAKC,SAAS,CAACT;YAE5B,MAAM,IAAI,CAACU,YAAY,CAACC,YAAY,GAAGC,KAAK,CAACX,UAAU,MAAMM,OAAO,aAAa;YAEjF,IAAI,CAACM,MAAM,CAACC,KAAK,CACf,CAAC,8BAA8B,EAAEd,aAAae,UAAU,EAAE;QAE9D,EAAE,OAAOC,OAAO;YACd,IAAI,CAACH,MAAM,CAACG,KAAK,CACf,CAAC,gCAAgC,EAAEA,MAAMC,OAAO,EAAE,EAClDD,MAAME,KAAK;QAEf;IACF;IAEA;;;;;GAKC,GACDC,mBAAmBC,UAAkB,EAAEC,UAAkB,EAAU;QACjE,MAAMC,YAAYnB,KAAKC,GAAG;QAC1B,MAAME,SAASD,KAAKC,MAAM,GAAGiB,QAAQ,CAAC,IAAIC,SAAS,CAAC;QACpD,OAAO,GAAGJ,WAAW,CAAC,EAAEC,WAAW,CAAC,EAAEC,UAAU,CAAC,EAAEhB,QAAQ;IAC7D;IAEA;;;;;;GAMC,GACD,MAAMmB,eACJL,UAAkB,EAClBC,UAAkB,EAClBK,KAAa,EACbX,UAAkB,EACH;QACf,MAAMf,eAAiC;YACrCe;YACAK;YACAC;YACAK;YACAC,WAAW;YACXL,WAAWnB,KAAKC,GAAG;QACrB;QAEA,MAAM,IAAI,CAACL,mBAAmB,CAACC;IACjC;IAEA;;;;GAIC,GACD,MAAM4B,iBAAiBb,UAAkB,EAAEc,QAAc,EAAiB;QACxE,IAAI;YACF,oDAAoD;YACpD,MAAMC,eAAe,IAAI,CAACC,eAAe,CAAChB;YAC1C,IAAI,CAACe,cAAc;gBACjB,IAAI,CAACjB,MAAM,CAACmB,IAAI,CAAC,CAAC,qBAAqB,EAAEjB,YAAY;gBACrD;YACF;YAEA,MAAMf,eAAiC;gBACrCe;gBACAK,YAAYU,aAAaV,UAAU;gBACnCC,YAAYS,aAAaT,UAAU;gBACnCK,OAAO;gBACPC,WAAW;gBACXL,WAAWnB,KAAKC,GAAG;gBACnByB;YACF;YAEA,MAAM,IAAI,CAAC9B,mBAAmB,CAACC;QACjC,EAAE,OAAOgB,OAAO;YACd,IAAI,CAACH,MAAM,CAACG,KAAK,CACf,CAAC,6BAA6B,EAAEA,MAAMC,OAAO,EAAE,EAC/CD,MAAME,KAAK;QAEf;IACF;IAEA;;;;;;;GAOC,GACD,MAAMe,iBACJb,UAAkB,EAClBC,UAAkB,EAClBK,KAAa,EACbX,UAAkB,EAClBC,KAAW,EACI;QACf,MAAMhB,eAAiC;YACrCe;YACAK;YACAC;YACAK;YACAC,WAAW;YACXL,WAAWnB,KAAKC,GAAG;YACnByB,UAAU;gBAAEb,OAAOA,OAAOC,WAAW;YAAgB;QACvD;QAEA,MAAM,IAAI,CAAClB,mBAAmB,CAACC;IACjC;IAEA;;;;GAIC,GACD,AAAQ+B,gBACNhB,UAAkB,EACiC;QACnD,IAAI;YACF,MAAMmB,QAAQnB,WAAWoB,KAAK,CAAC;YAC/B,IAAID,MAAME,MAAM,IAAI,GAAG;gBACrB,OAAO;oBACLhB,YAAYiB,SAASH,KAAK,CAAC,EAAE;oBAC7Bb,YAAYgB,SAASH,KAAK,CAAC,EAAE;gBAC/B;YACF;YACA,OAAO;QACT,EAAE,OAAOlB,OAAO;YACd,OAAO;QACT;IACF;IAEA;;GAEC,GACD,AAAQsB,sBAA4B;QAClCC,YAAY;YACV,MAAM,IAAI,CAACC,oBAAoB;QACjC,GAAG,IAAI,CAACC,cAAc;QAEtB,IAAI,CAAC5B,MAAM,CAAC6B,GAAG,CAAC;IAClB;IAEA;;GAEC,GACD,MAAcF,uBAAsC;QAClD,IAAI;YACF,MAAMG,QAAQ,IAAI,CAACjC,YAAY,CAACC,YAAY;YAC5C,MAAMiC,UAAU,GAAG,IAAI,CAAC1C,kBAAkB,CAAC,EAAE,CAAC;YAC9C,MAAM2C,OAAO,MAAMF,MAAME,IAAI,CAACD;YAE9B,IAAIC,KAAKT,MAAM,KAAK,GAAG;gBACrB;YACF;YAEA,uBAAuB;YACvB,MAAMU,iBAAqC,EAAE;YAC7C,MAAMC,WAAWJ,MAAMI,QAAQ;YAE/B,KAAK,MAAMC,OAAOH,KAAKI,KAAK,CAAC,GAAG,IAAI,CAACC,UAAU,EAAG;gBAChDH,SAASI,GAAG,CAACH;gBACbD,SAASK,GAAG,CAACJ,MAAM,sBAAsB;YAC3C;YAEA,MAAMK,UAAU,MAAMN,SAASO,IAAI;YAEnC,gBAAgB;YAChB,IAAID,WAAWA,QAAQjB,MAAM,GAAG,GAAG;gBACjC,IAAK,IAAImB,IAAI,GAAGA,IAAIF,QAAQjB,MAAM,EAAEmB,KAAK,EAAG;oBAC1C,MAAM,CAACC,UAAUjD,KAAK,GAAG8C,OAAO,CAACE,EAAE;oBACnC,IAAI,CAACC,YAAYjD,MAAM;wBACrB,IAAI;4BACF,MAAMkD,gBAAgBjD,KAAKkD,KAAK,CAACnD;4BACjCuC,eAAea,IAAI,CAACF;wBACtB,EAAE,OAAOG,YAAY;4BACnB,IAAI,CAAC/C,MAAM,CAACG,KAAK,CACf,CAAC,6BAA6B,EAAE4C,WAAW3C,OAAO,EAAE;wBAExD;oBACF;gBACF;YACF;YAEA,IAAI6B,eAAeV,MAAM,GAAG,GAAG;gBAC7B,MAAM,IAAI,CAACyB,mBAAmB,CAACf;gBAC/B,IAAI,CAACjC,MAAM,CAACC,KAAK,CAAC,CAAC,UAAU,EAAEgC,eAAeV,MAAM,CAAC,gBAAgB,CAAC;YACxE;QACF,EAAE,OAAOpB,OAAO;YACd,IAAI,CAACH,MAAM,CAACG,KAAK,CACf,CAAC,iCAAiC,EAAEA,MAAMC,OAAO,EAAE,EACnDD,MAAME,KAAK;QAEf;IACF;IAEA;;;GAGC,GACD,MAAc2C,oBAAoBC,MAA0B,EAAiB;QAC3E,IAAI;YACF,MAAMC,iBAAiBD,OAAOE,GAAG,CAAC,CAACC;gBACjC,MAAMC,UAAU,IAAIC,8CAAmB;gBACvCD,QAAQ9C,UAAU,GAAG6C,MAAM7C,UAAU;gBACrC8C,QAAQ7C,UAAU,GAAG4C,MAAM5C,UAAU;gBACrC6C,QAAQE,MAAM,GAAGH,MAAMtC,SAAS;gBAChCuC,QAAQG,MAAM,GAAGJ,MAAM3C,SAAS;gBAChC4C,QAAQI,SAAS,GAAGnE,KAAKC,GAAG;gBAC5B,OAAO8D;YACT;YAEA,MAAM,IAAI,CAACK,yBAAyB,CAACC,IAAI,CAACT;YAC1C,IAAI,CAAClD,MAAM,CAACC,KAAK,CACf,CAAC,MAAM,EAAEiD,eAAe3B,MAAM,CAAC,4BAA4B,CAAC;QAEhE,EAAE,OAAOpB,OAAO;YACd,IAAI,CAACH,MAAM,CAACG,KAAK,CACf,CAAC,gCAAgC,EAAEA,MAAMC,OAAO,EAAE,EAClDD,MAAME,KAAK;QAEf;IACF;IAjPAuD,YACE,AACiBF,yBAA0D,EAC3E,AAAiB7D,YAA0B,CAC3C;aAFiB6D,4BAAAA;aACA7D,eAAAA;aARFG,SAAS,IAAI6D,cAAM,CAAC5E,qBAAqB6E,IAAI;aAC7CzE,qBAAqB;aACrBgD,aAAa;aACbT,iBAAiB;QAOhC,4BAA4B;QAC5B,IAAI,CAACH,mBAAmB;IAC1B;AA2OF"}