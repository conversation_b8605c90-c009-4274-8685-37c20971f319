{"version": 3, "sources": ["../../../../../src/modules/marketing/dto/statistics/marketing-overview-statistics.dto.ts"], "sourcesContent": ["import { ApiProperty } from '@nestjs/swagger';\r\n\r\n/**\r\n * DTO cho thống kê tổng quan về marketing\r\n */\r\nexport class MarketingOverviewStatisticsDto {\r\n  @ApiProperty({\r\n    description: 'Tổng số audience',\r\n    example: 150,\r\n  })\r\n  totalAudiences: number;\r\n\r\n  @ApiProperty({\r\n    description: 'Tổng số segment',\r\n    example: 10,\r\n  })\r\n  totalSegments: number;\r\n\r\n  @ApiProperty({\r\n    description: 'Tổng số campaign',\r\n    example: 5,\r\n  })\r\n  totalCampaigns: number;\r\n\r\n  @ApiProperty({\r\n    description: 'Tổng số tag',\r\n    example: 20,\r\n  })\r\n  totalTags: number;\r\n\r\n  @ApiProperty({\r\n    description: 'Số lượng audience được thêm trong khoảng thời gian',\r\n    example: 25,\r\n  })\r\n  newAudiences: number;\r\n\r\n  @ApiProperty({\r\n    description: 'Số lượng campaign đã chạy trong khoảng thời gian',\r\n    example: 3,\r\n  })\r\n  activeCampaigns: number;\r\n\r\n  @ApiProperty({\r\n    description: 'Tỷ lệ mở email trung bình (%)',\r\n    example: 35.5,\r\n  })\r\n  averageOpenRate: number;\r\n\r\n  @ApiProperty({\r\n    description: 'Tỷ lệ click email trung bình (%)',\r\n    example: 12.3,\r\n  })\r\n  averageClickRate: number;\r\n\r\n  @ApiProperty({\r\n    description: 'Thời gian cập nhật thống kê (Unix timestamp)',\r\n    example: 1619171200,\r\n  })\r\n  updatedAt: number;\r\n}\r\n"], "names": ["MarketingOverviewStatisticsDto", "description", "example"], "mappings": ";;;;+BAKaA;;;eAAAA;;;yBALe;;;;;;;;;;AAKrB,IAAA,AAAMA,iCAAN,MAAMA;AAsDb;;;QApDIC,aAAa;QACbC,SAAS;;;;;;QAKTD,aAAa;QACbC,SAAS;;;;;;QAKTD,aAAa;QACbC,SAAS;;;;;;QAKTD,aAAa;QACbC,SAAS;;;;;;QAKTD,aAAa;QACbC,SAAS;;;;;;QAKTD,aAAa;QACbC,SAAS;;;;;;QAKTD,aAAa;QACbC,SAAS;;;;;;QAKTD,aAAa;QACbC,SAAS;;;;;;QAKTD,aAAa;QACbC,SAAS"}