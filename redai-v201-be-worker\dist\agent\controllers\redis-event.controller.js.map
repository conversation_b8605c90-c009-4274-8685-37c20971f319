{"version": 3, "sources": ["../../../src/agent/controllers/redis-event.controller.ts"], "sourcesContent": ["import { <PERSON>, Logger } from '@nestjs/common';\nimport { EventPattern, Payload } from '@nestjs/microservices';\nimport { UserAgentRunsQueries, UserMessagesQueries } from '../database';\nimport { REDIS_EVENTS, RunTriggerEvent, RunCancelEvent } from '../constants';\nimport { RedisService } from '../../infra/redis';\nimport { CustomConfigurableType } from '../system/core/react-agent-executor';\nimport { HumanMessage, ToolMessage } from '@langchain/core/messages';\nimport { Command } from '@langchain/langgraph';\nimport { workflow } from '../system/core/multi-agent';\nimport {\n  SUPERVISOR_TAG,\n  WORKER_TAG,\n  SUPERVISOR_TOOL_CALL_TAG,\n  WORKER_TOOL_CALL_TAG\n} from '../system/core/constants';\nimport { ApiKeyEncryptionHelper } from '../helper/api-key-encryption.helper';\n\n// Role tags for event transformation\nconst ROLE_TAGS = [SUPERVISOR_TAG, WORKER_TAG];\nconst TOOL_CALL_TAGS = [SUPERVISOR_TOOL_CALL_TAG, WORKER_TOOL_CALL_TAG];\n\n/**\n * Redis Event Controller for Worker\n *\n * This controller handles incoming Redis pub/sub events from the backend API\n * and processes agent runs using LangGraph thread-based architecture.\n *\n * LangGraph Architecture:\n * - Processes runs by threadId, not runId\n * - Uses AbortController for cancellation\n * - Maintains global threadId → AbortController mapping\n * - Cancellation by threadId ignores non-existent threads (as per LangGraph pattern)\n *\n * Note: @EventPattern handlers MUST be inside a @Controller() class in NestJS microservices.\n */\n@Controller()\nexport class RedisEventController {\n  private readonly logger = new Logger(RedisEventController.name);\n\n  // Global thread management for LangGraph\n  private readonly activeThreads = new Map<string, AbortController>(); // threadId → AbortController\n  private readonly threadToRun = new Map<string, string>(); // threadId → runId (for logging)\n\n  constructor(\n    private readonly userAgentRunsQueries: UserAgentRunsQueries,\n    private readonly userMessagesQueries: UserMessagesQueries,\n    private readonly redisService: RedisService,\n    private readonly apiKeyEncryptionHelper: ApiKeyEncryptionHelper,\n  ) {}\n\n  /**\n   * Handle run trigger events from backend\n   * @param data Run trigger event payload\n   */\n  @EventPattern(REDIS_EVENTS.RUN_TRIGGER)\n  async handleRunTrigger(@Payload() data: RunTriggerEvent): Promise<void> {\n    try {\n      this.logger.log(`Received run trigger event for thread ${data.threadId}, run ${data.runId}`);\n\n      // Validate event payload\n      if (!this.validateRunTriggerEvent(data)) {\n        this.logger.error(`Invalid run trigger event payload for thread ${data?.['threadId']}`);\n        return;\n      }\n\n      // Check if thread is already being processed\n      if (this.activeThreads.has(data.threadId)) {\n        this.logger.warn(`Thread ${data.threadId} is already being processed, ignoring duplicate trigger`);\n        return;\n      }\n\n      // Create AbortController for this thread (LangGraph pattern)\n      const abortController = new AbortController();\n      this.activeThreads.set(data.threadId, abortController);\n      this.threadToRun.set(data.threadId, data.runId);\n\n      // Fetch run data from database\n      const runData = await this.userAgentRunsQueries.getRunById(data.runId);\n\n      if (!runData) {\n        this.logger.error(`Run ${data.runId} not found in database for thread ${data.threadId}`);\n        this.cleanupThread(data.threadId);\n        return;\n      }\n\n      // Extract sessionId from event data or run payload for stream management\n      const sessionId = data.sessionId || runData.payload?.message?.sessionId || data.threadId;\n\n      this.logger.log(`Processing thread ${data.threadId} for agent ${data.agentId} (run ${data.runId}) with sessionId ${sessionId}`);\n\n      await this.processAgentThread(data.threadId, runData, abortController, data.jwt, sessionId);\n\n    } catch (error) {\n      this.logger.error(`Error handling run trigger for thread ${data.threadId}:`, {\n        message: error.message,\n        stack: error.stack,\n        name: error.name,\n        threadId: data.threadId,\n        runId: data.runId,\n        error: error,\n      });\n      this.cleanupThread(data.threadId);\n    }\n  }\n\n  /**\n   * Handle run cancel events from backend\n   * @param data Run cancel event payload\n   */\n  @EventPattern(REDIS_EVENTS.RUN_CANCEL)\n  async handleRunCancel(@Payload() data: RunCancelEvent): Promise<void> {\n    try {\n      this.logger.log(`Received run cancel event for thread ${data.threadId}: ${data.reason}`);\n\n      // Validate event payload\n      if (!this.validateRunCancelEvent(data)) {\n        this.logger.error(`Invalid run cancel event payload for thread ${data?.['threadId']}`);\n        return;\n      }\n\n      // LangGraph pattern: Cancel by threadId, ignore if not active\n      const abortController = this.activeThreads.get(data.threadId);\n      if (!abortController) {\n        this.logger.log(`Thread ${data.threadId} is not active, ignoring cancel request (this is normal)`);\n        return;\n      }\n\n      // Abort the thread using LangGraph AbortController pattern\n      abortController.abort();\n      this.logger.log(`Cancelled thread ${data.threadId}: ${data.reason}`);\n\n      // Emit cancellation event to Redis Streams\n      try {\n        // Get sessionId from run data if available\n        const runId = this.threadToRun.get(data.threadId);\n        let sessionId = data.threadId; // fallback to threadId\n\n        if (runId) {\n          try {\n            const runData = await this.userAgentRunsQueries.getRunById(runId);\n            sessionId = runData?.payload?.message?.sessionId || data.threadId;\n          } catch (error) {\n            this.logger.warn(`Failed to get sessionId for cancellation, using threadId: ${error.message}`);\n          }\n        }\n\n        await this.emitStream({\n          sessionId,\n          event: 'stream_cancelled',\n          data: { reason: data.reason, timestamp: Date.now(), threadId: data.threadId },\n        });\n      } catch (error) {\n        this.logger.warn(`Failed to emit cancellation event for thread ${data.threadId}:`, error);\n      }\n\n      // Cleanup thread resources\n      this.cleanupThread(data.threadId);\n\n    } catch (error) {\n      this.logger.error(`Error handling run cancel for thread ${data.threadId}:`, error);\n    }\n  }\n\n  /**\n   * Validate run trigger event payload\n   * @param data Event data to validate\n   * @returns True if valid\n   */\n  private validateRunTriggerEvent(data: any): data is RunTriggerEvent {\n    return (\n      data &&\n      typeof data.runId === 'string' &&\n      typeof data.threadId === 'string' &&\n      typeof data.sessionId === 'string' &&\n      typeof data.agentId === 'string' &&\n      typeof data.userId === 'number' &&\n      typeof data.jwt === 'string' &&\n      typeof data.timestamp === 'number' &&\n      data.eventType === REDIS_EVENTS.RUN_TRIGGER\n    );\n  }\n\n  /**\n   * Validate run cancel event payload\n   * @param data Event data to validate\n   * @returns True if valid\n   */\n  private validateRunCancelEvent(data: any): data is RunCancelEvent {\n    return (\n      data &&\n      typeof data.threadId === 'string' &&\n      typeof data.reason === 'string' &&\n      typeof data.timestamp === 'number' &&\n      data.eventType === REDIS_EVENTS.RUN_CANCEL\n    );\n  }\n\n  /**\n   * Process agent thread with LangGraph (placeholder for Task 19)\n   * @param threadId LangGraph thread ID\n   * @param runData Run data from database\n   * @param abortController AbortController for cancellation\n   * @param jwt JWT token for authenticated API calls\n   * @param sessionId Session ID for stream management\n   */\n  private async processAgentThread(\n    threadId: string,\n    runData: any,\n    abortController: AbortController,\n    jwt: string,\n    sessionId: string\n  ): Promise<void> {\n    this.logger.debug(`Processing LangGraph thread ${threadId} with run ${runData.id}:`, {\n      threadId,\n      runId: runData.id,\n      status: runData.status,\n      payloadSize: JSON.stringify(runData.payload).length,\n      aborted: abortController.signal.aborted,\n      hasJwt: !!jwt,\n      jwtLength: jwt.length,\n    });\n\n    // Redis streaming events will use the optimized RedisService directly\n\n    // Build CustomConfigurableType from payload data (includes API key decryption)\n    const customConfig = this.buildCustomConfigurable(runData, threadId);\n\n    // Get decrypted payload for message extraction\n    const userId = runData.created_by;\n    const decryptedPayload = this.decryptApiKeysInPayload(runData.payload, userId);\n\n    this.logger.log(`Starting LangGraph streaming for thread ${threadId} (run ${runData.id})`);\n    this.logger.debug(`CustomConfigurableType built:`, {\n      alwaysApproveToolCall: customConfig.alwaysApproveToolCall,\n      thread_id: customConfig.thread_id,\n      supervisorAgentId: customConfig.supervisorAgentId,\n      agentConfigCount: Object.keys(customConfig.agentConfigMap || {}).length,\n    });\n\n    // Prepare to accumulate partial tokens\n    const partialTokens: string[] = [];\n\n    try {\n      // Check if already aborted before starting\n      if (abortController.signal.aborted) {\n        this.logger.log(`Thread ${threadId} was cancelled before processing started`);\n        return;\n      }\n\n      // Extract message data from decrypted payload\n      const messageContent = decryptedPayload?.message?.content || '';\n\n      // Validate message content\n      if (!messageContent || messageContent.trim() === '') {\n        this.logger.error(`Empty message content for thread ${threadId}`, {\n          hasPayload: !!decryptedPayload,\n          hasMessage: !!decryptedPayload?.message,\n          messageContent: messageContent,\n          payloadStructure: {\n            keys: decryptedPayload ? Object.keys(decryptedPayload) : [],\n            messageKeys: decryptedPayload?.message ? Object.keys(decryptedPayload.message) : [],\n            messageType: typeof decryptedPayload?.message,\n            contentType: typeof decryptedPayload?.message?.content,\n          },\n          rawPayloadSample: JSON.stringify(decryptedPayload).substring(0, 500),\n        });\n        throw new Error('Message content is empty or invalid');\n      }\n\n      // Build input for multi-agent LangGraph workflow\n      const humanMessage = new HumanMessage(messageContent);\n      const input = {\n        messages: [humanMessage],\n        activeAgent: customConfig.supervisorAgentId || 'supervisor',\n      };\n\n      this.logger.debug(`Starting LangGraph streamEvents with config:`, {\n        threadId,\n        activeAgent: input.activeAgent,\n        messageLength: messageContent.length,\n        messagePreview: messageContent.substring(0, 100),\n        alwaysApproveToolCall: customConfig.alwaysApproveToolCall,\n        humanMessageType: humanMessage.constructor.name,\n        hasValidMessage: !!humanMessage.content,\n      });\n\n      // Start multi-agent LangGraph streaming\n      const streaming = workflow.streamEvents(input, {\n        configurable: customConfig,\n        subgraphs: true,\n        recursionLimit: 1000,\n        version: 'v2' as const,\n        signal: abortController.signal,\n      });\n      const streamingIterator = streaming[Symbol.asyncIterator]();\n\n      // Set up abort signal listener for graceful cancellation\n      const abortListener = async () => {\n        this.logger.log(`Thread ${threadId} processing was cancelled`);\n        // Force-close the LLM stream generator\n        await streamingIterator?.return?.();\n      };\n      abortController.signal.addEventListener('abort', abortListener);\n\n      // Process LangGraph events and transform to frontend events\n      for await (const { event, data, tags = [] } of streamingIterator) {\n        if (abortController.signal.aborted) {\n          break;\n        }\n\n        // Chat model streaming events\n        if (\n          event === 'on_chat_model_stream' &&\n          tags.some((t: string) => ROLE_TAGS.includes(t))\n        ) {\n          const role = this.getRoleFromTags(tags, ROLE_TAGS)!;\n          if (data.chunk?.content) {\n            const text = data.chunk.content;\n            partialTokens.push(text); // Accumulate tokens for potential persistence\n\n            // Log actual text content with smart truncation\n            const displayText = text.length > 50 ? `${text.substring(0, 50)}...` : text;\n            const safeText = displayText.replace(/\\n/g, '\\\\n').replace(/\\r/g, '\\\\r');\n\n            this.logger.debug(`📝 Streaming text token [${role}]: \"${safeText}\" (${text.length} chars)`, {\n              threadId,\n              role,\n              textLength: text.length,\n              fullText: text.length <= 20 ? text : undefined, // Include full text for short tokens\n            });\n\n            await this.emitStream({\n              sessionId,\n              event: 'stream_text_token',\n              data: { role, text },\n            });\n          } else {\n            this.logger.debug(`🔧 Streaming tool token [${role}]`, { threadId, role });\n            await this.emitStream({\n              sessionId,\n              event: 'stream_tool_token',\n              data: { role },\n            });\n          }\n\n          // Tool start/end events\n        } else if (['on_tool_start', 'on_tool_end'].includes(event)) {\n          const role = this.getRoleFromTags(tags, TOOL_CALL_TAGS)!;\n          const eventType = event === 'on_tool_start' ? 'tool_call_start' : 'tool_call_end';\n\n          // Enhanced tool logging with tool name if available\n          let toolInfo = '';\n          let toolName: string | undefined;\n\n          if (data && typeof data === 'object' && 'name' in data) {\n            toolName = (data as any).name;\n            toolInfo = ` (${toolName})`;\n          }\n\n          this.logger.debug(`🔧 Tool ${eventType} [${role}]${toolInfo}`, {\n            threadId,\n            role,\n            toolName,\n            eventType,\n          });\n\n          await this.emitStream({\n            sessionId,\n            event: eventType,\n            data: { role, toolName },\n          });\n\n          // Interrupt events (user decisions)\n        } else if (\n          event === 'on_chain_stream' &&\n          data.chunk?.[2]?.['__interrupt__']\n        ) {\n          const [{ value }] = data.chunk[2]['__interrupt__'];\n          const interruptValue = JSON.parse(value);\n\n          // Enhanced interrupt logging\n          const displayValue = JSON.stringify(interruptValue).length > 100\n            ? `${JSON.stringify(interruptValue).substring(0, 100)}...`\n            : JSON.stringify(interruptValue);\n\n          this.logger.debug(`⚠️ Tool call interrupt: ${displayValue}`, {\n            threadId,\n            interruptType: interruptValue.role || 'unknown',\n            hasPrompt: !!interruptValue.prompt,\n          });\n\n          await this.emitStream({\n            sessionId,\n            event: 'tool_call_interrupt',\n            data: interruptValue,\n          });\n        }\n      }\n\n      // If not cancelled, emit the \"end of stream\" marker\n      if (!abortController.signal.aborted) {\n        // Log complete response summary\n        const completeResponse = partialTokens.join('');\n        const responsePreview = completeResponse.length > 200\n          ? `${completeResponse.substring(0, 200)}...`\n          : completeResponse;\n        const safePreview = responsePreview.replace(/\\n/g, '\\\\n').replace(/\\r/g, '\\\\r');\n\n        this.logger.log(`✅ LangGraph thread ${threadId} completed: \"${safePreview}\"`, {\n          threadId,\n          runId: runData.id,\n          totalTokens: partialTokens.length,\n          totalChars: completeResponse.length,\n          responsePreview: safePreview,\n        });\n\n        await this.emitStream({\n          sessionId,\n          event: 'llm_stream_end',\n          data: { threadId, runId: runData.id, sessionId },\n        });\n\n        // Save complete assistant response to database\n        if (partialTokens.length > 0) {\n          await this.saveCompleteResponse(threadId, partialTokens.join(''), userId);\n        }\n\n        this.cleanupThread(threadId);\n      }\n\n    } catch (error) {\n      const isAbortError = abortController.signal.aborted ||\n        (error instanceof TypeError && error.message.includes('Invalid state: The reader is not attached to a stream'));\n\n      if (isAbortError) {\n        // Swallow it: this is expected on cancel\n        this.logger.log('LangGraph stream aborted cleanly.');\n      } else {\n        // Unexpected error: log with full stack trace, emit error event, and cleanup\n        this.logger.error(`Error in LangGraph streaming for ${threadId}:`, {\n          message: error.message,\n          stack: error.stack,\n          name: error.name,\n          threadId,\n          runId: runData.id,\n          error: error,\n        });\n\n        // Emit error event to SSE stream so frontend knows about the failure\n        try {\n          await this.emitStream({\n            sessionId,\n            event: 'stream_error',\n            data: {\n              error: error.message || 'Unknown streaming error',\n              errorName: error.name,\n              stack: error.stack,\n              threadId,\n              runId: runData.id,\n              sessionId,\n              timestamp: Date.now(),\n            },\n          });\n\n          // Also emit stream end to properly close the SSE connection\n          await this.emitStream({\n            sessionId,\n            event: 'llm_stream_end',\n            data: { threadId, runId: runData.id, sessionId, error: true },\n          });\n        } catch (emitError) {\n          this.logger.error(`Failed to emit error event for thread ${threadId}:`, emitError);\n        }\n\n        this.cleanupThread(threadId);\n        // Don't re-throw the error to prevent it from bubbling up to the event handler\n      }\n    } finally {\n      // On cancellation, persist the partial tokens\n      if (abortController.signal.aborted && partialTokens.length > 0) {\n        const partialText = partialTokens.join('');\n        const displayText = partialText.length > 100 ? `${partialText.substring(0, 100)}...` : partialText;\n        const safeText = displayText.replace(/\\n/g, '\\\\n').replace(/\\r/g, '\\\\r');\n\n        this.logger.log(`💾 Saving partial response for ${threadId}: \"${safeText}\"`, {\n          tokenCount: partialTokens.length,\n          textLength: partialText.length,\n          preview: safeText,\n        });\n        await this.savePartialResponse(threadId, partialText);\n      }\n    }\n  }\n\n  /**\n   * Cleanup thread resources\n   * @param threadId Thread ID to cleanup\n   */\n  private cleanupThread(threadId: string): void {\n    const runId = this.threadToRun.get(threadId);\n    this.activeThreads.delete(threadId);\n    this.threadToRun.delete(threadId);\n\n    this.logger.debug(`Cleaned up thread ${threadId}${runId ? ` (run ${runId})` : ''}`);\n  }\n\n  /**\n   * Get active threads count for monitoring\n   * @returns Number of active threads\n   */\n  getActiveThreadsCount(): number {\n    return this.activeThreads.size;\n  }\n\n  /**\n   * Get list of active thread IDs for debugging\n   * @returns Array of active thread IDs\n   */\n  getActiveThreadIds(): string[] {\n    return Array.from(this.activeThreads.keys());\n  }\n\n  /**\n   * Check if a specific thread is active\n   * @param threadId Thread ID to check\n   * @returns True if thread is active\n   */\n  isThreadActive(threadId: string): boolean {\n    return this.activeThreads.has(threadId);\n  }\n\n  /**\n   * Get thread-to-run mapping for debugging\n   * @returns Map of threadId to runId\n   */\n  getThreadToRunMapping(): Record<string, string> {\n    return Object.fromEntries(this.threadToRun.entries());\n  }\n\n  /**\n   * Extract role from LangGraph event tags\n   * @param tags Array of tags from LangGraph event\n   * @param roleTags Array of role tags to match against\n   * @returns Role string or null if not found\n   */\n  private getRoleFromTags(tags: string[], roleTags: string[]): string | null {\n    for (const tag of tags) {\n      if (roleTags.includes(tag)) {\n        return tag === SUPERVISOR_TAG || tag === SUPERVISOR_TOOL_CALL_TAG ? 'supervisor' : 'worker';\n      }\n    }\n    return null;\n  }\n\n  /**\n   * Save complete assistant response when streaming finishes successfully\n   * @param threadId Thread ID for the response\n   * @param completeText Complete accumulated text\n   * @param userId User ID who created the run\n   */\n  private async saveCompleteResponse(threadId: string, completeText: string, userId: number): Promise<void> {\n    try {\n      this.logger.log(`💾 Saving complete assistant response for thread ${threadId}`, {\n        threadId,\n        textLength: completeText.length,\n        userId,\n        preview: completeText.substring(0, 100) + (completeText.length > 100 ? '...' : ''),\n      });\n\n      await this.userMessagesQueries.createMessage({\n        thread_id: threadId,\n        role: 'assistant',\n        content: {\n          contentBlocks: [\n            {\n              type: 'text',\n              content: completeText,\n            }\n          ],\n          complete: true,\n          tokenCount: completeText.length,\n          timestamp: Date.now(),\n        },\n        created_by: userId,\n      });\n\n      this.logger.log(`✅ Successfully saved complete assistant response for thread ${threadId}`);\n\n    } catch (error) {\n      this.logger.error(`Failed to save complete response for thread ${threadId}:`, error);\n      // Don't throw - message saving should not fail the streaming completion\n    }\n  }\n\n  /**\n   * Save partial response when streaming is cancelled\n   * @param threadId Thread ID for the partial response\n   * @param partialText Accumulated partial text\n   */\n  private async savePartialResponse(threadId: string, partialText: string): Promise<void> {\n    try {\n      // Get userId from the run data\n      const runId = this.threadToRun.get(threadId);\n      if (!runId) {\n        this.logger.warn(`No runId found for thread ${threadId}, cannot save partial response`);\n        return;\n      }\n\n      const runData = await this.userAgentRunsQueries.getRunById(runId);\n      if (!runData) {\n        this.logger.warn(`No run data found for runId ${runId}, cannot save partial response`);\n        return;\n      }\n\n      const userId = runData.created_by;\n\n      this.logger.log(`💾 Saving partial assistant response for thread ${threadId}`, {\n        threadId,\n        partialLength: partialText.length,\n        userId,\n        preview: partialText.substring(0, 100) + (partialText.length > 100 ? '...' : ''),\n      });\n\n      await this.userMessagesQueries.createMessage({\n        thread_id: threadId,\n        role: 'assistant',\n        content: {\n          contentBlocks: [\n            {\n              type: 'text',\n              content: partialText,\n            }\n          ],\n          partial: true,\n          cancelled: true,\n          tokenCount: partialText.length,\n          timestamp: Date.now(),\n        },\n        created_by: userId,\n      });\n\n      this.logger.log(`✅ Successfully saved partial assistant response for thread ${threadId}`);\n\n    } catch (error) {\n      this.logger.error(`Failed to save partial response for thread ${threadId}:`, error);\n      // Don't throw - partial response saving should not fail the cancellation\n    }\n  }\n\n  /**\n   * Decrypt API keys in the payload based on user type\n   * @param payload The payload containing encrypted API keys\n   * @param userId User ID for decryption (from created_by field)\n   * @returns Payload with decrypted API keys\n   */\n  private decryptApiKeysInPayload(payload: any, userId: number): any {\n    try {\n      if (!payload) {\n        return payload;\n      }\n\n      // Deep clone payload to avoid modifying original\n      const decryptedPayload = JSON.parse(JSON.stringify(payload));\n\n      // Decrypt API keys in agentConfigMap\n      if (decryptedPayload.agentConfigMap) {\n        for (const agentId in decryptedPayload.agentConfigMap) {\n          const agentConfig = decryptedPayload.agentConfigMap[agentId];\n\n          if (agentConfig?.model?.apiKeys && Array.isArray(agentConfig.model.apiKeys)) {\n            this.logger.debug(`Decrypting ${agentConfig.model.apiKeys.length} API keys for agent ${agentId}`);\n\n            agentConfig.model.apiKeys = agentConfig.model.apiKeys.map((encryptedKey: string) => {\n              try {\n                // Determine if this is admin or user key based on agent type\n                if (agentConfig.model.type === 'SYSTEM') {\n                  // System agents use admin keys\n                  return this.apiKeyEncryptionHelper.decryptAdminApiKey(encryptedKey);\n                } else {\n                  // User agents use user-specific keys\n                  return this.apiKeyEncryptionHelper.decryptUserApiKey(encryptedKey, userId);\n                }\n              } catch (error) {\n                this.logger.error(`Failed to decrypt API key for agent ${agentId}:`, error);\n                // Return original key if decryption fails (might already be decrypted)\n                return encryptedKey;\n              }\n            });\n          }\n        }\n      }\n\n      this.logger.debug(`Successfully decrypted API keys in payload for user ${userId}`);\n      return decryptedPayload;\n\n    } catch (error) {\n      this.logger.error(`Failed to decrypt API keys in payload for user ${userId}:`, error);\n      // Return original payload if decryption fails\n      return payload;\n    }\n  }\n\n  /**\n   * Map payload data to CustomConfigurableType for LangGraph configuration\n   * @param runData Run data from database containing payload\n   * @param threadId Thread ID for the configuration\n   * @returns CustomConfigurableType configuration object\n   */\n  private buildCustomConfigurable(runData: any, threadId: string): CustomConfigurableType {\n    try {\n      // Decrypt API keys in payload before processing\n      const userId = runData.created_by; // Get user ID from database record\n      const decryptedPayload = this.decryptApiKeysInPayload(runData.payload, userId);\n\n      // Extract configuration values from decrypted payload with fallbacks\n      const alwaysApproveToolCall = decryptedPayload?.processing?.alwaysApproveToolCall || false;\n      const agentConfigMap = decryptedPayload?.agentConfigMap || {};\n      const supervisorAgentId = decryptedPayload?.primaryAgentId || '';\n\n      const config: CustomConfigurableType = {\n        alwaysApproveToolCall,\n        thread_id: threadId,\n        agentConfigMap,\n        supervisorAgentId,\n        multiMcpClients: undefined, // Skip for now as requested\n      };\n\n      this.logger.debug(`Built CustomConfigurableType for thread ${threadId}:`, {\n        alwaysApproveToolCall,\n        thread_id: threadId,\n        supervisorAgentId,\n        agentConfigCount: Object.keys(agentConfigMap).length,\n        hasMultiMcp: false,\n        userId: userId,\n        apiKeysDecrypted: true,\n      });\n\n      return config;\n\n    } catch (error) {\n      this.logger.error(`Failed to build CustomConfigurableType for thread ${threadId}:`, error);\n\n      // Return minimal fallback configuration\n      return {\n        alwaysApproveToolCall: false,\n        thread_id: threadId,\n        agentConfigMap: {},\n        supervisorAgentId: '',\n        multiMcpClients: undefined,\n      };\n    }\n  }\n\n  /**\n   * Emit streaming event to Redis Streams for backend SSE consumption\n   * Uses optimized RedisService with circuit breaker, retry logic, and error handling\n   * @param event Streaming event data\n   */\n  async emitStream(event: {\n    sessionId: string;\n    event: string;\n    data: any;\n  }): Promise<void> {\n    try {\n      const streamKey = `agent_stream:${event.sessionId}`;\n      const streamData = {\n        event: event.event,\n        data: JSON.stringify(event.data),\n        timestamp: Date.now().toString(),\n      };\n\n      // Publish to Redis Streams using optimized RedisService with circuit breaker and retry\n      await this.redisService.xadd(streamKey, streamData);\n\n      // Set TTL on stream (24 hours) to prevent indefinite storage\n      // Only set TTL on first message to avoid resetting it\n      if (event.event === 'stream_text_token' || event.event === 'tool_call_start') {\n        try {\n          const client = this.redisService.getRawClient();\n          await client.expire(streamKey, 24 * 60 * 60); // 24 hours TTL\n          this.logger.debug(`Set TTL on stream ${streamKey}`);\n        } catch (error) {\n          this.logger.warn(`Failed to set TTL on stream ${streamKey}:`, error.message);\n        }\n      }\n\n      // Also publish notification for real-time updates (following working pattern)\n      const notificationData = JSON.stringify({\n        event: event.event,\n        timestamp: Date.now(),\n      });\n      await this.redisService.publish(streamKey, notificationData);\n\n      this.logger.debug(`Emitted stream event ${event.event} for session ${event.sessionId}`, {\n        streamKey,\n        eventType: event.event,\n        sessionId: event.sessionId,\n        dataSize: JSON.stringify(event.data).length,\n      });\n\n    } catch (error) {\n      this.logger.error(`Failed to emit stream event ${event.event} for session ${event.sessionId}:`, {\n        error: error.message,\n        streamKey: `agent_stream:${event.sessionId}`,\n        eventType: event.event,\n        sessionId: event.sessionId,\n        isRedisError: error.name?.includes('Redis'),\n      });\n      throw error;\n    }\n  }\n\n}\n"], "names": ["RedisEventController", "ROLE_TAGS", "SUPERVISOR_TAG", "WORKER_TAG", "TOOL_CALL_TAGS", "SUPERVISOR_TOOL_CALL_TAG", "WORKER_TOOL_CALL_TAG", "handleRunTrigger", "data", "logger", "log", "threadId", "runId", "validateRunTriggerEvent", "error", "activeThreads", "has", "warn", "abortController", "AbortController", "set", "threadToRun", "runData", "userAgentRunsQueries", "getRunById", "cleanupThread", "sessionId", "payload", "message", "agentId", "processAgentThread", "jwt", "stack", "name", "handleRunCancel", "reason", "validateRunCancelEvent", "get", "abort", "emitStream", "event", "timestamp", "Date", "now", "userId", "eventType", "REDIS_EVENTS", "RUN_TRIGGER", "RUN_CANCEL", "debug", "id", "status", "payloadSize", "JSON", "stringify", "length", "aborted", "signal", "hasJwt", "jwtLength", "customConfig", "buildCustomConfigurable", "created_by", "decryptedPayload", "decryptApiKeysInPayload", "alwaysApproveToolCall", "thread_id", "supervisorAgentId", "agentConfigCount", "Object", "keys", "agentConfigMap", "partialTokens", "messageContent", "content", "trim", "hasPayload", "hasMessage", "payloadStructure", "messageKeys", "messageType", "contentType", "rawPayloadSample", "substring", "Error", "humanMessage", "HumanMessage", "input", "messages", "activeAgent", "messageLength", "messagePreview", "humanMessageType", "constructor", "hasValidMessage", "streaming", "workflow", "streamEvents", "configurable", "subgraphs", "recursionLimit", "version", "streamingIterator", "Symbol", "asyncIterator", "abortListener", "return", "addEventListener", "tags", "some", "t", "includes", "role", "getRoleFromTags", "chunk", "text", "push", "displayText", "safeText", "replace", "textLength", "fullText", "undefined", "toolInfo", "toolName", "value", "interruptValue", "parse", "displayValue", "interruptType", "has<PERSON>rompt", "prompt", "completeResponse", "join", "responsePreview", "safePreview", "totalTokens", "totalChars", "saveCompleteResponse", "isAbortError", "TypeError", "errorName", "emitError", "partialText", "tokenCount", "preview", "savePartialResponse", "delete", "getActiveThreadsCount", "size", "getActiveThreadIds", "Array", "from", "isThreadActive", "getThreadToRunMapping", "fromEntries", "entries", "roleTags", "tag", "completeText", "userMessagesQueries", "createMessage", "contentBlocks", "type", "complete", "partialLength", "partial", "cancelled", "agentConfig", "model", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "isArray", "map", "encrypted<PERSON>ey", "apiKeyEncryptionHelper", "decryptAdminApiKey", "decryptUserApiKey", "processing", "primaryAgentId", "config", "multiMcpClients", "hasMultiMcp", "apiKeysDecrypted", "streamKey", "streamData", "toString", "redisService", "xadd", "client", "getRawClient", "expire", "notificationData", "publish", "dataSize", "isRedisError", "<PERSON><PERSON>", "Map"], "mappings": ";;;;+BAoCaA;;;eAAAA;;;wBApCsB;+BACG;0BACoB;2BACI;uBACjC;0BAEa;4BAEjB;4BAMlB;wCACgC;;;;;;;;;;;;;;;AAEvC,qCAAqC;AACrC,MAAMC,YAAY;IAACC,0BAAc;IAAEC,sBAAU;CAAC;AAC9C,MAAMC,iBAAiB;IAACC,oCAAwB;IAAEC,gCAAoB;CAAC;AAiBhE,IAAA,AAAMN,uBAAN,MAAMA;IAcX;;;GAGC,GACD,MACMO,iBAAiB,AAAWC,IAAqB,EAAiB;QACtE,IAAI;YACF,IAAI,CAACC,MAAM,CAACC,GAAG,CAAC,CAAC,sCAAsC,EAAEF,KAAKG,QAAQ,CAAC,MAAM,EAAEH,KAAKI,KAAK,EAAE;YAE3F,yBAAyB;YACzB,IAAI,CAAC,IAAI,CAACC,uBAAuB,CAACL,OAAO;gBACvC,IAAI,CAACC,MAAM,CAACK,KAAK,CAAC,CAAC,6CAA6C,EAAEN,MAAM,CAAC,WAAW,EAAE;gBACtF;YACF;YAEA,6CAA6C;YAC7C,IAAI,IAAI,CAACO,aAAa,CAACC,GAAG,CAACR,KAAKG,QAAQ,GAAG;gBACzC,IAAI,CAACF,MAAM,CAACQ,IAAI,CAAC,CAAC,OAAO,EAAET,KAAKG,QAAQ,CAAC,uDAAuD,CAAC;gBACjG;YACF;YAEA,6DAA6D;YAC7D,MAAMO,kBAAkB,IAAIC;YAC5B,IAAI,CAACJ,aAAa,CAACK,GAAG,CAACZ,KAAKG,QAAQ,EAAEO;YACtC,IAAI,CAACG,WAAW,CAACD,GAAG,CAACZ,KAAKG,QAAQ,EAAEH,KAAKI,KAAK;YAE9C,+BAA+B;YAC/B,MAAMU,UAAU,MAAM,IAAI,CAACC,oBAAoB,CAACC,UAAU,CAAChB,KAAKI,KAAK;YAErE,IAAI,CAACU,SAAS;gBACZ,IAAI,CAACb,MAAM,CAACK,KAAK,CAAC,CAAC,IAAI,EAAEN,KAAKI,KAAK,CAAC,kCAAkC,EAAEJ,KAAKG,QAAQ,EAAE;gBACvF,IAAI,CAACc,aAAa,CAACjB,KAAKG,QAAQ;gBAChC;YACF;YAEA,yEAAyE;YACzE,MAAMe,YAAYlB,KAAKkB,SAAS,IAAIJ,QAAQK,OAAO,EAAEC,SAASF,aAAalB,KAAKG,QAAQ;YAExF,IAAI,CAACF,MAAM,CAACC,GAAG,CAAC,CAAC,kBAAkB,EAAEF,KAAKG,QAAQ,CAAC,WAAW,EAAEH,KAAKqB,OAAO,CAAC,MAAM,EAAErB,KAAKI,KAAK,CAAC,iBAAiB,EAAEc,WAAW;YAE9H,MAAM,IAAI,CAACI,kBAAkB,CAACtB,KAAKG,QAAQ,EAAEW,SAASJ,iBAAiBV,KAAKuB,GAAG,EAAEL;QAEnF,EAAE,OAAOZ,OAAO;YACd,IAAI,CAACL,MAAM,CAACK,KAAK,CAAC,CAAC,sCAAsC,EAAEN,KAAKG,QAAQ,CAAC,CAAC,CAAC,EAAE;gBAC3EiB,SAASd,MAAMc,OAAO;gBACtBI,OAAOlB,MAAMkB,KAAK;gBAClBC,MAAMnB,MAAMmB,IAAI;gBAChBtB,UAAUH,KAAKG,QAAQ;gBACvBC,OAAOJ,KAAKI,KAAK;gBACjBE,OAAOA;YACT;YACA,IAAI,CAACW,aAAa,CAACjB,KAAKG,QAAQ;QAClC;IACF;IAEA;;;GAGC,GACD,MACMuB,gBAAgB,AAAW1B,IAAoB,EAAiB;QACpE,IAAI;YACF,IAAI,CAACC,MAAM,CAACC,GAAG,CAAC,CAAC,qCAAqC,EAAEF,KAAKG,QAAQ,CAAC,EAAE,EAAEH,KAAK2B,MAAM,EAAE;YAEvF,yBAAyB;YACzB,IAAI,CAAC,IAAI,CAACC,sBAAsB,CAAC5B,OAAO;gBACtC,IAAI,CAACC,MAAM,CAACK,KAAK,CAAC,CAAC,4CAA4C,EAAEN,MAAM,CAAC,WAAW,EAAE;gBACrF;YACF;YAEA,8DAA8D;YAC9D,MAAMU,kBAAkB,IAAI,CAACH,aAAa,CAACsB,GAAG,CAAC7B,KAAKG,QAAQ;YAC5D,IAAI,CAACO,iBAAiB;gBACpB,IAAI,CAACT,MAAM,CAACC,GAAG,CAAC,CAAC,OAAO,EAAEF,KAAKG,QAAQ,CAAC,wDAAwD,CAAC;gBACjG;YACF;YAEA,2DAA2D;YAC3DO,gBAAgBoB,KAAK;YACrB,IAAI,CAAC7B,MAAM,CAACC,GAAG,CAAC,CAAC,iBAAiB,EAAEF,KAAKG,QAAQ,CAAC,EAAE,EAAEH,KAAK2B,MAAM,EAAE;YAEnE,2CAA2C;YAC3C,IAAI;gBACF,2CAA2C;gBAC3C,MAAMvB,QAAQ,IAAI,CAACS,WAAW,CAACgB,GAAG,CAAC7B,KAAKG,QAAQ;gBAChD,IAAIe,YAAYlB,KAAKG,QAAQ,EAAE,uBAAuB;gBAEtD,IAAIC,OAAO;oBACT,IAAI;wBACF,MAAMU,UAAU,MAAM,IAAI,CAACC,oBAAoB,CAACC,UAAU,CAACZ;wBAC3Dc,YAAYJ,SAASK,SAASC,SAASF,aAAalB,KAAKG,QAAQ;oBACnE,EAAE,OAAOG,OAAO;wBACd,IAAI,CAACL,MAAM,CAACQ,IAAI,CAAC,CAAC,0DAA0D,EAAEH,MAAMc,OAAO,EAAE;oBAC/F;gBACF;gBAEA,MAAM,IAAI,CAACW,UAAU,CAAC;oBACpBb;oBACAc,OAAO;oBACPhC,MAAM;wBAAE2B,QAAQ3B,KAAK2B,MAAM;wBAAEM,WAAWC,KAAKC,GAAG;wBAAIhC,UAAUH,KAAKG,QAAQ;oBAAC;gBAC9E;YACF,EAAE,OAAOG,OAAO;gBACd,IAAI,CAACL,MAAM,CAACQ,IAAI,CAAC,CAAC,6CAA6C,EAAET,KAAKG,QAAQ,CAAC,CAAC,CAAC,EAAEG;YACrF;YAEA,2BAA2B;YAC3B,IAAI,CAACW,aAAa,CAACjB,KAAKG,QAAQ;QAElC,EAAE,OAAOG,OAAO;YACd,IAAI,CAACL,MAAM,CAACK,KAAK,CAAC,CAAC,qCAAqC,EAAEN,KAAKG,QAAQ,CAAC,CAAC,CAAC,EAAEG;QAC9E;IACF;IAEA;;;;GAIC,GACD,AAAQD,wBAAwBL,IAAS,EAA2B;QAClE,OACEA,QACA,OAAOA,KAAKI,KAAK,KAAK,YACtB,OAAOJ,KAAKG,QAAQ,KAAK,YACzB,OAAOH,KAAKkB,SAAS,KAAK,YAC1B,OAAOlB,KAAKqB,OAAO,KAAK,YACxB,OAAOrB,KAAKoC,MAAM,KAAK,YACvB,OAAOpC,KAAKuB,GAAG,KAAK,YACpB,OAAOvB,KAAKiC,SAAS,KAAK,YAC1BjC,KAAKqC,SAAS,KAAKC,uBAAY,CAACC,WAAW;IAE/C;IAEA;;;;GAIC,GACD,AAAQX,uBAAuB5B,IAAS,EAA0B;QAChE,OACEA,QACA,OAAOA,KAAKG,QAAQ,KAAK,YACzB,OAAOH,KAAK2B,MAAM,KAAK,YACvB,OAAO3B,KAAKiC,SAAS,KAAK,YAC1BjC,KAAKqC,SAAS,KAAKC,uBAAY,CAACE,UAAU;IAE9C;IAEA;;;;;;;GAOC,GACD,MAAclB,mBACZnB,QAAgB,EAChBW,OAAY,EACZJ,eAAgC,EAChCa,GAAW,EACXL,SAAiB,EACF;QACf,IAAI,CAACjB,MAAM,CAACwC,KAAK,CAAC,CAAC,4BAA4B,EAAEtC,SAAS,UAAU,EAAEW,QAAQ4B,EAAE,CAAC,CAAC,CAAC,EAAE;YACnFvC;YACAC,OAAOU,QAAQ4B,EAAE;YACjBC,QAAQ7B,QAAQ6B,MAAM;YACtBC,aAAaC,KAAKC,SAAS,CAAChC,QAAQK,OAAO,EAAE4B,MAAM;YACnDC,SAAStC,gBAAgBuC,MAAM,CAACD,OAAO;YACvCE,QAAQ,CAAC,CAAC3B;YACV4B,WAAW5B,IAAIwB,MAAM;QACvB;QAEA,sEAAsE;QAEtE,+EAA+E;QAC/E,MAAMK,eAAe,IAAI,CAACC,uBAAuB,CAACvC,SAASX;QAE3D,+CAA+C;QAC/C,MAAMiC,SAAStB,QAAQwC,UAAU;QACjC,MAAMC,mBAAmB,IAAI,CAACC,uBAAuB,CAAC1C,QAAQK,OAAO,EAAEiB;QAEvE,IAAI,CAACnC,MAAM,CAACC,GAAG,CAAC,CAAC,wCAAwC,EAAEC,SAAS,MAAM,EAAEW,QAAQ4B,EAAE,CAAC,CAAC,CAAC;QACzF,IAAI,CAACzC,MAAM,CAACwC,KAAK,CAAC,CAAC,6BAA6B,CAAC,EAAE;YACjDgB,uBAAuBL,aAAaK,qBAAqB;YACzDC,WAAWN,aAAaM,SAAS;YACjCC,mBAAmBP,aAAaO,iBAAiB;YACjDC,kBAAkBC,OAAOC,IAAI,CAACV,aAAaW,cAAc,IAAI,CAAC,GAAGhB,MAAM;QACzE;QAEA,uCAAuC;QACvC,MAAMiB,gBAA0B,EAAE;QAElC,IAAI;YACF,2CAA2C;YAC3C,IAAItD,gBAAgBuC,MAAM,CAACD,OAAO,EAAE;gBAClC,IAAI,CAAC/C,MAAM,CAACC,GAAG,CAAC,CAAC,OAAO,EAAEC,SAAS,wCAAwC,CAAC;gBAC5E;YACF;YAEA,8CAA8C;YAC9C,MAAM8D,iBAAiBV,kBAAkBnC,SAAS8C,WAAW;YAE7D,2BAA2B;YAC3B,IAAI,CAACD,kBAAkBA,eAAeE,IAAI,OAAO,IAAI;gBACnD,IAAI,CAAClE,MAAM,CAACK,KAAK,CAAC,CAAC,iCAAiC,EAAEH,UAAU,EAAE;oBAChEiE,YAAY,CAAC,CAACb;oBACdc,YAAY,CAAC,CAACd,kBAAkBnC;oBAChC6C,gBAAgBA;oBAChBK,kBAAkB;wBAChBR,MAAMP,mBAAmBM,OAAOC,IAAI,CAACP,oBAAoB,EAAE;wBAC3DgB,aAAahB,kBAAkBnC,UAAUyC,OAAOC,IAAI,CAACP,iBAAiBnC,OAAO,IAAI,EAAE;wBACnFoD,aAAa,OAAOjB,kBAAkBnC;wBACtCqD,aAAa,OAAOlB,kBAAkBnC,SAAS8C;oBACjD;oBACAQ,kBAAkB7B,KAAKC,SAAS,CAACS,kBAAkBoB,SAAS,CAAC,GAAG;gBAClE;gBACA,MAAM,IAAIC,MAAM;YAClB;YAEA,iDAAiD;YACjD,MAAMC,eAAe,IAAIC,sBAAY,CAACb;YACtC,MAAMc,QAAQ;gBACZC,UAAU;oBAACH;iBAAa;gBACxBI,aAAa7B,aAAaO,iBAAiB,IAAI;YACjD;YAEA,IAAI,CAAC1D,MAAM,CAACwC,KAAK,CAAC,CAAC,4CAA4C,CAAC,EAAE;gBAChEtC;gBACA8E,aAAaF,MAAME,WAAW;gBAC9BC,eAAejB,eAAelB,MAAM;gBACpCoC,gBAAgBlB,eAAeU,SAAS,CAAC,GAAG;gBAC5ClB,uBAAuBL,aAAaK,qBAAqB;gBACzD2B,kBAAkBP,aAAaQ,WAAW,CAAC5D,IAAI;gBAC/C6D,iBAAiB,CAAC,CAACT,aAAaX,OAAO;YACzC;YAEA,wCAAwC;YACxC,MAAMqB,YAAYC,oBAAQ,CAACC,YAAY,CAACV,OAAO;gBAC7CW,cAActC;gBACduC,WAAW;gBACXC,gBAAgB;gBAChBC,SAAS;gBACT5C,QAAQvC,gBAAgBuC,MAAM;YAChC;YACA,MAAM6C,oBAAoBP,SAAS,CAACQ,OAAOC,aAAa,CAAC;YAEzD,yDAAyD;YACzD,MAAMC,gBAAgB;gBACpB,IAAI,CAAChG,MAAM,CAACC,GAAG,CAAC,CAAC,OAAO,EAAEC,SAAS,yBAAyB,CAAC;gBAC7D,uCAAuC;gBACvC,MAAM2F,mBAAmBI;YAC3B;YACAxF,gBAAgBuC,MAAM,CAACkD,gBAAgB,CAAC,SAASF;YAEjD,4DAA4D;YAC5D,WAAW,MAAM,EAAEjE,KAAK,EAAEhC,IAAI,EAAEoG,OAAO,EAAE,EAAE,IAAIN,kBAAmB;gBAChE,IAAIpF,gBAAgBuC,MAAM,CAACD,OAAO,EAAE;oBAClC;gBACF;gBAEA,8BAA8B;gBAC9B,IACEhB,UAAU,0BACVoE,KAAKC,IAAI,CAAC,CAACC,IAAc7G,UAAU8G,QAAQ,CAACD,KAC5C;oBACA,MAAME,OAAO,IAAI,CAACC,eAAe,CAACL,MAAM3G;oBACxC,IAAIO,KAAK0G,KAAK,EAAExC,SAAS;wBACvB,MAAMyC,OAAO3G,KAAK0G,KAAK,CAACxC,OAAO;wBAC/BF,cAAc4C,IAAI,CAACD,OAAO,8CAA8C;wBAExE,gDAAgD;wBAChD,MAAME,cAAcF,KAAK5D,MAAM,GAAG,KAAK,GAAG4D,KAAKhC,SAAS,CAAC,GAAG,IAAI,GAAG,CAAC,GAAGgC;wBACvE,MAAMG,WAAWD,YAAYE,OAAO,CAAC,OAAO,OAAOA,OAAO,CAAC,OAAO;wBAElE,IAAI,CAAC9G,MAAM,CAACwC,KAAK,CAAC,CAAC,yBAAyB,EAAE+D,KAAK,IAAI,EAAEM,SAAS,GAAG,EAAEH,KAAK5D,MAAM,CAAC,OAAO,CAAC,EAAE;4BAC3F5C;4BACAqG;4BACAQ,YAAYL,KAAK5D,MAAM;4BACvBkE,UAAUN,KAAK5D,MAAM,IAAI,KAAK4D,OAAOO;wBACvC;wBAEA,MAAM,IAAI,CAACnF,UAAU,CAAC;4BACpBb;4BACAc,OAAO;4BACPhC,MAAM;gCAAEwG;gCAAMG;4BAAK;wBACrB;oBACF,OAAO;wBACL,IAAI,CAAC1G,MAAM,CAACwC,KAAK,CAAC,CAAC,yBAAyB,EAAE+D,KAAK,CAAC,CAAC,EAAE;4BAAErG;4BAAUqG;wBAAK;wBACxE,MAAM,IAAI,CAACzE,UAAU,CAAC;4BACpBb;4BACAc,OAAO;4BACPhC,MAAM;gCAAEwG;4BAAK;wBACf;oBACF;gBAEA,wBAAwB;gBAC1B,OAAO,IAAI;oBAAC;oBAAiB;iBAAc,CAACD,QAAQ,CAACvE,QAAQ;oBAC3D,MAAMwE,OAAO,IAAI,CAACC,eAAe,CAACL,MAAMxG;oBACxC,MAAMyC,YAAYL,UAAU,kBAAkB,oBAAoB;oBAElE,oDAAoD;oBACpD,IAAImF,WAAW;oBACf,IAAIC;oBAEJ,IAAIpH,QAAQ,OAAOA,SAAS,YAAY,UAAUA,MAAM;wBACtDoH,WAAW,AAACpH,KAAayB,IAAI;wBAC7B0F,WAAW,CAAC,EAAE,EAAEC,SAAS,CAAC,CAAC;oBAC7B;oBAEA,IAAI,CAACnH,MAAM,CAACwC,KAAK,CAAC,CAAC,QAAQ,EAAEJ,UAAU,EAAE,EAAEmE,KAAK,CAAC,EAAEW,UAAU,EAAE;wBAC7DhH;wBACAqG;wBACAY;wBACA/E;oBACF;oBAEA,MAAM,IAAI,CAACN,UAAU,CAAC;wBACpBb;wBACAc,OAAOK;wBACPrC,MAAM;4BAAEwG;4BAAMY;wBAAS;oBACzB;gBAEA,oCAAoC;gBACtC,OAAO,IACLpF,UAAU,qBACVhC,KAAK0G,KAAK,EAAE,CAAC,EAAE,EAAE,CAAC,gBAAgB,EAClC;oBACA,MAAM,CAAC,EAAEW,KAAK,EAAE,CAAC,GAAGrH,KAAK0G,KAAK,CAAC,EAAE,CAAC,gBAAgB;oBAClD,MAAMY,iBAAiBzE,KAAK0E,KAAK,CAACF;oBAElC,6BAA6B;oBAC7B,MAAMG,eAAe3E,KAAKC,SAAS,CAACwE,gBAAgBvE,MAAM,GAAG,MACzD,GAAGF,KAAKC,SAAS,CAACwE,gBAAgB3C,SAAS,CAAC,GAAG,KAAK,GAAG,CAAC,GACxD9B,KAAKC,SAAS,CAACwE;oBAEnB,IAAI,CAACrH,MAAM,CAACwC,KAAK,CAAC,CAAC,wBAAwB,EAAE+E,cAAc,EAAE;wBAC3DrH;wBACAsH,eAAeH,eAAed,IAAI,IAAI;wBACtCkB,WAAW,CAAC,CAACJ,eAAeK,MAAM;oBACpC;oBAEA,MAAM,IAAI,CAAC5F,UAAU,CAAC;wBACpBb;wBACAc,OAAO;wBACPhC,MAAMsH;oBACR;gBACF;YACF;YAEA,oDAAoD;YACpD,IAAI,CAAC5G,gBAAgBuC,MAAM,CAACD,OAAO,EAAE;gBACnC,gCAAgC;gBAChC,MAAM4E,mBAAmB5D,cAAc6D,IAAI,CAAC;gBAC5C,MAAMC,kBAAkBF,iBAAiB7E,MAAM,GAAG,MAC9C,GAAG6E,iBAAiBjD,SAAS,CAAC,GAAG,KAAK,GAAG,CAAC,GAC1CiD;gBACJ,MAAMG,cAAcD,gBAAgBf,OAAO,CAAC,OAAO,OAAOA,OAAO,CAAC,OAAO;gBAEzE,IAAI,CAAC9G,MAAM,CAACC,GAAG,CAAC,CAAC,mBAAmB,EAAEC,SAAS,aAAa,EAAE4H,YAAY,CAAC,CAAC,EAAE;oBAC5E5H;oBACAC,OAAOU,QAAQ4B,EAAE;oBACjBsF,aAAahE,cAAcjB,MAAM;oBACjCkF,YAAYL,iBAAiB7E,MAAM;oBACnC+E,iBAAiBC;gBACnB;gBAEA,MAAM,IAAI,CAAChG,UAAU,CAAC;oBACpBb;oBACAc,OAAO;oBACPhC,MAAM;wBAAEG;wBAAUC,OAAOU,QAAQ4B,EAAE;wBAAExB;oBAAU;gBACjD;gBAEA,+CAA+C;gBAC/C,IAAI8C,cAAcjB,MAAM,GAAG,GAAG;oBAC5B,MAAM,IAAI,CAACmF,oBAAoB,CAAC/H,UAAU6D,cAAc6D,IAAI,CAAC,KAAKzF;gBACpE;gBAEA,IAAI,CAACnB,aAAa,CAACd;YACrB;QAEF,EAAE,OAAOG,OAAO;YACd,MAAM6H,eAAezH,gBAAgBuC,MAAM,CAACD,OAAO,IAChD1C,iBAAiB8H,aAAa9H,MAAMc,OAAO,CAACmF,QAAQ,CAAC;YAExD,IAAI4B,cAAc;gBAChB,yCAAyC;gBACzC,IAAI,CAAClI,MAAM,CAACC,GAAG,CAAC;YAClB,OAAO;gBACL,6EAA6E;gBAC7E,IAAI,CAACD,MAAM,CAACK,KAAK,CAAC,CAAC,iCAAiC,EAAEH,SAAS,CAAC,CAAC,EAAE;oBACjEiB,SAASd,MAAMc,OAAO;oBACtBI,OAAOlB,MAAMkB,KAAK;oBAClBC,MAAMnB,MAAMmB,IAAI;oBAChBtB;oBACAC,OAAOU,QAAQ4B,EAAE;oBACjBpC,OAAOA;gBACT;gBAEA,qEAAqE;gBACrE,IAAI;oBACF,MAAM,IAAI,CAACyB,UAAU,CAAC;wBACpBb;wBACAc,OAAO;wBACPhC,MAAM;4BACJM,OAAOA,MAAMc,OAAO,IAAI;4BACxBiH,WAAW/H,MAAMmB,IAAI;4BACrBD,OAAOlB,MAAMkB,KAAK;4BAClBrB;4BACAC,OAAOU,QAAQ4B,EAAE;4BACjBxB;4BACAe,WAAWC,KAAKC,GAAG;wBACrB;oBACF;oBAEA,4DAA4D;oBAC5D,MAAM,IAAI,CAACJ,UAAU,CAAC;wBACpBb;wBACAc,OAAO;wBACPhC,MAAM;4BAAEG;4BAAUC,OAAOU,QAAQ4B,EAAE;4BAAExB;4BAAWZ,OAAO;wBAAK;oBAC9D;gBACF,EAAE,OAAOgI,WAAW;oBAClB,IAAI,CAACrI,MAAM,CAACK,KAAK,CAAC,CAAC,sCAAsC,EAAEH,SAAS,CAAC,CAAC,EAAEmI;gBAC1E;gBAEA,IAAI,CAACrH,aAAa,CAACd;YACnB,+EAA+E;YACjF;QACF,SAAU;YACR,8CAA8C;YAC9C,IAAIO,gBAAgBuC,MAAM,CAACD,OAAO,IAAIgB,cAAcjB,MAAM,GAAG,GAAG;gBAC9D,MAAMwF,cAAcvE,cAAc6D,IAAI,CAAC;gBACvC,MAAMhB,cAAc0B,YAAYxF,MAAM,GAAG,MAAM,GAAGwF,YAAY5D,SAAS,CAAC,GAAG,KAAK,GAAG,CAAC,GAAG4D;gBACvF,MAAMzB,WAAWD,YAAYE,OAAO,CAAC,OAAO,OAAOA,OAAO,CAAC,OAAO;gBAElE,IAAI,CAAC9G,MAAM,CAACC,GAAG,CAAC,CAAC,+BAA+B,EAAEC,SAAS,GAAG,EAAE2G,SAAS,CAAC,CAAC,EAAE;oBAC3E0B,YAAYxE,cAAcjB,MAAM;oBAChCiE,YAAYuB,YAAYxF,MAAM;oBAC9B0F,SAAS3B;gBACX;gBACA,MAAM,IAAI,CAAC4B,mBAAmB,CAACvI,UAAUoI;YAC3C;QACF;IACF;IAEA;;;GAGC,GACD,AAAQtH,cAAcd,QAAgB,EAAQ;QAC5C,MAAMC,QAAQ,IAAI,CAACS,WAAW,CAACgB,GAAG,CAAC1B;QACnC,IAAI,CAACI,aAAa,CAACoI,MAAM,CAACxI;QAC1B,IAAI,CAACU,WAAW,CAAC8H,MAAM,CAACxI;QAExB,IAAI,CAACF,MAAM,CAACwC,KAAK,CAAC,CAAC,kBAAkB,EAAEtC,WAAWC,QAAQ,CAAC,MAAM,EAAEA,MAAM,CAAC,CAAC,GAAG,IAAI;IACpF;IAEA;;;GAGC,GACDwI,wBAAgC;QAC9B,OAAO,IAAI,CAACrI,aAAa,CAACsI,IAAI;IAChC;IAEA;;;GAGC,GACDC,qBAA+B;QAC7B,OAAOC,MAAMC,IAAI,CAAC,IAAI,CAACzI,aAAa,CAACuD,IAAI;IAC3C;IAEA;;;;GAIC,GACDmF,eAAe9I,QAAgB,EAAW;QACxC,OAAO,IAAI,CAACI,aAAa,CAACC,GAAG,CAACL;IAChC;IAEA;;;GAGC,GACD+I,wBAAgD;QAC9C,OAAOrF,OAAOsF,WAAW,CAAC,IAAI,CAACtI,WAAW,CAACuI,OAAO;IACpD;IAEA;;;;;GAKC,GACD,AAAQ3C,gBAAgBL,IAAc,EAAEiD,QAAkB,EAAiB;QACzE,KAAK,MAAMC,OAAOlD,KAAM;YACtB,IAAIiD,SAAS9C,QAAQ,CAAC+C,MAAM;gBAC1B,OAAOA,QAAQ5J,0BAAc,IAAI4J,QAAQzJ,oCAAwB,GAAG,eAAe;YACrF;QACF;QACA,OAAO;IACT;IAEA;;;;;GAKC,GACD,MAAcqI,qBAAqB/H,QAAgB,EAAEoJ,YAAoB,EAAEnH,MAAc,EAAiB;QACxG,IAAI;YACF,IAAI,CAACnC,MAAM,CAACC,GAAG,CAAC,CAAC,iDAAiD,EAAEC,UAAU,EAAE;gBAC9EA;gBACA6G,YAAYuC,aAAaxG,MAAM;gBAC/BX;gBACAqG,SAASc,aAAa5E,SAAS,CAAC,GAAG,OAAQ4E,CAAAA,aAAaxG,MAAM,GAAG,MAAM,QAAQ,EAAC;YAClF;YAEA,MAAM,IAAI,CAACyG,mBAAmB,CAACC,aAAa,CAAC;gBAC3C/F,WAAWvD;gBACXqG,MAAM;gBACNtC,SAAS;oBACPwF,eAAe;wBACb;4BACEC,MAAM;4BACNzF,SAASqF;wBACX;qBACD;oBACDK,UAAU;oBACVpB,YAAYe,aAAaxG,MAAM;oBAC/Bd,WAAWC,KAAKC,GAAG;gBACrB;gBACAmB,YAAYlB;YACd;YAEA,IAAI,CAACnC,MAAM,CAACC,GAAG,CAAC,CAAC,4DAA4D,EAAEC,UAAU;QAE3F,EAAE,OAAOG,OAAO;YACd,IAAI,CAACL,MAAM,CAACK,KAAK,CAAC,CAAC,4CAA4C,EAAEH,SAAS,CAAC,CAAC,EAAEG;QAC9E,wEAAwE;QAC1E;IACF;IAEA;;;;GAIC,GACD,MAAcoI,oBAAoBvI,QAAgB,EAAEoI,WAAmB,EAAiB;QACtF,IAAI;YACF,+BAA+B;YAC/B,MAAMnI,QAAQ,IAAI,CAACS,WAAW,CAACgB,GAAG,CAAC1B;YACnC,IAAI,CAACC,OAAO;gBACV,IAAI,CAACH,MAAM,CAACQ,IAAI,CAAC,CAAC,0BAA0B,EAAEN,SAAS,8BAA8B,CAAC;gBACtF;YACF;YAEA,MAAMW,UAAU,MAAM,IAAI,CAACC,oBAAoB,CAACC,UAAU,CAACZ;YAC3D,IAAI,CAACU,SAAS;gBACZ,IAAI,CAACb,MAAM,CAACQ,IAAI,CAAC,CAAC,4BAA4B,EAAEL,MAAM,8BAA8B,CAAC;gBACrF;YACF;YAEA,MAAMgC,SAAStB,QAAQwC,UAAU;YAEjC,IAAI,CAACrD,MAAM,CAACC,GAAG,CAAC,CAAC,gDAAgD,EAAEC,UAAU,EAAE;gBAC7EA;gBACA0J,eAAetB,YAAYxF,MAAM;gBACjCX;gBACAqG,SAASF,YAAY5D,SAAS,CAAC,GAAG,OAAQ4D,CAAAA,YAAYxF,MAAM,GAAG,MAAM,QAAQ,EAAC;YAChF;YAEA,MAAM,IAAI,CAACyG,mBAAmB,CAACC,aAAa,CAAC;gBAC3C/F,WAAWvD;gBACXqG,MAAM;gBACNtC,SAAS;oBACPwF,eAAe;wBACb;4BACEC,MAAM;4BACNzF,SAASqE;wBACX;qBACD;oBACDuB,SAAS;oBACTC,WAAW;oBACXvB,YAAYD,YAAYxF,MAAM;oBAC9Bd,WAAWC,KAAKC,GAAG;gBACrB;gBACAmB,YAAYlB;YACd;YAEA,IAAI,CAACnC,MAAM,CAACC,GAAG,CAAC,CAAC,2DAA2D,EAAEC,UAAU;QAE1F,EAAE,OAAOG,OAAO;YACd,IAAI,CAACL,MAAM,CAACK,KAAK,CAAC,CAAC,2CAA2C,EAAEH,SAAS,CAAC,CAAC,EAAEG;QAC7E,yEAAyE;QAC3E;IACF;IAEA;;;;;GAKC,GACD,AAAQkD,wBAAwBrC,OAAY,EAAEiB,MAAc,EAAO;QACjE,IAAI;YACF,IAAI,CAACjB,SAAS;gBACZ,OAAOA;YACT;YAEA,iDAAiD;YACjD,MAAMoC,mBAAmBV,KAAK0E,KAAK,CAAC1E,KAAKC,SAAS,CAAC3B;YAEnD,qCAAqC;YACrC,IAAIoC,iBAAiBQ,cAAc,EAAE;gBACnC,IAAK,MAAM1C,WAAWkC,iBAAiBQ,cAAc,CAAE;oBACrD,MAAMiG,cAAczG,iBAAiBQ,cAAc,CAAC1C,QAAQ;oBAE5D,IAAI2I,aAAaC,OAAOC,WAAWnB,MAAMoB,OAAO,CAACH,YAAYC,KAAK,CAACC,OAAO,GAAG;wBAC3E,IAAI,CAACjK,MAAM,CAACwC,KAAK,CAAC,CAAC,WAAW,EAAEuH,YAAYC,KAAK,CAACC,OAAO,CAACnH,MAAM,CAAC,oBAAoB,EAAE1B,SAAS;wBAEhG2I,YAAYC,KAAK,CAACC,OAAO,GAAGF,YAAYC,KAAK,CAACC,OAAO,CAACE,GAAG,CAAC,CAACC;4BACzD,IAAI;gCACF,6DAA6D;gCAC7D,IAAIL,YAAYC,KAAK,CAACN,IAAI,KAAK,UAAU;oCACvC,+BAA+B;oCAC/B,OAAO,IAAI,CAACW,sBAAsB,CAACC,kBAAkB,CAACF;gCACxD,OAAO;oCACL,qCAAqC;oCACrC,OAAO,IAAI,CAACC,sBAAsB,CAACE,iBAAiB,CAACH,cAAcjI;gCACrE;4BACF,EAAE,OAAO9B,OAAO;gCACd,IAAI,CAACL,MAAM,CAACK,KAAK,CAAC,CAAC,oCAAoC,EAAEe,QAAQ,CAAC,CAAC,EAAEf;gCACrE,uEAAuE;gCACvE,OAAO+J;4BACT;wBACF;oBACF;gBACF;YACF;YAEA,IAAI,CAACpK,MAAM,CAACwC,KAAK,CAAC,CAAC,oDAAoD,EAAEL,QAAQ;YACjF,OAAOmB;QAET,EAAE,OAAOjD,OAAO;YACd,IAAI,CAACL,MAAM,CAACK,KAAK,CAAC,CAAC,+CAA+C,EAAE8B,OAAO,CAAC,CAAC,EAAE9B;YAC/E,8CAA8C;YAC9C,OAAOa;QACT;IACF;IAEA;;;;;GAKC,GACD,AAAQkC,wBAAwBvC,OAAY,EAAEX,QAAgB,EAA0B;QACtF,IAAI;YACF,gDAAgD;YAChD,MAAMiC,SAAStB,QAAQwC,UAAU,EAAE,mCAAmC;YACtE,MAAMC,mBAAmB,IAAI,CAACC,uBAAuB,CAAC1C,QAAQK,OAAO,EAAEiB;YAEvE,qEAAqE;YACrE,MAAMqB,wBAAwBF,kBAAkBkH,YAAYhH,yBAAyB;YACrF,MAAMM,iBAAiBR,kBAAkBQ,kBAAkB,CAAC;YAC5D,MAAMJ,oBAAoBJ,kBAAkBmH,kBAAkB;YAE9D,MAAMC,SAAiC;gBACrClH;gBACAC,WAAWvD;gBACX4D;gBACAJ;gBACAiH,iBAAiB1D;YACnB;YAEA,IAAI,CAACjH,MAAM,CAACwC,KAAK,CAAC,CAAC,wCAAwC,EAAEtC,SAAS,CAAC,CAAC,EAAE;gBACxEsD;gBACAC,WAAWvD;gBACXwD;gBACAC,kBAAkBC,OAAOC,IAAI,CAACC,gBAAgBhB,MAAM;gBACpD8H,aAAa;gBACbzI,QAAQA;gBACR0I,kBAAkB;YACpB;YAEA,OAAOH;QAET,EAAE,OAAOrK,OAAO;YACd,IAAI,CAACL,MAAM,CAACK,KAAK,CAAC,CAAC,kDAAkD,EAAEH,SAAS,CAAC,CAAC,EAAEG;YAEpF,wCAAwC;YACxC,OAAO;gBACLmD,uBAAuB;gBACvBC,WAAWvD;gBACX4D,gBAAgB,CAAC;gBACjBJ,mBAAmB;gBACnBiH,iBAAiB1D;YACnB;QACF;IACF;IAEA;;;;GAIC,GACD,MAAMnF,WAAWC,KAIhB,EAAiB;QAChB,IAAI;YACF,MAAM+I,YAAY,CAAC,aAAa,EAAE/I,MAAMd,SAAS,EAAE;YACnD,MAAM8J,aAAa;gBACjBhJ,OAAOA,MAAMA,KAAK;gBAClBhC,MAAM6C,KAAKC,SAAS,CAACd,MAAMhC,IAAI;gBAC/BiC,WAAWC,KAAKC,GAAG,GAAG8I,QAAQ;YAChC;YAEA,uFAAuF;YACvF,MAAM,IAAI,CAACC,YAAY,CAACC,IAAI,CAACJ,WAAWC;YAExC,6DAA6D;YAC7D,sDAAsD;YACtD,IAAIhJ,MAAMA,KAAK,KAAK,uBAAuBA,MAAMA,KAAK,KAAK,mBAAmB;gBAC5E,IAAI;oBACF,MAAMoJ,SAAS,IAAI,CAACF,YAAY,CAACG,YAAY;oBAC7C,MAAMD,OAAOE,MAAM,CAACP,WAAW,KAAK,KAAK,KAAK,eAAe;oBAC7D,IAAI,CAAC9K,MAAM,CAACwC,KAAK,CAAC,CAAC,kBAAkB,EAAEsI,WAAW;gBACpD,EAAE,OAAOzK,OAAO;oBACd,IAAI,CAACL,MAAM,CAACQ,IAAI,CAAC,CAAC,4BAA4B,EAAEsK,UAAU,CAAC,CAAC,EAAEzK,MAAMc,OAAO;gBAC7E;YACF;YAEA,8EAA8E;YAC9E,MAAMmK,mBAAmB1I,KAAKC,SAAS,CAAC;gBACtCd,OAAOA,MAAMA,KAAK;gBAClBC,WAAWC,KAAKC,GAAG;YACrB;YACA,MAAM,IAAI,CAAC+I,YAAY,CAACM,OAAO,CAACT,WAAWQ;YAE3C,IAAI,CAACtL,MAAM,CAACwC,KAAK,CAAC,CAAC,qBAAqB,EAAET,MAAMA,KAAK,CAAC,aAAa,EAAEA,MAAMd,SAAS,EAAE,EAAE;gBACtF6J;gBACA1I,WAAWL,MAAMA,KAAK;gBACtBd,WAAWc,MAAMd,SAAS;gBAC1BuK,UAAU5I,KAAKC,SAAS,CAACd,MAAMhC,IAAI,EAAE+C,MAAM;YAC7C;QAEF,EAAE,OAAOzC,OAAO;YACd,IAAI,CAACL,MAAM,CAACK,KAAK,CAAC,CAAC,4BAA4B,EAAE0B,MAAMA,KAAK,CAAC,aAAa,EAAEA,MAAMd,SAAS,CAAC,CAAC,CAAC,EAAE;gBAC9FZ,OAAOA,MAAMc,OAAO;gBACpB2J,WAAW,CAAC,aAAa,EAAE/I,MAAMd,SAAS,EAAE;gBAC5CmB,WAAWL,MAAMA,KAAK;gBACtBd,WAAWc,MAAMd,SAAS;gBAC1BwK,cAAcpL,MAAMmB,IAAI,EAAE8E,SAAS;YACrC;YACA,MAAMjG;QACR;IACF;IA/vBA+E,YACE,AAAiBtE,oBAA0C,EAC3D,AAAiByI,mBAAwC,EACzD,AAAiB0B,YAA0B,EAC3C,AAAiBZ,sBAA8C,CAC/D;aAJiBvJ,uBAAAA;aACAyI,sBAAAA;aACA0B,eAAAA;aACAZ,yBAAAA;aAVFrK,SAAS,IAAI0L,cAAM,CAACnM,qBAAqBiC,IAAI;aAG7ClB,gBAAgB,IAAIqL;aACpB/K,cAAc,IAAI+K;IAOhC;AA4vBL;;6DAtvB6BrJ;;;;;;;;;6DAuDAC"}