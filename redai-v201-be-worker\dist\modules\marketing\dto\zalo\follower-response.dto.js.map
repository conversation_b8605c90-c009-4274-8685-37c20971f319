{"version": 3, "sources": ["../../../../../src/modules/marketing/dto/zalo/follower-response.dto.ts"], "sourcesContent": ["import { ApiProperty } from '@nestjs/swagger';\r\n\r\n/**\r\n * DTO cho phản hồi thông tin người theo dõi\r\n */\r\nexport class FollowerResponseDto {\r\n  @ApiProperty({\r\n    description: 'ID của người theo dõi trong hệ thống',\r\n    example: 1,\r\n  })\r\n  id: number;\r\n\r\n  @ApiProperty({\r\n    description: 'ID của người dùng Zalo',\r\n    example: '123456789',\r\n  })\r\n  userId: string;\r\n\r\n  @ApiProperty({\r\n    description: 'Tên hiển thị của người dùng',\r\n    example: 'Nguyễn Văn A',\r\n    nullable: true,\r\n  })\r\n  displayName?: string;\r\n\r\n  @ApiProperty({\r\n    description: 'URL avatar của người dùng',\r\n    example: 'https://zalo.me/avatar/123456789.jpg',\r\n    nullable: true,\r\n  })\r\n  avatarUrl?: string;\r\n\r\n  @ApiProperty({\r\n    description: 'Số điện thoại của người dùng',\r\n    example: '0912345678',\r\n    nullable: true,\r\n  })\r\n  phone?: string;\r\n\r\n  @ApiProperty({\r\n    description: '<PERSON><PERSON><PERSON><PERSON> tính của người dùng (1: Nam, 2: N<PERSON>)',\r\n    example: 1,\r\n    nullable: true,\r\n  })\r\n  gender?: number;\r\n\r\n  @ApiProperty({\r\n    description: 'Ngày sinh của người dùng (định dạng dd/mm/yyyy)',\r\n    example: '01/01/1990',\r\n    nullable: true,\r\n  })\r\n  birthDate?: string;\r\n\r\n  @ApiProperty({\r\n    description: 'Các tag gán cho người dùng',\r\n    example: ['vip', 'new-customer'],\r\n    type: [String],\r\n  })\r\n  tags: string[];\r\n\r\n  @ApiProperty({\r\n    description: 'Trạng thái (active, unfollowed)',\r\n    example: 'active',\r\n  })\r\n  status: string;\r\n\r\n  @ApiProperty({\r\n    description: 'Thời điểm theo dõi (Unix timestamp)',\r\n    example: 1625097600000,\r\n  })\r\n  followedAt: number;\r\n}\r\n"], "names": ["FollowerResponseDto", "description", "example", "nullable", "type", "String"], "mappings": ";;;;+BAKaA;;;eAAAA;;;yBALe;;;;;;;;;;AAKrB,IAAA,AAAMA,sBAAN,MAAMA;AAkEb;;;QAhEIC,aAAa;QACbC,SAAS;;;;;;QAKTD,aAAa;QACbC,SAAS;;;;;;QAKTD,aAAa;QACbC,SAAS;QACTC,UAAU;;;;;;QAKVF,aAAa;QACbC,SAAS;QACTC,UAAU;;;;;;QAKVF,aAAa;QACbC,SAAS;QACTC,UAAU;;;;;;QAKVF,aAAa;QACbC,SAAS;QACTC,UAAU;;;;;;QAKVF,aAAa;QACbC,SAAS;QACTC,UAAU;;;;;;QAKVF,aAAa;QACbC,SAAS;YAAC;YAAO;SAAe;QAChCE,MAAM;YAACC;SAAO;;;;;;QAKdJ,aAAa;QACbC,SAAS;;;;;;QAKTD,aAAa;QACbC,SAAS"}