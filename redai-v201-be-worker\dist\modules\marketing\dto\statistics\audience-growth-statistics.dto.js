"use strict";
Object.defineProperty(exports, "__esModule", {
    value: true
});
function _export(target, all) {
    for(var name in all)Object.defineProperty(target, name, {
        enumerable: true,
        get: Object.getOwnPropertyDescriptor(all, name).get
    });
}
_export(exports, {
    get AudienceGrowthStatisticsDto () {
        return AudienceGrowthStatisticsDto;
    },
    get GrowthDataPointDto () {
        return GrowthDataPointDto;
    }
});
const _swagger = require("@nestjs/swagger");
function _ts_decorate(decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for(var i = decorators.length - 1; i >= 0; i--)if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
}
function _ts_metadata(k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
}
let GrowthDataPointDto = class GrowthDataPointDto {
};
_ts_decorate([
    (0, _swagger.ApiProperty)({
        description: 'Nhãn thời gian (ngày, tuần, tháng, năm)',
        example: '2023-01'
    }),
    _ts_metadata("design:type", String)
], GrowthDataPointDto.prototype, "label", void 0);
_ts_decorate([
    (0, _swagger.ApiProperty)({
        description: 'Thời gian (Unix timestamp)',
        example: 1672531200
    }),
    _ts_metadata("design:type", Number)
], GrowthDataPointDto.prototype, "timestamp", void 0);
_ts_decorate([
    (0, _swagger.ApiProperty)({
        description: 'Số lượng',
        example: 25
    }),
    _ts_metadata("design:type", Number)
], GrowthDataPointDto.prototype, "value", void 0);
let AudienceGrowthStatisticsDto = class AudienceGrowthStatisticsDto {
};
_ts_decorate([
    (0, _swagger.ApiProperty)({
        description: 'Dữ liệu tăng trưởng audience',
        type: [
            GrowthDataPointDto
        ]
    }),
    _ts_metadata("design:type", Array)
], AudienceGrowthStatisticsDto.prototype, "audienceGrowth", void 0);
_ts_decorate([
    (0, _swagger.ApiProperty)({
        description: 'Tổng số audience mới trong khoảng thời gian',
        example: 150
    }),
    _ts_metadata("design:type", Number)
], AudienceGrowthStatisticsDto.prototype, "totalNewAudiences", void 0);
_ts_decorate([
    (0, _swagger.ApiProperty)({
        description: 'Tỷ lệ tăng trưởng so với khoảng thời gian trước (%)',
        example: 15.5
    }),
    _ts_metadata("design:type", Number)
], AudienceGrowthStatisticsDto.prototype, "growthRate", void 0);
_ts_decorate([
    (0, _swagger.ApiProperty)({
        description: 'Thời gian cập nhật thống kê (Unix timestamp)',
        example: 1619171200
    }),
    _ts_metadata("design:type", Number)
], AudienceGrowthStatisticsDto.prototype, "updatedAt", void 0);

//# sourceMappingURL=audience-growth-statistics.dto.js.map