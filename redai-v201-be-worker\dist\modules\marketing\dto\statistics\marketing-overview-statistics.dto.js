"use strict";
Object.defineProperty(exports, "__esModule", {
    value: true
});
Object.defineProperty(exports, "MarketingOverviewStatisticsDto", {
    enumerable: true,
    get: function() {
        return MarketingOverviewStatisticsDto;
    }
});
const _swagger = require("@nestjs/swagger");
function _ts_decorate(decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for(var i = decorators.length - 1; i >= 0; i--)if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
}
function _ts_metadata(k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
}
let MarketingOverviewStatisticsDto = class MarketingOverviewStatisticsDto {
};
_ts_decorate([
    (0, _swagger.ApiProperty)({
        description: 'Tổng số audience',
        example: 150
    }),
    _ts_metadata("design:type", Number)
], MarketingOverviewStatisticsDto.prototype, "totalAudiences", void 0);
_ts_decorate([
    (0, _swagger.ApiProperty)({
        description: 'Tổng số segment',
        example: 10
    }),
    _ts_metadata("design:type", Number)
], MarketingOverviewStatisticsDto.prototype, "totalSegments", void 0);
_ts_decorate([
    (0, _swagger.ApiProperty)({
        description: 'Tổng số campaign',
        example: 5
    }),
    _ts_metadata("design:type", Number)
], MarketingOverviewStatisticsDto.prototype, "totalCampaigns", void 0);
_ts_decorate([
    (0, _swagger.ApiProperty)({
        description: 'Tổng số tag',
        example: 20
    }),
    _ts_metadata("design:type", Number)
], MarketingOverviewStatisticsDto.prototype, "totalTags", void 0);
_ts_decorate([
    (0, _swagger.ApiProperty)({
        description: 'Số lượng audience được thêm trong khoảng thời gian',
        example: 25
    }),
    _ts_metadata("design:type", Number)
], MarketingOverviewStatisticsDto.prototype, "newAudiences", void 0);
_ts_decorate([
    (0, _swagger.ApiProperty)({
        description: 'Số lượng campaign đã chạy trong khoảng thời gian',
        example: 3
    }),
    _ts_metadata("design:type", Number)
], MarketingOverviewStatisticsDto.prototype, "activeCampaigns", void 0);
_ts_decorate([
    (0, _swagger.ApiProperty)({
        description: 'Tỷ lệ mở email trung bình (%)',
        example: 35.5
    }),
    _ts_metadata("design:type", Number)
], MarketingOverviewStatisticsDto.prototype, "averageOpenRate", void 0);
_ts_decorate([
    (0, _swagger.ApiProperty)({
        description: 'Tỷ lệ click email trung bình (%)',
        example: 12.3
    }),
    _ts_metadata("design:type", Number)
], MarketingOverviewStatisticsDto.prototype, "averageClickRate", void 0);
_ts_decorate([
    (0, _swagger.ApiProperty)({
        description: 'Thời gian cập nhật thống kê (Unix timestamp)',
        example: 1619171200
    }),
    _ts_metadata("design:type", Number)
], MarketingOverviewStatisticsDto.prototype, "updatedAt", void 0);

//# sourceMappingURL=marketing-overview-statistics.dto.js.map