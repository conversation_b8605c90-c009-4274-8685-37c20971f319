"use strict";
Object.defineProperty(exports, "__esModule", {
    value: true
});
Object.defineProperty(exports, "ZaloZnsMessage", {
    enumerable: true,
    get: function() {
        return ZaloZnsMessage;
    }
});
const _typeorm = require("typeorm");
function _ts_decorate(decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for(var i = decorators.length - 1; i >= 0; i--)if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
}
function _ts_metadata(k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
}
let ZaloZnsMessage = class ZaloZnsMessage {
};
_ts_decorate([
    (0, _typeorm.PrimaryGeneratedColumn)({
        name: 'id'
    }),
    _ts_metadata("design:type", Number)
], ZaloZnsMessage.prototype, "id", void 0);
_ts_decorate([
    (0, _typeorm.Column)({
        name: 'user_id'
    }),
    _ts_metadata("design:type", Number)
], ZaloZnsMessage.prototype, "userId", void 0);
_ts_decorate([
    (0, _typeorm.Column)({
        name: 'oa_id',
        length: 50
    }),
    _ts_metadata("design:type", String)
], ZaloZnsMessage.prototype, "oaId", void 0);
_ts_decorate([
    (0, _typeorm.Column)({
        name: 'template_id',
        length: 50
    }),
    _ts_metadata("design:type", String)
], ZaloZnsMessage.prototype, "templateId", void 0);
_ts_decorate([
    (0, _typeorm.Column)({
        name: 'phone',
        length: 20
    }),
    _ts_metadata("design:type", String)
], ZaloZnsMessage.prototype, "phone", void 0);
_ts_decorate([
    (0, _typeorm.Column)({
        name: 'message_id',
        length: 50,
        nullable: true
    }),
    _ts_metadata("design:type", String)
], ZaloZnsMessage.prototype, "messageId", void 0);
_ts_decorate([
    (0, _typeorm.Column)({
        name: 'tracking_id',
        length: 50
    }),
    _ts_metadata("design:type", String)
], ZaloZnsMessage.prototype, "trackingId", void 0);
_ts_decorate([
    (0, _typeorm.Column)({
        name: 'template_data',
        type: 'jsonb'
    }),
    _ts_metadata("design:type", Object)
], ZaloZnsMessage.prototype, "templateData", void 0);
_ts_decorate([
    (0, _typeorm.Column)({
        name: 'status',
        length: 20,
        default: 'pending'
    }),
    _ts_metadata("design:type", String)
], ZaloZnsMessage.prototype, "status", void 0);
_ts_decorate([
    (0, _typeorm.Column)({
        name: 'delivered_time',
        type: 'bigint',
        nullable: true
    }),
    _ts_metadata("design:type", Number)
], ZaloZnsMessage.prototype, "deliveredTime", void 0);
_ts_decorate([
    (0, _typeorm.Column)({
        name: 'created_at',
        type: 'bigint'
    }),
    _ts_metadata("design:type", Number)
], ZaloZnsMessage.prototype, "createdAt", void 0);
_ts_decorate([
    (0, _typeorm.Column)({
        name: 'updated_at',
        type: 'bigint'
    }),
    _ts_metadata("design:type", Number)
], ZaloZnsMessage.prototype, "updatedAt", void 0);
ZaloZnsMessage = _ts_decorate([
    (0, _typeorm.Entity)('zalo_zns_messages')
], ZaloZnsMessage);

//# sourceMappingURL=zalo-zns-message.entity.js.map