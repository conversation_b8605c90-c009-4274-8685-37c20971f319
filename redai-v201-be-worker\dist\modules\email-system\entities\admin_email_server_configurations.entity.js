"use strict";
Object.defineProperty(exports, "__esModule", {
    value: true
});
Object.defineProperty(exports, "AdminEmailServerConfigurationEntity", {
    enumerable: true,
    get: function() {
        return AdminEmailServerConfigurationEntity;
    }
});
const _typeorm = require("typeorm");
function _ts_decorate(decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for(var i = decorators.length - 1; i >= 0; i--)if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
}
function _ts_metadata(k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
}
let AdminEmailServerConfigurationEntity = class AdminEmailServerConfigurationEntity {
};
_ts_decorate([
    (0, _typeorm.PrimaryGeneratedColumn)({
        type: 'integer'
    }),
    _ts_metadata("design:type", Number)
], AdminEmailServerConfigurationEntity.prototype, "id", void 0);
_ts_decorate([
    (0, _typeorm.Column)({
        name: 'server_name',
        type: 'varchar',
        length: 100,
        nullable: true
    }),
    _ts_metadata("design:type", String)
], AdminEmailServerConfigurationEntity.prototype, "serverName", void 0);
_ts_decorate([
    (0, _typeorm.Column)({
        name: 'host',
        type: 'varchar',
        length: 255,
        nullable: true
    }),
    _ts_metadata("design:type", String)
], AdminEmailServerConfigurationEntity.prototype, "host", void 0);
_ts_decorate([
    (0, _typeorm.Column)({
        name: 'port',
        type: 'integer',
        nullable: true
    }),
    _ts_metadata("design:type", Number)
], AdminEmailServerConfigurationEntity.prototype, "port", void 0);
_ts_decorate([
    (0, _typeorm.Column)({
        name: 'username',
        type: 'varchar',
        length: 255,
        nullable: true
    }),
    _ts_metadata("design:type", String)
], AdminEmailServerConfigurationEntity.prototype, "username", void 0);
_ts_decorate([
    (0, _typeorm.Column)({
        name: 'password',
        type: 'varchar',
        length: 255,
        nullable: true
    }),
    _ts_metadata("design:type", String)
], AdminEmailServerConfigurationEntity.prototype, "password", void 0);
_ts_decorate([
    (0, _typeorm.Column)({
        name: 'use_ssl',
        type: 'boolean',
        nullable: true
    }),
    _ts_metadata("design:type", Boolean)
], AdminEmailServerConfigurationEntity.prototype, "useSsl", void 0);
_ts_decorate([
    (0, _typeorm.Column)({
        name: 'additional_settings',
        type: 'json',
        nullable: true
    }),
    _ts_metadata("design:type", typeof Record === "undefined" ? Object : Record)
], AdminEmailServerConfigurationEntity.prototype, "additionalSettings", void 0);
_ts_decorate([
    (0, _typeorm.Column)({
        name: 'created_by',
        type: 'integer',
        nullable: true
    }),
    _ts_metadata("design:type", Number)
], AdminEmailServerConfigurationEntity.prototype, "createdBy", void 0);
_ts_decorate([
    (0, _typeorm.Column)({
        name: 'created_at',
        type: 'bigint',
        nullable: true
    }),
    _ts_metadata("design:type", Number)
], AdminEmailServerConfigurationEntity.prototype, "createdAt", void 0);
_ts_decorate([
    (0, _typeorm.Column)({
        name: 'updated_at',
        type: 'bigint',
        nullable: true
    }),
    _ts_metadata("design:type", Number)
], AdminEmailServerConfigurationEntity.prototype, "updatedAt", void 0);
AdminEmailServerConfigurationEntity = _ts_decorate([
    (0, _typeorm.Entity)({
        name: 'admin_email_server_configurations'
    })
], AdminEmailServerConfigurationEntity);

//# sourceMappingURL=admin_email_server_configurations.entity.js.map