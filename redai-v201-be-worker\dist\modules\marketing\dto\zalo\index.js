"use strict";
Object.defineProperty(exports, "__esModule", {
    value: true
});
_export_star(require("./connect-official-account.dto"), exports);
_export_star(require("./official-account-response.dto"), exports);
_export_star(require("./follower-response.dto"), exports);
_export_star(require("./follower-query.dto"), exports);
_export_star(require("./message-request.dto"), exports);
_export_star(require("./message-response.dto"), exports);
_export_star(require("./message-query.dto"), exports);
_export_star(require("./tag-request.dto"), exports);
_export_star(require("./zns-template-response.dto"), exports);
_export_star(require("./zns-template-query.dto"), exports);
_export_star(require("./register-zns-template.dto"), exports);
_export_star(require("./send-zns-message.dto"), exports);
_export_star(require("./zns-message-response.dto"), exports);
_export_star(require("./zns-message-query.dto"), exports);
_export_star(require("./zalo-segment.dto"), exports);
_export_star(require("./zalo-campaign.dto"), exports);
_export_star(require("./zalo-campaign-log-query.dto"), exports);
_export_star(require("./zalo-campaign-log-response.dto"), exports);
_export_star(require("./zalo-automation.dto"), exports);
_export_star(require("./zalo-automation-log-query.dto"), exports);
_export_star(require("./zalo-automation-log-response.dto"), exports);
function _export_star(from, to) {
    Object.keys(from).forEach(function(k) {
        if (k !== "default" && !Object.prototype.hasOwnProperty.call(to, k)) {
            Object.defineProperty(to, k, {
                enumerable: true,
                get: function() {
                    return from[k];
                }
            });
        }
    });
    return from;
}

//# sourceMappingURL=index.js.map