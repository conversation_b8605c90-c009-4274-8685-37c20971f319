"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ChatController = void 0;
const guards_1 = require("../../auth/guards");
const common_1 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
const decorators_1 = require("../../auth/decorators");
const response_1 = require("../../../common/response");
const api_error_response_decorator_1 = require("../../../common/decorators/api-error-response.decorator");
const chat_error_codes_1 = require("../exceptions/chat-error-codes");
const exceptions_1 = require("../../../common/exceptions");
const chat_service_1 = require("../services/chat.service");
const message_request_dto_1 = require("../dto/message-request.dto");
const message_response_dto_1 = require("../dto/message-response.dto");
let ChatController = class ChatController {
    chatService;
    constructor(chatService) {
        this.chatService = chatService;
    }
    async sendMessage(user, messageRequest, request) {
        const authHeader = request.headers.authorization;
        const jwt = authHeader?.startsWith('Bearer ') ? authHeader.substring(7) : '';
        const result = await this.chatService.processMessage(messageRequest, user.id, jwt);
        return response_1.ApiResponseDto.created(result, 'Message sent successfully and run created');
    }
    async cancelRun(runId) {
        const success = await this.chatService.cancelRun(runId);
        const result = {
            success,
            message: success ? 'Run cancelled successfully' : 'Failed to cancel run',
            runId,
        };
        return response_1.ApiResponseDto.success(result, result.message);
    }
    async getAgentConfigSummary() {
        const summary = await this.chatService.getAgentConfigSummary();
        return response_1.ApiResponseDto.success(summary, 'Agent configuration summary retrieved successfully');
    }
    async getRedisHealth() {
        const isHealthy = await this.chatService.getRedisHealth();
        const result = {
            healthy: isHealthy,
            status: isHealthy ? 'connected' : 'disconnected',
            timestamp: Date.now(),
        };
        return response_1.ApiResponseDto.success(result, 'Redis health status retrieved successfully');
    }
};
exports.ChatController = ChatController;
__decorate([
    (0, common_1.Post)('message'),
    (0, common_1.HttpCode)(common_1.HttpStatus.CREATED),
    (0, swagger_1.ApiOperation)({
        summary: 'Send message to supervisor agent',
        description: 'Send a message to the supervisor agent and create a run for processing. The system automatically selects the supervisor agent.'
    }),
    (0, swagger_1.ApiCreatedResponse)({
        description: 'Message sent successfully and run created',
        schema: response_1.ApiResponseDto.getSchema(message_response_dto_1.MessageResponseDto),
    }),
    (0, api_error_response_decorator_1.ApiErrorResponse)(chat_error_codes_1.CHAT_ERROR_CODES.THREAD_VALIDATION_FAILED, chat_error_codes_1.CHAT_ERROR_CODES.THREAD_CREATION_FAILED, exceptions_1.ErrorCode.INTERNAL_SERVER_ERROR),
    (0, swagger_1.ApiBody)({ type: message_request_dto_1.MessageRequestDto }),
    __param(0, (0, decorators_1.CurrentUser)()),
    __param(1, (0, common_1.Body)()),
    __param(2, (0, common_1.Req)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, message_request_dto_1.MessageRequestDto, Object]),
    __metadata("design:returntype", Promise)
], ChatController.prototype, "sendMessage", null);
__decorate([
    (0, common_1.Delete)('runs/:runId'),
    (0, common_1.HttpCode)(common_1.HttpStatus.OK),
    (0, swagger_1.ApiParam)({
        name: 'runId',
        description: 'ID of the run to cancel',
        example: 'run_123456-789-abc'
    }),
    (0, swagger_1.ApiOperation)({
        summary: 'Cancel a run',
        description: 'Cancel a running or pending run. This will stop processing and mark the run as failed.'
    }),
    (0, swagger_1.ApiOkResponse)({
        description: 'Run cancelled successfully',
        schema: response_1.ApiResponseDto.getSchema({
            type: 'object',
            properties: {
                success: { type: 'boolean', example: true },
                message: { type: 'string', example: 'Run cancelled successfully' },
                runId: { type: 'string', example: 'run_123456-789-abc' }
            }
        }),
    }),
    (0, api_error_response_decorator_1.ApiErrorResponse)(chat_error_codes_1.CHAT_ERROR_CODES.THREAD_NOT_FOUND, exceptions_1.ErrorCode.INTERNAL_SERVER_ERROR),
    __param(0, (0, common_1.Param)('runId')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], ChatController.prototype, "cancelRun", null);
__decorate([
    (0, common_1.Post)('debug/agents'),
    (0, swagger_1.ApiOperation)({
        summary: 'Get agent configuration summary',
        description: 'Get a summary of all available agent configurations for debugging purposes.'
    }),
    (0, swagger_1.ApiOkResponse)({
        description: 'Agent configuration summary retrieved successfully',
        schema: response_1.ApiResponseDto.getSchema(Object),
    }),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], ChatController.prototype, "getAgentConfigSummary", null);
__decorate([
    (0, common_1.Post)('debug/redis'),
    (0, swagger_1.ApiOperation)({
        summary: 'Get Redis health status',
        description: 'Check if Redis connection is healthy for pub/sub communication.'
    }),
    (0, swagger_1.ApiOkResponse)({
        description: 'Redis health status retrieved successfully',
        schema: response_1.ApiResponseDto.getSchema(Object),
    }),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], ChatController.prototype, "getRedisHealth", null);
exports.ChatController = ChatController = __decorate([
    (0, swagger_1.ApiTags)('Chat'),
    (0, swagger_1.ApiBearerAuth)('JWT-auth'),
    (0, common_1.UseGuards)(guards_1.JwtUserGuard),
    (0, swagger_1.ApiExtraModels)(message_request_dto_1.MessageRequestDto, message_response_dto_1.MessageResponseDto),
    (0, common_1.Controller)('chat'),
    __metadata("design:paramtypes", [chat_service_1.ChatService])
], ChatController);
//# sourceMappingURL=chat.controller.js.map