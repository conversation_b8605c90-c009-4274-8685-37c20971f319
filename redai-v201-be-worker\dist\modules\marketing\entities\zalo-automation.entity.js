"use strict";
Object.defineProperty(exports, "__esModule", {
    value: true
});
Object.defineProperty(exports, "ZaloAutomation", {
    enumerable: true,
    get: function() {
        return ZaloAutomation;
    }
});
const _typeorm = require("typeorm");
const _zalo = require("../dto/zalo");
function _ts_decorate(decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for(var i = decorators.length - 1; i >= 0; i--)if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
}
function _ts_metadata(k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
}
let ZaloAutomation = class ZaloAutomation {
};
_ts_decorate([
    (0, _typeorm.PrimaryGeneratedColumn)(),
    _ts_metadata("design:type", Number)
], ZaloAutomation.prototype, "id", void 0);
_ts_decorate([
    (0, _typeorm.Column)({
        name: 'user_id'
    }),
    _ts_metadata("design:type", Number)
], ZaloAutomation.prototype, "userId", void 0);
_ts_decorate([
    (0, _typeorm.Column)({
        name: 'oa_id'
    }),
    _ts_metadata("design:type", String)
], ZaloAutomation.prototype, "oaId", void 0);
_ts_decorate([
    (0, _typeorm.Column)(),
    _ts_metadata("design:type", String)
], ZaloAutomation.prototype, "name", void 0);
_ts_decorate([
    (0, _typeorm.Column)({
        nullable: true
    }),
    _ts_metadata("design:type", String)
], ZaloAutomation.prototype, "description", void 0);
_ts_decorate([
    (0, _typeorm.Column)({
        type: 'json'
    }),
    _ts_metadata("design:type", typeof _zalo.ZaloAutomationTriggerDto === "undefined" ? Object : _zalo.ZaloAutomationTriggerDto)
], ZaloAutomation.prototype, "trigger", void 0);
_ts_decorate([
    (0, _typeorm.Column)({
        type: 'json'
    }),
    _ts_metadata("design:type", Array)
], ZaloAutomation.prototype, "actions", void 0);
_ts_decorate([
    (0, _typeorm.Column)({
        type: 'enum',
        enum: _zalo.ZaloAutomationStatus,
        default: _zalo.ZaloAutomationStatus.ACTIVE
    }),
    _ts_metadata("design:type", typeof _zalo.ZaloAutomationStatus === "undefined" ? Object : _zalo.ZaloAutomationStatus)
], ZaloAutomation.prototype, "status", void 0);
_ts_decorate([
    (0, _typeorm.Column)({
        name: 'trigger_count',
        default: 0
    }),
    _ts_metadata("design:type", Number)
], ZaloAutomation.prototype, "triggerCount", void 0);
_ts_decorate([
    (0, _typeorm.Column)({
        name: 'created_at',
        type: 'bigint'
    }),
    _ts_metadata("design:type", Number)
], ZaloAutomation.prototype, "createdAt", void 0);
_ts_decorate([
    (0, _typeorm.Column)({
        name: 'updated_at',
        type: 'bigint'
    }),
    _ts_metadata("design:type", Number)
], ZaloAutomation.prototype, "updatedAt", void 0);
ZaloAutomation = _ts_decorate([
    (0, _typeorm.Entity)('zalo_automations')
], ZaloAutomation);

//# sourceMappingURL=zalo-automation.entity.js.map