{"version": 3, "sources": ["../../../../../src/modules/marketing/dto/zalo/zalo-automation-log-response.dto.ts"], "sourcesContent": ["import { ApiProperty } from '@nestjs/swagger';\r\n\r\n/**\r\n * DTO cho phản hồi thông tin log tự động hóa Zalo\r\n */\r\nexport class ZaloAutomationLogResponseDto {\r\n  @ApiProperty({\r\n    description: 'ID của log',\r\n    example: 1,\r\n  })\r\n  id: number;\r\n\r\n  @ApiProperty({\r\n    description: 'ID của tự động hóa',\r\n    example: 1,\r\n  })\r\n  automationId: number;\r\n\r\n  @ApiProperty({\r\n    description: 'ID của người dùng',\r\n    example: 123,\r\n  })\r\n  userId: number;\r\n\r\n  @ApiProperty({\r\n    description: 'ID của Official Account',\r\n    example: '*********',\r\n  })\r\n  oaId: string;\r\n\r\n  @ApiProperty({\r\n    description: 'ID của người theo dõi',\r\n    example: 1,\r\n  })\r\n  followerId: number;\r\n\r\n  @ApiProperty({\r\n    description: 'ID người dùng của người theo dõi trên Zalo',\r\n    example: '*********',\r\n  })\r\n  followerUserId: string;\r\n\r\n  @ApiProperty({\r\n    description: '<PERSON><PERSON><PERSON> sự kiện kích ho<PERSON>t',\r\n    example: 'follow',\r\n  })\r\n  triggerType: string;\r\n\r\n  @ApiProperty({\r\n    description: 'Loại hành động',\r\n    example: 'send_message',\r\n  })\r\n  actionType: string;\r\n\r\n  @ApiProperty({\r\n    description: 'Trạng thái của log',\r\n    example: 'success',\r\n    enum: ['pending', 'success', 'failed'],\r\n  })\r\n  status: string;\r\n\r\n  @ApiProperty({\r\n    description: 'Thông báo lỗi (nếu có)',\r\n    example: 'Không thể gửi tin nhắn',\r\n    nullable: true,\r\n  })\r\n  error?: string;\r\n\r\n  @ApiProperty({\r\n    description: 'Thời điểm tạo (Unix timestamp)',\r\n    example: 1625097600000,\r\n  })\r\n  createdAt: number;\r\n\r\n  @ApiProperty({\r\n    description: 'Thông tin người theo dõi',\r\n    example: {\r\n      displayName: 'Nguyễn Văn A',\r\n      avatar: 'https://example.com/avatar.jpg',\r\n    },\r\n    nullable: true,\r\n  })\r\n  follower?: {\r\n    displayName?: string;\r\n    avatar?: string;\r\n  };\r\n\r\n  @ApiProperty({\r\n    description: 'Thông tin tự động hóa',\r\n    example: {\r\n      name: 'Chào mừng người theo dõi mới',\r\n    },\r\n    nullable: true,\r\n  })\r\n  automation?: {\r\n    name?: string;\r\n  };\r\n}\r\n"], "names": ["ZaloAutomationLogResponseDto", "description", "example", "enum", "nullable", "displayName", "avatar", "name"], "mappings": ";;;;+BAKaA;;;eAAAA;;;yBALe;;;;;;;;;;AAKrB,IAAA,AAAMA,+BAAN,MAAMA;AA4Fb;;;QA1FIC,aAAa;QACbC,SAAS;;;;;;QAKTD,aAAa;QACbC,SAAS;;;;;;QAKTD,aAAa;QACbC,SAAS;;;;;;QAKTD,aAAa;QACbC,SAAS;;;;;;QAKTD,aAAa;QACbC,SAAS;;;;;;QAKTD,aAAa;QACbC,SAAS;;;;;;QAKTD,aAAa;QACbC,SAAS;;;;;;QAKTD,aAAa;QACbC,SAAS;;;;;;QAKTD,aAAa;QACbC,SAAS;QACTC,MAAM;YAAC;YAAW;YAAW;SAAS;;;;;;QAKtCF,aAAa;QACbC,SAAS;QACTE,UAAU;;;;;;QAKVH,aAAa;QACbC,SAAS;;;;;;QAKTD,aAAa;QACbC,SAAS;YACPG,aAAa;YACbC,QAAQ;QACV;QACAF,UAAU;;;;;;QAQVH,aAAa;QACbC,SAAS;YACPK,MAAM;QACR;QACAH,UAAU"}