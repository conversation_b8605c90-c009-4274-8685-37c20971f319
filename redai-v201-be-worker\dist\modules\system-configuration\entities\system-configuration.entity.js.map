{"version": 3, "sources": ["../../../../src/modules/system-configuration/entities/system-configuration.entity.ts"], "sourcesContent": ["import { Column, Entity, PrimaryColumn } from 'typeorm';\r\n\r\n/**\r\n * Entity đại diện cho bảng system_configuration trong cơ sở dữ liệu\r\n * Lưu trữ thông tin cấu hình của hệ thống\r\n */\r\n@Entity('system_configuration')\r\nexport class SystemConfiguration {\r\n  /**\r\n   * ID của cấu hình\r\n   */\r\n  @PrimaryColumn({ type: 'integer' })\r\n  id: number;\r\n\r\n  /**\r\n   * Mã ngân hàng\r\n   */\r\n  @Column({ name: 'bank_code', length: 20, nullable: true })\r\n  bankCode: string;\r\n\r\n  /**\r\n   * Số tài khoản\r\n   */\r\n  @Column({ name: 'account_number', length: 255, nullable: true })\r\n  accountNumber: string;\r\n\r\n  /**\r\n   * Tên tài khoản\r\n   */\r\n  @Column({ name: 'account_name', length: 255, nullable: true })\r\n  accountName: string;\r\n\r\n  /**\r\n   * Kích hoạt hay không\r\n   */\r\n  @Column({ name: 'active', type: 'boolean', default: true })\r\n  active: boolean;\r\n\r\n  /**\r\n   * Phần trăm phí sàn\r\n   * Ví dụ: 2.50 nghĩa là 2.50%\r\n   */\r\n  @Column({ name: 'fee_percentage', type: 'double precision', nullable: true })\r\n  feePercentage: number;\r\n\r\n  /**\r\n   * Link mẫu hóa đơn đầu vào\r\n   */\r\n  @Column({ name: 'purchase_invoice_template', length: 255, nullable: true })\r\n  purchaseInvoiceTemplate: string;\r\n\r\n  /**\r\n   * ID của tài khoản email notification\r\n   */\r\n  @Column({\r\n    name: 'email_notification_system_id',\r\n    type: 'integer',\r\n    nullable: true,\r\n  })\r\n  emailNotificationSystemId: number;\r\n\r\n  /**\r\n   * Hợp đồng đối tác kinh doanh ban đầu\r\n   */\r\n  @Column({\r\n    name: 'initial_affiliate_contract_business',\r\n    length: 255,\r\n    nullable: true,\r\n  })\r\n  initialAffiliateContractBusiness: string;\r\n\r\n  /**\r\n   * Quy tắc hợp đồng kinh doanh ban đầu\r\n   */\r\n  @Column({\r\n    name: 'initial_rule_contract_business',\r\n    length: 255,\r\n    nullable: true,\r\n  })\r\n  initialRuleContractBusiness: string;\r\n\r\n  /**\r\n   * Hợp đồng đối tác khách hàng ban đầu\r\n   */\r\n  @Column({\r\n    name: 'initial_affiliate_contract_customer',\r\n    length: 255,\r\n    nullable: true,\r\n  })\r\n  initialAffiliateContractCustomer: string;\r\n\r\n  /**\r\n   * Quy tắc hợp đồng khách hàng ban đầu\r\n   */\r\n  @Column({\r\n    name: 'initial_rule_contract_customer',\r\n    length: 255,\r\n    nullable: true,\r\n  })\r\n  initialRuleContractCustomer: string;\r\n\r\n  /**\r\n   * Thời gian tạo (Unix timestamp)\r\n   */\r\n  @Column({ name: 'created_at', type: 'bigint', nullable: true })\r\n  createdAt: number;\r\n\r\n  /**\r\n   * Thời gian cập nhật (Unix timestamp)\r\n   */\r\n  @Column({ name: 'updated_at', type: 'bigint', nullable: true })\r\n  updatedAt: number;\r\n}\r\n"], "names": ["SystemConfiguration", "type", "name", "length", "nullable", "default"], "mappings": ";;;;+BAOaA;;;eAAAA;;;yBAPiC;;;;;;;;;;AAOvC,IAAA,AAAMA,sBAAN,MAAMA;AAyGb;;;QArGmBC,MAAM;;;;;;QAMbC,MAAM;QAAaC,QAAQ;QAAIC,UAAU;;;;;;QAMzCF,MAAM;QAAkBC,QAAQ;QAAKC,UAAU;;;;;;QAM/CF,MAAM;QAAgBC,QAAQ;QAAKC,UAAU;;;;;;QAM7CF,MAAM;QAAUD,MAAM;QAAWI,SAAS;;;;;;QAO1CH,MAAM;QAAkBD,MAAM;QAAoBG,UAAU;;;;;;QAM5DF,MAAM;QAA6BC,QAAQ;QAAKC,UAAU;;;;;;QAOlEF,MAAM;QACND,MAAM;QACNG,UAAU;;;;;;QAQVF,MAAM;QACNC,QAAQ;QACRC,UAAU;;;;;;QAQVF,MAAM;QACNC,QAAQ;QACRC,UAAU;;;;;;QAQVF,MAAM;QACNC,QAAQ;QACRC,UAAU;;;;;;QAQVF,MAAM;QACNC,QAAQ;QACRC,UAAU;;;;;;QAOFF,MAAM;QAAcD,MAAM;QAAUG,UAAU;;;;;;QAM9CF,MAAM;QAAcD,MAAM;QAAUG,UAAU"}