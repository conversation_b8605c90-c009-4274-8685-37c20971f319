{"version": 3, "sources": ["../../../../../src/modules/marketing/dto/campaign/campaign-server.dto.ts"], "sourcesContent": ["import { IsIn, IsNotEmpty, IsOptional, IsString } from 'class-validator';\r\nimport { ApiProperty } from '@nestjs/swagger';\r\n\r\n/**\r\n * Enum cho các loại máy chủ\r\n */\r\nexport enum ServerType {\r\n  SMTP = 'smtp',\r\n  API = 'api',\r\n  SMS_GATEWAY = 'sms_gateway',\r\n  FIREBASE = 'firebase',\r\n}\r\n\r\n/**\r\n * DTO cho thông tin máy chủ gửi\r\n */\r\nexport class CampaignServerDto {\r\n  /**\r\n   * Loại máy chủ\r\n   * @example \"smtp\"\r\n   */\r\n  @ApiProperty({\r\n    description: 'Loại máy chủ',\r\n    enum: ServerType,\r\n    example: ServerType.SMTP,\r\n  })\r\n  @IsNotEmpty({ message: '<PERSON>ạ<PERSON> máy chủ không được để trống' })\r\n  @IsIn(Object.values(ServerType), {\r\n    message: `Loại máy chủ phải là một trong các giá trị: ${Object.values(ServerType).join(', ')}`,\r\n  })\r\n  type: ServerType;\r\n\r\n  /**\r\n   * Tên máy chủ\r\n   * @example \"Gmail SMTP\"\r\n   */\r\n  @ApiProperty({\r\n    description: 'Tên máy chủ',\r\n    example: 'Gmail SMTP',\r\n  })\r\n  @IsNotEmpty({ message: 'Tên máy chủ không được để trống' })\r\n  @IsString({ message: 'Tên máy chủ phải là chuỗi' })\r\n  name: string;\r\n\r\n  /**\r\n   * Cấu hình máy chủ\r\n   * @example { \"host\": \"smtp.gmail.com\", \"port\": 587, \"secure\": false, \"auth\": { \"user\": \"<EMAIL>\", \"pass\": \"password\" } }\r\n   */\r\n  @ApiProperty({\r\n    description: 'Cấu hình máy chủ',\r\n    example: {\r\n      host: 'smtp.gmail.com',\r\n      port: 587,\r\n      secure: false,\r\n      auth: {\r\n        user: '<EMAIL>',\r\n        pass: 'password',\r\n      },\r\n    },\r\n  })\r\n  @IsNotEmpty({ message: 'Cấu hình máy chủ không được để trống' })\r\n  config: any;\r\n\r\n  /**\r\n   * Địa chỉ email người gửi (chỉ áp dụng cho máy chủ email)\r\n   * @example \"<EMAIL>\"\r\n   */\r\n  @ApiProperty({\r\n    description: 'Địa chỉ email người gửi (chỉ áp dụng cho máy chủ email)',\r\n    example: '<EMAIL>',\r\n    required: false,\r\n  })\r\n  @IsOptional()\r\n  @IsString({ message: 'Địa chỉ email người gửi phải là chuỗi' })\r\n  fromEmail?: string;\r\n\r\n  /**\r\n   * Tên người gửi (chỉ áp dụng cho máy chủ email)\r\n   * @example \"Marketing Team\"\r\n   */\r\n  @ApiProperty({\r\n    description: 'Tên người gửi (chỉ áp dụng cho máy chủ email)',\r\n    example: 'Marketing Team',\r\n    required: false,\r\n  })\r\n  @IsOptional()\r\n  @IsString({ message: 'Tên người gửi phải là chuỗi' })\r\n  fromName?: string;\r\n}\r\n"], "names": ["CampaignServerDto", "ServerType", "description", "enum", "example", "message", "values", "Object", "join", "host", "port", "secure", "auth", "user", "pass", "required"], "mappings": ";;;;;;;;;;;QAgBaA;eAAAA;;QAVDC;eAAAA;;;gCAN2C;yBAC3B;;;;;;;;;;AAKrB,IAAA,AAAKA,oCAAAA;;;;;WAAAA;;AAUL,IAAA,AAAMD,oBAAN,MAAMA;AAwEb;;;QAlEIE,aAAa;QACbC,MAAMF;QACNG,OAAO;;;QAEKC,SAAS;;qCACVC;QACXD,SAAS,CAAC,4CAA4C,EAAEE,OAAOD,MAAM,CAACL,YAAYO,IAAI,CAAC,OAAO;;;;;;QAS9FN,aAAa;QACbE,SAAS;;;QAEGC,SAAS;;;QACXA,SAAS;;;;;;QAQnBH,aAAa;QACbE,SAAS;YACPK,MAAM;YACNC,MAAM;YACNC,QAAQ;YACRC,MAAM;gBACJC,MAAM;gBACNC,MAAM;YACR;QACF;;;QAEYT,SAAS;;;;;;QAQrBH,aAAa;QACbE,SAAS;QACTW,UAAU;;;;QAGAV,SAAS;;;;;;QAQnBH,aAAa;QACbE,SAAS;QACTW,UAAU;;;;QAGAV,SAAS"}