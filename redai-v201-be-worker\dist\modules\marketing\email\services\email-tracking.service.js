"use strict";
Object.defineProperty(exports, "__esModule", {
    value: true
});
Object.defineProperty(exports, "EmailTrackingService", {
    enumerable: true,
    get: function() {
        return EmailTrackingService;
    }
});
const _common = require("@nestjs/common");
const _typeorm = require("@nestjs/typeorm");
const _typeorm1 = require("typeorm");
const _infra = require("../../../../infra");
const _usercampaignhistoryentity = require("../../entities/user-campaign-history.entity");
function _ts_decorate(decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for(var i = decorators.length - 1; i >= 0; i--)if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
}
function _ts_metadata(k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
}
function _ts_param(paramIndex, decorator) {
    return function(target, key) {
        decorator(target, key, paramIndex);
    };
}
let EmailTrackingService = class EmailTrackingService {
    /**
   * Lưu tracking event vào Redis
   * @param trackingData Dữ liệu tracking
   */ async saveTrackingToRedis(trackingData) {
        try {
            const redisKey = `${this.REDIS_TRACKING_KEY}:${Date.now()}:${Math.random()}`;
            const data = JSON.stringify(trackingData);
            await this.redisService.getRawClient().setex(redisKey, 3600, data); // TTL 1 hour
            this.logger.debug(`Tracking data saved to Redis: ${trackingData.trackingId}`);
        } catch (error) {
            this.logger.error(`Error saving tracking to Redis: ${error.message}`, error.stack);
        }
    }
    /**
   * Tạo tracking ID duy nhất
   * @param campaignId ID campaign
   * @param audienceId ID audience
   * @returns Tracking ID
   */ generateTrackingId(campaignId, audienceId) {
        const timestamp = Date.now();
        const random = Math.random().toString(36).substring(2);
        return `${campaignId}_${audienceId}_${timestamp}_${random}`;
    }
    /**
   * Xử lý tracking event khi email được gửi
   * @param campaignId ID campaign
   * @param audienceId ID audience
   * @param email Email người nhận
   * @param trackingId ID tracking
   */ async trackEmailSent(campaignId, audienceId, email, trackingId) {
        const trackingData = {
            trackingId,
            campaignId,
            audienceId,
            email,
            eventType: 'sent',
            timestamp: Date.now()
        };
        await this.saveTrackingToRedis(trackingData);
    }
    /**
   * Xử lý tracking event khi email được mở (pixel tracking)
   * @param trackingId ID tracking
   * @param metadata Thông tin bổ sung (IP, User-Agent, etc.)
   */ async trackEmailOpened(trackingId, metadata) {
        try {
            // Lấy thông tin campaign và audience từ tracking ID
            const trackingInfo = this.parseTrackingId(trackingId);
            if (!trackingInfo) {
                this.logger.warn(`Invalid tracking ID: ${trackingId}`);
                return;
            }
            const trackingData = {
                trackingId,
                campaignId: trackingInfo.campaignId,
                audienceId: trackingInfo.audienceId,
                email: '',
                eventType: 'opened',
                timestamp: Date.now(),
                metadata
            };
            await this.saveTrackingToRedis(trackingData);
        } catch (error) {
            this.logger.error(`Error tracking email opened: ${error.message}`, error.stack);
        }
    }
    /**
   * Xử lý tracking event khi email gửi thất bại
   * @param campaignId ID campaign
   * @param audienceId ID audience
   * @param email Email người nhận
   * @param trackingId ID tracking
   * @param error Thông tin lỗi
   */ async trackEmailFailed(campaignId, audienceId, email, trackingId, error) {
        const trackingData = {
            trackingId,
            campaignId,
            audienceId,
            email,
            eventType: 'failed',
            timestamp: Date.now(),
            metadata: {
                error: error?.message || 'Unknown error'
            }
        };
        await this.saveTrackingToRedis(trackingData);
    }
    /**
   * Parse tracking ID để lấy thông tin campaign và audience
   * @param trackingId ID tracking
   * @returns Thông tin parsed hoặc null
   */ parseTrackingId(trackingId) {
        try {
            const parts = trackingId.split('_');
            if (parts.length >= 2) {
                return {
                    campaignId: parseInt(parts[0]),
                    audienceId: parseInt(parts[1])
                };
            }
            return null;
        } catch (error) {
            return null;
        }
    }
    /**
   * Khởi động batch processor để lưu dữ liệu từ Redis vào database
   */ startBatchProcessor() {
        setInterval(async ()=>{
            await this.processBatchTracking();
        }, this.BATCH_INTERVAL);
        this.logger.log('Email tracking batch processor started');
    }
    /**
   * Xử lý batch tracking data từ Redis vào database
   */ async processBatchTracking() {
        try {
            const redis = this.redisService.getRawClient();
            const pattern = `${this.REDIS_TRACKING_KEY}:*`;
            const keys = await redis.keys(pattern);
            if (keys.length === 0) {
                return;
            }
            // Lấy dữ liệu từ Redis
            const trackingEvents = [];
            const pipeline = redis.pipeline();
            for (const key of keys.slice(0, this.BATCH_SIZE)){
                pipeline.get(key);
                pipeline.del(key); // Xóa key sau khi lấy
            }
            const results = await pipeline.exec();
            // Parse dữ liệu
            if (results && results.length > 0) {
                for(let i = 0; i < results.length; i += 2){
                    const [getError, data] = results[i];
                    if (!getError && data) {
                        try {
                            const trackingEvent = JSON.parse(data);
                            trackingEvents.push(trackingEvent);
                        } catch (parseError) {
                            this.logger.error(`Error parsing tracking data: ${parseError.message}`);
                        }
                    }
                }
            }
            if (trackingEvents.length > 0) {
                await this.saveBatchToDatabase(trackingEvents);
                this.logger.debug(`Processed ${trackingEvents.length} tracking events`);
            }
        } catch (error) {
            this.logger.error(`Error processing batch tracking: ${error.message}`, error.stack);
        }
    }
    /**
   * Lưu batch tracking events vào database
   * @param events Danh sách tracking events
   */ async saveBatchToDatabase(events) {
        try {
            const historyEntries = events.map((event)=>{
                const history = new _usercampaignhistoryentity.UserCampaignHistory();
                history.campaignId = event.campaignId;
                history.audienceId = event.audienceId;
                history.status = event.eventType;
                history.sentAt = event.timestamp;
                history.createdAt = Date.now();
                return history;
            });
            await this.campaignHistoryRepository.save(historyEntries);
            this.logger.debug(`Saved ${historyEntries.length} tracking events to database`);
        } catch (error) {
            this.logger.error(`Error saving batch to database: ${error.message}`, error.stack);
        }
    }
    constructor(campaignHistoryRepository, redisService){
        this.campaignHistoryRepository = campaignHistoryRepository;
        this.redisService = redisService;
        this.logger = new _common.Logger(EmailTrackingService.name);
        this.REDIS_TRACKING_KEY = 'email_tracking';
        this.BATCH_SIZE = 100;
        this.BATCH_INTERVAL = 30000;
        // Khởi động batch processor
        this.startBatchProcessor();
    }
};
EmailTrackingService = _ts_decorate([
    (0, _common.Injectable)(),
    _ts_param(0, (0, _typeorm.InjectRepository)(_usercampaignhistoryentity.UserCampaignHistory)),
    _ts_metadata("design:type", Function),
    _ts_metadata("design:paramtypes", [
        typeof _typeorm1.Repository === "undefined" ? Object : _typeorm1.Repository,
        typeof _infra.RedisService === "undefined" ? Object : _infra.RedisService
    ])
], EmailTrackingService);

//# sourceMappingURL=email-tracking.service.js.map