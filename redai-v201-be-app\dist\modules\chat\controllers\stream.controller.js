"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var StreamController_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.StreamController = void 0;
const common_1 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
const guards_1 = require("../../auth/guards");
const response_1 = require("../../../common/response");
const api_error_response_decorator_1 = require("../../../common/decorators/api-error-response.decorator");
const chat_error_codes_1 = require("../exceptions/chat-error-codes");
const exceptions_1 = require("../../../common/exceptions");
const exceptions_2 = require("../../../common/exceptions");
const redis_service_1 = require("../../../shared/services/redis.service");
let StreamController = StreamController_1 = class StreamController {
    redisService;
    logger = new common_1.Logger(StreamController_1.name);
    constructor(redisService) {
        this.redisService = redisService;
    }
    async streamEvents(req, res, threadId, fromMessageId) {
        this.logger.log(`🔥 Starting chat SSE stream for thread ${threadId}`);
        try {
            res.set({
                'Content-Type': 'text/event-stream',
                'Cache-Control': 'no-cache, no-transform',
                Connection: 'keep-alive',
                'Access-Control-Allow-Origin': '*',
                'Access-Control-Allow-Headers': 'Cache-Control',
                'X-Accel-Buffering': 'no',
            });
            const resumeMode = fromMessageId ? 'resume' : 'unconsumed';
            res.write(`event: connected\n`);
            res.write(`data: {"threadId":"${threadId}","from":"${fromMessageId || 'unconsumed'}","timestamp":${Date.now()},"status":"connected","mode":"${resumeMode}"}\n\n`);
            this.logger.log(`✅ Chat SSE headers set for thread ${threadId} (${resumeMode}: ${fromMessageId || 'unconsumed'})`);
            const streamKey = `agent_stream:${threadId}`;
            const groupName = `sse-group:${threadId}`;
            const consumerId = `consumer-${Date.now()}-${Math.random().toString(36).substring(2, 11)}`;
            this.logger.log(`🔥 Creating consumer group ${groupName} for stream ${streamKey}`);
            try {
                const streamExists = await this.checkStreamExists(streamKey);
                if (!streamExists) {
                    await this.redisService.createConsumerGroup(streamKey, groupName, '$');
                    this.logger.log(`✅ Consumer group ${groupName} created for new stream ${streamKey}`);
                    res.write(`event: waiting\n`);
                    res.write(`data: {"threadId":"${threadId}","status":"waiting_for_stream","timestamp":${Date.now()}}\n\n`);
                }
                else {
                    await this.redisService.createConsumerGroup(streamKey, groupName, '0');
                    this.logger.log(`✅ Consumer group ${groupName} created for existing stream ${streamKey}`);
                }
                await this.consumeStreamMessages(res, streamKey, groupName, consumerId, threadId, fromMessageId);
            }
            catch (error) {
                this.logger.error(`💥 Failed to create consumer group ${groupName}:`, error);
                if (!res.destroyed) {
                    res.write(`event: error\n`);
                    res.write(`data: {"error":"Failed to initialize stream consumer","threadId":"${threadId}"}\n\n`);
                    res.end();
                }
                return;
            }
            req.on('close', () => {
                this.logger.log(`🔌 Client disconnected from chat thread ${threadId}`);
                this.cleanupConsumerGroup(streamKey, groupName);
            });
            req.on('error', (error) => {
                this.logger.error(`❌ Chat SSE error for thread ${threadId}:`, error);
                this.cleanupConsumerGroup(streamKey, groupName);
                if (!res.destroyed) {
                    res.end();
                }
            });
        }
        catch (error) {
            this.logger.error(`💥 Failed to establish chat SSE for thread ${threadId}:`, error);
            if (!res.headersSent) {
                throw error;
            }
            if (!res.destroyed) {
                res.write(`event: error\n`);
                res.write(`data: {"error":"Failed to establish SSE connection","threadId":"${threadId}"}\n\n`);
                res.end();
            }
        }
    }
    async consumeStreamMessages(res, streamKey, groupName, consumerId, threadId, _fromMessageId) {
        this.logger.log(`🔄 Starting stream consumption for thread ${threadId} (pub/sub + initial read)`);
        const client = this.redisService.getDuplicateClient();
        const subscriber = this.redisService.getDuplicateClient();
        const cleanup = async () => {
            try {
                this.logger.debug(`🧹 Cleaning up Redis connections for thread ${threadId}`);
                if (subscriber.status === 'ready' || subscriber.status === 'connecting') {
                    try {
                        await subscriber.unsubscribe(streamKey);
                        this.logger.debug(`✅ Unsubscribed from ${streamKey}`);
                    }
                    catch (unsubError) {
                        this.logger.debug(`⚠️ Unsubscribe failed (connection may already be closed): ${unsubError.message}`);
                    }
                }
                if (subscriber.status !== 'end' && subscriber.status !== 'close') {
                    try {
                        subscriber.disconnect();
                        this.logger.debug(`✅ Subscriber disconnected`);
                    }
                    catch (disconnectError) {
                        this.logger.debug(`⚠️ Subscriber disconnect failed: ${disconnectError.message}`);
                    }
                }
                if (client.status !== 'end' && client.status !== 'close') {
                    try {
                        client.disconnect();
                        this.logger.debug(`✅ Main client disconnected`);
                    }
                    catch (disconnectError) {
                        this.logger.debug(`⚠️ Main client disconnect failed: ${disconnectError.message}`);
                    }
                }
                this.logger.debug(`✅ Redis connections cleanup completed for thread ${threadId}`);
            }
            catch (error) {
                this.logger.warn(`⚠️ Error during Redis cleanup for thread ${threadId}:`, {
                    message: error.message,
                    name: error.name,
                });
            }
        };
        const parseFields = (fields) => {
            const obj = {};
            for (let i = 0; i < fields.length; i += 2) {
                const key = fields[i];
                try {
                    obj[key] = JSON.parse(fields[i + 1]);
                }
                catch {
                    obj[key] = fields[i + 1];
                }
            }
            return obj;
        };
        let hasCompletedResume = false;
        let streamHasEnded = false;
        const readMessages = async (fromStart = false) => {
            try {
                const startId = fromStart ? '0' : '>';
                const chunks = await client.xreadgroup('GROUP', groupName, consumerId, 'COUNT', 20, 'STREAMS', streamKey, startId);
                if (!chunks) {
                    if (fromStart && !hasCompletedResume) {
                        this.logger.log(`📖 Resume complete - no historical messages found for thread ${threadId}`);
                        hasCompletedResume = true;
                        res.write(`event: resume_complete\n`);
                        res.write(`data: {"threadId":"${threadId}","status":"no_historical_messages","timestamp":${Date.now()}}\n\n`);
                    }
                    else if (!fromStart) {
                        this.logger.debug(`🔄 No new messages available for thread ${threadId}`);
                    }
                    return;
                }
                const [[, messages]] = chunks;
                if (fromStart && !hasCompletedResume) {
                    this.logger.log(`📖 Resuming stream for thread ${threadId} - found ${messages.length} historical messages`);
                }
                for (const [id, fields] of messages) {
                    const payload = parseFields(fields);
                    res.write(`id: ${id}\n`);
                    res.write(`data: ${JSON.stringify(payload)}\n\n`);
                    this.logger.debug(`📤 Sent message ${id} for thread ${threadId}: ${payload.event}`);
                    if (payload.event === 'llm_stream_end') {
                        this.logger.log(`🏁 Stream ended for thread ${threadId}`);
                        streamHasEnded = true;
                        res.write(`event: end\n`);
                        res.write(`data: ${JSON.stringify(payload.data || {})}\n\n`);
                        await cleanup();
                        res.end();
                        return;
                    }
                    if (payload.event === 'stream_error') {
                        this.logger.error(`💥 Stream error for thread ${threadId}:`, payload.data);
                        res.write(`event: error\n`);
                        res.write(`data: ${JSON.stringify(payload.data)}\n\n`);
                    }
                }
                if (fromStart && messages.length > 0 && !streamHasEnded) {
                    await readMessages(true);
                }
                else if (fromStart) {
                    hasCompletedResume = true;
                    this.logger.log(`📖 Resume complete for thread ${threadId}`);
                    res.write(`event: resume_complete\n`);
                    res.write(`data: {"threadId":"${threadId}","status":"historical_messages_sent","timestamp":${Date.now()}}\n\n`);
                }
            }
            catch (error) {
                this.logger.error(`💥 Error reading messages for thread ${threadId}:`, error);
                if (!res.destroyed) {
                    res.write(`event: error\n`);
                    res.write(`data: {"error":"Stream reading error","threadId":"${threadId}"}\n\n`);
                    await cleanup();
                    res.end();
                }
            }
        };
        try {
            await subscriber.subscribe(streamKey);
            subscriber.on('message', async () => {
                if (hasCompletedResume && !streamHasEnded) {
                    await readMessages(false);
                }
            });
            this.logger.log(`📖 Starting stream resume for thread ${threadId}`);
            await readMessages(true);
            res.on('close', async () => {
                this.logger.log(`🛑 SSE connection closed for thread ${threadId}`);
                await cleanup();
                await this.cleanupConsumerGroup(streamKey, groupName);
            });
            return new Promise(() => {
                this.logger.log(`🔄 SSE connection established and listening for thread ${threadId}`);
            });
        }
        catch (error) {
            this.logger.error(`💥 Error setting up stream consumption for thread ${threadId}:`, error);
            if (!res.destroyed) {
                res.write(`event: error\n`);
                res.write(`data: {"error":"Setup error","threadId":"${threadId}"}\n\n`);
                await cleanup();
                res.end();
            }
        }
    }
    async checkStreamExists(streamKey) {
        try {
            const client = this.redisService.getDuplicateClient();
            try {
                const length = await client.xlen(streamKey);
                client.disconnect();
                return length > 0;
            }
            catch (error) {
                client.disconnect();
                return false;
            }
        }
        catch (error) {
            this.logger.warn(`Error checking stream existence for ${streamKey}:`, error.message);
            return false;
        }
    }
    async cleanupConsumerGroup(streamKey, groupName) {
        try {
            await this.redisService.deleteConsumerGroup(streamKey, groupName);
            this.logger.log(`🧹 Cleaned up consumer group ${groupName} for stream ${streamKey}`);
        }
        catch (error) {
            this.logger.error(`💥 Error cleaning up consumer group ${groupName}:`, {
                message: error.message,
                stack: error.stack,
                name: error.name,
                groupName,
                streamKey,
                error: error,
            });
        }
    }
    getHealth() {
        const result = {
            status: 'healthy',
            timestamp: Date.now(),
            service: 'chat-streaming',
        };
        return response_1.ApiResponseDto.success(result, 'Chat streaming service is healthy');
    }
};
exports.StreamController = StreamController;
__decorate([
    (0, common_1.Get)('events/:threadId'),
    (0, swagger_1.ApiOperation)({
        summary: 'Stream chat events via Server-Sent Events',
        description: 'Establishes SSE connection to stream real-time chat events for a specific thread. Resumes from last unread position.',
    }),
    (0, swagger_1.ApiParam)({
        name: 'threadId',
        description: 'Chat thread ID to stream events for',
        example: 'thread_123456',
    }),
    (0, swagger_1.ApiQuery)({
        name: 'from',
        description: 'Optional message ID to resume from. If not provided, starts from latest.',
        example: '1749266123456-0',
        required: false,
    }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'SSE stream established successfully',
        headers: {
            'Content-Type': { description: 'text/event-stream' },
            'Cache-Control': { description: 'no-cache' },
            Connection: { description: 'keep-alive' },
        },
    }),
    (0, api_error_response_decorator_1.ApiErrorResponse)(chat_error_codes_1.CHAT_ERROR_CODES.THREAD_VALIDATION_FAILED, chat_error_codes_1.CHAT_ERROR_CODES.STREAM_CONNECTION_FAILED, exceptions_1.ErrorCode.INTERNAL_SERVER_ERROR),
    __param(0, (0, common_1.Req)()),
    __param(1, (0, common_1.Res)()),
    __param(2, (0, common_1.Param)('threadId', new common_1.ParseUUIDPipe({
        version: '4',
        exceptionFactory: () => new exceptions_2.AppException(chat_error_codes_1.CHAT_ERROR_CODES.THREAD_VALIDATION_FAILED, 'threadId is required and must be a valid UUID'),
    }))),
    __param(3, (0, common_1.Query)('from')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, Object, String, String]),
    __metadata("design:returntype", Promise)
], StreamController.prototype, "streamEvents", null);
__decorate([
    (0, common_1.Get)('health'),
    (0, swagger_1.ApiOperation)({
        summary: 'Health check for chat streaming service',
        description: 'Returns the health status of the chat streaming service',
    }),
    (0, swagger_1.ApiOkResponse)({
        description: 'Service is healthy',
        schema: response_1.ApiResponseDto.getSchema({
            type: 'object',
            properties: {
                status: { type: 'string', example: 'healthy' },
                timestamp: { type: 'number', example: 1749269123456 },
                service: { type: 'string', example: 'chat-streaming' },
            },
        }),
    }),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", void 0)
], StreamController.prototype, "getHealth", null);
exports.StreamController = StreamController = StreamController_1 = __decorate([
    (0, swagger_1.ApiTags)('Chat'),
    (0, swagger_1.ApiBearerAuth)('JWT-auth'),
    (0, common_1.Controller)('chat/stream'),
    (0, common_1.UseGuards)(guards_1.JwtUserGuard),
    __metadata("design:paramtypes", [redis_service_1.RedisService])
], StreamController);
//# sourceMappingURL=stream.controller.js.map