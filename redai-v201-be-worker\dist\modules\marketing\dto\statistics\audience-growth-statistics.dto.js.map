{"version": 3, "sources": ["../../../../../src/modules/marketing/dto/statistics/audience-growth-statistics.dto.ts"], "sourcesContent": ["import { ApiProperty } from '@nestjs/swagger';\r\n\r\n/**\r\n * DTO cho một điểm dữ liệu trong biểu đồ tăng trưởng\r\n */\r\nexport class GrowthDataPointDto {\r\n  @ApiProperty({\r\n    description: '<PERSON><PERSON><PERSON><PERSON> thời gian (ngày, tuần, tháng, năm)',\r\n    example: '2023-01',\r\n  })\r\n  label: string;\r\n\r\n  @ApiProperty({\r\n    description: 'Thời gian (Unix timestamp)',\r\n    example: 1672531200,\r\n  })\r\n  timestamp: number;\r\n\r\n  @ApiProperty({\r\n    description: 'Số lượng',\r\n    example: 25,\r\n  })\r\n  value: number;\r\n}\r\n\r\n/**\r\n * DTO cho thống kê tăng trưởng audience\r\n */\r\nexport class AudienceGrowthStatisticsDto {\r\n  @ApiProperty({\r\n    description: 'Dữ liệu tăng trưởng audience',\r\n    type: [GrowthDataPointDto],\r\n  })\r\n  audienceGrowth: GrowthDataPointDto[];\r\n\r\n  @ApiProperty({\r\n    description: 'Tổng số audience mới trong khoảng thời gian',\r\n    example: 150,\r\n  })\r\n  totalNewAudiences: number;\r\n\r\n  @ApiProperty({\r\n    description: 'Tỷ lệ tăng trưởng so với khoảng thời gian trước (%)',\r\n    example: 15.5,\r\n  })\r\n  growthRate: number;\r\n\r\n  @ApiProperty({\r\n    description: 'Thời gian cập nhật thống kê (Unix timestamp)',\r\n    example: 1619171200,\r\n  })\r\n  updatedAt: number;\r\n}\r\n"], "names": ["AudienceGrowthStatisticsDto", "GrowthDataPointDto", "description", "example", "type"], "mappings": ";;;;;;;;;;;QA4BaA;eAAAA;;QAvBAC;eAAAA;;;yBALe;;;;;;;;;;AAKrB,IAAA,AAAMA,qBAAN,MAAMA;AAkBb;;;QAhBIC,aAAa;QACbC,SAAS;;;;;;QAKTD,aAAa;QACbC,SAAS;;;;;;QAKTD,aAAa;QACbC,SAAS;;;;AAQN,IAAA,AAAMH,8BAAN,MAAMA;AAwBb;;;QAtBIE,aAAa;QACbE,MAAM;YAACH;SAAmB;;;;;;QAK1BC,aAAa;QACbC,SAAS;;;;;;QAKTD,aAAa;QACbC,SAAS;;;;;;QAKTD,aAAa;QACbC,SAAS"}