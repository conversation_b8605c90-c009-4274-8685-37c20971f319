{"version": 3, "sources": ["../../../../../src/modules/marketing/dto/zalo/send-zns-message.dto.ts"], "sourcesContent": ["import { ApiProperty } from '@nestjs/swagger';\r\nimport { IsNotEmpty, IsObject, IsString } from 'class-validator';\r\n\r\n/**\r\n * DTO cho việc gửi tin nhắn ZNS\r\n */\r\nexport class SendZnsMessageDto {\r\n  @ApiProperty({\r\n    description: 'ID của template ZNS',\r\n    example: 'template123456789',\r\n  })\r\n  @IsString()\r\n  @IsNotEmpty()\r\n  templateId: string;\r\n\r\n  @ApiProperty({\r\n    description: 'Số điện thoại người nhận',\r\n    example: '0912345678',\r\n  })\r\n  @IsString()\r\n  @IsNotEmpty()\r\n  phone: string;\r\n\r\n  @ApiProperty({\r\n    description: 'Dữ liệu cho template',\r\n    example: {\r\n      orderId: '123456',\r\n      shopName: 'RedAI Shop',\r\n    },\r\n  })\r\n  @IsObject()\r\n  @IsNotEmpty()\r\n  templateData: Record<string, string>;\r\n}\r\n"], "names": ["SendZnsMessageDto", "description", "example", "orderId", "shopName"], "mappings": ";;;;+BAMaA;;;eAAAA;;;yBANe;gCACmB;;;;;;;;;;AAKxC,IAAA,AAAMA,oBAAN,MAAMA;AA2Bb;;;QAzBIC,aAAa;QACbC,SAAS;;;;;;;;QAOTD,aAAa;QACbC,SAAS;;;;;;;;QAOTD,aAAa;QACbC,SAAS;YACPC,SAAS;YACTC,UAAU;QACZ"}