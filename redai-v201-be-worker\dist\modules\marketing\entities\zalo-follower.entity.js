"use strict";
Object.defineProperty(exports, "__esModule", {
    value: true
});
Object.defineProperty(exports, "ZaloFollower", {
    enumerable: true,
    get: function() {
        return ZaloFollower;
    }
});
const _typeorm = require("typeorm");
function _ts_decorate(decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for(var i = decorators.length - 1; i >= 0; i--)if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
}
function _ts_metadata(k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
}
let ZaloFollower = class ZaloFollower {
};
_ts_decorate([
    (0, _typeorm.PrimaryGeneratedColumn)({
        name: 'id'
    }),
    _ts_metadata("design:type", Number)
], ZaloFollower.prototype, "id", void 0);
_ts_decorate([
    (0, _typeorm.Column)({
        name: 'oa_id',
        length: 50
    }),
    _ts_metadata("design:type", String)
], ZaloFollower.prototype, "oaId", void 0);
_ts_decorate([
    (0, _typeorm.Column)({
        name: 'user_id',
        length: 50
    }),
    _ts_metadata("design:type", String)
], ZaloFollower.prototype, "userId", void 0);
_ts_decorate([
    (0, _typeorm.Column)({
        name: 'display_name',
        length: 255,
        nullable: true
    }),
    _ts_metadata("design:type", String)
], ZaloFollower.prototype, "displayName", void 0);
_ts_decorate([
    (0, _typeorm.Column)({
        name: 'avatar_url',
        length: 500,
        nullable: true
    }),
    _ts_metadata("design:type", String)
], ZaloFollower.prototype, "avatarUrl", void 0);
_ts_decorate([
    (0, _typeorm.Column)({
        name: 'phone',
        length: 20,
        nullable: true
    }),
    _ts_metadata("design:type", String)
], ZaloFollower.prototype, "phone", void 0);
_ts_decorate([
    (0, _typeorm.Column)({
        name: 'gender',
        type: 'smallint',
        nullable: true
    }),
    _ts_metadata("design:type", Number)
], ZaloFollower.prototype, "gender", void 0);
_ts_decorate([
    (0, _typeorm.Column)({
        name: 'birth_date',
        length: 20,
        nullable: true
    }),
    _ts_metadata("design:type", String)
], ZaloFollower.prototype, "birthDate", void 0);
_ts_decorate([
    (0, _typeorm.Column)({
        name: 'tags',
        type: 'jsonb',
        default: '[]'
    }),
    _ts_metadata("design:type", Array)
], ZaloFollower.prototype, "tags", void 0);
_ts_decorate([
    (0, _typeorm.Column)({
        name: 'followed_at',
        type: 'bigint'
    }),
    _ts_metadata("design:type", Number)
], ZaloFollower.prototype, "followedAt", void 0);
_ts_decorate([
    (0, _typeorm.Column)({
        name: 'unfollowed_at',
        type: 'bigint',
        nullable: true
    }),
    _ts_metadata("design:type", Number)
], ZaloFollower.prototype, "unfollowedAt", void 0);
_ts_decorate([
    (0, _typeorm.Column)({
        name: 'status',
        length: 20,
        default: 'active'
    }),
    _ts_metadata("design:type", String)
], ZaloFollower.prototype, "status", void 0);
_ts_decorate([
    (0, _typeorm.Column)({
        name: 'created_at',
        type: 'bigint'
    }),
    _ts_metadata("design:type", Number)
], ZaloFollower.prototype, "createdAt", void 0);
_ts_decorate([
    (0, _typeorm.Column)({
        name: 'updated_at',
        type: 'bigint'
    }),
    _ts_metadata("design:type", Number)
], ZaloFollower.prototype, "updatedAt", void 0);
ZaloFollower = _ts_decorate([
    (0, _typeorm.Entity)('zalo_followers')
], ZaloFollower);

//# sourceMappingURL=zalo-follower.entity.js.map