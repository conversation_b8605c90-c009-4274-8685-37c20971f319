{"version": 3, "sources": ["../../../src/shared/enums/user-agent-run-status.enum.ts"], "sourcesContent": ["/**\n * Enum for user agent run status\n * \n * IMPORTANT: This enum must be kept identical in both redai-v201-be-app and redai-v201-be-worker\n * to maintain consistency across separate codebases.\n * \n * Database enum values: 'created', 'running', 'completed', 'failed'\n */\nexport enum UserAgentRunStatus {\n  /**\n   * Initial state when run is created\n   */\n  CREATED = 'created',\n\n  /**\n   * When worker starts processing the run\n   */\n  RUNNING = 'running',\n\n  /**\n   * Successful completion of the run\n   */\n  COMPLETED = 'completed',\n\n  /**\n   * Error or failure during run processing\n   */\n  FAILED = 'failed'\n}\n\n/**\n * Valid status transitions for user agent runs\n */\nexport const VALID_STATUS_TRANSITIONS: Record<UserAgentRunStatus, UserAgentRunStatus[]> = {\n  [UserAgentRunStatus.CREATED]: [UserAgentRunStatus.RUNNING, UserAgentRunStatus.FAILED],\n  [UserAgentRunStatus.RUNNING]: [UserAgentRunStatus.COMPLETED, UserAgentRunStatus.FAILED],\n  [UserAgentRunStatus.COMPLETED]: [], // Terminal state\n  [UserAgentRunStatus.FAILED]: []     // Terminal state\n};\n\n/**\n * Validates if a status transition is allowed\n * @param from Current status\n * @param to Target status\n * @returns true if transition is valid, false otherwise\n */\nexport function isValidStatusTransition(\n  from: UserAgentRunStatus,\n  to: UserAgentRunStatus\n): boolean {\n  return VALID_STATUS_TRANSITIONS[from]?.includes(to) ?? false;\n}\n\n/**\n * Gets all possible next statuses from current status\n * @param current Current status\n * @returns Array of valid next statuses\n */\nexport function getValidNextStatuses(current: UserAgentRunStatus): UserAgentRunStatus[] {\n  return VALID_STATUS_TRANSITIONS[current] || [];\n}\n"], "names": ["UserAgentRunStatus", "VALID_STATUS_TRANSITIONS", "getValidNextStatuses", "isValidStatusTransition", "from", "to", "includes", "current"], "mappings": "AAAA;;;;;;;CAOC;;;;;;;;;;;QACWA;eAAAA;;QAyBCC;eAAAA;;QAyBGC;eAAAA;;QAZAC;eAAAA;;;AAtCT,IAAA,AAAKH,4CAAAA;IACV;;GAEC;IAGD;;GAEC;IAGD;;GAEC;IAGD;;GAEC;WAlBSA;;AAyBL,MAAMC,2BAA6E;IACxF,WAA4B,EAAE;;;KAAuD;IACrF,WAA4B,EAAE;;;KAAyD;IACvF,aAA8B,EAAE,EAAE;IAClC,UAA2B,EAAE,EAAE,CAAK,iBAAiB;AACvD;AAQO,SAASE,wBACdC,IAAwB,EACxBC,EAAsB;IAEtB,OAAOJ,wBAAwB,CAACG,KAAK,EAAEE,SAASD,OAAO;AACzD;AAOO,SAASH,qBAAqBK,OAA2B;IAC9D,OAAON,wBAAwB,CAACM,QAAQ,IAAI,EAAE;AAChD"}