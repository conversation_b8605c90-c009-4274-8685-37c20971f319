{"version": 3, "sources": ["../../../../src/modules/marketing/entities/user-segment.entity.ts"], "sourcesContent": ["import { Column, Entity, PrimaryGeneratedColumn } from 'typeorm';\r\n\r\n/**\r\n * Entity đại diện cho bảng user_segments trong cơ sở dữ liệu\r\n * Phân khúc khách hàng của người dùng\r\n */\r\n@Entity('user_segments')\r\nexport class UserSegment {\r\n  /**\r\n   * ID của segment\r\n   */\r\n  @PrimaryGeneratedColumn({ name: 'id', type: 'bigint' })\r\n  id: number;\r\n\r\n  /**\r\n   * ID của người dùng\r\n   */\r\n  @Column({ name: 'user_id', comment: 'Mã khách hàng' })\r\n  userId: number;\r\n\r\n  // Không sử dụng quan hệ với bảng User, chỉ lưu ID\r\n\r\n  /**\r\n   * Tên segment\r\n   */\r\n  @Column({\r\n    name: 'name',\r\n    length: 255,\r\n    nullable: true,\r\n    comment: 'Tên tập khách hàng',\r\n  })\r\n  name: string;\r\n\r\n  /**\r\n   * <PERSON>ô tả segment\r\n   */\r\n  @Column({\r\n    name: 'description',\r\n    type: 'text',\r\n    nullable: true,\r\n    comment: '<PERSON><PERSON> tả',\r\n  })\r\n  description: string;\r\n\r\n  /**\r\n   * <PERSON>i<PERSON>u kiện lọc kh<PERSON>ch hàng\r\n   */\r\n  @Column({\r\n    name: 'criteria',\r\n    type: 'jsonb',\r\n    nullable: true,\r\n    comment: '<PERSON><PERSON><PERSON> trữ điều kiện lọc khách hàng khi tạo segment',\r\n  })\r\n  criteria: any;\r\n\r\n  /**\r\n   * Thời gian tạo (Unix timestamp)\r\n   */\r\n  @Column({\r\n    name: 'created_at',\r\n    type: 'bigint',\r\n    nullable: true,\r\n    comment: 'Thời gian tạo',\r\n  })\r\n  createdAt: number;\r\n\r\n  /**\r\n   * Thời gian cập nhật (Unix timestamp)\r\n   */\r\n  @Column({\r\n    name: 'updated_at',\r\n    type: 'bigint',\r\n    nullable: true,\r\n    comment: 'Thời gian cập nhật',\r\n  })\r\n  updatedAt: number;\r\n}\r\n"], "names": ["UserSegment", "name", "type", "comment", "length", "nullable"], "mappings": ";;;;+BAOaA;;;eAAAA;;;yBAP0C;;;;;;;;;;AAOhD,IAAA,AAAMA,cAAN,MAAMA;AAqEb;;;QAjE4BC,MAAM;QAAMC,MAAM;;;;;;QAMlCD,MAAM;QAAWE,SAAS;;;;;;QASlCF,MAAM;QACNG,QAAQ;QACRC,UAAU;QACVF,SAAS;;;;;;QAQTF,MAAM;QACNC,MAAM;QACNG,UAAU;QACVF,SAAS;;;;;;QAQTF,MAAM;QACNC,MAAM;QACNG,UAAU;QACVF,SAAS;;;;;;QAQTF,MAAM;QACNC,MAAM;QACNG,UAAU;QACVF,SAAS;;;;;;QAQTF,MAAM;QACNC,MAAM;QACNG,UAAU;QACVF,SAAS"}