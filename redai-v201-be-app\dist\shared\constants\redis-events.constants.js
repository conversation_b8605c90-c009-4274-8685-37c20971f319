"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.REDIS_CONFIG = exports.REDIS_EVENTS = void 0;
exports.REDIS_EVENTS = {
    RUN_TRIGGER: 'run.trigger',
    RUN_CANCEL: 'run.cancel',
    RUN_STATUS_UPDATE: 'run.status.update',
    RUN_COMPLETED: 'run.completed',
    RUN_ERROR: 'run.error',
};
exports.REDIS_CONFIG = {
    DEFAULT_HOST: 'localhost',
    DEFAULT_PORT: 6379,
    DEFAULT_DB: 0,
    RETRY_DELAY_ON_FAILOVER: 100,
    MAX_RETRIES_PER_REQUEST: 3,
    PUBLISH_TIMEOUT: 5000,
    HEALTH_CHECK_TIMEOUT: 3000,
};
//# sourceMappingURL=redis-events.constants.js.map