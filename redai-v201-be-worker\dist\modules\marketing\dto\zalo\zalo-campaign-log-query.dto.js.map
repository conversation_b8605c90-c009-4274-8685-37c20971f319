{"version": 3, "sources": ["../../../../../src/modules/marketing/dto/zalo/zalo-campaign-log-query.dto.ts"], "sourcesContent": ["import { ApiProperty } from '@nestjs/swagger';\r\nimport { IsEnum, IsOptional, IsString } from 'class-validator';\r\nimport { QueryDto, SortDirection } from '@common/dto';\r\n\r\n/**\r\n * Enum cho trạng thái log chiến dịch Zalo\r\n */\r\nexport enum ZaloCampaignLogStatus {\r\n  PENDING = 'pending',\r\n  SUCCESS = 'success',\r\n  FAILED = 'failed',\r\n  DELETED = 'deleted',\r\n  ALL = 'all',\r\n}\r\n\r\n/**\r\n * DTO cho việc truy vấn danh sách log chiến dịch Zalo\r\n */\r\nexport class ZaloCampaignLogQueryDto extends QueryDto {\r\n  @ApiProperty({\r\n    description: 'Lọc theo ID của chiến dịch',\r\n    example: 1,\r\n    required: false,\r\n  })\r\n  @IsOptional()\r\n  @IsString()\r\n  campaignId?: string;\r\n\r\n  @ApiProperty({\r\n    description: 'Lọc theo ID của người theo dõi',\r\n    example: '123456789',\r\n    required: false,\r\n  })\r\n  @IsOptional()\r\n  @IsString()\r\n  followerId?: string;\r\n\r\n  @ApiProperty({\r\n    description: 'Lọc theo trạng thái',\r\n    enum: ZaloCampaignLogStatus,\r\n    example: ZaloCampaignLogStatus.SUCCESS,\r\n    required: false,\r\n  })\r\n  @IsOptional()\r\n  @IsEnum(ZaloCampaignLogStatus)\r\n  status?: ZaloCampaignLogStatus;\r\n\r\n  constructor() {\r\n    super();\r\n    this.sortBy = 'createdAt';\r\n    this.sortDirection = SortDirection.DESC;\r\n  }\r\n}\r\n"], "names": ["ZaloCampaignLogQueryDto", "ZaloCampaignLogStatus", "QueryDto", "constructor", "sortBy", "sortDirection", "SortDirection", "DESC", "description", "example", "required", "enum"], "mappings": ";;;;;;;;;;;QAkBaA;eAAAA;;QAXDC;eAAAA;;;yBAPgB;gCACiB;qBACL;;;;;;;;;;AAKjC,IAAA,AAAKA,+CAAAA;;;;;;WAAAA;;AAWL,IAAA,AAAMD,0BAAN,MAAMA,gCAAgCE,aAAQ;IA6BnDC,aAAc;QACZ,KAAK;QACL,IAAI,CAACC,MAAM,GAAG;QACd,IAAI,CAACC,aAAa,GAAGC,kBAAa,CAACC,IAAI;IACzC;AACF;;;QAhCIC,aAAa;QACbC,SAAS;QACTC,UAAU;;;;;;;;QAOVF,aAAa;QACbC,SAAS;QACTC,UAAU;;;;;;;;QAOVF,aAAa;QACbG,MAAMV;QACNQ,OAAO;QACPC,UAAU"}