{"version": 3, "sources": ["../../../src/infra/redis/redis.service.ts"], "sourcesContent": ["// src/redis/redis.service.ts\r\nimport { Injectable, OnModuleDestroy, OnModuleInit, Logger } from '@nestjs/common';\r\nimport Redis, { RedisOptions } from 'ioredis';\r\nimport { env } from '../../config';\r\n\r\n// Import opossum using require for better compatibility\r\nconst CircuitBreaker = require('opossum');\r\nimport {\r\n  RedisError,\r\n  RedisErrorFactory,\r\n  RedisConnectionError,\r\n  RedisCircuitBreakerError,\r\n  RedisRetryExhaustedError,\r\n  RedisErrorContext\r\n} from './redis-errors';\r\n\r\ninterface RedisHealthMetrics {\r\n  isHealthy: boolean;\r\n  connectionStatus: string;\r\n  connectionAttempts: number;\r\n  lastPingTime: number;\r\n  lastPingDuration: number;\r\n  circuitBreakerState: string;\r\n  operationCounts: {\r\n    xadd: number;\r\n    xread: number;\r\n    publish: number;\r\n    ping: number;\r\n  };\r\n  errorCounts: {\r\n    connection: number;\r\n    timeout: number;\r\n    circuit: number;\r\n    retry: number;\r\n  };\r\n  uptime: number;\r\n}\r\n\r\n/**\r\n * Enhanced Redis Service with Connection Pooling and Reliability Features\r\n *\r\n * Features:\r\n * - Connection pooling with configurable min/max connections\r\n * - Retry logic with exponential backoff and jitter\r\n * - Connection health monitoring\r\n * - Proper error handling and logging\r\n * - Graceful shutdown with connection cleanup\r\n */\r\n@Injectable()\r\nexport class RedisService implements OnModuleInit, OnModuleDestroy {\r\n  private readonly logger = new Logger(RedisService.name);\r\n  private redis: Redis;\r\n  private circuitBreaker: any; // Circuit breaker instance\r\n  private isHealthy = true;\r\n  private connectionAttempts = 0;\r\n  private readonly maxConnectionAttempts = 5;\r\n\r\n  // Health monitoring properties\r\n  private healthMetrics: RedisHealthMetrics;\r\n  private healthCheckInterval: NodeJS.Timeout | null = null;\r\n  private serviceStartTime: number;\r\n  private lastPingTime = 0;\r\n  private lastPingDuration = 0;\r\n\r\n  async onModuleInit() {\r\n    this.serviceStartTime = Date.now();\r\n    this.initializeHealthMetrics();\r\n    await this.initializeRedisConnection();\r\n    this.initializeCircuitBreaker();\r\n    this.setupHealthMonitoring();\r\n    this.startHealthCheckInterval();\r\n  }\r\n\r\n  /**\r\n   * Initialize Redis connection with pooling and retry configuration\r\n   */\r\n  private async initializeRedisConnection(): Promise<void> {\r\n    const redisUrl = env.external.REDIS_URL;\r\n\r\n    const redisOptions: RedisOptions = {\r\n      // Connection pooling configuration\r\n      lazyConnect: true,\r\n      maxRetriesPerRequest: env.redis.REDIS_RETRY_ATTEMPTS,\r\n      connectTimeout: env.redis.REDIS_CONNECTION_TIMEOUT_MS,\r\n      commandTimeout: env.redis.REDIS_COMMAND_TIMEOUT_MS,\r\n\r\n      // Enhanced retry strategy with exponential backoff and jitter\r\n      retryStrategy: (times: number) => {\r\n        if (times > env.redis.REDIS_RETRY_ATTEMPTS) {\r\n          this.logger.error(`Redis connection failed after ${times} attempts`);\r\n          return null; // Stop retrying\r\n        }\r\n\r\n        // Exponential backoff: base * 2^(attempt-1) with jitter\r\n        const baseDelay = env.redis.REDIS_RETRY_DELAY_MS;\r\n        const exponentialDelay = baseDelay * Math.pow(2, times - 1);\r\n\r\n        // Add jitter (±20% of delay) to prevent thundering herd\r\n        const jitterRange = exponentialDelay * 0.2;\r\n        const jitter = (Math.random() * 2 - 1) * jitterRange;\r\n        const finalDelay = Math.min(exponentialDelay + jitter, 10000); // Cap at 10 seconds\r\n\r\n        this.logger.warn(`Redis connection attempt ${times} failed, retrying in ${Math.round(finalDelay)}ms (base: ${baseDelay}ms, exponential: ${exponentialDelay}ms, jitter: ${Math.round(jitter)}ms)`);\r\n        return Math.max(finalDelay, 100); // Minimum 100ms delay\r\n      },\r\n\r\n      // Connection event handlers\r\n      enableReadyCheck: true,\r\n      maxLoadingRetryTime: 5000,\r\n    };\r\n\r\n    try {\r\n      this.redis = new Redis(redisUrl, redisOptions);\r\n      await this.setupConnectionEventHandlers();\r\n      await this.redis.connect();\r\n\r\n      this.logger.log('Redis connection initialized successfully with pooling');\r\n      this.isHealthy = true;\r\n      this.connectionAttempts = 0;\r\n\r\n    } catch (error) {\r\n      this.connectionAttempts++;\r\n      this.isHealthy = false;\r\n\r\n      const redisError = RedisErrorFactory.createFromError(\r\n        error,\r\n        'Redis Connection Initialization',\r\n        { attempt: this.connectionAttempts, maxAttempts: this.maxConnectionAttempts }\r\n      );\r\n\r\n      this.logger.error('Failed to initialize Redis connection:', redisError.toLogObject());\r\n\r\n      if (this.connectionAttempts < this.maxConnectionAttempts) {\r\n        // Retry connection after delay\r\n        const retryDelay = env.redis.REDIS_RETRY_DELAY_MS * this.connectionAttempts;\r\n        this.logger.log(`Retrying Redis connection in ${retryDelay}ms...`);\r\n        setTimeout(() => this.initializeRedisConnection(), retryDelay);\r\n      } else {\r\n        this.logger.error('Max Redis connection attempts reached, service will be unhealthy');\r\n        throw new RedisConnectionError(\r\n          'Failed to establish Redis connection after maximum attempts',\r\n          {\r\n            operation: 'Redis Connection Initialization',\r\n            attempt: this.connectionAttempts,\r\n            maxAttempts: this.maxConnectionAttempts,\r\n            timestamp: Date.now()\r\n          },\r\n          error as Error\r\n        );\r\n      }\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Initialize health metrics tracking\r\n   */\r\n  private initializeHealthMetrics(): void {\r\n    this.healthMetrics = {\r\n      isHealthy: false,\r\n      connectionStatus: 'initializing',\r\n      connectionAttempts: 0,\r\n      lastPingTime: 0,\r\n      lastPingDuration: 0,\r\n      circuitBreakerState: 'closed',\r\n      operationCounts: {\r\n        xadd: 0,\r\n        xread: 0,\r\n        publish: 0,\r\n        ping: 0,\r\n      },\r\n      errorCounts: {\r\n        connection: 0,\r\n        timeout: 0,\r\n        circuit: 0,\r\n        retry: 0,\r\n      },\r\n      uptime: 0,\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Start periodic health check interval\r\n   */\r\n  private startHealthCheckInterval(): void {\r\n    // Health check every 30 seconds\r\n    this.healthCheckInterval = setInterval(async () => {\r\n      await this.performHealthCheck();\r\n    }, 30000);\r\n\r\n    this.logger.log('Health check interval started (30s)');\r\n  }\r\n\r\n  /**\r\n   * Perform comprehensive health check\r\n   */\r\n  private async performHealthCheck(): Promise<void> {\r\n    const startTime = Date.now();\r\n\r\n    try {\r\n      // Update uptime\r\n      this.healthMetrics.uptime = Date.now() - this.serviceStartTime;\r\n\r\n      // Perform ping test\r\n      await this.ping();\r\n\r\n      // Update health metrics\r\n      this.lastPingTime = Date.now();\r\n      this.lastPingDuration = this.lastPingTime - startTime;\r\n      this.healthMetrics.lastPingTime = this.lastPingTime;\r\n      this.healthMetrics.lastPingDuration = this.lastPingDuration;\r\n      this.healthMetrics.connectionStatus = this.redis?.status || 'unknown';\r\n      this.healthMetrics.circuitBreakerState = this.circuitBreaker?.opened ? 'open' :\r\n                                                this.circuitBreaker?.halfOpen ? 'half-open' : 'closed';\r\n\r\n      // Check if health status changed\r\n      const wasHealthy = this.healthMetrics.isHealthy;\r\n      this.healthMetrics.isHealthy = this.isConnectionHealthy();\r\n\r\n      if (!wasHealthy && this.healthMetrics.isHealthy) {\r\n        this.logger.log('Redis health check: Service recovered and is now healthy');\r\n      }\r\n\r\n      // Log health status periodically (every 5 minutes)\r\n      if (this.lastPingTime % (5 * 60 * 1000) < 30000) {\r\n        this.logger.log('Redis health status:', this.getHealthSummary());\r\n      }\r\n\r\n    } catch (error) {\r\n      this.lastPingDuration = Date.now() - startTime;\r\n      this.healthMetrics.lastPingDuration = this.lastPingDuration;\r\n      this.healthMetrics.isHealthy = false;\r\n      this.healthMetrics.errorCounts.connection++;\r\n\r\n      this.logger.warn('Redis health check failed:', {\r\n        error: error instanceof Error ? error.message : String(error),\r\n        duration: this.lastPingDuration,\r\n        connectionStatus: this.redis?.status,\r\n      });\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Get health summary for logging\r\n   */\r\n  private getHealthSummary(): Record<string, any> {\r\n    return {\r\n      isHealthy: this.healthMetrics.isHealthy,\r\n      connectionStatus: this.healthMetrics.connectionStatus,\r\n      uptime: Math.round(this.healthMetrics.uptime / 1000) + 's',\r\n      lastPingDuration: this.healthMetrics.lastPingDuration + 'ms',\r\n      circuitBreakerState: this.healthMetrics.circuitBreakerState,\r\n      operationCounts: this.healthMetrics.operationCounts,\r\n      errorCounts: this.healthMetrics.errorCounts,\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Initialize circuit breaker for Redis operations\r\n   */\r\n  private initializeCircuitBreaker(): void {\r\n    const circuitBreakerOptions = {\r\n      timeout: env.redis.REDIS_COMMAND_TIMEOUT_MS, // Command timeout\r\n      errorThresholdPercentage: 50, // Open circuit if 50% of requests fail\r\n      resetTimeout: 30000, // Try to close circuit after 30 seconds\r\n      rollingCountTimeout: 10000, // Rolling window of 10 seconds\r\n      rollingCountBuckets: 10, // Number of buckets in rolling window\r\n      name: 'RedisCircuitBreaker',\r\n      group: 'redis-operations',\r\n    };\r\n\r\n    // Create circuit breaker for ping operation (health check)\r\n    this.circuitBreaker = new CircuitBreaker(this.executeRedisOperation.bind(this), circuitBreakerOptions);\r\n\r\n    // Circuit breaker event handlers\r\n    this.circuitBreaker.on('open', () => {\r\n      this.logger.warn('Redis circuit breaker opened - Redis operations will be rejected');\r\n      this.isHealthy = false;\r\n    });\r\n\r\n    this.circuitBreaker.on('halfOpen', () => {\r\n      this.logger.log('Redis circuit breaker half-open - testing Redis connection');\r\n    });\r\n\r\n    this.circuitBreaker.on('close', () => {\r\n      this.logger.log('Redis circuit breaker closed - Redis operations resumed');\r\n      this.isHealthy = true;\r\n    });\r\n\r\n    this.circuitBreaker.on('fallback', (result: any) => {\r\n      this.logger.warn('Redis circuit breaker fallback triggered:', result);\r\n    });\r\n\r\n    // Enable fallback for circuit breaker\r\n    this.circuitBreaker.fallback(() => {\r\n      const context: RedisErrorContext = {\r\n        operation: 'Circuit Breaker Fallback',\r\n        timestamp: Date.now(),\r\n      };\r\n\r\n      // Track circuit breaker errors\r\n      this.healthMetrics.errorCounts.circuit++;\r\n\r\n      throw new RedisCircuitBreakerError(\r\n        'Redis service unavailable - circuit breaker is open',\r\n        context\r\n      );\r\n    });\r\n\r\n    this.logger.log('Circuit breaker initialized for Redis operations');\r\n  }\r\n\r\n  /**\r\n   * Execute Redis operation with error handling (used by circuit breaker)\r\n   */\r\n  private async executeRedisOperation(operation: () => Promise<any>): Promise<any> {\r\n    if (!this.redis) {\r\n      throw new Error('Redis client not initialized');\r\n    }\r\n    return await operation();\r\n  }\r\n\r\n  /**\r\n   * Execute operation with circuit breaker protection\r\n   */\r\n  async executeWithBreaker<T>(operation: () => Promise<T>, operationName: string = 'Unknown Operation'): Promise<T> {\r\n    const startTime = Date.now();\r\n\r\n    try {\r\n      const result = await this.circuitBreaker.fire(operation);\r\n\r\n      // Log successful operation\r\n      const duration = Date.now() - startTime;\r\n      this.logger.debug(`Circuit breaker operation succeeded: ${operationName}`, {\r\n        operation: operationName,\r\n        duration,\r\n        circuitBreakerState: this.circuitBreaker.stats,\r\n      });\r\n\r\n      return result;\r\n\r\n    } catch (error) {\r\n      const duration = Date.now() - startTime;\r\n\r\n      // Create structured error\r\n      const redisError = error instanceof RedisError\r\n        ? error\r\n        : RedisErrorFactory.createFromError(error, operationName, { duration });\r\n\r\n      // Log with structured information\r\n      this.logger.error(`Circuit breaker operation failed: ${operationName}`, {\r\n        ...redisError.toLogObject(),\r\n        circuitBreakerState: this.circuitBreaker.stats,\r\n      });\r\n\r\n      throw redisError;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Execute operation with retry logic and exponential backoff\r\n   */\r\n  async executeWithRetry<T>(\r\n    operation: () => Promise<T>,\r\n    operationName: string,\r\n    maxRetries: number = env.redis.REDIS_RETRY_ATTEMPTS\r\n  ): Promise<T> {\r\n    let lastError: Error;\r\n    const startTime = Date.now();\r\n\r\n    for (let attempt = 1; attempt <= maxRetries + 1; attempt++) {\r\n      const attemptStartTime = Date.now();\r\n\r\n      try {\r\n        const result = await operation();\r\n\r\n        if (attempt > 1) {\r\n          const totalDuration = Date.now() - startTime;\r\n          this.logger.log(`${operationName} succeeded on attempt ${attempt}`, {\r\n            operation: operationName,\r\n            attempt,\r\n            maxAttempts: maxRetries + 1,\r\n            totalDuration,\r\n            finalAttempt: true,\r\n          });\r\n        }\r\n\r\n        return result;\r\n\r\n      } catch (error) {\r\n        lastError = error as Error;\r\n        const attemptDuration = Date.now() - attemptStartTime;\r\n\r\n        // Create structured error for logging\r\n        const redisError = error instanceof RedisError\r\n          ? error\r\n          : RedisErrorFactory.createFromError(error, operationName, {\r\n              attempt,\r\n              maxAttempts: maxRetries + 1,\r\n              duration: attemptDuration\r\n            });\r\n\r\n        // Check if this is a transient error that should be retried\r\n        const isRetryable = redisError.isRetryable && attempt <= maxRetries;\r\n\r\n        if (!isRetryable) {\r\n          const totalDuration = Date.now() - startTime;\r\n\r\n          // Create final error for retry exhaustion\r\n          const finalError = attempt > maxRetries\r\n            ? new RedisRetryExhaustedError(\r\n                `${operationName} failed after ${attempt} attempts`,\r\n                {\r\n                  operation: operationName,\r\n                  attempt,\r\n                  maxAttempts: maxRetries + 1,\r\n                  duration: totalDuration,\r\n                  timestamp: Date.now(),\r\n                },\r\n                lastError\r\n              )\r\n            : redisError;\r\n\r\n          this.logger.error(`${operationName} failed permanently:`, finalError.toLogObject());\r\n          throw finalError;\r\n        }\r\n\r\n        // Calculate delay with exponential backoff and jitter\r\n        const baseDelay = env.redis.REDIS_RETRY_DELAY_MS;\r\n        const exponentialDelay = baseDelay * Math.pow(2, attempt - 1);\r\n        const jitterRange = exponentialDelay * 0.2;\r\n        const jitter = (Math.random() * 2 - 1) * jitterRange;\r\n        const delay = Math.min(exponentialDelay + jitter, 5000); // Cap at 5 seconds for operations\r\n\r\n        this.logger.warn(`${operationName} failed on attempt ${attempt}, retrying in ${Math.round(delay)}ms`, {\r\n          ...redisError.toLogObject(),\r\n          retryDelay: Math.round(delay),\r\n          nextAttempt: attempt + 1,\r\n        });\r\n\r\n        // Wait before retrying\r\n        await new Promise(resolve => setTimeout(resolve, Math.max(delay, 50)));\r\n      }\r\n    }\r\n\r\n    // This should never be reached, but TypeScript requires it\r\n    throw lastError!;\r\n  }\r\n\r\n\r\n\r\n  /**\r\n   * Setup connection event handlers for monitoring and logging\r\n   */\r\n  private async setupConnectionEventHandlers(): Promise<void> {\r\n    this.redis.on('connect', () => {\r\n      this.logger.log('Redis client connected successfully');\r\n      this.isHealthy = true;\r\n    });\r\n\r\n    this.redis.on('ready', () => {\r\n      this.logger.log('Redis client ready for commands');\r\n      this.isHealthy = true;\r\n    });\r\n\r\n    this.redis.on('error', (error) => {\r\n      this.logger.error('Redis connection error:', error);\r\n      this.isHealthy = false;\r\n    });\r\n\r\n    this.redis.on('close', () => {\r\n      this.logger.warn('Redis connection closed');\r\n      this.isHealthy = false;\r\n    });\r\n\r\n    this.redis.on('reconnecting', (delay: number) => {\r\n      this.logger.log(`Redis reconnecting in ${delay}ms`);\r\n    });\r\n\r\n    this.redis.on('end', () => {\r\n      this.logger.warn('Redis connection ended');\r\n      this.isHealthy = false;\r\n    });\r\n  }\r\n\r\n  /**\r\n   * Setup periodic health monitoring\r\n   */\r\n  private setupHealthMonitoring(): void {\r\n    // Health check every 30 seconds\r\n    setInterval(async () => {\r\n      try {\r\n        await this.ping();\r\n        if (!this.isHealthy) {\r\n          this.logger.log('Redis health check passed, marking as healthy');\r\n          this.isHealthy = true;\r\n        }\r\n      } catch (error) {\r\n        if (this.isHealthy) {\r\n          this.logger.error('Redis health check failed, marking as unhealthy:', error);\r\n          this.isHealthy = false;\r\n        }\r\n      }\r\n    }, 30000);\r\n  }\r\n\r\n  /**\r\n   * Execute Redis ping command for health checking\r\n   */\r\n  async ping(): Promise<string> {\r\n    this.healthMetrics.operationCounts.ping++;\r\n\r\n    return this.executeWithBreaker(async () => {\r\n      if (!this.redis) {\r\n        throw new Error('Redis client not initialized');\r\n      }\r\n      return this.redis.ping();\r\n    }, 'PING');\r\n  }\r\n\r\n  /**\r\n   * Get connection health status\r\n   */\r\n  isConnectionHealthy(): boolean {\r\n    return this.isHealthy && this.redis?.status === 'ready';\r\n  }\r\n\r\n  /**\r\n   * Get connection statistics for monitoring\r\n   */\r\n  getConnectionStats(): {\r\n    status: string;\r\n    isHealthy: boolean;\r\n    connectionAttempts: number;\r\n    uptime: number;\r\n  } {\r\n    return {\r\n      status: this.redis?.status || 'disconnected',\r\n      isHealthy: this.isHealthy,\r\n      connectionAttempts: this.connectionAttempts,\r\n      uptime: this.redis ? Date.now() - (this.redis as any).createBuiltinCommand?.startTime || 0 : 0,\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Get comprehensive health metrics\r\n   */\r\n  getHealthMetrics(): RedisHealthMetrics {\r\n    // Update uptime before returning\r\n    this.healthMetrics.uptime = Date.now() - this.serviceStartTime;\r\n    this.healthMetrics.isHealthy = this.isConnectionHealthy();\r\n    this.healthMetrics.connectionStatus = this.redis?.status || 'disconnected';\r\n    this.healthMetrics.connectionAttempts = this.connectionAttempts;\r\n\r\n    return { ...this.healthMetrics };\r\n  }\r\n\r\n  /**\r\n   * Get health status for health check endpoints\r\n   */\r\n  getHealthStatus(): {\r\n    status: 'healthy' | 'unhealthy';\r\n    details: Record<string, any>;\r\n  } {\r\n    const metrics = this.getHealthMetrics();\r\n\r\n    return {\r\n      status: metrics.isHealthy ? 'healthy' : 'unhealthy',\r\n      details: {\r\n        redis: {\r\n          connectionStatus: metrics.connectionStatus,\r\n          lastPingDuration: metrics.lastPingDuration,\r\n          circuitBreakerState: metrics.circuitBreakerState,\r\n          uptime: Math.round(metrics.uptime / 1000) + 's',\r\n        },\r\n        operations: metrics.operationCounts,\r\n        errors: metrics.errorCounts,\r\n        lastHealthCheck: new Date(metrics.lastPingTime).toISOString(),\r\n      },\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Execute Redis XADD command with error handling, circuit breaker, and retry logic\r\n   */\r\n  async xadd(\r\n    stream: string,\r\n    data: Record<string, string>,\r\n  ): Promise<string | null> {\r\n    const operationName = `XADD to stream ${stream}`;\r\n    this.healthMetrics.operationCounts.xadd++;\r\n\r\n    return this.executeWithBreaker(async () => {\r\n      return this.executeWithRetry(async () => {\r\n        if (!this.isConnectionHealthy()) {\r\n          const context: RedisErrorContext = {\r\n            operation: operationName,\r\n            stream,\r\n            data,\r\n            timestamp: Date.now(),\r\n          };\r\n          throw new RedisConnectionError('Redis connection is not healthy', context);\r\n        }\r\n\r\n        const result = await this.redis.xadd(stream, '*', ...Object.entries(data).flat());\r\n        this.logger.debug(`Successfully added entry to stream ${stream}: ${result}`, {\r\n          operation: operationName,\r\n          stream,\r\n          entryId: result,\r\n          dataSize: Object.keys(data).length,\r\n        });\r\n        return result;\r\n      }, operationName);\r\n    }, operationName);\r\n  }\r\n\r\n  /**\r\n   * Execute Redis XREAD command with error handling, circuit breaker, and retry logic\r\n   */\r\n  async xread(stream: string, lastId: string = '$'): Promise<any> {\r\n    const operationName = `XREAD from stream ${stream}`;\r\n    this.healthMetrics.operationCounts.xread++;\r\n\r\n    return this.executeWithBreaker(async () => {\r\n      return this.executeWithRetry(async () => {\r\n        if (!this.isConnectionHealthy()) {\r\n          const context: RedisErrorContext = {\r\n            operation: operationName,\r\n            stream,\r\n            data: { lastId },\r\n            timestamp: Date.now(),\r\n          };\r\n          throw new RedisConnectionError('Redis connection is not healthy', context);\r\n        }\r\n\r\n        const result = await this.redis.xread('BLOCK', 0, 'STREAMS', stream, lastId);\r\n        this.logger.debug(`Successfully read from stream ${stream} from ${lastId}`, {\r\n          operation: operationName,\r\n          stream,\r\n          lastId,\r\n          resultCount: result ? result.length : 0,\r\n        });\r\n        return result;\r\n      }, operationName);\r\n    }, operationName);\r\n  }\r\n\r\n  /**\r\n   * Execute Redis PUBLISH command with error handling, circuit breaker, and retry logic\r\n   */\r\n  async publish(channel: string, message: string): Promise<number> {\r\n    const operationName = `PUBLISH to channel ${channel}`;\r\n    this.healthMetrics.operationCounts.publish++;\r\n\r\n    return this.executeWithBreaker(async () => {\r\n      return this.executeWithRetry(async () => {\r\n        if (!this.isConnectionHealthy()) {\r\n          const context: RedisErrorContext = {\r\n            operation: operationName,\r\n            channel,\r\n            data: { messageLength: message.length },\r\n            timestamp: Date.now(),\r\n          };\r\n          throw new RedisConnectionError('Redis connection is not healthy', context);\r\n        }\r\n\r\n        const result = await this.redis.publish(channel, message);\r\n        this.logger.debug(`Successfully published to channel ${channel}`, {\r\n          operation: operationName,\r\n          channel,\r\n          messageLength: message.length,\r\n          subscriberCount: result,\r\n        });\r\n        return result;\r\n      }, operationName);\r\n    }, operationName);\r\n  }\r\n\r\n  /**\r\n   * Get raw Redis client for advanced operations\r\n   * @deprecated Use specific methods instead for better error handling\r\n   */\r\n  getRawClient(): Redis {\r\n    if (!this.redis) {\r\n      throw new Error('Redis client not initialized');\r\n    }\r\n\r\n    if (!this.isConnectionHealthy()) {\r\n      this.logger.warn('Returning Redis client in unhealthy state - operations may fail');\r\n    }\r\n\r\n    return this.redis;\r\n  }\r\n\r\n  /**\r\n   * Create a duplicate Redis connection for pub/sub operations\r\n   */\r\n  getDuplicateClient(): Redis {\r\n    if (!this.redis) {\r\n      throw new Error('Redis client not initialized');\r\n    }\r\n\r\n    const duplicate = this.redis.duplicate();\r\n    this.logger.debug('Created duplicate Redis client for pub/sub operations');\r\n    return duplicate;\r\n  }\r\n\r\n  /**\r\n   * Graceful shutdown with connection cleanup\r\n   */\r\n  async onModuleDestroy(): Promise<void> {\r\n    try {\r\n      // Stop health check interval\r\n      if (this.healthCheckInterval) {\r\n        clearInterval(this.healthCheckInterval);\r\n        this.healthCheckInterval = null;\r\n        this.logger.log('Health check interval stopped');\r\n      }\r\n\r\n      // Close Redis connection\r\n      if (this.redis) {\r\n        this.logger.log('Closing Redis connection...');\r\n        await this.redis.quit();\r\n        this.logger.log('Redis connection closed successfully');\r\n      }\r\n\r\n      // Log final health metrics\r\n      this.logger.log('Final Redis health metrics:', this.getHealthSummary());\r\n\r\n    } catch (error) {\r\n      this.logger.error('Error during Redis service shutdown:', error);\r\n      // Force disconnect if graceful quit fails\r\n      if (this.redis) {\r\n        this.redis.disconnect();\r\n      }\r\n    }\r\n  }\r\n\r\n}\r\n"], "names": ["RedisService", "CircuitBreaker", "require", "onModuleInit", "serviceStartTime", "Date", "now", "initializeHealthMetrics", "initializeRedisConnection", "initializeCircuitBreaker", "setupHealthMonitoring", "startHealthCheckInterval", "redisUrl", "env", "external", "REDIS_URL", "redisOptions", "lazyConnect", "maxRetriesPerRequest", "redis", "REDIS_RETRY_ATTEMPTS", "connectTimeout", "REDIS_CONNECTION_TIMEOUT_MS", "commandTimeout", "REDIS_COMMAND_TIMEOUT_MS", "retryStrategy", "times", "logger", "error", "baseDelay", "REDIS_RETRY_DELAY_MS", "exponentialDelay", "Math", "pow", "jitter<PERSON><PERSON><PERSON>", "jitter", "random", "finalDelay", "min", "warn", "round", "max", "enableReadyCheck", "maxLoadingRetryTime", "Redis", "setupConnectionEventHandlers", "connect", "log", "is<PERSON><PERSON><PERSON>", "connectionAttempts", "redisError", "RedisErrorFactory", "createFromError", "attempt", "maxAttempts", "maxConnectionAttempts", "toLogObject", "retry<PERSON><PERSON><PERSON>", "setTimeout", "RedisConnectionError", "operation", "timestamp", "healthMetrics", "connectionStatus", "lastPingTime", "lastPingDuration", "circuitBreakerState", "operationCounts", "xadd", "xread", "publish", "ping", "errorCounts", "connection", "timeout", "circuit", "retry", "uptime", "healthCheckInterval", "setInterval", "performHealthCheck", "startTime", "status", "circuitBreaker", "opened", "halfOpen", "<PERSON><PERSON><PERSON><PERSON>", "isConnectionHealthy", "getHealthSummary", "Error", "message", "String", "duration", "circuitBreakerOptions", "errorThresholdPercentage", "resetTimeout", "rollingCountTimeout", "rollingCountBuckets", "name", "group", "executeRedisOperation", "bind", "on", "result", "fallback", "context", "RedisCircuitBreakerError", "executeWithBreaker", "operationName", "fire", "debug", "stats", "RedisError", "executeWithRetry", "maxRetries", "lastError", "attemptStartTime", "totalDuration", "finalAttempt", "attemptDuration", "isRetryable", "finalError", "RedisRetryExhaustedError", "delay", "nextAttempt", "Promise", "resolve", "getConnectionStats", "createBuiltinCommand", "getHealthMetrics", "getHealthStatus", "metrics", "details", "operations", "errors", "lastHealthCheck", "toISOString", "stream", "data", "Object", "entries", "flat", "entryId", "dataSize", "keys", "length", "lastId", "resultCount", "channel", "messageLength", "subscriberCount", "getRawClient", "getDuplicateClient", "duplicate", "onModuleDestroy", "clearInterval", "quit", "disconnect", "<PERSON><PERSON>"], "mappings": "AAAA,6BAA6B;;;;;+BAiDhBA;;;eAAAA;;;wBAhDqD;gEAC9B;wBAChB;6BAWb;;;;;;;;;;;;AATP,wDAAwD;AACxD,MAAMC,iBAAiBC,QAAQ;AA2CxB,IAAA,AAAMF,eAAN,MAAMA;IAeX,MAAMG,eAAe;QACnB,IAAI,CAACC,gBAAgB,GAAGC,KAAKC,GAAG;QAChC,IAAI,CAACC,uBAAuB;QAC5B,MAAM,IAAI,CAACC,yBAAyB;QACpC,IAAI,CAACC,wBAAwB;QAC7B,IAAI,CAACC,qBAAqB;QAC1B,IAAI,CAACC,wBAAwB;IAC/B;IAEA;;GAEC,GACD,MAAcH,4BAA2C;QACvD,MAAMI,WAAWC,WAAG,CAACC,QAAQ,CAACC,SAAS;QAEvC,MAAMC,eAA6B;YACjC,mCAAmC;YACnCC,aAAa;YACbC,sBAAsBL,WAAG,CAACM,KAAK,CAACC,oBAAoB;YACpDC,gBAAgBR,WAAG,CAACM,KAAK,CAACG,2BAA2B;YACrDC,gBAAgBV,WAAG,CAACM,KAAK,CAACK,wBAAwB;YAElD,8DAA8D;YAC9DC,eAAe,CAACC;gBACd,IAAIA,QAAQb,WAAG,CAACM,KAAK,CAACC,oBAAoB,EAAE;oBAC1C,IAAI,CAACO,MAAM,CAACC,KAAK,CAAC,CAAC,8BAA8B,EAAEF,MAAM,SAAS,CAAC;oBACnE,OAAO,MAAM,gBAAgB;gBAC/B;gBAEA,wDAAwD;gBACxD,MAAMG,YAAYhB,WAAG,CAACM,KAAK,CAACW,oBAAoB;gBAChD,MAAMC,mBAAmBF,YAAYG,KAAKC,GAAG,CAAC,GAAGP,QAAQ;gBAEzD,wDAAwD;gBACxD,MAAMQ,cAAcH,mBAAmB;gBACvC,MAAMI,SAAS,AAACH,CAAAA,KAAKI,MAAM,KAAK,IAAI,CAAA,IAAKF;gBACzC,MAAMG,aAAaL,KAAKM,GAAG,CAACP,mBAAmBI,QAAQ,QAAQ,oBAAoB;gBAEnF,IAAI,CAACR,MAAM,CAACY,IAAI,CAAC,CAAC,yBAAyB,EAAEb,MAAM,qBAAqB,EAAEM,KAAKQ,KAAK,CAACH,YAAY,UAAU,EAAER,UAAU,iBAAiB,EAAEE,iBAAiB,YAAY,EAAEC,KAAKQ,KAAK,CAACL,QAAQ,GAAG,CAAC;gBAChM,OAAOH,KAAKS,GAAG,CAACJ,YAAY,MAAM,sBAAsB;YAC1D;YAEA,4BAA4B;YAC5BK,kBAAkB;YAClBC,qBAAqB;QACvB;QAEA,IAAI;YACF,IAAI,CAACxB,KAAK,GAAG,IAAIyB,gBAAK,CAAChC,UAAUI;YACjC,MAAM,IAAI,CAAC6B,4BAA4B;YACvC,MAAM,IAAI,CAAC1B,KAAK,CAAC2B,OAAO;YAExB,IAAI,CAACnB,MAAM,CAACoB,GAAG,CAAC;YAChB,IAAI,CAACC,SAAS,GAAG;YACjB,IAAI,CAACC,kBAAkB,GAAG;QAE5B,EAAE,OAAOrB,OAAO;YACd,IAAI,CAACqB,kBAAkB;YACvB,IAAI,CAACD,SAAS,GAAG;YAEjB,MAAME,aAAaC,8BAAiB,CAACC,eAAe,CAClDxB,OACA,mCACA;gBAAEyB,SAAS,IAAI,CAACJ,kBAAkB;gBAAEK,aAAa,IAAI,CAACC,qBAAqB;YAAC;YAG9E,IAAI,CAAC5B,MAAM,CAACC,KAAK,CAAC,0CAA0CsB,WAAWM,WAAW;YAElF,IAAI,IAAI,CAACP,kBAAkB,GAAG,IAAI,CAACM,qBAAqB,EAAE;gBACxD,+BAA+B;gBAC/B,MAAME,aAAa5C,WAAG,CAACM,KAAK,CAACW,oBAAoB,GAAG,IAAI,CAACmB,kBAAkB;gBAC3E,IAAI,CAACtB,MAAM,CAACoB,GAAG,CAAC,CAAC,6BAA6B,EAAEU,WAAW,KAAK,CAAC;gBACjEC,WAAW,IAAM,IAAI,CAAClD,yBAAyB,IAAIiD;YACrD,OAAO;gBACL,IAAI,CAAC9B,MAAM,CAACC,KAAK,CAAC;gBAClB,MAAM,IAAI+B,iCAAoB,CAC5B,+DACA;oBACEC,WAAW;oBACXP,SAAS,IAAI,CAACJ,kBAAkB;oBAChCK,aAAa,IAAI,CAACC,qBAAqB;oBACvCM,WAAWxD,KAAKC,GAAG;gBACrB,GACAsB;YAEJ;QACF;IACF;IAEA;;GAEC,GACD,AAAQrB,0BAAgC;QACtC,IAAI,CAACuD,aAAa,GAAG;YACnBd,WAAW;YACXe,kBAAkB;YAClBd,oBAAoB;YACpBe,cAAc;YACdC,kBAAkB;YAClBC,qBAAqB;YACrBC,iBAAiB;gBACfC,MAAM;gBACNC,OAAO;gBACPC,SAAS;gBACTC,MAAM;YACR;YACAC,aAAa;gBACXC,YAAY;gBACZC,SAAS;gBACTC,SAAS;gBACTC,OAAO;YACT;YACAC,QAAQ;QACV;IACF;IAEA;;GAEC,GACD,AAAQlE,2BAAiC;QACvC,gCAAgC;QAChC,IAAI,CAACmE,mBAAmB,GAAGC,YAAY;YACrC,MAAM,IAAI,CAACC,kBAAkB;QAC/B,GAAG;QAEH,IAAI,CAACrD,MAAM,CAACoB,GAAG,CAAC;IAClB;IAEA;;GAEC,GACD,MAAciC,qBAAoC;QAChD,MAAMC,YAAY5E,KAAKC,GAAG;QAE1B,IAAI;YACF,gBAAgB;YAChB,IAAI,CAACwD,aAAa,CAACe,MAAM,GAAGxE,KAAKC,GAAG,KAAK,IAAI,CAACF,gBAAgB;YAE9D,oBAAoB;YACpB,MAAM,IAAI,CAACmE,IAAI;YAEf,wBAAwB;YACxB,IAAI,CAACP,YAAY,GAAG3D,KAAKC,GAAG;YAC5B,IAAI,CAAC2D,gBAAgB,GAAG,IAAI,CAACD,YAAY,GAAGiB;YAC5C,IAAI,CAACnB,aAAa,CAACE,YAAY,GAAG,IAAI,CAACA,YAAY;YACnD,IAAI,CAACF,aAAa,CAACG,gBAAgB,GAAG,IAAI,CAACA,gBAAgB;YAC3D,IAAI,CAACH,aAAa,CAACC,gBAAgB,GAAG,IAAI,CAAC5C,KAAK,EAAE+D,UAAU;YAC5D,IAAI,CAACpB,aAAa,CAACI,mBAAmB,GAAG,IAAI,CAACiB,cAAc,EAAEC,SAAS,SAC7B,IAAI,CAACD,cAAc,EAAEE,WAAW,cAAc;YAExF,iCAAiC;YACjC,MAAMC,aAAa,IAAI,CAACxB,aAAa,CAACd,SAAS;YAC/C,IAAI,CAACc,aAAa,CAACd,SAAS,GAAG,IAAI,CAACuC,mBAAmB;YAEvD,IAAI,CAACD,cAAc,IAAI,CAACxB,aAAa,CAACd,SAAS,EAAE;gBAC/C,IAAI,CAACrB,MAAM,CAACoB,GAAG,CAAC;YAClB;YAEA,mDAAmD;YACnD,IAAI,IAAI,CAACiB,YAAY,GAAI,CAAA,IAAI,KAAK,IAAG,IAAK,OAAO;gBAC/C,IAAI,CAACrC,MAAM,CAACoB,GAAG,CAAC,wBAAwB,IAAI,CAACyC,gBAAgB;YAC/D;QAEF,EAAE,OAAO5D,OAAO;YACd,IAAI,CAACqC,gBAAgB,GAAG5D,KAAKC,GAAG,KAAK2E;YACrC,IAAI,CAACnB,aAAa,CAACG,gBAAgB,GAAG,IAAI,CAACA,gBAAgB;YAC3D,IAAI,CAACH,aAAa,CAACd,SAAS,GAAG;YAC/B,IAAI,CAACc,aAAa,CAACU,WAAW,CAACC,UAAU;YAEzC,IAAI,CAAC9C,MAAM,CAACY,IAAI,CAAC,8BAA8B;gBAC7CX,OAAOA,iBAAiB6D,QAAQ7D,MAAM8D,OAAO,GAAGC,OAAO/D;gBACvDgE,UAAU,IAAI,CAAC3B,gBAAgB;gBAC/BF,kBAAkB,IAAI,CAAC5C,KAAK,EAAE+D;YAChC;QACF;IACF;IAEA;;GAEC,GACD,AAAQM,mBAAwC;QAC9C,OAAO;YACLxC,WAAW,IAAI,CAACc,aAAa,CAACd,SAAS;YACvCe,kBAAkB,IAAI,CAACD,aAAa,CAACC,gBAAgB;YACrDc,QAAQ7C,KAAKQ,KAAK,CAAC,IAAI,CAACsB,aAAa,CAACe,MAAM,GAAG,QAAQ;YACvDZ,kBAAkB,IAAI,CAACH,aAAa,CAACG,gBAAgB,GAAG;YACxDC,qBAAqB,IAAI,CAACJ,aAAa,CAACI,mBAAmB;YAC3DC,iBAAiB,IAAI,CAACL,aAAa,CAACK,eAAe;YACnDK,aAAa,IAAI,CAACV,aAAa,CAACU,WAAW;QAC7C;IACF;IAEA;;GAEC,GACD,AAAQ/D,2BAAiC;QACvC,MAAMoF,wBAAwB;YAC5BnB,SAAS7D,WAAG,CAACM,KAAK,CAACK,wBAAwB;YAC3CsE,0BAA0B;YAC1BC,cAAc;YACdC,qBAAqB;YACrBC,qBAAqB;YACrBC,MAAM;YACNC,OAAO;QACT;QAEA,2DAA2D;QAC3D,IAAI,CAAChB,cAAc,GAAG,IAAIlF,eAAe,IAAI,CAACmG,qBAAqB,CAACC,IAAI,CAAC,IAAI,GAAGR;QAEhF,iCAAiC;QACjC,IAAI,CAACV,cAAc,CAACmB,EAAE,CAAC,QAAQ;YAC7B,IAAI,CAAC3E,MAAM,CAACY,IAAI,CAAC;YACjB,IAAI,CAACS,SAAS,GAAG;QACnB;QAEA,IAAI,CAACmC,cAAc,CAACmB,EAAE,CAAC,YAAY;YACjC,IAAI,CAAC3E,MAAM,CAACoB,GAAG,CAAC;QAClB;QAEA,IAAI,CAACoC,cAAc,CAACmB,EAAE,CAAC,SAAS;YAC9B,IAAI,CAAC3E,MAAM,CAACoB,GAAG,CAAC;YAChB,IAAI,CAACC,SAAS,GAAG;QACnB;QAEA,IAAI,CAACmC,cAAc,CAACmB,EAAE,CAAC,YAAY,CAACC;YAClC,IAAI,CAAC5E,MAAM,CAACY,IAAI,CAAC,6CAA6CgE;QAChE;QAEA,sCAAsC;QACtC,IAAI,CAACpB,cAAc,CAACqB,QAAQ,CAAC;YAC3B,MAAMC,UAA6B;gBACjC7C,WAAW;gBACXC,WAAWxD,KAAKC,GAAG;YACrB;YAEA,+BAA+B;YAC/B,IAAI,CAACwD,aAAa,CAACU,WAAW,CAACG,OAAO;YAEtC,MAAM,IAAI+B,qCAAwB,CAChC,uDACAD;QAEJ;QAEA,IAAI,CAAC9E,MAAM,CAACoB,GAAG,CAAC;IAClB;IAEA;;GAEC,GACD,MAAcqD,sBAAsBxC,SAA6B,EAAgB;QAC/E,IAAI,CAAC,IAAI,CAACzC,KAAK,EAAE;YACf,MAAM,IAAIsE,MAAM;QAClB;QACA,OAAO,MAAM7B;IACf;IAEA;;GAEC,GACD,MAAM+C,mBAAsB/C,SAA2B,EAAEgD,gBAAwB,mBAAmB,EAAc;QAChH,MAAM3B,YAAY5E,KAAKC,GAAG;QAE1B,IAAI;YACF,MAAMiG,SAAS,MAAM,IAAI,CAACpB,cAAc,CAAC0B,IAAI,CAACjD;YAE9C,2BAA2B;YAC3B,MAAMgC,WAAWvF,KAAKC,GAAG,KAAK2E;YAC9B,IAAI,CAACtD,MAAM,CAACmF,KAAK,CAAC,CAAC,qCAAqC,EAAEF,eAAe,EAAE;gBACzEhD,WAAWgD;gBACXhB;gBACA1B,qBAAqB,IAAI,CAACiB,cAAc,CAAC4B,KAAK;YAChD;YAEA,OAAOR;QAET,EAAE,OAAO3E,OAAO;YACd,MAAMgE,WAAWvF,KAAKC,GAAG,KAAK2E;YAE9B,0BAA0B;YAC1B,MAAM/B,aAAatB,iBAAiBoF,uBAAU,GAC1CpF,QACAuB,8BAAiB,CAACC,eAAe,CAACxB,OAAOgF,eAAe;gBAAEhB;YAAS;YAEvE,kCAAkC;YAClC,IAAI,CAACjE,MAAM,CAACC,KAAK,CAAC,CAAC,kCAAkC,EAAEgF,eAAe,EAAE;gBACtE,GAAG1D,WAAWM,WAAW,EAAE;gBAC3BU,qBAAqB,IAAI,CAACiB,cAAc,CAAC4B,KAAK;YAChD;YAEA,MAAM7D;QACR;IACF;IAEA;;GAEC,GACD,MAAM+D,iBACJrD,SAA2B,EAC3BgD,aAAqB,EACrBM,aAAqBrG,WAAG,CAACM,KAAK,CAACC,oBAAoB,EACvC;QACZ,IAAI+F;QACJ,MAAMlC,YAAY5E,KAAKC,GAAG;QAE1B,IAAK,IAAI+C,UAAU,GAAGA,WAAW6D,aAAa,GAAG7D,UAAW;YAC1D,MAAM+D,mBAAmB/G,KAAKC,GAAG;YAEjC,IAAI;gBACF,MAAMiG,SAAS,MAAM3C;gBAErB,IAAIP,UAAU,GAAG;oBACf,MAAMgE,gBAAgBhH,KAAKC,GAAG,KAAK2E;oBACnC,IAAI,CAACtD,MAAM,CAACoB,GAAG,CAAC,GAAG6D,cAAc,sBAAsB,EAAEvD,SAAS,EAAE;wBAClEO,WAAWgD;wBACXvD;wBACAC,aAAa4D,aAAa;wBAC1BG;wBACAC,cAAc;oBAChB;gBACF;gBAEA,OAAOf;YAET,EAAE,OAAO3E,OAAO;gBACduF,YAAYvF;gBACZ,MAAM2F,kBAAkBlH,KAAKC,GAAG,KAAK8G;gBAErC,sCAAsC;gBACtC,MAAMlE,aAAatB,iBAAiBoF,uBAAU,GAC1CpF,QACAuB,8BAAiB,CAACC,eAAe,CAACxB,OAAOgF,eAAe;oBACtDvD;oBACAC,aAAa4D,aAAa;oBAC1BtB,UAAU2B;gBACZ;gBAEJ,4DAA4D;gBAC5D,MAAMC,cAActE,WAAWsE,WAAW,IAAInE,WAAW6D;gBAEzD,IAAI,CAACM,aAAa;oBAChB,MAAMH,gBAAgBhH,KAAKC,GAAG,KAAK2E;oBAEnC,0CAA0C;oBAC1C,MAAMwC,aAAapE,UAAU6D,aACzB,IAAIQ,qCAAwB,CAC1B,GAAGd,cAAc,cAAc,EAAEvD,QAAQ,SAAS,CAAC,EACnD;wBACEO,WAAWgD;wBACXvD;wBACAC,aAAa4D,aAAa;wBAC1BtB,UAAUyB;wBACVxD,WAAWxD,KAAKC,GAAG;oBACrB,GACA6G,aAEFjE;oBAEJ,IAAI,CAACvB,MAAM,CAACC,KAAK,CAAC,GAAGgF,cAAc,oBAAoB,CAAC,EAAEa,WAAWjE,WAAW;oBAChF,MAAMiE;gBACR;gBAEA,sDAAsD;gBACtD,MAAM5F,YAAYhB,WAAG,CAACM,KAAK,CAACW,oBAAoB;gBAChD,MAAMC,mBAAmBF,YAAYG,KAAKC,GAAG,CAAC,GAAGoB,UAAU;gBAC3D,MAAMnB,cAAcH,mBAAmB;gBACvC,MAAMI,SAAS,AAACH,CAAAA,KAAKI,MAAM,KAAK,IAAI,CAAA,IAAKF;gBACzC,MAAMyF,QAAQ3F,KAAKM,GAAG,CAACP,mBAAmBI,QAAQ,OAAO,kCAAkC;gBAE3F,IAAI,CAACR,MAAM,CAACY,IAAI,CAAC,GAAGqE,cAAc,mBAAmB,EAAEvD,QAAQ,cAAc,EAAErB,KAAKQ,KAAK,CAACmF,OAAO,EAAE,CAAC,EAAE;oBACpG,GAAGzE,WAAWM,WAAW,EAAE;oBAC3BC,YAAYzB,KAAKQ,KAAK,CAACmF;oBACvBC,aAAavE,UAAU;gBACzB;gBAEA,uBAAuB;gBACvB,MAAM,IAAIwE,QAAQC,CAAAA,UAAWpE,WAAWoE,SAAS9F,KAAKS,GAAG,CAACkF,OAAO;YACnE;QACF;QAEA,2DAA2D;QAC3D,MAAMR;IACR;IAIA;;GAEC,GACD,MAActE,+BAA8C;QAC1D,IAAI,CAAC1B,KAAK,CAACmF,EAAE,CAAC,WAAW;YACvB,IAAI,CAAC3E,MAAM,CAACoB,GAAG,CAAC;YAChB,IAAI,CAACC,SAAS,GAAG;QACnB;QAEA,IAAI,CAAC7B,KAAK,CAACmF,EAAE,CAAC,SAAS;YACrB,IAAI,CAAC3E,MAAM,CAACoB,GAAG,CAAC;YAChB,IAAI,CAACC,SAAS,GAAG;QACnB;QAEA,IAAI,CAAC7B,KAAK,CAACmF,EAAE,CAAC,SAAS,CAAC1E;YACtB,IAAI,CAACD,MAAM,CAACC,KAAK,CAAC,2BAA2BA;YAC7C,IAAI,CAACoB,SAAS,GAAG;QACnB;QAEA,IAAI,CAAC7B,KAAK,CAACmF,EAAE,CAAC,SAAS;YACrB,IAAI,CAAC3E,MAAM,CAACY,IAAI,CAAC;YACjB,IAAI,CAACS,SAAS,GAAG;QACnB;QAEA,IAAI,CAAC7B,KAAK,CAACmF,EAAE,CAAC,gBAAgB,CAACqB;YAC7B,IAAI,CAAChG,MAAM,CAACoB,GAAG,CAAC,CAAC,sBAAsB,EAAE4E,MAAM,EAAE,CAAC;QACpD;QAEA,IAAI,CAACxG,KAAK,CAACmF,EAAE,CAAC,OAAO;YACnB,IAAI,CAAC3E,MAAM,CAACY,IAAI,CAAC;YACjB,IAAI,CAACS,SAAS,GAAG;QACnB;IACF;IAEA;;GAEC,GACD,AAAQtC,wBAA8B;QACpC,gCAAgC;QAChCqE,YAAY;YACV,IAAI;gBACF,MAAM,IAAI,CAACR,IAAI;gBACf,IAAI,CAAC,IAAI,CAACvB,SAAS,EAAE;oBACnB,IAAI,CAACrB,MAAM,CAACoB,GAAG,CAAC;oBAChB,IAAI,CAACC,SAAS,GAAG;gBACnB;YACF,EAAE,OAAOpB,OAAO;gBACd,IAAI,IAAI,CAACoB,SAAS,EAAE;oBAClB,IAAI,CAACrB,MAAM,CAACC,KAAK,CAAC,oDAAoDA;oBACtE,IAAI,CAACoB,SAAS,GAAG;gBACnB;YACF;QACF,GAAG;IACL;IAEA;;GAEC,GACD,MAAMuB,OAAwB;QAC5B,IAAI,CAACT,aAAa,CAACK,eAAe,CAACI,IAAI;QAEvC,OAAO,IAAI,CAACoC,kBAAkB,CAAC;YAC7B,IAAI,CAAC,IAAI,CAACxF,KAAK,EAAE;gBACf,MAAM,IAAIsE,MAAM;YAClB;YACA,OAAO,IAAI,CAACtE,KAAK,CAACoD,IAAI;QACxB,GAAG;IACL;IAEA;;GAEC,GACDgB,sBAA+B;QAC7B,OAAO,IAAI,CAACvC,SAAS,IAAI,IAAI,CAAC7B,KAAK,EAAE+D,WAAW;IAClD;IAEA;;GAEC,GACD6C,qBAKE;QACA,OAAO;YACL7C,QAAQ,IAAI,CAAC/D,KAAK,EAAE+D,UAAU;YAC9BlC,WAAW,IAAI,CAACA,SAAS;YACzBC,oBAAoB,IAAI,CAACA,kBAAkB;YAC3C4B,QAAQ,IAAI,CAAC1D,KAAK,GAAGd,KAAKC,GAAG,KAAK,AAAC,IAAI,CAACa,KAAK,CAAS6G,oBAAoB,EAAE/C,aAAa,IAAI;QAC/F;IACF;IAEA;;GAEC,GACDgD,mBAAuC;QACrC,iCAAiC;QACjC,IAAI,CAACnE,aAAa,CAACe,MAAM,GAAGxE,KAAKC,GAAG,KAAK,IAAI,CAACF,gBAAgB;QAC9D,IAAI,CAAC0D,aAAa,CAACd,SAAS,GAAG,IAAI,CAACuC,mBAAmB;QACvD,IAAI,CAACzB,aAAa,CAACC,gBAAgB,GAAG,IAAI,CAAC5C,KAAK,EAAE+D,UAAU;QAC5D,IAAI,CAACpB,aAAa,CAACb,kBAAkB,GAAG,IAAI,CAACA,kBAAkB;QAE/D,OAAO;YAAE,GAAG,IAAI,CAACa,aAAa;QAAC;IACjC;IAEA;;GAEC,GACDoE,kBAGE;QACA,MAAMC,UAAU,IAAI,CAACF,gBAAgB;QAErC,OAAO;YACL/C,QAAQiD,QAAQnF,SAAS,GAAG,YAAY;YACxCoF,SAAS;gBACPjH,OAAO;oBACL4C,kBAAkBoE,QAAQpE,gBAAgB;oBAC1CE,kBAAkBkE,QAAQlE,gBAAgB;oBAC1CC,qBAAqBiE,QAAQjE,mBAAmB;oBAChDW,QAAQ7C,KAAKQ,KAAK,CAAC2F,QAAQtD,MAAM,GAAG,QAAQ;gBAC9C;gBACAwD,YAAYF,QAAQhE,eAAe;gBACnCmE,QAAQH,QAAQ3D,WAAW;gBAC3B+D,iBAAiB,IAAIlI,KAAK8H,QAAQnE,YAAY,EAAEwE,WAAW;YAC7D;QACF;IACF;IAEA;;GAEC,GACD,MAAMpE,KACJqE,MAAc,EACdC,IAA4B,EACJ;QACxB,MAAM9B,gBAAgB,CAAC,eAAe,EAAE6B,QAAQ;QAChD,IAAI,CAAC3E,aAAa,CAACK,eAAe,CAACC,IAAI;QAEvC,OAAO,IAAI,CAACuC,kBAAkB,CAAC;YAC7B,OAAO,IAAI,CAACM,gBAAgB,CAAC;gBAC3B,IAAI,CAAC,IAAI,CAAC1B,mBAAmB,IAAI;oBAC/B,MAAMkB,UAA6B;wBACjC7C,WAAWgD;wBACX6B;wBACAC;wBACA7E,WAAWxD,KAAKC,GAAG;oBACrB;oBACA,MAAM,IAAIqD,iCAAoB,CAAC,mCAAmC8C;gBACpE;gBAEA,MAAMF,SAAS,MAAM,IAAI,CAACpF,KAAK,CAACiD,IAAI,CAACqE,QAAQ,QAAQE,OAAOC,OAAO,CAACF,MAAMG,IAAI;gBAC9E,IAAI,CAAClH,MAAM,CAACmF,KAAK,CAAC,CAAC,mCAAmC,EAAE2B,OAAO,EAAE,EAAElC,QAAQ,EAAE;oBAC3E3C,WAAWgD;oBACX6B;oBACAK,SAASvC;oBACTwC,UAAUJ,OAAOK,IAAI,CAACN,MAAMO,MAAM;gBACpC;gBACA,OAAO1C;YACT,GAAGK;QACL,GAAGA;IACL;IAEA;;GAEC,GACD,MAAMvC,MAAMoE,MAAc,EAAES,SAAiB,GAAG,EAAgB;QAC9D,MAAMtC,gBAAgB,CAAC,kBAAkB,EAAE6B,QAAQ;QACnD,IAAI,CAAC3E,aAAa,CAACK,eAAe,CAACE,KAAK;QAExC,OAAO,IAAI,CAACsC,kBAAkB,CAAC;YAC7B,OAAO,IAAI,CAACM,gBAAgB,CAAC;gBAC3B,IAAI,CAAC,IAAI,CAAC1B,mBAAmB,IAAI;oBAC/B,MAAMkB,UAA6B;wBACjC7C,WAAWgD;wBACX6B;wBACAC,MAAM;4BAAEQ;wBAAO;wBACfrF,WAAWxD,KAAKC,GAAG;oBACrB;oBACA,MAAM,IAAIqD,iCAAoB,CAAC,mCAAmC8C;gBACpE;gBAEA,MAAMF,SAAS,MAAM,IAAI,CAACpF,KAAK,CAACkD,KAAK,CAAC,SAAS,GAAG,WAAWoE,QAAQS;gBACrE,IAAI,CAACvH,MAAM,CAACmF,KAAK,CAAC,CAAC,8BAA8B,EAAE2B,OAAO,MAAM,EAAES,QAAQ,EAAE;oBAC1EtF,WAAWgD;oBACX6B;oBACAS;oBACAC,aAAa5C,SAASA,OAAO0C,MAAM,GAAG;gBACxC;gBACA,OAAO1C;YACT,GAAGK;QACL,GAAGA;IACL;IAEA;;GAEC,GACD,MAAMtC,QAAQ8E,OAAe,EAAE1D,OAAe,EAAmB;QAC/D,MAAMkB,gBAAgB,CAAC,mBAAmB,EAAEwC,SAAS;QACrD,IAAI,CAACtF,aAAa,CAACK,eAAe,CAACG,OAAO;QAE1C,OAAO,IAAI,CAACqC,kBAAkB,CAAC;YAC7B,OAAO,IAAI,CAACM,gBAAgB,CAAC;gBAC3B,IAAI,CAAC,IAAI,CAAC1B,mBAAmB,IAAI;oBAC/B,MAAMkB,UAA6B;wBACjC7C,WAAWgD;wBACXwC;wBACAV,MAAM;4BAAEW,eAAe3D,QAAQuD,MAAM;wBAAC;wBACtCpF,WAAWxD,KAAKC,GAAG;oBACrB;oBACA,MAAM,IAAIqD,iCAAoB,CAAC,mCAAmC8C;gBACpE;gBAEA,MAAMF,SAAS,MAAM,IAAI,CAACpF,KAAK,CAACmD,OAAO,CAAC8E,SAAS1D;gBACjD,IAAI,CAAC/D,MAAM,CAACmF,KAAK,CAAC,CAAC,kCAAkC,EAAEsC,SAAS,EAAE;oBAChExF,WAAWgD;oBACXwC;oBACAC,eAAe3D,QAAQuD,MAAM;oBAC7BK,iBAAiB/C;gBACnB;gBACA,OAAOA;YACT,GAAGK;QACL,GAAGA;IACL;IAEA;;;GAGC,GACD2C,eAAsB;QACpB,IAAI,CAAC,IAAI,CAACpI,KAAK,EAAE;YACf,MAAM,IAAIsE,MAAM;QAClB;QAEA,IAAI,CAAC,IAAI,CAACF,mBAAmB,IAAI;YAC/B,IAAI,CAAC5D,MAAM,CAACY,IAAI,CAAC;QACnB;QAEA,OAAO,IAAI,CAACpB,KAAK;IACnB;IAEA;;GAEC,GACDqI,qBAA4B;QAC1B,IAAI,CAAC,IAAI,CAACrI,KAAK,EAAE;YACf,MAAM,IAAIsE,MAAM;QAClB;QAEA,MAAMgE,YAAY,IAAI,CAACtI,KAAK,CAACsI,SAAS;QACtC,IAAI,CAAC9H,MAAM,CAACmF,KAAK,CAAC;QAClB,OAAO2C;IACT;IAEA;;GAEC,GACD,MAAMC,kBAAiC;QACrC,IAAI;YACF,6BAA6B;YAC7B,IAAI,IAAI,CAAC5E,mBAAmB,EAAE;gBAC5B6E,cAAc,IAAI,CAAC7E,mBAAmB;gBACtC,IAAI,CAACA,mBAAmB,GAAG;gBAC3B,IAAI,CAACnD,MAAM,CAACoB,GAAG,CAAC;YAClB;YAEA,yBAAyB;YACzB,IAAI,IAAI,CAAC5B,KAAK,EAAE;gBACd,IAAI,CAACQ,MAAM,CAACoB,GAAG,CAAC;gBAChB,MAAM,IAAI,CAAC5B,KAAK,CAACyI,IAAI;gBACrB,IAAI,CAACjI,MAAM,CAACoB,GAAG,CAAC;YAClB;YAEA,2BAA2B;YAC3B,IAAI,CAACpB,MAAM,CAACoB,GAAG,CAAC,+BAA+B,IAAI,CAACyC,gBAAgB;QAEtE,EAAE,OAAO5D,OAAO;YACd,IAAI,CAACD,MAAM,CAACC,KAAK,CAAC,wCAAwCA;YAC1D,0CAA0C;YAC1C,IAAI,IAAI,CAACT,KAAK,EAAE;gBACd,IAAI,CAACA,KAAK,CAAC0I,UAAU;YACvB;QACF;IACF;;aA7qBiBlI,SAAS,IAAImI,cAAM,CAAC9J,aAAakG,IAAI;aAG9ClD,YAAY;aACZC,qBAAqB;aACZM,wBAAwB;aAIjCuB,sBAA6C;aAE7Cd,eAAe;aACfC,mBAAmB;;AAmqB7B"}