"use strict";
Object.defineProperty(exports, "__esModule", {
    value: true
});
Object.defineProperty(exports, "ZaloAutomationLogResponseDto", {
    enumerable: true,
    get: function() {
        return ZaloAutomationLogResponseDto;
    }
});
const _swagger = require("@nestjs/swagger");
function _ts_decorate(decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for(var i = decorators.length - 1; i >= 0; i--)if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
}
function _ts_metadata(k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
}
let ZaloAutomationLogResponseDto = class ZaloAutomationLogResponseDto {
};
_ts_decorate([
    (0, _swagger.ApiProperty)({
        description: 'ID của log',
        example: 1
    }),
    _ts_metadata("design:type", Number)
], ZaloAutomationLogResponseDto.prototype, "id", void 0);
_ts_decorate([
    (0, _swagger.ApiProperty)({
        description: 'ID của tự động hóa',
        example: 1
    }),
    _ts_metadata("design:type", Number)
], ZaloAutomationLogResponseDto.prototype, "automationId", void 0);
_ts_decorate([
    (0, _swagger.ApiProperty)({
        description: 'ID của người dùng',
        example: 123
    }),
    _ts_metadata("design:type", Number)
], ZaloAutomationLogResponseDto.prototype, "userId", void 0);
_ts_decorate([
    (0, _swagger.ApiProperty)({
        description: 'ID của Official Account',
        example: '*********'
    }),
    _ts_metadata("design:type", String)
], ZaloAutomationLogResponseDto.prototype, "oaId", void 0);
_ts_decorate([
    (0, _swagger.ApiProperty)({
        description: 'ID của người theo dõi',
        example: 1
    }),
    _ts_metadata("design:type", Number)
], ZaloAutomationLogResponseDto.prototype, "followerId", void 0);
_ts_decorate([
    (0, _swagger.ApiProperty)({
        description: 'ID người dùng của người theo dõi trên Zalo',
        example: '*********'
    }),
    _ts_metadata("design:type", String)
], ZaloAutomationLogResponseDto.prototype, "followerUserId", void 0);
_ts_decorate([
    (0, _swagger.ApiProperty)({
        description: 'Loại sự kiện kích hoạt',
        example: 'follow'
    }),
    _ts_metadata("design:type", String)
], ZaloAutomationLogResponseDto.prototype, "triggerType", void 0);
_ts_decorate([
    (0, _swagger.ApiProperty)({
        description: 'Loại hành động',
        example: 'send_message'
    }),
    _ts_metadata("design:type", String)
], ZaloAutomationLogResponseDto.prototype, "actionType", void 0);
_ts_decorate([
    (0, _swagger.ApiProperty)({
        description: 'Trạng thái của log',
        example: 'success',
        enum: [
            'pending',
            'success',
            'failed'
        ]
    }),
    _ts_metadata("design:type", String)
], ZaloAutomationLogResponseDto.prototype, "status", void 0);
_ts_decorate([
    (0, _swagger.ApiProperty)({
        description: 'Thông báo lỗi (nếu có)',
        example: 'Không thể gửi tin nhắn',
        nullable: true
    }),
    _ts_metadata("design:type", String)
], ZaloAutomationLogResponseDto.prototype, "error", void 0);
_ts_decorate([
    (0, _swagger.ApiProperty)({
        description: 'Thời điểm tạo (Unix timestamp)',
        example: 1625097600000
    }),
    _ts_metadata("design:type", Number)
], ZaloAutomationLogResponseDto.prototype, "createdAt", void 0);
_ts_decorate([
    (0, _swagger.ApiProperty)({
        description: 'Thông tin người theo dõi',
        example: {
            displayName: 'Nguyễn Văn A',
            avatar: 'https://example.com/avatar.jpg'
        },
        nullable: true
    }),
    _ts_metadata("design:type", Object)
], ZaloAutomationLogResponseDto.prototype, "follower", void 0);
_ts_decorate([
    (0, _swagger.ApiProperty)({
        description: 'Thông tin tự động hóa',
        example: {
            name: 'Chào mừng người theo dõi mới'
        },
        nullable: true
    }),
    _ts_metadata("design:type", Object)
], ZaloAutomationLogResponseDto.prototype, "automation", void 0);

//# sourceMappingURL=zalo-automation-log-response.dto.js.map