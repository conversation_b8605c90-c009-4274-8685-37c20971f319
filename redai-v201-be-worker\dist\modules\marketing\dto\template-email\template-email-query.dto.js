"use strict";
Object.defineProperty(exports, "__esModule", {
    value: true
});
Object.defineProperty(exports, "TemplateEmailQueryDto", {
    enumerable: true,
    get: function() {
        return TemplateEmailQueryDto;
    }
});
const _swagger = require("@nestjs/swagger");
const _classvalidator = require("class-validator");
const _dto = require("../../../../common/dto");
function _ts_decorate(decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for(var i = decorators.length - 1; i >= 0; i--)if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
}
function _ts_metadata(k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
}
let TemplateEmailQueryDto = class TemplateEmailQueryDto extends _dto.QueryDto {
    constructor(){
        super();
        this.sortBy = 'createdAt';
        this.sortDirection = _dto.SortDirection.DESC;
    }
};
_ts_decorate([
    (0, _swagger.ApiProperty)({
        description: 'Filtrar por nombre del template',
        example: 'bienvenida',
        required: false
    }),
    (0, _classvalidator.IsString)(),
    (0, _classvalidator.IsOptional)(),
    _ts_metadata("design:type", String)
], TemplateEmailQueryDto.prototype, "name", void 0);
_ts_decorate([
    (0, _swagger.ApiProperty)({
        description: 'Filtrar por tag',
        example: 'registro',
        required: false
    }),
    (0, _classvalidator.IsString)(),
    (0, _classvalidator.IsOptional)(),
    _ts_metadata("design:type", String)
], TemplateEmailQueryDto.prototype, "tag", void 0);

//# sourceMappingURL=template-email-query.dto.js.map