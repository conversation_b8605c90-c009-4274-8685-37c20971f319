"use strict";
Object.defineProperty(exports, "__esModule", {
    value: true
});
Object.defineProperty(exports, "SegmentStatsDto", {
    enumerable: true,
    get: function() {
        return SegmentStatsDto;
    }
});
const _swagger = require("@nestjs/swagger");
function _ts_decorate(decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for(var i = decorators.length - 1; i >= 0; i--)if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
}
function _ts_metadata(k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
}
let SegmentStatsDto = class SegmentStatsDto {
};
_ts_decorate([
    (0, _swagger.ApiProperty)({
        description: 'ID của segment',
        example: 1
    }),
    _ts_metadata("design:type", Number)
], SegmentStatsDto.prototype, "segmentId", void 0);
_ts_decorate([
    (0, _swagger.ApiProperty)({
        description: 'Tên segment',
        example: 'Khách hàng tiềm năng'
    }),
    _ts_metadata("design:type", String)
], SegmentStatsDto.prototype, "segmentName", void 0);
_ts_decorate([
    (0, _swagger.ApiProperty)({
        description: 'Tổng số khách hàng trong segment',
        example: 150
    }),
    _ts_metadata("design:type", Number)
], SegmentStatsDto.prototype, "totalAudiences", void 0);
_ts_decorate([
    (0, _swagger.ApiProperty)({
        description: 'Tỷ lệ khách hàng trong segment so với tổng số khách hàng',
        example: 0.25
    }),
    _ts_metadata("design:type", Number)
], SegmentStatsDto.prototype, "percentageOfTotal", void 0);
_ts_decorate([
    (0, _swagger.ApiProperty)({
        description: 'Thời gian cập nhật thống kê (Unix timestamp)',
        example: 1619171200
    }),
    _ts_metadata("design:type", Number)
], SegmentStatsDto.prototype, "updatedAt", void 0);

//# sourceMappingURL=segment-stats.dto.js.map