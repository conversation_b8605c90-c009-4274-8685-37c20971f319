"use strict";
Object.defineProperty(exports, "__esModule", {
    value: true
});
Object.defineProperty(exports, "EmailSystemService", {
    enumerable: true,
    get: function() {
        return EmailSystemService;
    }
});
const _common = require("@nestjs/common");
const _typeorm = require("@nestjs/typeorm");
const _typeorm1 = require("typeorm");
const _admintemplateemailentity = require("./entities/admin-template-email.entity");
const _bullmq = require("@nestjs/bullmq");
const _bullmq1 = require("bullmq");
const _queue = require("../../queue");
function _ts_decorate(decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for(var i = decorators.length - 1; i >= 0; i--)if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
}
function _ts_metadata(k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
}
function _ts_param(paramIndex, decorator) {
    return function(target, key) {
        decorator(target, key, paramIndex);
    };
}
let EmailSystemService = class EmailSystemService {
    /**
   * Thêm job gửi email vào queue
   * @param jobData Dữ liệu job email
   * @returns Job ID
   */ async addEmailJob(jobData) {
        try {
            const job = await this.emailQueue.add('send-email', jobData);
            this.logger.log(`Đã thêm job email vào queue với ID: ${job.id}`);
            return job.id;
        } catch (error) {
            this.logger.error(`Lỗi khi thêm job email vào queue: ${error.message}`, error.stack);
            throw error;
        }
    }
    /**
   * Lấy template email theo category
   * @param category Danh mục email cần tìm
   * @returns Template email tìm thấy hoặc null
   */ async getTemplateByCategory(category) {
        try {
            const template = await this.adminTemplateEmailRepository.findOne({
                where: {
                    category
                }
            });
            if (!template) {
                this.logger.warn(`Template email với category ${category} không tồn tại`);
                return null;
            }
            return template;
        } catch (error) {
            this.logger.error(`Lỗi khi lấy template email: ${error.message}`, error.stack);
            throw error;
        }
    }
    /**
   * Thay thế các placeholder trong nội dung email
   * @param content Nội dung cần thay thế
   * @param data Dữ liệu để thay thế
   * @returns Nội dung đã được thay thế
   */ replacePlaceholders(content, data) {
        if (!content) return '';
        let replacedContent = content;
        // Thay thế các placeholder dạng {{key}} trong nội dung
        Object.entries(data).forEach(([key, value])=>{
            const placeholder = new RegExp(`{{${key}}}`, 'g');
            replacedContent = replacedContent.replace(placeholder, String(value));
        });
        return replacedContent;
    }
    constructor(adminTemplateEmailRepository, emailQueue){
        this.adminTemplateEmailRepository = adminTemplateEmailRepository;
        this.emailQueue = emailQueue;
        this.logger = new _common.Logger(EmailSystemService.name);
    }
};
EmailSystemService = _ts_decorate([
    (0, _common.Injectable)(),
    _ts_param(0, (0, _typeorm.InjectRepository)(_admintemplateemailentity.AdminTemplateEmail)),
    _ts_param(1, (0, _bullmq.InjectQueue)(_queue.QueueName.EMAIL_SYSTEM)),
    _ts_metadata("design:type", Function),
    _ts_metadata("design:paramtypes", [
        typeof _typeorm1.Repository === "undefined" ? Object : _typeorm1.Repository,
        typeof _bullmq1.Queue === "undefined" ? Object : _bullmq1.Queue
    ])
], EmailSystemService);

//# sourceMappingURL=email-system.service.js.map