"use strict";
Object.defineProperty(exports, "__esModule", {
    value: true
});
function _export(target, all) {
    for(var name in all)Object.defineProperty(target, name, {
        enumerable: true,
        get: Object.getOwnPropertyDescriptor(all, name).get
    });
}
_export(exports, {
    get MessageDirection () {
        return MessageDirection;
    },
    get MessageQueryDto () {
        return MessageQueryDto;
    },
    get MessageTypeFilter () {
        return MessageTypeFilter;
    }
});
const _swagger = require("@nestjs/swagger");
const _classvalidator = require("class-validator");
const _dto = require("../../../../common/dto");
function _ts_decorate(decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for(var i = decorators.length - 1; i >= 0; i--)if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
}
function _ts_metadata(k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
}
var MessageDirection = /*#__PURE__*/ function(MessageDirection) {
    MessageDirection["INCOMING"] = "incoming";
    MessageDirection["OUTGOING"] = "outgoing";
    MessageDirection["ALL"] = "all";
    return MessageDirection;
}({});
var MessageTypeFilter = /*#__PURE__*/ function(MessageTypeFilter) {
    MessageTypeFilter["TEXT"] = "text";
    MessageTypeFilter["IMAGE"] = "image";
    MessageTypeFilter["FILE"] = "file";
    MessageTypeFilter["TEMPLATE"] = "template";
    MessageTypeFilter["ALL"] = "all";
    return MessageTypeFilter;
}({});
let MessageQueryDto = class MessageQueryDto extends _dto.QueryDto {
    constructor(){
        super();
        this.sortBy = 'timestamp';
        this.sortDirection = _dto.SortDirection.DESC;
    }
};
_ts_decorate([
    (0, _swagger.ApiProperty)({
        description: 'Lọc theo loại tin nhắn',
        enum: MessageTypeFilter,
        example: "all",
        required: false
    }),
    (0, _classvalidator.IsOptional)(),
    (0, _classvalidator.IsEnum)(MessageTypeFilter),
    _ts_metadata("design:type", String)
], MessageQueryDto.prototype, "messageType", void 0);
_ts_decorate([
    (0, _swagger.ApiProperty)({
        description: 'Lọc theo hướng tin nhắn',
        enum: MessageDirection,
        example: "all",
        required: false
    }),
    (0, _classvalidator.IsOptional)(),
    (0, _classvalidator.IsEnum)(MessageDirection),
    _ts_metadata("design:type", String)
], MessageQueryDto.prototype, "direction", void 0);
_ts_decorate([
    (0, _swagger.ApiProperty)({
        description: 'Tìm kiếm theo nội dung tin nhắn',
        example: 'xin chào',
        required: false
    }),
    (0, _classvalidator.IsOptional)(),
    (0, _classvalidator.IsString)(),
    _ts_metadata("design:type", String)
], MessageQueryDto.prototype, "content", void 0);

//# sourceMappingURL=message-query.dto.js.map