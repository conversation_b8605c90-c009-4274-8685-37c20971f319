"use strict";
Object.defineProperty(exports, "__esModule", {
    value: true
});
Object.defineProperty(exports, "TemplateEmailResponseDto", {
    enumerable: true,
    get: function() {
        return TemplateEmailResponseDto;
    }
});
const _swagger = require("@nestjs/swagger");
function _ts_decorate(decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for(var i = decorators.length - 1; i >= 0; i--)if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
}
function _ts_metadata(k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
}
let TemplateEmailResponseDto = class TemplateEmailResponseDto {
};
_ts_decorate([
    (0, _swagger.ApiProperty)({
        description: 'ID del template',
        example: 1
    }),
    _ts_metadata("design:type", Number)
], TemplateEmailResponseDto.prototype, "id", void 0);
_ts_decorate([
    (0, _swagger.ApiProperty)({
        description: 'ID del usuario propietario',
        example: 123
    }),
    _ts_metadata("design:type", Number)
], TemplateEmailResponseDto.prototype, "userId", void 0);
_ts_decorate([
    (0, _swagger.ApiProperty)({
        description: 'Nombre del template',
        example: 'Plantilla de bienvenida'
    }),
    _ts_metadata("design:type", String)
], TemplateEmailResponseDto.prototype, "name", void 0);
_ts_decorate([
    (0, _swagger.ApiProperty)({
        description: 'Asunto del email',
        example: 'Bienvenido a nuestra plataforma'
    }),
    _ts_metadata("design:type", String)
], TemplateEmailResponseDto.prototype, "subject", void 0);
_ts_decorate([
    (0, _swagger.ApiProperty)({
        description: 'Contenido HTML del email',
        example: '<h1>Bienvenido</h1><p>Gracias por registrarte en nuestra plataforma.</p>'
    }),
    _ts_metadata("design:type", String)
], TemplateEmailResponseDto.prototype, "content", void 0);
_ts_decorate([
    (0, _swagger.ApiProperty)({
        description: 'Tags asociados al template',
        example: [
            'bienvenida',
            'registro'
        ]
    }),
    _ts_metadata("design:type", Array)
], TemplateEmailResponseDto.prototype, "tags", void 0);
_ts_decorate([
    (0, _swagger.ApiProperty)({
        description: 'Placeholders utilizados en el template',
        example: [
            'userName',
            'companyName',
            'date'
        ],
        type: [
            String
        ]
    }),
    _ts_metadata("design:type", Array)
], TemplateEmailResponseDto.prototype, "placeholders", void 0);
_ts_decorate([
    (0, _swagger.ApiProperty)({
        description: 'Fecha de creación (Unix timestamp)',
        example: 1625097600000
    }),
    _ts_metadata("design:type", Number)
], TemplateEmailResponseDto.prototype, "createdAt", void 0);
_ts_decorate([
    (0, _swagger.ApiProperty)({
        description: 'Fecha de última actualización (Unix timestamp)',
        example: 1625097600000
    }),
    _ts_metadata("design:type", Number)
], TemplateEmailResponseDto.prototype, "updatedAt", void 0);

//# sourceMappingURL=template-email-response.dto.js.map