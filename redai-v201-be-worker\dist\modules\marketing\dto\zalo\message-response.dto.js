"use strict";
Object.defineProperty(exports, "__esModule", {
    value: true
});
Object.defineProperty(exports, "MessageResponseDto", {
    enumerable: true,
    get: function() {
        return MessageResponseDto;
    }
});
const _swagger = require("@nestjs/swagger");
function _ts_decorate(decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for(var i = decorators.length - 1; i >= 0; i--)if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
}
function _ts_metadata(k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
}
let MessageResponseDto = class MessageResponseDto {
};
_ts_decorate([
    (0, _swagger.ApiProperty)({
        description: 'ID của tin nhắn trong hệ thống',
        example: 1
    }),
    _ts_metadata("design:type", Number)
], MessageResponseDto.prototype, "id", void 0);
_ts_decorate([
    (0, _swagger.ApiProperty)({
        description: 'ID của tin nhắn trên Zalo',
        example: 'msg123456789',
        nullable: true
    }),
    _ts_metadata("design:type", String)
], MessageResponseDto.prototype, "messageId", void 0);
_ts_decorate([
    (0, _swagger.ApiProperty)({
        description: 'Loại tin nhắn (text, image, file, template)',
        example: 'text'
    }),
    _ts_metadata("design:type", String)
], MessageResponseDto.prototype, "messageType", void 0);
_ts_decorate([
    (0, _swagger.ApiProperty)({
        description: 'Nội dung tin nhắn',
        example: 'Xin chào! Cảm ơn bạn đã quan tâm đến sản phẩm của chúng tôi.',
        nullable: true
    }),
    _ts_metadata("design:type", String)
], MessageResponseDto.prototype, "content", void 0);
_ts_decorate([
    (0, _swagger.ApiProperty)({
        description: 'Dữ liệu bổ sung của tin nhắn',
        example: {
            url: 'https://example.com/image.jpg'
        },
        nullable: true
    }),
    _ts_metadata("design:type", Object)
], MessageResponseDto.prototype, "data", void 0);
_ts_decorate([
    (0, _swagger.ApiProperty)({
        description: 'Hướng tin nhắn (incoming, outgoing)',
        example: 'outgoing'
    }),
    _ts_metadata("design:type", String)
], MessageResponseDto.prototype, "direction", void 0);
_ts_decorate([
    (0, _swagger.ApiProperty)({
        description: 'Thời điểm gửi/nhận (Unix timestamp)',
        example: 1625097600000
    }),
    _ts_metadata("design:type", Number)
], MessageResponseDto.prototype, "timestamp", void 0);

//# sourceMappingURL=message-response.dto.js.map