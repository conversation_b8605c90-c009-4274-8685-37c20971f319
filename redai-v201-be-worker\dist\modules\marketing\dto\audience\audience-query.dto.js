"use strict";
Object.defineProperty(exports, "__esModule", {
    value: true
});
function _export(target, all) {
    for(var name in all)Object.defineProperty(target, name, {
        enumerable: true,
        get: Object.getOwnPropertyDescriptor(all, name).get
    });
}
_export(exports, {
    get AudienceQueryDto () {
        return AudienceQueryDto;
    },
    get AudienceSortField () {
        return AudienceSortField;
    },
    get SortOrder () {
        return SortOrder;
    }
});
const _swagger = require("@nestjs/swagger");
const _classvalidator = require("class-validator");
const _classtransformer = require("class-transformer");
function _ts_decorate(decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for(var i = decorators.length - 1; i >= 0; i--)if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
}
function _ts_metadata(k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
}
var AudienceSortField = /*#__PURE__*/ function(AudienceSortField) {
    AudienceSortField["ID"] = "id";
    AudienceSortField["EMAIL"] = "email";
    AudienceSortField["PHONE"] = "phone";
    AudienceSortField["CREATED_AT"] = "createdAt";
    AudienceSortField["UPDATED_AT"] = "updatedAt";
    return AudienceSortField;
}({});
var SortOrder = /*#__PURE__*/ function(SortOrder) {
    SortOrder["ASC"] = "ASC";
    SortOrder["DESC"] = "DESC";
    return SortOrder;
}({});
let AudienceQueryDto = class AudienceQueryDto {
    constructor(){
        /**
   * Trang hiện tại (bắt đầu từ 1)
   * @example 1
   */ this.page = 1;
        /**
   * Số lượng item trên mỗi trang
   * @example 10
   */ this.limit = 10;
        /**
   * Sắp xếp theo trường
   * @example "createdAt"
   */ this.sortBy = "createdAt";
        /**
   * Thứ tự sắp xếp
   * @example "DESC"
   */ this.sortOrder = "DESC";
    }
};
_ts_decorate([
    (0, _swagger.ApiProperty)({
        description: 'Trang hiện tại (bắt đầu từ 1)',
        example: 1,
        default: 1,
        required: false
    }),
    (0, _classvalidator.IsOptional)(),
    (0, _classvalidator.IsInt)({
        message: 'Trang phải là số nguyên'
    }),
    (0, _classvalidator.Min)(1, {
        message: 'Trang phải lớn hơn hoặc bằng 1'
    }),
    (0, _classtransformer.Type)(()=>Number),
    _ts_metadata("design:type", Number)
], AudienceQueryDto.prototype, "page", void 0);
_ts_decorate([
    (0, _swagger.ApiProperty)({
        description: 'Số lượng item trên mỗi trang',
        example: 10,
        default: 10,
        required: false
    }),
    (0, _classvalidator.IsOptional)(),
    (0, _classvalidator.IsInt)({
        message: 'Số lượng item phải là số nguyên'
    }),
    (0, _classvalidator.Min)(1, {
        message: 'Số lượng item phải lớn hơn hoặc bằng 1'
    }),
    (0, _classtransformer.Type)(()=>Number),
    _ts_metadata("design:type", Number)
], AudienceQueryDto.prototype, "limit", void 0);
_ts_decorate([
    (0, _swagger.ApiProperty)({
        description: 'Tìm kiếm theo email',
        example: 'example.com',
        required: false
    }),
    (0, _classvalidator.IsOptional)(),
    (0, _classvalidator.IsString)({
        message: 'Email phải là chuỗi'
    }),
    _ts_metadata("design:type", String)
], AudienceQueryDto.prototype, "email", void 0);
_ts_decorate([
    (0, _swagger.ApiProperty)({
        description: 'Tìm kiếm theo số điện thoại',
        example: '+84',
        required: false
    }),
    (0, _classvalidator.IsOptional)(),
    (0, _classvalidator.IsString)({
        message: 'Số điện thoại phải là chuỗi'
    }),
    _ts_metadata("design:type", String)
], AudienceQueryDto.prototype, "phone", void 0);
_ts_decorate([
    (0, _swagger.ApiProperty)({
        description: 'Tìm kiếm theo tag ID',
        example: 1,
        required: false
    }),
    (0, _classvalidator.IsOptional)(),
    (0, _classvalidator.IsInt)({
        message: 'Tag ID phải là số nguyên'
    }),
    (0, _classtransformer.Type)(()=>Number),
    _ts_metadata("design:type", Number)
], AudienceQueryDto.prototype, "tagId", void 0);
_ts_decorate([
    (0, _swagger.ApiProperty)({
        description: 'Tìm kiếm theo tên trường tùy chỉnh',
        example: 'address',
        required: false
    }),
    (0, _classvalidator.IsOptional)(),
    (0, _classvalidator.IsString)({
        message: 'Tên trường tùy chỉnh phải là chuỗi'
    }),
    _ts_metadata("design:type", String)
], AudienceQueryDto.prototype, "customFieldName", void 0);
_ts_decorate([
    (0, _swagger.ApiProperty)({
        description: 'Tìm kiếm theo giá trị trường tùy chỉnh',
        example: 'Hanoi',
        required: false
    }),
    (0, _classvalidator.IsOptional)(),
    (0, _classvalidator.IsString)({
        message: 'Giá trị trường tùy chỉnh phải là chuỗi'
    }),
    _ts_metadata("design:type", String)
], AudienceQueryDto.prototype, "customFieldValue", void 0);
_ts_decorate([
    (0, _swagger.ApiProperty)({
        description: 'Sắp xếp theo trường',
        enum: AudienceSortField,
        example: "createdAt",
        default: "createdAt",
        required: false
    }),
    (0, _classvalidator.IsOptional)(),
    (0, _classvalidator.IsEnum)(AudienceSortField, {
        message: `Trường sắp xếp phải là một trong các giá trị: ${Object.values(AudienceSortField).join(', ')}`
    }),
    _ts_metadata("design:type", String)
], AudienceQueryDto.prototype, "sortBy", void 0);
_ts_decorate([
    (0, _swagger.ApiProperty)({
        description: 'Thứ tự sắp xếp',
        enum: SortOrder,
        example: "DESC",
        default: "DESC",
        required: false
    }),
    (0, _classvalidator.IsOptional)(),
    (0, _classvalidator.IsEnum)(SortOrder, {
        message: `Thứ tự sắp xếp phải là một trong các giá trị: ${Object.values(SortOrder).join(', ')}`
    }),
    _ts_metadata("design:type", String)
], AudienceQueryDto.prototype, "sortOrder", void 0);

//# sourceMappingURL=audience-query.dto.js.map