"use strict";
Object.defineProperty(exports, "__esModule", {
    value: true
});
_export_star(require("./tag"), exports);
_export_star(require("./audience"), exports);
_export_star(require("./segment"), exports);
_export_star(require("./campaign"), exports);
_export_star(require("./common"), exports);
_export_star(require("./statistics"), exports);
_export_star(require("./template-email"), exports);
_export_star(require("./zalo"), exports);
_export_star(require("./audience-custom-field-definition"), exports);
function _export_star(from, to) {
    Object.keys(from).forEach(function(k) {
        if (k !== "default" && !Object.prototype.hasOwnProperty.call(to, k)) {
            Object.defineProperty(to, k, {
                enumerable: true,
                get: function() {
                    return from[k];
                }
            });
        }
    });
    return from;
}

//# sourceMappingURL=index.js.map