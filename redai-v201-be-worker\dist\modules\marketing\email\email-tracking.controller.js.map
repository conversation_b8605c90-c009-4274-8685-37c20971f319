{"version": 3, "sources": ["../../../../src/modules/marketing/email/email-tracking.controller.ts"], "sourcesContent": ["import { <PERSON>, Get, Param, Req, Re<PERSON>, Logger } from '@nestjs/common';\r\nimport { Request, Response } from 'express';\r\nimport { EmailTrackingService } from './services';\r\n\r\n/**\r\n * Controller xử lý tracking email\r\n */\r\n@Controller('api/email-tracking')\r\nexport class EmailTrackingController {\r\n  private readonly logger = new Logger(EmailTrackingController.name);\r\n\r\n  constructor(private readonly emailTrackingService: EmailTrackingService) {}\r\n\r\n  /**\r\n   * Endpoint cho tracking pixel\r\n   * @param trackingId ID tracking từ URL\r\n   * @param req Request object\r\n   * @param res Response object\r\n   */\r\n  @Get('pixel/:trackingId')\r\n  async trackPixel(\r\n    @Param('trackingId') trackingId: string,\r\n    @Req() req: Request,\r\n    @Res() res: Response,\r\n  ): Promise<void> {\r\n    try {\r\n      // Lấy thông tin metadata từ request\r\n      const metadata = {\r\n        ip: req.ip || req.connection.remoteAddress,\r\n        userAgent: req.get('User-Agent'),\r\n        referer: req.get('Referer'),\r\n        timestamp: Date.now(),\r\n      };\r\n\r\n      // Track email opened\r\n      await this.emailTrackingService.trackEmailOpened(trackingId, metadata);\r\n\r\n      this.logger.debug(`Email opened tracked: ${trackingId}`);\r\n\r\n      // Trả về ảnh pixel 1x1 transparent\r\n      const pixelBuffer = Buffer.from(\r\n        'iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg==',\r\n        'base64',\r\n      );\r\n\r\n      res.set({\r\n        'Content-Type': 'image/png',\r\n        'Content-Length': pixelBuffer.length.toString(),\r\n        'Cache-Control': 'no-cache, no-store, must-revalidate',\r\n        Pragma: 'no-cache',\r\n        Expires: '0',\r\n      });\r\n\r\n      res.send(pixelBuffer);\r\n    } catch (error) {\r\n      this.logger.error(`Error tracking pixel: ${error.message}`, error.stack);\r\n\r\n      // Vẫn trả về pixel để không làm hỏng email\r\n      const pixelBuffer = Buffer.from(\r\n        'iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg==',\r\n        'base64',\r\n      );\r\n\r\n      res.set({\r\n        'Content-Type': 'image/png',\r\n        'Content-Length': pixelBuffer.length.toString(),\r\n      });\r\n\r\n      res.send(pixelBuffer);\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Endpoint để track click links (optional)\r\n   * @param trackingId ID tracking\r\n   * @param url URL gốc cần redirect\r\n   * @param req Request object\r\n   * @param res Response object\r\n   */\r\n  @Get('click/:trackingId')\r\n  async trackClick(\r\n    @Param('trackingId') trackingId: string,\r\n    @Req() req: Request,\r\n    @Res() res: Response,\r\n  ): Promise<void> {\r\n    try {\r\n      const url = req.query.url as string;\r\n\r\n      if (!url) {\r\n        res.status(400).send('Missing URL parameter');\r\n        return;\r\n      }\r\n\r\n      // Lấy thông tin metadata từ request\r\n      const metadata = {\r\n        ip: req.ip || req.connection.remoteAddress,\r\n        userAgent: req.get('User-Agent'),\r\n        referer: req.get('Referer'),\r\n        clickedUrl: url,\r\n        timestamp: Date.now(),\r\n      };\r\n\r\n      // Track email clicked (có thể extend EmailTrackingService để support click tracking)\r\n      // await this.emailTrackingService.trackEmailClicked(trackingId, metadata);\r\n\r\n      this.logger.debug(`Email click tracked: ${trackingId} -> ${url}`);\r\n\r\n      // Redirect đến URL gốc\r\n      res.redirect(url);\r\n    } catch (error) {\r\n      this.logger.error(`Error tracking click: ${error.message}`, error.stack);\r\n\r\n      // Redirect về trang chủ nếu có lỗi\r\n      const fallbackUrl = (req.query.url as string) || 'https://redai.com';\r\n      res.redirect(fallbackUrl);\r\n    }\r\n  }\r\n}\r\n"], "names": ["EmailTrackingController", "trackPixel", "trackingId", "req", "res", "metadata", "ip", "connection", "remoteAddress", "userAgent", "get", "referer", "timestamp", "Date", "now", "emailTrackingService", "trackEmailOpened", "logger", "debug", "pixelBuffer", "<PERSON><PERSON><PERSON>", "from", "set", "length", "toString", "Pragma", "Expires", "send", "error", "message", "stack", "trackClick", "url", "query", "status", "clickedUrl", "redirect", "fallbackUrl", "constructor", "<PERSON><PERSON>", "name"], "mappings": ";;;;+BAQaA;;;eAAAA;;;wBAR4C;yBACvB;0BACG;;;;;;;;;;;;;;;AAM9B,IAAA,AAAMA,0BAAN,MAAMA;IAKX;;;;;GAKC,GACD,MACMC,WACJ,AAAqBC,UAAkB,EACvC,AAAOC,GAAY,EACnB,AAAOC,GAAa,EACL;QACf,IAAI;YACF,oCAAoC;YACpC,MAAMC,WAAW;gBACfC,IAAIH,IAAIG,EAAE,IAAIH,IAAII,UAAU,CAACC,aAAa;gBAC1CC,WAAWN,IAAIO,GAAG,CAAC;gBACnBC,SAASR,IAAIO,GAAG,CAAC;gBACjBE,WAAWC,KAAKC,GAAG;YACrB;YAEA,qBAAqB;YACrB,MAAM,IAAI,CAACC,oBAAoB,CAACC,gBAAgB,CAACd,YAAYG;YAE7D,IAAI,CAACY,MAAM,CAACC,KAAK,CAAC,CAAC,sBAAsB,EAAEhB,YAAY;YAEvD,mCAAmC;YACnC,MAAMiB,cAAcC,OAAOC,IAAI,CAC7B,oGACA;YAGFjB,IAAIkB,GAAG,CAAC;gBACN,gBAAgB;gBAChB,kBAAkBH,YAAYI,MAAM,CAACC,QAAQ;gBAC7C,iBAAiB;gBACjBC,QAAQ;gBACRC,SAAS;YACX;YAEAtB,IAAIuB,IAAI,CAACR;QACX,EAAE,OAAOS,OAAO;YACd,IAAI,CAACX,MAAM,CAACW,KAAK,CAAC,CAAC,sBAAsB,EAAEA,MAAMC,OAAO,EAAE,EAAED,MAAME,KAAK;YAEvE,2CAA2C;YAC3C,MAAMX,cAAcC,OAAOC,IAAI,CAC7B,oGACA;YAGFjB,IAAIkB,GAAG,CAAC;gBACN,gBAAgB;gBAChB,kBAAkBH,YAAYI,MAAM,CAACC,QAAQ;YAC/C;YAEApB,IAAIuB,IAAI,CAACR;QACX;IACF;IAEA;;;;;;GAMC,GACD,MACMY,WACJ,AAAqB7B,UAAkB,EACvC,AAAOC,GAAY,EACnB,AAAOC,GAAa,EACL;QACf,IAAI;YACF,MAAM4B,MAAM7B,IAAI8B,KAAK,CAACD,GAAG;YAEzB,IAAI,CAACA,KAAK;gBACR5B,IAAI8B,MAAM,CAAC,KAAKP,IAAI,CAAC;gBACrB;YACF;YAEA,oCAAoC;YACpC,MAAMtB,WAAW;gBACfC,IAAIH,IAAIG,EAAE,IAAIH,IAAII,UAAU,CAACC,aAAa;gBAC1CC,WAAWN,IAAIO,GAAG,CAAC;gBACnBC,SAASR,IAAIO,GAAG,CAAC;gBACjByB,YAAYH;gBACZpB,WAAWC,KAAKC,GAAG;YACrB;YAEA,qFAAqF;YACrF,2EAA2E;YAE3E,IAAI,CAACG,MAAM,CAACC,KAAK,CAAC,CAAC,qBAAqB,EAAEhB,WAAW,IAAI,EAAE8B,KAAK;YAEhE,uBAAuB;YACvB5B,IAAIgC,QAAQ,CAACJ;QACf,EAAE,OAAOJ,OAAO;YACd,IAAI,CAACX,MAAM,CAACW,KAAK,CAAC,CAAC,sBAAsB,EAAEA,MAAMC,OAAO,EAAE,EAAED,MAAME,KAAK;YAEvE,mCAAmC;YACnC,MAAMO,cAAc,AAAClC,IAAI8B,KAAK,CAACD,GAAG,IAAe;YACjD5B,IAAIgC,QAAQ,CAACC;QACf;IACF;IAzGAC,YAAY,AAAiBvB,oBAA0C,CAAE;aAA5CA,uBAAAA;aAFZE,SAAS,IAAIsB,cAAM,CAACvC,wBAAwBwC,IAAI;IAES;AA0G5E"}