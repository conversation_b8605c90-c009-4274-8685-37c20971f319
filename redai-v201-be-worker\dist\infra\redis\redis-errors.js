/**
 * Custom Redis Error Classes for Enhanced Error Handling
 * 
 * These error classes provide structured error information for different
 * types of Redis failures, enabling better error handling and logging.
 */ "use strict";
Object.defineProperty(exports, "__esModule", {
    value: true
});
function _export(target, all) {
    for(var name in all)Object.defineProperty(target, name, {
        enumerable: true,
        get: Object.getOwnPropertyDescriptor(all, name).get
    });
}
_export(exports, {
    get RedisCircuitBreakerError () {
        return RedisCircuitBreakerError;
    },
    get RedisConnectionError () {
        return RedisConnectionError;
    },
    get RedisError () {
        return RedisError;
    },
    get RedisErrorCode () {
        return RedisErrorCode;
    },
    get RedisErrorFactory () {
        return RedisErrorFactory;
    },
    get RedisPubSubError () {
        return RedisPubSubError;
    },
    get RedisRetryExhaustedError () {
        return RedisRetryExhaustedError;
    },
    get RedisStreamError () {
        return RedisStreamError;
    },
    get RedisTimeoutError () {
        return RedisTimeoutError;
    }
});
var RedisErrorCode = /*#__PURE__*/ function(RedisErrorCode) {
    RedisErrorCode["CONNECTION_FAILED"] = "REDIS_CONNECTION_FAILED";
    RedisErrorCode["CONNECTION_TIMEOUT"] = "REDIS_CONNECTION_TIMEOUT";
    RedisErrorCode["COMMAND_TIMEOUT"] = "REDIS_COMMAND_TIMEOUT";
    RedisErrorCode["AUTHENTICATION_FAILED"] = "REDIS_AUTH_FAILED";
    RedisErrorCode["PERMISSION_DENIED"] = "REDIS_PERMISSION_DENIED";
    RedisErrorCode["INVALID_COMMAND"] = "REDIS_INVALID_COMMAND";
    RedisErrorCode["MEMORY_FULL"] = "REDIS_MEMORY_FULL";
    RedisErrorCode["SERVER_ERROR"] = "REDIS_SERVER_ERROR";
    RedisErrorCode["NETWORK_ERROR"] = "REDIS_NETWORK_ERROR";
    RedisErrorCode["CIRCUIT_BREAKER_OPEN"] = "REDIS_CIRCUIT_BREAKER_OPEN";
    RedisErrorCode["RETRY_EXHAUSTED"] = "REDIS_RETRY_EXHAUSTED";
    RedisErrorCode["STREAM_ERROR"] = "REDIS_STREAM_ERROR";
    RedisErrorCode["PUBSUB_ERROR"] = "REDIS_PUBSUB_ERROR";
    RedisErrorCode["UNKNOWN_ERROR"] = "REDIS_UNKNOWN_ERROR";
    return RedisErrorCode;
}({});
let RedisError = class RedisError extends Error {
    /**
   * Get structured error information for logging
   */ toLogObject() {
        return {
            errorCode: this.code,
            message: this.message,
            context: this.context,
            isRetryable: this.isRetryable,
            originalError: this.originalError ? {
                name: this.originalError.name,
                message: this.originalError.message,
                stack: this.originalError.stack
            } : undefined,
            timestamp: new Date().toISOString()
        };
    }
    constructor(code, message, context, originalError, isRetryable = false){
        super(message);
        this.name = 'RedisError';
        this.code = code;
        this.context = context;
        this.originalError = originalError;
        this.isRetryable = isRetryable;
        // Maintain proper stack trace
        if (Error.captureStackTrace) {
            Error.captureStackTrace(this, RedisError);
        }
    }
};
let RedisConnectionError = class RedisConnectionError extends RedisError {
    constructor(message, context, originalError){
        super("REDIS_CONNECTION_FAILED", message, context, originalError, true);
        this.name = 'RedisConnectionError';
    }
};
let RedisTimeoutError = class RedisTimeoutError extends RedisError {
    constructor(message, context, originalError){
        super("REDIS_COMMAND_TIMEOUT", message, context, originalError, true);
        this.name = 'RedisTimeoutError';
    }
};
let RedisStreamError = class RedisStreamError extends RedisError {
    constructor(message, context, originalError){
        super("REDIS_STREAM_ERROR", message, context, originalError, true);
        this.name = 'RedisStreamError';
    }
};
let RedisPubSubError = class RedisPubSubError extends RedisError {
    constructor(message, context, originalError){
        super("REDIS_PUBSUB_ERROR", message, context, originalError, true);
        this.name = 'RedisPubSubError';
    }
};
let RedisCircuitBreakerError = class RedisCircuitBreakerError extends RedisError {
    constructor(message, context){
        super("REDIS_CIRCUIT_BREAKER_OPEN", message, context, undefined, false);
        this.name = 'RedisCircuitBreakerError';
    }
};
let RedisRetryExhaustedError = class RedisRetryExhaustedError extends RedisError {
    constructor(message, context, originalError){
        super("REDIS_RETRY_EXHAUSTED", message, context, originalError, false);
        this.name = 'RedisRetryExhaustedError';
    }
};
let RedisErrorFactory = class RedisErrorFactory {
    static createFromError(originalError, operation, context = {}) {
        const fullContext = {
            operation,
            timestamp: Date.now(),
            ...context
        };
        const errorMessage = originalError?.message || 'Unknown Redis error';
        const errorCode = originalError?.code;
        // Connection errors
        if (this.isConnectionError(originalError)) {
            return new RedisConnectionError(`Redis connection failed during ${operation}: ${errorMessage}`, fullContext, originalError);
        }
        // Timeout errors
        if (this.isTimeoutError(originalError)) {
            return new RedisTimeoutError(`Redis operation timed out during ${operation}: ${errorMessage}`, fullContext, originalError);
        }
        // Stream errors
        if (operation.includes('XADD') || operation.includes('XREAD')) {
            return new RedisStreamError(`Redis stream operation failed during ${operation}: ${errorMessage}`, fullContext, originalError);
        }
        // Pub/Sub errors
        if (operation.includes('PUBLISH') || operation.includes('SUBSCRIBE')) {
            return new RedisPubSubError(`Redis pub/sub operation failed during ${operation}: ${errorMessage}`, fullContext, originalError);
        }
        // Circuit breaker errors
        if (errorMessage.includes('circuit breaker')) {
            return new RedisCircuitBreakerError(`Redis circuit breaker is open for ${operation}`, fullContext);
        }
        // Default to generic Redis error
        return new RedisError("REDIS_UNKNOWN_ERROR", `Redis operation failed during ${operation}: ${errorMessage}`, fullContext, originalError, this.isRetryableError(originalError));
    }
    static isConnectionError(error) {
        const connectionErrorPatterns = [
            'ECONNRESET',
            'ECONNREFUSED',
            'ENOTFOUND',
            'EAI_AGAIN',
            'Connection is closed',
            'Redis connection lost'
        ];
        const errorMessage = error?.message || '';
        const errorCode = error?.code || '';
        return connectionErrorPatterns.some((pattern)=>errorMessage.includes(pattern) || errorCode === pattern);
    }
    static isTimeoutError(error) {
        const timeoutErrorPatterns = [
            'ETIMEDOUT',
            'timeout',
            'Connection timeout',
            'Command timeout'
        ];
        const errorMessage = error?.message || '';
        const errorCode = error?.code || '';
        return timeoutErrorPatterns.some((pattern)=>errorMessage.toLowerCase().includes(pattern.toLowerCase()) || errorCode === pattern);
    }
    static isRetryableError(error) {
        const retryablePatterns = [
            'ECONNRESET',
            'ECONNREFUSED',
            'ETIMEDOUT',
            'ENOTFOUND',
            'EAI_AGAIN',
            'EPIPE',
            'READONLY',
            'LOADING',
            'BUSY'
        ];
        const errorMessage = error?.message || '';
        const errorCode = error?.code || '';
        return retryablePatterns.some((pattern)=>errorMessage.includes(pattern) || errorCode === pattern);
    }
};

//# sourceMappingURL=redis-errors.js.map