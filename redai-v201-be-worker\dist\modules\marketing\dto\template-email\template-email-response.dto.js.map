{"version": 3, "sources": ["../../../../../src/modules/marketing/dto/template-email/template-email-response.dto.ts"], "sourcesContent": ["import { ApiProperty } from '@nestjs/swagger';\r\n\r\n/**\r\n * DTO para la respuesta de un template de email\r\n */\r\nexport class TemplateEmailResponseDto {\r\n  @ApiProperty({\r\n    description: 'ID del template',\r\n    example: 1,\r\n  })\r\n  id: number;\r\n\r\n  @ApiProperty({\r\n    description: 'ID del usuario propietario',\r\n    example: 123,\r\n  })\r\n  userId: number;\r\n\r\n  @ApiProperty({\r\n    description: 'Nombre del template',\r\n    example: 'Plantilla de bienvenida',\r\n  })\r\n  name: string;\r\n\r\n  @ApiProperty({\r\n    description: 'Asunto del email',\r\n    example: 'Bienvenido a nuestra plataforma',\r\n  })\r\n  subject: string;\r\n\r\n  @ApiProperty({\r\n    description: 'Contenido HTML del email',\r\n    example:\r\n      '<h1>Bienvenido</h1><p>Gracias por registrarte en nuestra plataforma.</p>',\r\n  })\r\n  content: string;\r\n\r\n  @ApiProperty({\r\n    description: 'Tags asociados al template',\r\n    example: ['bienvenida', 'registro'],\r\n  })\r\n  tags: string[];\r\n\r\n  @ApiProperty({\r\n    description: 'Placeholders utilizados en el template',\r\n    example: ['userName', 'companyName', 'date'],\r\n    type: [String],\r\n  })\r\n  placeholders: string[];\r\n\r\n  @ApiProperty({\r\n    description: 'Fecha de creación (Unix timestamp)',\r\n    example: 1625097600000,\r\n  })\r\n  createdAt: number;\r\n\r\n  @ApiProperty({\r\n    description: 'Fecha de última actualización (Unix timestamp)',\r\n    example: 1625097600000,\r\n  })\r\n  updatedAt: number;\r\n}\r\n"], "names": ["TemplateEmailResponseDto", "description", "example", "type", "String"], "mappings": ";;;;+BAKaA;;;eAAAA;;;yBALe;;;;;;;;;;AAKrB,IAAA,AAAMA,2BAAN,MAAMA;AAwDb;;;QAtDIC,aAAa;QACbC,SAAS;;;;;;QAKTD,aAAa;QACbC,SAAS;;;;;;QAKTD,aAAa;QACbC,SAAS;;;;;;QAKTD,aAAa;QACbC,SAAS;;;;;;QAKTD,aAAa;QACbC,SACE;;;;;;QAKFD,aAAa;QACbC,SAAS;YAAC;YAAc;SAAW;;;;;;QAKnCD,aAAa;QACbC,SAAS;YAAC;YAAY;YAAe;SAAO;QAC5CC,MAAM;YAACC;SAAO;;;;;;QAKdH,aAAa;QACbC,SAAS;;;;;;QAKTD,aAAa;QACbC,SAAS"}