{"version": 3, "sources": ["../../../../../src/modules/marketing/dto/template-email/template-email-query.dto.ts"], "sourcesContent": ["import { ApiProperty } from '@nestjs/swagger';\r\nimport { IsOptional, IsString } from 'class-validator';\r\nimport { QueryDto, SortDirection } from '@common/dto';\r\n\r\n/**\r\n * DTO para consultar templates de email con paginación y filtros\r\n */\r\nexport class TemplateEmailQueryDto extends QueryDto {\r\n  @ApiProperty({\r\n    description: 'Filtrar por nombre del template',\r\n    example: 'bienvenida',\r\n    required: false,\r\n  })\r\n  @IsString()\r\n  @IsOptional()\r\n  name?: string;\r\n\r\n  @ApiProperty({\r\n    description: 'Filtrar por tag',\r\n    example: 'registro',\r\n    required: false,\r\n  })\r\n  @IsString()\r\n  @IsOptional()\r\n  tag?: string;\r\n\r\n  constructor() {\r\n    super();\r\n    this.sortBy = 'createdAt';\r\n    this.sortDirection = SortDirection.DESC;\r\n  }\r\n}\r\n"], "names": ["TemplateEmailQueryDto", "QueryDto", "constructor", "sortBy", "sortDirection", "SortDirection", "DESC", "description", "example", "required"], "mappings": ";;;;+BAOaA;;;eAAAA;;;yBAPe;gCACS;qBACG;;;;;;;;;;AAKjC,IAAA,AAAMA,wBAAN,MAAMA,8BAA8BC,aAAQ;IAmBjDC,aAAc;QACZ,KAAK;QACL,IAAI,CAACC,MAAM,GAAG;QACd,IAAI,CAACC,aAAa,GAAGC,kBAAa,CAACC,IAAI;IACzC;AACF;;;QAtBIC,aAAa;QACbC,SAAS;QACTC,UAAU;;;;;;;;QAOVF,aAAa;QACbC,SAAS;QACTC,UAAU"}