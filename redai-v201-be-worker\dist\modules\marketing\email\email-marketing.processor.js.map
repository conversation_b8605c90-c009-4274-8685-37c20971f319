{"version": 3, "sources": ["../../../../src/modules/marketing/email/email-marketing.processor.ts"], "sourcesContent": ["import { Processor, WorkerHost } from '@nestjs/bullmq';\r\nimport { Logger, Injectable } from '@nestjs/common';\r\nimport { Job } from 'bullmq';\r\nimport * as nodemailer from 'nodemailer';\r\nimport { QueueName } from '../../../queue';\r\nimport { EmailMarketingJobDto } from './dto';\r\nimport { EmailTemplateService, EmailTrackingService } from './services';\r\nimport { env } from '../../../config/env';\r\n\r\n/**\r\n * Processor xử lý queue email marketing\r\n */\r\n@Injectable()\r\n@Processor(QueueName.EMAIL_MARKETING, { concurrency: 10 })\r\nexport class EmailMarketingProcessor extends WorkerHost {\r\n  private readonly logger = new Logger(EmailMarketingProcessor.name);\r\n\r\n  constructor(\r\n    private readonly emailTemplateService: EmailTemplateService,\r\n    private readonly emailTrackingService: EmailTrackingService,\r\n  ) {\r\n    super();\r\n  }\r\n\r\n  /**\r\n   * Xử lý job gửi email marketing\r\n   * @param job Job từ queue\r\n   */\r\n  async process(job: Job<EmailMarketingJobDto>): Promise<void> {\r\n    const jobData = job.data;\r\n\r\n    this.logger.log(\r\n      `Processing email marketing job: ${job.id} for campaign ${jobData.campaignId}`,\r\n    );\r\n\r\n    try {\r\n      // Validate job data\r\n      if (!this.validateJobData(jobData)) {\r\n        throw new Error('Invalid job data');\r\n      }\r\n\r\n      // Inject custom variables vào subject và content\r\n      const processedSubject = this.emailTemplateService.injectVariables(\r\n        jobData.subject,\r\n        jobData.customFields,\r\n      );\r\n\r\n      const processedContent = this.emailTemplateService.injectVariables(\r\n        jobData.content,\r\n        jobData.customFields,\r\n      );\r\n\r\n      // Inject tracking pixel vào content\r\n      const contentWithTracking = this.emailTemplateService.injectTrackingPixel(\r\n        processedContent,\r\n        jobData.trackingId,\r\n        env.app.BASE_URL || 'http://localhost:3000',\r\n      );\r\n\r\n      // Tạo email transporter\r\n      const transporter = this.createTransporter(jobData.server);\r\n\r\n      // Chuẩn bị email options\r\n      const mailOptions = {\r\n        from:\r\n          jobData.server?.from ||\r\n          env.email.MAIL_DEFAULT_FROM ||\r\n          '<EMAIL>',\r\n        to: jobData.email,\r\n        subject: processedSubject,\r\n        html: contentWithTracking,\r\n      };\r\n\r\n      // Gửi email\r\n      const info = await transporter.sendMail(mailOptions);\r\n\r\n      // Track email sent\r\n      await this.emailTrackingService.trackEmailSent(\r\n        jobData.campaignId,\r\n        jobData.audienceId,\r\n        jobData.email,\r\n        jobData.trackingId,\r\n      );\r\n\r\n      this.logger.log(\r\n        `Email sent successfully: ${info.messageId} to ${jobData.email}`,\r\n      );\r\n\r\n      // Update job progress\r\n      await job.updateProgress(100);\r\n    } catch (error) {\r\n      this.logger.error(\r\n        `Error processing email job ${job.id}: ${error.message}`,\r\n        error.stack,\r\n      );\r\n\r\n      // Track email failed\r\n      await this.emailTrackingService.trackEmailFailed(\r\n        jobData.campaignId,\r\n        jobData.audienceId,\r\n        jobData.email,\r\n        jobData.trackingId,\r\n        error,\r\n      );\r\n\r\n      throw error; // Re-throw để Bull có thể retry\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Validate job data\r\n   * @param jobData Dữ liệu job\r\n   * @returns True nếu valid\r\n   */\r\n  private validateJobData(jobData: EmailMarketingJobDto): boolean {\r\n    if (!jobData.campaignId || !jobData.audienceId) {\r\n      this.logger.error('Missing campaignId or audienceId');\r\n      return false;\r\n    }\r\n\r\n    if (!jobData.email || !this.isValidEmail(jobData.email)) {\r\n      this.logger.error(`Invalid email: ${jobData.email}`);\r\n      return false;\r\n    }\r\n\r\n    if (!jobData.trackingId) {\r\n      this.logger.error('Missing trackingId');\r\n      return false;\r\n    }\r\n\r\n    return true;\r\n  }\r\n\r\n  /**\r\n   * Validate email format\r\n   * @param email Email cần validate\r\n   * @returns True nếu email hợp lệ\r\n   */\r\n  private isValidEmail(email: string): boolean {\r\n    const emailRegex = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/;\r\n    return emailRegex.test(email);\r\n  }\r\n\r\n  /**\r\n   * Tạo email transporter\r\n   * @param serverConfig Cấu hình server\r\n   * @returns Nodemailer transporter\r\n   */\r\n  private createTransporter(serverConfig?: any): nodemailer.Transporter {\r\n    // Sử dụng cấu hình từ job nếu có, nếu không dùng cấu hình mặc định\r\n    const config = {\r\n      host: serverConfig?.host || env.email.MAIL_HOST,\r\n      port: serverConfig?.port || Number(env.email.MAIL_PORT),\r\n      secure:\r\n        serverConfig?.secure !== undefined\r\n          ? serverConfig.secure\r\n          : env.email.MAIL_SECURE,\r\n      auth: {\r\n        user: serverConfig?.user || env.email.MAIL_USERNAME,\r\n        pass: serverConfig?.password || env.email.MAIL_PASSWORD,\r\n      },\r\n    };\r\n\r\n    return nodemailer.createTransport(config);\r\n  }\r\n\r\n  /**\r\n   * Xử lý khi job failed\r\n   * @param job Job bị failed\r\n   * @param err Lỗi\r\n   */\r\n  async onFailed(job: Job<EmailMarketingJobDto>, err: Error): Promise<void> {\r\n    this.logger.error(`Job ${job.id} failed: ${err.message}`, err.stack);\r\n\r\n    const jobData = job.data;\r\n\r\n    // Track email failed nếu chưa track\r\n    try {\r\n      await this.emailTrackingService.trackEmailFailed(\r\n        jobData.campaignId,\r\n        jobData.audienceId,\r\n        jobData.email,\r\n        jobData.trackingId,\r\n        err,\r\n      );\r\n    } catch (trackingError) {\r\n      this.logger.error(\r\n        `Error tracking failed email: ${trackingError.message}`,\r\n      );\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Xử lý khi job completed\r\n   * @param job Job đã hoàn thành\r\n   */\r\n  async onCompleted(job: Job<EmailMarketingJobDto>): Promise<void> {\r\n    this.logger.debug(`Job ${job.id} completed successfully`);\r\n  }\r\n\r\n  /**\r\n   * Xử lý khi job active\r\n   * @param job Job đang xử lý\r\n   */\r\n  async onActive(job: Job<EmailMarketingJobDto>): Promise<void> {\r\n    this.logger.debug(`Job ${job.id} started processing`);\r\n  }\r\n}\r\n"], "names": ["EmailMarketingProcessor", "WorkerHost", "process", "job", "jobData", "data", "logger", "log", "id", "campaignId", "validateJobData", "Error", "processedSubject", "emailTemplateService", "injectVariables", "subject", "customFields", "processedContent", "content", "contentWithTracking", "injectTrackingPixel", "trackingId", "env", "app", "BASE_URL", "transporter", "createTransporter", "server", "mailOptions", "from", "email", "MAIL_DEFAULT_FROM", "to", "html", "info", "sendMail", "emailTrackingService", "trackEmailSent", "audienceId", "messageId", "updateProgress", "error", "message", "stack", "trackEmailFailed", "isValidEmail", "emailRegex", "test", "serverConfig", "config", "host", "MAIL_HOST", "port", "Number", "MAIL_PORT", "secure", "undefined", "MAIL_SECURE", "auth", "user", "MAIL_USERNAME", "pass", "password", "MAIL_PASSWORD", "nodemailer", "createTransport", "onFailed", "err", "trackingError", "onCompleted", "debug", "onActive", "constructor", "<PERSON><PERSON>", "name", "EMAIL_MARKETING", "concurrency"], "mappings": ";;;;+BAcaA;;;eAAAA;;;wBAdyB;wBACH;oEAEP;uBACF;0BAEiC;qBACvC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOb,IAAA,AAAMA,0BAAN,MAAMA,gCAAgCC,kBAAU;IAUrD;;;GAGC,GACD,MAAMC,QAAQC,GAA8B,EAAiB;QAC3D,MAAMC,UAAUD,IAAIE,IAAI;QAExB,IAAI,CAACC,MAAM,CAACC,GAAG,CACb,CAAC,gCAAgC,EAAEJ,IAAIK,EAAE,CAAC,cAAc,EAAEJ,QAAQK,UAAU,EAAE;QAGhF,IAAI;YACF,oBAAoB;YACpB,IAAI,CAAC,IAAI,CAACC,eAAe,CAACN,UAAU;gBAClC,MAAM,IAAIO,MAAM;YAClB;YAEA,iDAAiD;YACjD,MAAMC,mBAAmB,IAAI,CAACC,oBAAoB,CAACC,eAAe,CAChEV,QAAQW,OAAO,EACfX,QAAQY,YAAY;YAGtB,MAAMC,mBAAmB,IAAI,CAACJ,oBAAoB,CAACC,eAAe,CAChEV,QAAQc,OAAO,EACfd,QAAQY,YAAY;YAGtB,oCAAoC;YACpC,MAAMG,sBAAsB,IAAI,CAACN,oBAAoB,CAACO,mBAAmB,CACvEH,kBACAb,QAAQiB,UAAU,EAClBC,QAAG,CAACC,GAAG,CAACC,QAAQ,IAAI;YAGtB,wBAAwB;YACxB,MAAMC,cAAc,IAAI,CAACC,iBAAiB,CAACtB,QAAQuB,MAAM;YAEzD,yBAAyB;YACzB,MAAMC,cAAc;gBAClBC,MACEzB,QAAQuB,MAAM,EAAEE,QAChBP,QAAG,CAACQ,KAAK,CAACC,iBAAiB,IAC3B;gBACFC,IAAI5B,QAAQ0B,KAAK;gBACjBf,SAASH;gBACTqB,MAAMd;YACR;YAEA,YAAY;YACZ,MAAMe,OAAO,MAAMT,YAAYU,QAAQ,CAACP;YAExC,mBAAmB;YACnB,MAAM,IAAI,CAACQ,oBAAoB,CAACC,cAAc,CAC5CjC,QAAQK,UAAU,EAClBL,QAAQkC,UAAU,EAClBlC,QAAQ0B,KAAK,EACb1B,QAAQiB,UAAU;YAGpB,IAAI,CAACf,MAAM,CAACC,GAAG,CACb,CAAC,yBAAyB,EAAE2B,KAAKK,SAAS,CAAC,IAAI,EAAEnC,QAAQ0B,KAAK,EAAE;YAGlE,sBAAsB;YACtB,MAAM3B,IAAIqC,cAAc,CAAC;QAC3B,EAAE,OAAOC,OAAO;YACd,IAAI,CAACnC,MAAM,CAACmC,KAAK,CACf,CAAC,2BAA2B,EAAEtC,IAAIK,EAAE,CAAC,EAAE,EAAEiC,MAAMC,OAAO,EAAE,EACxDD,MAAME,KAAK;YAGb,qBAAqB;YACrB,MAAM,IAAI,CAACP,oBAAoB,CAACQ,gBAAgB,CAC9CxC,QAAQK,UAAU,EAClBL,QAAQkC,UAAU,EAClBlC,QAAQ0B,KAAK,EACb1B,QAAQiB,UAAU,EAClBoB;YAGF,MAAMA,OAAO,gCAAgC;QAC/C;IACF;IAEA;;;;GAIC,GACD,AAAQ/B,gBAAgBN,OAA6B,EAAW;QAC9D,IAAI,CAACA,QAAQK,UAAU,IAAI,CAACL,QAAQkC,UAAU,EAAE;YAC9C,IAAI,CAAChC,MAAM,CAACmC,KAAK,CAAC;YAClB,OAAO;QACT;QAEA,IAAI,CAACrC,QAAQ0B,KAAK,IAAI,CAAC,IAAI,CAACe,YAAY,CAACzC,QAAQ0B,KAAK,GAAG;YACvD,IAAI,CAACxB,MAAM,CAACmC,KAAK,CAAC,CAAC,eAAe,EAAErC,QAAQ0B,KAAK,EAAE;YACnD,OAAO;QACT;QAEA,IAAI,CAAC1B,QAAQiB,UAAU,EAAE;YACvB,IAAI,CAACf,MAAM,CAACmC,KAAK,CAAC;YAClB,OAAO;QACT;QAEA,OAAO;IACT;IAEA;;;;GAIC,GACD,AAAQI,aAAaf,KAAa,EAAW;QAC3C,MAAMgB,aAAa;QACnB,OAAOA,WAAWC,IAAI,CAACjB;IACzB;IAEA;;;;GAIC,GACD,AAAQJ,kBAAkBsB,YAAkB,EAA0B;QACpE,mEAAmE;QACnE,MAAMC,SAAS;YACbC,MAAMF,cAAcE,QAAQ5B,QAAG,CAACQ,KAAK,CAACqB,SAAS;YAC/CC,MAAMJ,cAAcI,QAAQC,OAAO/B,QAAG,CAACQ,KAAK,CAACwB,SAAS;YACtDC,QACEP,cAAcO,WAAWC,YACrBR,aAAaO,MAAM,GACnBjC,QAAG,CAACQ,KAAK,CAAC2B,WAAW;YAC3BC,MAAM;gBACJC,MAAMX,cAAcW,QAAQrC,QAAG,CAACQ,KAAK,CAAC8B,aAAa;gBACnDC,MAAMb,cAAcc,YAAYxC,QAAG,CAACQ,KAAK,CAACiC,aAAa;YACzD;QACF;QAEA,OAAOC,YAAWC,eAAe,CAAChB;IACpC;IAEA;;;;GAIC,GACD,MAAMiB,SAAS/D,GAA8B,EAAEgE,GAAU,EAAiB;QACxE,IAAI,CAAC7D,MAAM,CAACmC,KAAK,CAAC,CAAC,IAAI,EAAEtC,IAAIK,EAAE,CAAC,SAAS,EAAE2D,IAAIzB,OAAO,EAAE,EAAEyB,IAAIxB,KAAK;QAEnE,MAAMvC,UAAUD,IAAIE,IAAI;QAExB,oCAAoC;QACpC,IAAI;YACF,MAAM,IAAI,CAAC+B,oBAAoB,CAACQ,gBAAgB,CAC9CxC,QAAQK,UAAU,EAClBL,QAAQkC,UAAU,EAClBlC,QAAQ0B,KAAK,EACb1B,QAAQiB,UAAU,EAClB8C;QAEJ,EAAE,OAAOC,eAAe;YACtB,IAAI,CAAC9D,MAAM,CAACmC,KAAK,CACf,CAAC,6BAA6B,EAAE2B,cAAc1B,OAAO,EAAE;QAE3D;IACF;IAEA;;;GAGC,GACD,MAAM2B,YAAYlE,GAA8B,EAAiB;QAC/D,IAAI,CAACG,MAAM,CAACgE,KAAK,CAAC,CAAC,IAAI,EAAEnE,IAAIK,EAAE,CAAC,uBAAuB,CAAC;IAC1D;IAEA;;;GAGC,GACD,MAAM+D,SAASpE,GAA8B,EAAiB;QAC5D,IAAI,CAACG,MAAM,CAACgE,KAAK,CAAC,CAAC,IAAI,EAAEnE,IAAIK,EAAE,CAAC,mBAAmB,CAAC;IACtD;IA7LAgE,YACE,AAAiB3D,oBAA0C,EAC3D,AAAiBuB,oBAA0C,CAC3D;QACA,KAAK,SAHYvB,uBAAAA,2BACAuB,uBAAAA,2BAJF9B,SAAS,IAAImE,cAAM,CAACzE,wBAAwB0E,IAAI;IAOjE;AAyLF;;;4CAlMqBC;QAAmBC,aAAa"}