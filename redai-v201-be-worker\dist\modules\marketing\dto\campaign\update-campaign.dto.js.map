{"version": 3, "sources": ["../../../../../src/modules/marketing/dto/campaign/update-campaign.dto.ts"], "sourcesContent": ["import {\r\n  IsIn,\r\n  IsN<PERSON>ber,\r\n  IsOptional,\r\n  IsString,\r\n  ValidateNested,\r\n} from 'class-validator';\r\nimport { ApiProperty } from '@nestjs/swagger';\r\nimport { Type } from 'class-transformer';\r\nimport { CampaignPlatform } from './create-campaign.dto';\r\nimport { CampaignServerDto } from './campaign-server.dto';\r\n\r\n/**\r\n * Enum cho các trạng thái chiến dịch\r\n */\r\nexport enum CampaignStatus {\r\n  DRAFT = 'draft',\r\n  SCHEDULED = 'scheduled',\r\n  RUNNING = 'running',\r\n  COMPLETED = 'completed',\r\n  CANCELLED = 'cancelled',\r\n  FAILED = 'failed',\r\n}\r\n\r\n/**\r\n * DTO cho việc cập nhật campaign\r\n */\r\nexport class UpdateCampaignDto {\r\n  /**\r\n   * Tiêu đề chiến dịch\r\n   * @example \"Khuyến mãi tháng 5\"\r\n   */\r\n  @ApiProperty({\r\n    description: 'Tiêu đề chiến dịch',\r\n    example: 'Khuyến mãi tháng 5',\r\n    required: false,\r\n  })\r\n  @IsOptional()\r\n  @IsString({ message: 'Tiêu đề phải là chuỗi' })\r\n  title?: string;\r\n\r\n  /**\r\n   * <PERSON><PERSON> tả chiến dịch\r\n   * @example \"Chiến dịch khuyến mãi dành cho khách hàng VIP\"\r\n   */\r\n  @ApiProperty({\r\n    description: 'Mô tả chiến dịch',\r\n    example: 'Chiến dịch khuyến mãi dành cho khách hàng VIP',\r\n    required: false,\r\n  })\r\n  @IsOptional()\r\n  @IsString({ message: 'Mô tả phải là chuỗi' })\r\n  description?: string;\r\n\r\n  /**\r\n   * Nền tảng gửi\r\n   * @example \"email\"\r\n   */\r\n  @ApiProperty({\r\n    description: 'Nền tảng gửi',\r\n    enum: CampaignPlatform,\r\n    example: CampaignPlatform.EMAIL,\r\n    required: false,\r\n  })\r\n  @IsOptional()\r\n  @IsIn(Object.values(CampaignPlatform), {\r\n    message: `Nền tảng phải là một trong các giá trị: ${Object.values(CampaignPlatform).join(', ')}`,\r\n  })\r\n  platform?: CampaignPlatform;\r\n\r\n  /**\r\n   * Nội dung chiến dịch\r\n   * @example \"<p>Xin chào quý khách,</p><p>Chúng tôi xin gửi đến quý khách chương trình khuyến mãi...</p>\"\r\n   */\r\n  @ApiProperty({\r\n    description: 'Nội dung chiến dịch',\r\n    example:\r\n      '<p>Xin chào quý khách,</p><p>Chúng tôi xin gửi đến quý khách chương trình khuyến mãi...</p>',\r\n    required: false,\r\n  })\r\n  @IsOptional()\r\n  @IsString({ message: 'Nội dung phải là chuỗi' })\r\n  content?: string;\r\n\r\n  /**\r\n   * Thông tin máy chủ gửi\r\n   */\r\n  @ApiProperty({\r\n    description: 'Thông tin máy chủ gửi',\r\n    type: CampaignServerDto,\r\n    required: false,\r\n  })\r\n  @IsOptional()\r\n  @ValidateNested()\r\n  @Type(() => CampaignServerDto)\r\n  server?: CampaignServerDto;\r\n\r\n  /**\r\n   * Thời gian dự kiến gửi chiến dịch (Unix timestamp)\r\n   * @example 1619171200\r\n   */\r\n  @ApiProperty({\r\n    description: 'Thời gian dự kiến gửi chiến dịch (Unix timestamp)',\r\n    example: 1619171200,\r\n    required: false,\r\n  })\r\n  @IsOptional()\r\n  @IsNumber({}, { message: 'Thời gian dự kiến phải là số' })\r\n  scheduledAt?: number;\r\n\r\n  /**\r\n   * Tiêu đề email (chỉ áp dụng cho chiến dịch email)\r\n   * @example \"Khuyến mãi đặc biệt dành cho bạn\"\r\n   */\r\n  @ApiProperty({\r\n    description: 'Tiêu đề email (chỉ áp dụng cho chiến dịch email)',\r\n    example: 'Khuyến mãi đặc biệt dành cho bạn',\r\n    required: false,\r\n  })\r\n  @IsOptional()\r\n  @IsString({ message: 'Tiêu đề email phải là chuỗi' })\r\n  subject?: string;\r\n\r\n  /**\r\n   * Trạng thái chiến dịch\r\n   * @example \"draft\"\r\n   */\r\n  @ApiProperty({\r\n    description: 'Trạng thái chiến dịch',\r\n    enum: CampaignStatus,\r\n    example: CampaignStatus.DRAFT,\r\n    required: false,\r\n  })\r\n  @IsOptional()\r\n  @IsIn(Object.values(CampaignStatus), {\r\n    message: `Trạng thái phải là một trong các giá trị: ${Object.values(CampaignStatus).join(', ')}`,\r\n  })\r\n  status?: CampaignStatus;\r\n\r\n  /**\r\n   * ID của segment\r\n   * @example 1\r\n   */\r\n  @ApiProperty({\r\n    description: 'ID của segment',\r\n    example: 1,\r\n    required: false,\r\n  })\r\n  @IsOptional()\r\n  @IsNumber({}, { message: 'ID segment phải là số' })\r\n  segmentId?: number;\r\n\r\n  /**\r\n   * Danh sách ID của audience\r\n   * @example [1, 2, 3]\r\n   */\r\n  @ApiProperty({\r\n    description: 'Danh sách ID của audience',\r\n    example: [1, 2, 3],\r\n    required: false,\r\n    type: [Number],\r\n  })\r\n  @IsOptional()\r\n  audienceIds?: number[];\r\n}\r\n"], "names": ["CampaignStatus", "UpdateCampaignDto", "description", "example", "required", "message", "enum", "CampaignPlatform", "EMAIL", "values", "Object", "join", "type", "CampaignServerDto", "Number"], "mappings": ";;;;;;;;;;;QAeYA;eAAAA;;QAYCC;eAAAA;;;gCArBN;yBACqB;kCACP;mCACY;mCACC;;;;;;;;;;AAK3B,IAAA,AAAKD,wCAAAA;;;;;;;WAAAA;;AAYL,IAAA,AAAMC,oBAAN,MAAMA;AAyIb;;;QAnIIC,aAAa;QACbC,SAAS;QACTC,UAAU;;;;QAGAC,SAAS;;;;;;QAQnBH,aAAa;QACbC,SAAS;QACTC,UAAU;;;;QAGAC,SAAS;;;;;;QAQnBH,aAAa;QACbI,MAAMC,mCAAgB;QACtBJ,SAASI,mCAAgB,CAACC,KAAK;QAC/BJ,UAAU;;;qCAGCK;QACXJ,SAAS,CAAC,wCAAwC,EAAEK,OAAOD,MAAM,CAACF,mCAAgB,EAAEI,IAAI,CAAC,OAAO;;;;;;QAShGT,aAAa;QACbC,SACE;QACFC,UAAU;;;;QAGAC,SAAS;;;;;;QAOnBH,aAAa;QACbU,MAAMC,oCAAiB;QACvBT,UAAU;;;;oCAIAS,oCAAiB;;;;;QAQ3BX,aAAa;QACbC,SAAS;QACTC,UAAU;;;;QAGIC,SAAS;;;;;;QAQvBH,aAAa;QACbC,SAAS;QACTC,UAAU;;;;QAGAC,SAAS;;;;;;QAQnBH,aAAa;QACbI,MAAMN;QACNG,OAAO;QACPC,UAAU;;;qCAGCK;QACXJ,SAAS,CAAC,0CAA0C,EAAEK,OAAOD,MAAM,CAACT,gBAAgBW,IAAI,CAAC,OAAO;;;;;;QAShGT,aAAa;QACbC,SAAS;QACTC,UAAU;;;;QAGIC,SAAS;;;;;;QAQvBH,aAAa;QACbC,SAAS;YAAC;YAAG;YAAG;SAAE;QAClBC,UAAU;QACVQ,MAAM;YAACE;SAAO"}