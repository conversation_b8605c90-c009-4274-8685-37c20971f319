"use strict";
Object.defineProperty(exports, "__esModule", {
    value: true
});
Object.defineProperty(exports, "GoogleAdsCampaign", {
    enumerable: true,
    get: function() {
        return GoogleAdsCampaign;
    }
});
const _typeorm = require("typeorm");
function _ts_decorate(decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for(var i = decorators.length - 1; i >= 0; i--)if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
}
function _ts_metadata(k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
}
let GoogleAdsCampaign = class GoogleAdsCampaign {
};
_ts_decorate([
    (0, _typeorm.PrimaryGeneratedColumn)({
        name: 'id',
        type: 'bigint'
    }),
    _ts_metadata("design:type", Number)
], GoogleAdsCampaign.prototype, "id", void 0);
_ts_decorate([
    (0, _typeorm.Column)({
        name: 'user_id',
        nullable: false,
        comment: 'ID của người dùng'
    }),
    _ts_metadata("design:type", Number)
], GoogleAdsCampaign.prototype, "userId", void 0);
_ts_decorate([
    (0, _typeorm.Column)({
        name: 'account_id',
        nullable: false,
        comment: 'ID của tài khoản Google Ads'
    }),
    _ts_metadata("design:type", Number)
], GoogleAdsCampaign.prototype, "accountId", void 0);
_ts_decorate([
    (0, _typeorm.Column)({
        name: 'campaign_id',
        nullable: false,
        comment: 'ID của chiến dịch trên Google Ads'
    }),
    _ts_metadata("design:type", String)
], GoogleAdsCampaign.prototype, "campaignId", void 0);
_ts_decorate([
    (0, _typeorm.Column)({
        name: 'name',
        length: 255,
        nullable: false,
        comment: 'Tên chiến dịch'
    }),
    _ts_metadata("design:type", String)
], GoogleAdsCampaign.prototype, "name", void 0);
_ts_decorate([
    (0, _typeorm.Column)({
        name: 'status',
        length: 20,
        nullable: false,
        comment: 'Trạng thái chiến dịch'
    }),
    _ts_metadata("design:type", String)
], GoogleAdsCampaign.prototype, "status", void 0);
_ts_decorate([
    (0, _typeorm.Column)({
        name: 'type',
        length: 50,
        nullable: false,
        comment: 'Loại chiến dịch (SEARCH, DISPLAY, ...)'
    }),
    _ts_metadata("design:type", String)
], GoogleAdsCampaign.prototype, "type", void 0);
_ts_decorate([
    (0, _typeorm.Column)({
        name: 'budget',
        type: 'bigint',
        nullable: false,
        comment: 'Ngân sách hàng ngày (micro amount)'
    }),
    _ts_metadata("design:type", Number)
], GoogleAdsCampaign.prototype, "budget", void 0);
_ts_decorate([
    (0, _typeorm.Column)({
        name: 'start_date',
        length: 10,
        nullable: true,
        comment: 'Ngày bắt đầu (YYYY-MM-DD)'
    }),
    _ts_metadata("design:type", String)
], GoogleAdsCampaign.prototype, "startDate", void 0);
_ts_decorate([
    (0, _typeorm.Column)({
        name: 'end_date',
        length: 10,
        nullable: true,
        comment: 'Ngày kết thúc (YYYY-MM-DD)'
    }),
    _ts_metadata("design:type", String)
], GoogleAdsCampaign.prototype, "endDate", void 0);
_ts_decorate([
    (0, _typeorm.Column)({
        name: 'user_campaign_id',
        type: 'bigint',
        nullable: true,
        comment: 'ID của chiến dịch marketing'
    }),
    _ts_metadata("design:type", Number)
], GoogleAdsCampaign.prototype, "userCampaignId", void 0);
_ts_decorate([
    (0, _typeorm.Column)({
        name: 'created_at',
        type: 'bigint',
        nullable: false,
        comment: 'Thời gian tạo'
    }),
    _ts_metadata("design:type", Number)
], GoogleAdsCampaign.prototype, "createdAt", void 0);
_ts_decorate([
    (0, _typeorm.Column)({
        name: 'updated_at',
        type: 'bigint',
        nullable: true,
        comment: 'Thời gian cập nhật'
    }),
    _ts_metadata("design:type", Number)
], GoogleAdsCampaign.prototype, "updatedAt", void 0);
GoogleAdsCampaign = _ts_decorate([
    (0, _typeorm.Entity)('google_ads_campaigns')
], GoogleAdsCampaign);

//# sourceMappingURL=google-ads-campaign.entity.js.map