{"version": 3, "sources": ["../../../../src/modules/marketing/entities/zalo-zns-message.entity.ts"], "sourcesContent": ["import { Column, Entity, PrimaryGeneratedColumn } from 'typeorm';\r\n\r\n/**\r\n * Entity đại diện cho bảng zalo_zns_messages trong cơ sở dữ liệu\r\n * Lưu trữ lịch sử các tin nhắn ZNS đã gửi\r\n */\r\n@Entity('zalo_zns_messages')\r\nexport class ZaloZnsMessage {\r\n  /**\r\n   * ID tự động tăng\r\n   */\r\n  @PrimaryGeneratedColumn({ name: 'id' })\r\n  id: number;\r\n\r\n  /**\r\n   * ID người dùng gửi tin nhắn\r\n   */\r\n  @Column({ name: 'user_id' })\r\n  userId: number;\r\n\r\n  /**\r\n   * ID của Official Account\r\n   */\r\n  @Column({ name: 'oa_id', length: 50 })\r\n  oaId: string;\r\n\r\n  /**\r\n   * ID của template\r\n   */\r\n  @Column({ name: 'template_id', length: 50 })\r\n  templateId: string;\r\n\r\n  /**\r\n   * Số điện thoại người nhận\r\n   */\r\n  @Column({ name: 'phone', length: 20 })\r\n  phone: string;\r\n\r\n  /**\r\n   * ID của tin nhắn trên Zalo\r\n   */\r\n  @Column({ name: 'message_id', length: 50, nullable: true })\r\n  messageId: string;\r\n\r\n  /**\r\n   * ID giao dịch\r\n   */\r\n  @Column({ name: 'tracking_id', length: 50 })\r\n  trackingId: string;\r\n\r\n  /**\r\n   * Dữ liệu đã gửi cho template (JSON)\r\n   */\r\n  @Column({ name: 'template_data', type: 'jsonb' })\r\n  templateData: any;\r\n\r\n  /**\r\n   * Trạng thái tin nhắn (pending, delivered, failed)\r\n   */\r\n  @Column({ name: 'status', length: 20, default: 'pending' })\r\n  status: string;\r\n\r\n  /**\r\n   * Thời điểm gửi thành công (Unix timestamp)\r\n   */\r\n  @Column({ name: 'delivered_time', type: 'bigint', nullable: true })\r\n  deliveredTime: number;\r\n\r\n  /**\r\n   * Thời điểm tạo (Unix timestamp)\r\n   */\r\n  @Column({ name: 'created_at', type: 'bigint' })\r\n  createdAt: number;\r\n\r\n  /**\r\n   * Thời điểm cập nhật (Unix timestamp)\r\n   */\r\n  @Column({ name: 'updated_at', type: 'bigint' })\r\n  updatedAt: number;\r\n}\r\n"], "names": ["ZaloZnsMessage", "name", "length", "nullable", "type", "default"], "mappings": ";;;;+BAOaA;;;eAAAA;;;yBAP0C;;;;;;;;;;AAOhD,IAAA,AAAMA,iBAAN,MAAMA;AAwEb;;;QApE4BC,MAAM;;;;;;QAMtBA,MAAM;;;;;;QAMNA,MAAM;QAASC,QAAQ;;;;;;QAMvBD,MAAM;QAAeC,QAAQ;;;;;;QAM7BD,MAAM;QAASC,QAAQ;;;;;;QAMvBD,MAAM;QAAcC,QAAQ;QAAIC,UAAU;;;;;;QAM1CF,MAAM;QAAeC,QAAQ;;;;;;QAM7BD,MAAM;QAAiBG,MAAM;;;;;;QAM7BH,MAAM;QAAUC,QAAQ;QAAIG,SAAS;;;;;;QAMrCJ,MAAM;QAAkBG,MAAM;QAAUD,UAAU;;;;;;QAMlDF,MAAM;QAAcG,MAAM;;;;;;QAM1BH,MAAM;QAAcG,MAAM"}