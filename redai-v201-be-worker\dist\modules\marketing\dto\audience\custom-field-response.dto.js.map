{"version": 3, "sources": ["../../../../../src/modules/marketing/dto/audience/custom-field-response.dto.ts"], "sourcesContent": ["import { ApiProperty } from '@nestjs/swagger';\r\nimport { CustomFieldType } from './create-custom-field.dto';\r\n\r\n/**\r\n * DTO cho phản hồi thông tin trường tùy chỉnh\r\n */\r\nexport class CustomFieldResponseDto {\r\n  /**\r\n   * ID của trường tùy chỉnh\r\n   * @example 1\r\n   */\r\n  @ApiProperty({\r\n    description: 'ID của trường tùy chỉnh',\r\n    example: 1,\r\n  })\r\n  id: number;\r\n\r\n  /**\r\n   * ID của audience\r\n   * @example 1\r\n   */\r\n  @ApiProperty({\r\n    description: 'ID của audience',\r\n    example: 1,\r\n  })\r\n  audienceId: number;\r\n\r\n  /**\r\n   * Tên trường\r\n   * @example \"Địa chỉ\"\r\n   */\r\n  @ApiProperty({\r\n    description: 'Tên trường',\r\n    example: 'Địa chỉ',\r\n  })\r\n  fieldName: string;\r\n\r\n  /**\r\n   * Giá trị trường\r\n   * @example \"Hà Nội, Việt Nam\"\r\n   */\r\n  @ApiProperty({\r\n    description: 'Gi<PERSON> trị trường',\r\n    example: '<PERSON><PERSON>ộ<PERSON>, Vi<PERSON>t Nam',\r\n  })\r\n  fieldValue: any;\r\n\r\n  /**\r\n   * <PERSON>ể<PERSON> dữ liệu của trường\r\n   * @example \"text\"\r\n   */\r\n  @ApiProperty({\r\n    description: 'Kiểu dữ liệu của trường',\r\n    enum: CustomFieldType,\r\n    example: CustomFieldType.TEXT,\r\n  })\r\n  fieldType: CustomFieldType;\r\n\r\n  /**\r\n   * Thời gian tạo (Unix timestamp)\r\n   * @example 1619171200\r\n   */\r\n  @ApiProperty({\r\n    description: 'Thời gian tạo (Unix timestamp)',\r\n    example: 1619171200,\r\n  })\r\n  createdAt: number;\r\n\r\n  /**\r\n   * Thời gian cập nhật (Unix timestamp)\r\n   * @example 1619171200\r\n   */\r\n  @ApiProperty({\r\n    description: 'Thời gian cập nhật (Unix timestamp)',\r\n    example: 1619171200,\r\n  })\r\n  updatedAt: number;\r\n}\r\n"], "names": ["CustomFieldResponseDto", "description", "example", "enum", "CustomFieldType", "TEXT"], "mappings": ";;;;+BAMaA;;;eAAAA;;;yBANe;sCACI;;;;;;;;;;AAKzB,IAAA,AAAMA,yBAAN,MAAMA;AAuEb;;;QAjEIC,aAAa;QACbC,SAAS;;;;;;QASTD,aAAa;QACbC,SAAS;;;;;;QASTD,aAAa;QACbC,SAAS;;;;;;QASTD,aAAa;QACbC,SAAS;;;;;;QASTD,aAAa;QACbE,MAAMC,qCAAe;QACrBF,SAASE,qCAAe,CAACC,IAAI;;;;;;QAS7BJ,aAAa;QACbC,SAAS;;;;;;QASTD,aAAa;QACbC,SAAS"}