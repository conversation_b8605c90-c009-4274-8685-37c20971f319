{"version": 3, "sources": ["../../../../src/shared/services/sms/fpt-sms-provider.service.ts"], "sourcesContent": ["import { Injectable } from '@nestjs/common';\r\nimport { HttpService } from '@nestjs/axios';\r\nimport { ConfigService } from '@nestjs/config';\r\nimport { firstValueFrom } from 'rxjs';\r\nimport { catchError } from 'rxjs/operators';\r\nimport { BaseSmsProvider } from './base-sms-provider.service';\r\nimport {\r\n  SmsResponse,\r\n  BulkSmsResponse,\r\n  MessageStatusResponse,\r\n  ConnectionTestResponse,\r\n  MessageStatus,\r\n} from './sms-provider.interface';\r\nimport { env } from '../../../config/env';\r\n\r\n/**\r\n * Interface cho cấu hình FPT SMS\r\n */\r\nexport interface FptSmsConfig {\r\n  /**\r\n   * Client ID dùng cho xác thực OAuth\r\n   */\r\n  clientId: string;\r\n\r\n  /**\r\n   * Client Secret dùng cho xác thực OAuth\r\n   */\r\n  clientSecret: string;\r\n\r\n  /**\r\n   * Phạm vi quyền truy cập cho xác thực OAuth\r\n   */\r\n  scope?: string;\r\n\r\n  /**\r\n   * URL cơ sở của API FPT SMS\r\n   */\r\n  apiUrl?: string;\r\n\r\n  /**\r\n   * Brandname mặc định\r\n   */\r\n  brandName?: string;\r\n}\r\n\r\n/**\r\n * Interface cho response của API gửi tin nhắn CSKH quốc tế\r\n */\r\nexport interface FptInternationalSmsResponse {\r\n  /**\r\n   * ID của tin nhắn đã gửi\r\n   */\r\n  MessageId: number;\r\n\r\n  /**\r\n   * Số điện thoại đã gửi tin\r\n   */\r\n  Phone: string;\r\n\r\n  /**\r\n   * Tên brandname đã gửi tin\r\n   */\r\n  BrandName: string;\r\n\r\n  /**\r\n   * Nội dung tin nhắn đã gửi\r\n   */\r\n  Message: string;\r\n\r\n  /**\r\n   * ID của đối tác đã gửi tin\r\n   */\r\n  PartnerId: string;\r\n\r\n  /**\r\n   * Nhà mạng của số điện thoại\r\n   */\r\n  Telco: string;\r\n}\r\n\r\n/**\r\n * Interface cho options của API gửi tin nhắn CSKH quốc tế\r\n */\r\nexport interface FptInternationalSmsOptions {\r\n  /**\r\n   * Brandname đã đăng ký với FPT\r\n   */\r\n  brandName?: string;\r\n\r\n  /**\r\n   * Request ID tùy chọn để phân biệt nguồn gửi\r\n   */\r\n  requestId?: string;\r\n}\r\n\r\n/**\r\n * Interface cho request khởi tạo campaign\r\n */\r\nexport interface FptCreateCampaignRequest {\r\n  /**\r\n   * Tên campaign muốn khởi tạo (không được trùng nhau, chỉ sử dụng một lần)\r\n   */\r\n  campaignName: string;\r\n\r\n  /**\r\n   * Brandname đã đăng ký với FPT\r\n   */\r\n  brandName: string;\r\n\r\n  /**\r\n   * Nội dung tin nhắn cần gửi đi\r\n   */\r\n  message: string;\r\n\r\n  /**\r\n   * Thời gian dự kiến gửi tin (định dạng: yyyy-mm-dd HH:ii)\r\n   */\r\n  scheduleTime: string;\r\n\r\n  /**\r\n   * Hạn mức gửi tin của Campaign\r\n   */\r\n  quota: number;\r\n}\r\n\r\n/**\r\n * Interface cho response khởi tạo campaign\r\n */\r\nexport interface FptCreateCampaignResponse {\r\n  /**\r\n   * Mã của campaign khi khởi tạo thành công\r\n   */\r\n  CampaignCode: string;\r\n}\r\n\r\n/**\r\n * Service tích hợp với API FPT SMS\r\n */\r\n@Injectable()\r\nexport class FptSmsProvider extends BaseSmsProvider {\r\n  readonly providerName = 'FPT SMS';\r\n\r\n  private readonly apiUrl: string;\r\n  private readonly clientId: string;\r\n  private readonly clientSecret: string;\r\n  private readonly scope: string;\r\n  private readonly defaultBrandName: string;\r\n\r\n  // Token truy cập và thời gian hết hạn của nó\r\n  private accessToken: string | null = null;\r\n  private tokenExpiration: Date | null = null;\r\n\r\n  constructor(\r\n    private readonly httpService: HttpService,\r\n    private readonly configService: ConfigService,\r\n  ) {\r\n    super('FptSmsProvider');\r\n\r\n    // Tải cấu hình từ biến môi trường hoặc sử dụng giá trị mặc định\r\n    this.clientId = env.fpt.FPT_SMS_CLIENT_ID || '';\r\n    this.clientSecret = env.fpt.FPT_SMS_CLIENT_SECRET || '';\r\n    this.scope = env.fpt.FPT_SMS_SCOPE || 'send_brandname_otp send_brandname';\r\n    this.apiUrl = env.fpt.FPT_SMS_API_URL || 'http://api.fpt.net/api';\r\n    this.defaultBrandName = env.fpt.FPT_SMS_BRANDNAME || '';\r\n  }\r\n\r\n  /**\r\n   * Lấy token truy cập hợp lệ cho API FPT SMS\r\n   * @param forceRefresh Buộc làm mới token ngay cả khi nó vẫn còn hợp lệ\r\n   * @returns Token truy cập\r\n   */\r\n  private async getAccessToken(forceRefresh = false): Promise<string> {\r\n    // Kiểm tra xem token có còn hợp lệ không\r\n    const now = new Date();\r\n    if (\r\n      !forceRefresh &&\r\n      this.accessToken &&\r\n      this.tokenExpiration &&\r\n      this.tokenExpiration > now\r\n    ) {\r\n      // Token vẫn còn hợp lệ, trả về token\r\n      return this.accessToken;\r\n    }\r\n\r\n    try {\r\n      this.logger.debug('Lấy token truy cập mới cho FPT SMS');\r\n\r\n      const response = await firstValueFrom(\r\n        this.httpService\r\n          .post(\r\n            `${this.apiUrl}/oauth2/token`,\r\n            {\r\n              grant_type: 'client_credentials',\r\n              client_id: this.clientId,\r\n              client_secret: this.clientSecret,\r\n              scope: this.scope,\r\n              session_id: Date.now().toString(),\r\n            },\r\n            {\r\n              headers: {\r\n                'Content-Type': 'application/json',\r\n              },\r\n            },\r\n          )\r\n          .pipe(\r\n            catchError((error) => {\r\n              this.logger.error(\r\n                `Erreur lors de l'obtention du token d'accès FPT SMS: ${error.message}`,\r\n                error.stack,\r\n              );\r\n              throw error;\r\n            }),\r\n          ),\r\n      );\r\n\r\n      if (response.data.error) {\r\n        throw new Error(`Erreur FPT SMS: ${response.data.error_description}`);\r\n      }\r\n\r\n      // Lưu token và tính toán thời gian hết hạn của nó\r\n      this.accessToken = response.data.access_token;\r\n      this.tokenExpiration = new Date(\r\n        now.getTime() + response.data.expires_in * 1000,\r\n      );\r\n\r\n      // Kiểm tra token không phải là null\r\n      if (!this.accessToken) {\r\n        throw new Error('Token truy cập FPT SMS là null');\r\n      }\r\n\r\n      return this.accessToken;\r\n    } catch (error) {\r\n      this.logger.error(\r\n        `Lỗi khi lấy token truy cập FPT SMS: ${error.message}`,\r\n        error.stack,\r\n      );\r\n      throw error;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Gửi tin nhắn SMS đến một số điện thoại qua FPT SMS\r\n   * @param phoneNumber Số điện thoại của người nhận\r\n   * @param message Nội dung tin nhắn\r\n   * @param options Các tùy chọn bổ sung (brandName)\r\n   * @returns Promise chứa ID tin nhắn hoặc lỗi\r\n   */\r\n  async sendSms(\r\n    phoneNumber: string,\r\n    message: string,\r\n    options?: any,\r\n  ): Promise<SmsResponse> {\r\n    try {\r\n      this.logger.debug(`Gửi SMS đến ${phoneNumber} qua FPT SMS`);\r\n\r\n      const brandName = options?.brandName || this.defaultBrandName;\r\n\r\n      if (!brandName) {\r\n        throw new Error('Tham số \"brandName\" là bắt buộc đối với FPT SMS');\r\n      }\r\n\r\n      // Lấy token truy cập hợp lệ\r\n      const accessToken = await this.getAccessToken();\r\n\r\n      // Định dạng số điện thoại\r\n      const formattedPhoneNumber = this.formatPhoneNumber(phoneNumber);\r\n      // Xóa dấu + cho FPT SMS\r\n      const cleanedPhoneNumber = formattedPhoneNumber.startsWith('+')\r\n        ? formattedPhoneNumber.substring(1)\r\n        : formattedPhoneNumber;\r\n\r\n      // Gửi SMS qua FPT SMS\r\n      const response = await firstValueFrom(\r\n        this.httpService\r\n          .post(\r\n            `${this.apiUrl}/push-brandname-otp`,\r\n            {\r\n              access_token: accessToken,\r\n              session_id: Date.now().toString(),\r\n              BrandName: brandName,\r\n              Phone: cleanedPhoneNumber,\r\n              Message: message,\r\n              Quota: options?.quota || 1,\r\n            },\r\n            {\r\n              headers: {\r\n                'Content-Type': 'application/json',\r\n              },\r\n            },\r\n          )\r\n          .pipe(\r\n            catchError((error) => {\r\n              this.logger.error(\r\n                `Erreur lors de l'envoi du SMS via FPT SMS: ${error.message}`,\r\n                error.stack,\r\n              );\r\n              throw error;\r\n            }),\r\n          ),\r\n      );\r\n\r\n      if (response.data.errorid === 0) {\r\n        return {\r\n          success: true,\r\n          messageId: response.data.requestid,\r\n          rawResponse: response.data,\r\n        };\r\n      } else {\r\n        return {\r\n          success: false,\r\n          errorCode: response.data.errorid.toString(),\r\n          errorMessage: response.data.errordes || 'Lỗi không xác định',\r\n          rawResponse: response.data,\r\n        };\r\n      }\r\n    } catch (error) {\r\n      this.logger.error(\r\n        `Lỗi khi gửi SMS qua FPT SMS: ${error.message}`,\r\n        error.stack,\r\n      );\r\n      return {\r\n        success: false,\r\n        errorMessage: error.message || 'Lỗi không xác định',\r\n      };\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Kiểm tra trạng thái của tin nhắn đã gửi qua FPT SMS\r\n   * @param messageId ID của tin nhắn cần kiểm tra\r\n   * @returns Promise chứa trạng thái của tin nhắn\r\n   */\r\n  async checkMessageStatus(messageId: string): Promise<MessageStatusResponse> {\r\n    try {\r\n      this.logger.debug(\r\n        `Kiểm tra trạng thái tin nhắn ${messageId} qua FPT SMS`,\r\n      );\r\n\r\n      // Lấy token truy cập hợp lệ\r\n      const accessToken = await this.getAccessToken();\r\n\r\n      // Kiểm tra trạng thái tin nhắn qua FPT SMS\r\n      const response = await firstValueFrom(\r\n        this.httpService\r\n          .post(\r\n            `${this.apiUrl}/dlr-otp-recheck`,\r\n            {\r\n              access_token: accessToken,\r\n              session_id: Date.now().toString(),\r\n              RequestCode: messageId,\r\n            },\r\n            {\r\n              headers: {\r\n                'Content-Type': 'application/json',\r\n              },\r\n            },\r\n          )\r\n          .pipe(\r\n            catchError((error) => {\r\n              this.logger.error(\r\n                `Erreur lors de la vérification du statut du message via FPT SMS: ${error.message}`,\r\n                error.stack,\r\n              );\r\n              throw error;\r\n            }),\r\n          ),\r\n      );\r\n\r\n      // Xác định trạng thái dựa trên phản hồi\r\n      let status = MessageStatus.UNKNOWN;\r\n      let details = '';\r\n\r\n      if (response.data.errorid === 0) {\r\n        status = MessageStatus.DELIVERED;\r\n      } else {\r\n        status = MessageStatus.FAILED;\r\n        details = response.data.errordes || 'Lỗi không xác định';\r\n      }\r\n\r\n      return {\r\n        messageId,\r\n        status,\r\n        updatedAt: new Date(),\r\n        details,\r\n        rawResponse: response.data,\r\n      };\r\n    } catch (error) {\r\n      this.logger.error(\r\n        `Lỗi khi kiểm tra trạng thái tin nhắn qua FPT SMS: ${error.message}`,\r\n        error.stack,\r\n      );\r\n      return {\r\n        messageId,\r\n        status: MessageStatus.UNKNOWN,\r\n        updatedAt: new Date(),\r\n        details: error.message || 'Lỗi không xác định',\r\n      };\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Gửi tin nhắn SMS với brandname qua FPT SMS\r\n   * @param phoneNumber Số điện thoại của người nhận\r\n   * @param message Nội dung tin nhắn\r\n   * @param brandname Tên thương hiệu sử dụng làm người gửi\r\n   * @param options Các tùy chọn bổ sung\r\n   * @returns Promise chứa ID tin nhắn hoặc lỗi\r\n   */\r\n  async sendBrandnameSms(\r\n    phoneNumber: string,\r\n    message: string,\r\n    brandname: string,\r\n    options?: any,\r\n  ): Promise<SmsResponse> {\r\n    try {\r\n      this.logger.debug(`Gửi SMS brandname đến ${phoneNumber} qua FPT SMS`);\r\n\r\n      // Lấy token truy cập hợp lệ\r\n      const accessToken = await this.getAccessToken();\r\n\r\n      // Định dạng số điện thoại\r\n      const formattedPhoneNumber = this.formatPhoneNumber(phoneNumber);\r\n      // Xóa dấu + cho FPT SMS\r\n      const cleanedPhoneNumber = formattedPhoneNumber.startsWith('+')\r\n        ? formattedPhoneNumber.substring(1)\r\n        : formattedPhoneNumber;\r\n\r\n      // Tạo chiến dịch cho SMS brandname\r\n      const campaignResponse = await firstValueFrom(\r\n        this.httpService\r\n          .post(\r\n            `${this.apiUrl}/create-campaign`,\r\n            {\r\n              access_token: accessToken,\r\n              session_id: Date.now().toString(),\r\n              CampaignName: options?.campaignName || `Campaign_${Date.now()}`,\r\n              BrandName: brandname,\r\n              Message: message,\r\n              ScheduleTime:\r\n                options?.scheduleTime || this.formatScheduleTime(new Date()),\r\n              Quota: options?.quota || 1,\r\n            },\r\n            {\r\n              headers: {\r\n                'Content-Type': 'application/json',\r\n              },\r\n            },\r\n          )\r\n          .pipe(\r\n            catchError((error) => {\r\n              this.logger.error(\r\n                `Erreur lors de la création de la campagne FPT SMS: ${error.message}`,\r\n                error.stack,\r\n              );\r\n              throw error;\r\n            }),\r\n          ),\r\n      );\r\n\r\n      if (campaignResponse.data.errorid !== 0) {\r\n        return {\r\n          success: false,\r\n          errorCode: campaignResponse.data.errorid.toString(),\r\n          errorMessage:\r\n            campaignResponse.data.errordes || 'Lỗi khi tạo chiến dịch',\r\n          rawResponse: campaignResponse.data,\r\n        };\r\n      }\r\n\r\n      // Gửi SMS brandname\r\n      const adsResponse = await firstValueFrom(\r\n        this.httpService\r\n          .post(\r\n            `${this.apiUrl}/push-ads`,\r\n            {\r\n              access_token: accessToken,\r\n              session_id: Date.now().toString(),\r\n              CampaignCode: campaignResponse.data.campaigncode,\r\n              PhoneList: cleanedPhoneNumber,\r\n            },\r\n            {\r\n              headers: {\r\n                'Content-Type': 'application/json',\r\n              },\r\n            },\r\n          )\r\n          .pipe(\r\n            catchError((error) => {\r\n              this.logger.error(\r\n                `Erreur lors de l'envoi du SMS brandname via FPT SMS: ${error.message}`,\r\n                error.stack,\r\n              );\r\n              throw error;\r\n            }),\r\n          ),\r\n      );\r\n\r\n      if (adsResponse.data.errorid === 0) {\r\n        return {\r\n          success: true,\r\n          messageId: adsResponse.data.requestid,\r\n          rawResponse: {\r\n            campaign: campaignResponse.data,\r\n            ads: adsResponse.data,\r\n          },\r\n        };\r\n      } else {\r\n        return {\r\n          success: false,\r\n          errorCode: adsResponse.data.errorid.toString(),\r\n          errorMessage: adsResponse.data.errordes || 'Lỗi không xác định',\r\n          rawResponse: {\r\n            campaign: campaignResponse.data,\r\n            ads: adsResponse.data,\r\n          },\r\n        };\r\n      }\r\n    } catch (error) {\r\n      this.logger.error(\r\n        `Lỗi khi gửi SMS brandname qua FPT SMS: ${error.message}`,\r\n        error.stack,\r\n      );\r\n      return {\r\n        success: false,\r\n        errorMessage: error.message || 'Lỗi không xác định',\r\n      };\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Gửi tin nhắn SMS OTP qua FPT SMS\r\n   * @param phoneNumber Số điện thoại của người nhận\r\n   * @param otpCode Mã OTP cần gửi\r\n   * @param options Các tùy chọn bổ sung (brandName, template)\r\n   * @returns Promise chứa ID tin nhắn hoặc lỗi\r\n   */\r\n  async sendOtp(\r\n    phoneNumber: string,\r\n    otpCode: string,\r\n    options?: any,\r\n  ): Promise<SmsResponse> {\r\n    this.logger.debug(\r\n      `Gửi mã OTP ${otpCode} đến số điện thoại ${phoneNumber} qua FPT SMS`,\r\n    );\r\n\r\n    // Xây dựng tin nhắn OTP\r\n    const message = options?.template\r\n      ? options.template.replace('{code}', otpCode)\r\n      : `Mã xác thực của bạn là: ${otpCode}`;\r\n\r\n    // Sử dụng API push-brandname-otp để gửi OTP\r\n    return this.sendSms(phoneNumber, message, options);\r\n  }\r\n\r\n  /**\r\n   * Khởi tạo campaign cho SMS brandname qua FPT SMS\r\n   * @param request Thông tin campaign cần tạo\r\n   * @returns Promise chứa mã campaign hoặc lỗi\r\n   */\r\n  async createCampaign(\r\n    request: FptCreateCampaignRequest,\r\n  ): Promise<{ success: boolean; campaignCode?: string; errorMessage?: string; errorCode?: string; rawResponse?: any }> {\r\n    try {\r\n      this.logger.debug(\r\n        `Khởi tạo campaign \"${request.campaignName}\" qua FPT SMS`,\r\n      );\r\n\r\n      // Lấy token truy cập hợp lệ\r\n      const accessToken = await this.getAccessToken();\r\n\r\n      // Chuẩn bị payload theo tài liệu API\r\n      const payload = {\r\n        access_token: accessToken,\r\n        session_id: Date.now().toString(),\r\n        CampaignName: request.campaignName,\r\n        BrandName: request.brandName,\r\n        Message: request.message,\r\n        ScheduleTime: request.scheduleTime,\r\n        Quota: request.quota,\r\n      };\r\n\r\n      this.logger.debug('Payload gửi tạo campaign:', payload);\r\n\r\n      // Gửi request tạo campaign\r\n      const response = await firstValueFrom(\r\n        this.httpService\r\n          .post(`${this.apiUrl}/create-campaign`, payload, {\r\n            headers: {\r\n              'Content-Type': 'application/json',\r\n            },\r\n          })\r\n          .pipe(\r\n            catchError((error) => {\r\n              this.logger.error(\r\n                `Lỗi khi tạo campaign qua FPT SMS: ${error.message}`,\r\n                error.stack,\r\n              );\r\n              throw error;\r\n            }),\r\n          ),\r\n      );\r\n\r\n      // Kiểm tra response thành công (HTTP 200 và có CampaignCode)\r\n      if (response.status === 200 && response.data.CampaignCode) {\r\n        const responseData = response.data as FptCreateCampaignResponse;\r\n        this.logger.debug(\r\n          `Campaign \"${request.campaignName}\" đã được tạo thành công với mã: ${responseData.CampaignCode}`,\r\n        );\r\n        return {\r\n          success: true,\r\n          campaignCode: responseData.CampaignCode,\r\n          rawResponse: responseData,\r\n        };\r\n      } else if (response.data.error) {\r\n        // Trường hợp lỗi với error code\r\n        this.logger.warn(\r\n          `Lỗi tạo campaign: ${response.data.error} - ${response.data.error_description}`,\r\n        );\r\n        return {\r\n          success: false,\r\n          errorCode: response.data.error.toString(),\r\n          errorMessage: response.data.error_description || 'Lỗi không xác định',\r\n          rawResponse: response.data,\r\n        };\r\n      } else {\r\n        // Trường hợp khác\r\n        return {\r\n          success: false,\r\n          errorMessage: 'Phản hồi không hợp lệ từ FPT SMS',\r\n          rawResponse: response.data,\r\n        };\r\n      }\r\n    } catch (error) {\r\n      this.logger.error(\r\n        `Lỗi khi tạo campaign qua FPT SMS: ${error.message}`,\r\n        error.stack,\r\n      );\r\n      return {\r\n        success: false,\r\n        errorMessage: error.message || 'Lỗi không xác định',\r\n      };\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Gửi tin nhắn CSKH quốc tế qua FPT SMS\r\n   * @param phoneNumber Số điện thoại quốc tế của người nhận (ví dụ: 1749123456)\r\n   * @param message Nội dung tin nhắn (sẽ được mã hóa Base64 tự động)\r\n   * @param options Các tùy chọn bổ sung (brandName, requestId)\r\n   * @returns Promise chứa thông tin tin nhắn đã gửi hoặc lỗi\r\n   */\r\n  async sendInternationalSms(\r\n    phoneNumber: string,\r\n    message: string,\r\n    options?: FptInternationalSmsOptions,\r\n  ): Promise<SmsResponse> {\r\n    try {\r\n      this.logger.debug(\r\n        `Gửi SMS CSKH quốc tế đến ${phoneNumber} qua FPT SMS`,\r\n      );\r\n\r\n      const brandName = options?.brandName || this.defaultBrandName;\r\n\r\n      if (!brandName) {\r\n        throw new Error(\r\n          'Tham số \"brandName\" là bắt buộc đối với FPT SMS quốc tế',\r\n        );\r\n      }\r\n\r\n      // Lấy token truy cập hợp lệ\r\n      const accessToken = await this.getAccessToken();\r\n\r\n      // Mã hóa nội dung tin nhắn thành Base64\r\n      const encodedMessage = Buffer.from(message, 'utf8').toString('base64');\r\n\r\n      // Chuẩn bị payload\r\n      const payload = {\r\n        access_token: accessToken,\r\n        session_id: Date.now().toString(),\r\n        BrandName: brandName,\r\n        Phone: phoneNumber, // Số điện thoại quốc tế không cần format\r\n        Message: encodedMessage,\r\n        ...(options?.requestId && { RequestId: options.requestId }),\r\n      };\r\n\r\n      // Gửi SMS CSKH quốc tế qua FPT SMS\r\n      const response = await firstValueFrom(\r\n        this.httpService\r\n          .post(`${this.apiUrl}/push-brandname-international`, payload, {\r\n            headers: {\r\n              'Content-Type': 'application/json',\r\n            },\r\n          })\r\n          .pipe(\r\n            catchError((error) => {\r\n              this.logger.error(\r\n                `Lỗi khi gửi SMS CSKH quốc tế qua FPT SMS: ${error.message}`,\r\n                error.stack,\r\n              );\r\n              throw error;\r\n            }),\r\n          ),\r\n      );\r\n\r\n      // Kiểm tra response thành công (HTTP 200 và có MessageId)\r\n      if (response.status === 200 && response.data.MessageId) {\r\n        const responseData = response.data as FptInternationalSmsResponse;\r\n        return {\r\n          success: true,\r\n          messageId: responseData.MessageId.toString(),\r\n          rawResponse: responseData,\r\n        };\r\n      } else if (response.data.error) {\r\n        // Trường hợp lỗi với error code\r\n        return {\r\n          success: false,\r\n          errorCode: response.data.error.toString(),\r\n          errorMessage: response.data.error_description || 'Lỗi không xác định',\r\n          rawResponse: response.data,\r\n        };\r\n      } else {\r\n        // Trường hợp khác\r\n        return {\r\n          success: false,\r\n          errorMessage: 'Phản hồi không hợp lệ từ FPT SMS',\r\n          rawResponse: response.data,\r\n        };\r\n      }\r\n    } catch (error) {\r\n      this.logger.error(\r\n        `Lỗi khi gửi SMS CSKH quốc tế qua FPT SMS: ${error.message}`,\r\n        error.stack,\r\n      );\r\n      return {\r\n        success: false,\r\n        errorMessage: error.message || 'Lỗi không xác định',\r\n      };\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Kiểm tra kết nối với FPT SMS\r\n   * @param config Cấu hình của FPT SMS\r\n   * @returns Promise chỉ ra liệu kết nối có thành công hay không\r\n   */\r\n  async testConnection(config: FptSmsConfig): Promise<ConnectionTestResponse> {\r\n    try {\r\n      this.logger.debug('Kiểm tra kết nối với FPT SMS');\r\n\r\n      const clientId = config.clientId || this.clientId;\r\n      const clientSecret = config.clientSecret || this.clientSecret;\r\n      const scope = config.scope || this.scope;\r\n      const apiUrl = config.apiUrl || this.apiUrl;\r\n\r\n      if (!clientId || !clientSecret) {\r\n        return {\r\n          success: false,\r\n          message: 'Thiếu thông tin xác thực FPT SMS',\r\n        };\r\n      }\r\n\r\n      // Kiểm tra việc lấy token truy cập\r\n      const response = await firstValueFrom(\r\n        this.httpService\r\n          .post(\r\n            `${apiUrl}/oauth2/token`,\r\n            {\r\n              grant_type: 'client_credentials',\r\n              client_id: clientId,\r\n              client_secret: clientSecret,\r\n              scope: scope,\r\n              session_id: Date.now().toString(),\r\n            },\r\n            {\r\n              headers: {\r\n                'Content-Type': 'application/json',\r\n              },\r\n            },\r\n          )\r\n          .pipe(\r\n            catchError((error) => {\r\n              this.logger.error(\r\n                `Erreur lors du test de connexion avec FPT SMS: ${error.message}`,\r\n                error.stack,\r\n              );\r\n              throw error;\r\n            }),\r\n          ),\r\n      );\r\n\r\n      if (response.data.error) {\r\n        return {\r\n          success: false,\r\n          message: response.data.error_description || 'Lỗi không xác định',\r\n          details: response.data,\r\n        };\r\n      }\r\n\r\n      return {\r\n        success: true,\r\n        message: 'Kết nối thành công',\r\n        details: {\r\n          accessToken: response.data.access_token,\r\n          expiresIn: response.data.expires_in,\r\n          tokenType: response.data.token_type,\r\n          scope: response.data.scope,\r\n        },\r\n      };\r\n    } catch (error) {\r\n      this.logger.error(\r\n        `Lỗi khi kiểm tra kết nối với FPT SMS: ${error.message}`,\r\n        error.stack,\r\n      );\r\n      return {\r\n        success: false,\r\n        message: error.message || 'Lỗi không xác định',\r\n      };\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Định dạng ngày tháng theo định dạng yêu cầu của FPT SMS (yyyy-MM-dd HH:mm)\r\n   * @param date Ngày cần định dạng\r\n   * @returns Chuỗi ngày đã định dạng\r\n   */\r\n  private formatScheduleTime(date: Date): string {\r\n    const year = date.getFullYear();\r\n    const month = String(date.getMonth() + 1).padStart(2, '0');\r\n    const day = String(date.getDate()).padStart(2, '0');\r\n    const hours = String(date.getHours()).padStart(2, '0');\r\n    const minutes = String(date.getMinutes()).padStart(2, '0');\r\n\r\n    return `${year}-${month}-${day} ${hours}:${minutes}`;\r\n  }\r\n\r\n  /**\r\n   * Tạo tên campaign duy nhất\r\n   * @param prefix Tiền tố cho tên campaign (tùy chọn)\r\n   * @returns Tên campaign duy nhất\r\n   */\r\n  static generateUniqueCampaignName(prefix = 'Campaign'): string {\r\n    const timestamp = Date.now();\r\n    const random = Math.random().toString(36).substring(2, 8);\r\n    return `${prefix}_${timestamp}_${random}`;\r\n  }\r\n\r\n  /**\r\n   * Validate định dạng thời gian schedule\r\n   * @param scheduleTime Thời gian cần validate (yyyy-mm-dd HH:ii)\r\n   * @returns true nếu định dạng hợp lệ\r\n   */\r\n  static validateScheduleTime(scheduleTime: string): boolean {\r\n    const regex = /^\\d{4}-\\d{2}-\\d{2} \\d{2}:\\d{2}$/;\r\n    if (!regex.test(scheduleTime)) {\r\n      return false;\r\n    }\r\n\r\n    // Kiểm tra thời gian có hợp lệ không\r\n    const [datePart, timePart] = scheduleTime.split(' ');\r\n    const [year, month, day] = datePart.split('-').map(Number);\r\n    const [hour, minute] = timePart.split(':').map(Number);\r\n\r\n    const date = new Date(year, month - 1, day, hour, minute);\r\n    return (\r\n      date.getFullYear() === year &&\r\n      date.getMonth() === month - 1 &&\r\n      date.getDate() === day &&\r\n      date.getHours() === hour &&\r\n      date.getMinutes() === minute\r\n    );\r\n  }\r\n\r\n  /**\r\n   * Tính toán quota cần thiết cho campaign\r\n   * @param messageLength Độ dài tin nhắn (ký tự)\r\n   * @param phoneCount Số lượng số điện thoại\r\n   * @returns Quota cần thiết\r\n   */\r\n  static calculateQuota(messageLength: number, phoneCount: number): number {\r\n    // Mỗi tin nhắn SMS có thể chứa tối đa 160 ký tự\r\n    // Nếu vượt quá sẽ được chia thành nhiều tin nhắn\r\n    const smsPerMessage = Math.ceil(messageLength / 160);\r\n    return smsPerMessage * phoneCount;\r\n  }\r\n}\r\n"], "names": ["FptSmsProvider", "BaseSmsProvider", "getAccessToken", "forceRefresh", "now", "Date", "accessToken", "tokenExpiration", "logger", "debug", "response", "firstValueFrom", "httpService", "post", "apiUrl", "grant_type", "client_id", "clientId", "client_secret", "clientSecret", "scope", "session_id", "toString", "headers", "pipe", "catchError", "error", "message", "stack", "data", "Error", "error_description", "access_token", "getTime", "expires_in", "sendSms", "phoneNumber", "options", "brandName", "defaultBrandName", "formattedPhoneNumber", "formatPhoneNumber", "cleanedPhoneNumber", "startsWith", "substring", "BrandName", "Phone", "Message", "<PERSON><PERSON><PERSON>", "quota", "errorid", "success", "messageId", "requestid", "rawResponse", "errorCode", "errorMessage", "errordes", "checkMessageStatus", "RequestCode", "status", "MessageStatus", "UNKNOWN", "details", "DELIVERED", "FAILED", "updatedAt", "sendBrandnameSms", "brandname", "campaignResponse", "CampaignName", "campaignName", "ScheduleTime", "scheduleTime", "formatScheduleTime", "adsResponse", "CampaignCode", "campaigncode", "PhoneList", "campaign", "ads", "sendOtp", "otpCode", "template", "replace", "createCampaign", "request", "payload", "responseData", "campaignCode", "warn", "sendInternationalSms", "encodedMessage", "<PERSON><PERSON><PERSON>", "from", "requestId", "RequestId", "MessageId", "testConnection", "config", "expiresIn", "tokenType", "token_type", "date", "year", "getFullYear", "month", "String", "getMonth", "padStart", "day", "getDate", "hours", "getHours", "minutes", "getMinutes", "generateUniqueCampaignName", "prefix", "timestamp", "random", "Math", "validateScheduleTime", "regex", "test", "datePart", "timePart", "split", "map", "Number", "hour", "minute", "calculateQuota", "messageLength", "phoneCount", "smsPerMessage", "ceil", "constructor", "configService", "providerName", "env", "fpt", "FPT_SMS_CLIENT_ID", "FPT_SMS_CLIENT_SECRET", "FPT_SMS_SCOPE", "FPT_SMS_API_URL", "FPT_SMS_BRANDNAME"], "mappings": ";;;;+BA2IaA;;;eAAAA;;;wBA3Ic;uBACC;wBACE;sBACC;2BACJ;wCACK;sCAOzB;qBACa;;;;;;;;;;AA8Hb,IAAA,AAAMA,iBAAN,MAAMA,uBAAuBC,uCAAe;IA2BjD;;;;GAIC,GACD,MAAcC,eAAeC,eAAe,KAAK,EAAmB;QAClE,yCAAyC;QACzC,MAAMC,MAAM,IAAIC;QAChB,IACE,CAACF,gBACD,IAAI,CAACG,WAAW,IAChB,IAAI,CAACC,eAAe,IACpB,IAAI,CAACA,eAAe,GAAGH,KACvB;YACA,qCAAqC;YACrC,OAAO,IAAI,CAACE,WAAW;QACzB;QAEA,IAAI;YACF,IAAI,CAACE,MAAM,CAACC,KAAK,CAAC;YAElB,MAAMC,WAAW,MAAMC,IAAAA,oBAAc,EACnC,IAAI,CAACC,WAAW,CACbC,IAAI,CACH,GAAG,IAAI,CAACC,MAAM,CAAC,aAAa,CAAC,EAC7B;gBACEC,YAAY;gBACZC,WAAW,IAAI,CAACC,QAAQ;gBACxBC,eAAe,IAAI,CAACC,YAAY;gBAChCC,OAAO,IAAI,CAACA,KAAK;gBACjBC,YAAYhB,KAAKD,GAAG,GAAGkB,QAAQ;YACjC,GACA;gBACEC,SAAS;oBACP,gBAAgB;gBAClB;YACF,GAEDC,IAAI,CACHC,IAAAA,qBAAU,EAAC,CAACC;gBACV,IAAI,CAAClB,MAAM,CAACkB,KAAK,CACf,CAAC,qDAAqD,EAAEA,MAAMC,OAAO,EAAE,EACvED,MAAME,KAAK;gBAEb,MAAMF;YACR;YAIN,IAAIhB,SAASmB,IAAI,CAACH,KAAK,EAAE;gBACvB,MAAM,IAAII,MAAM,CAAC,gBAAgB,EAAEpB,SAASmB,IAAI,CAACE,iBAAiB,EAAE;YACtE;YAEA,kDAAkD;YAClD,IAAI,CAACzB,WAAW,GAAGI,SAASmB,IAAI,CAACG,YAAY;YAC7C,IAAI,CAACzB,eAAe,GAAG,IAAIF,KACzBD,IAAI6B,OAAO,KAAKvB,SAASmB,IAAI,CAACK,UAAU,GAAG;YAG7C,oCAAoC;YACpC,IAAI,CAAC,IAAI,CAAC5B,WAAW,EAAE;gBACrB,MAAM,IAAIwB,MAAM;YAClB;YAEA,OAAO,IAAI,CAACxB,WAAW;QACzB,EAAE,OAAOoB,OAAO;YACd,IAAI,CAAClB,MAAM,CAACkB,KAAK,CACf,CAAC,oCAAoC,EAAEA,MAAMC,OAAO,EAAE,EACtDD,MAAME,KAAK;YAEb,MAAMF;QACR;IACF;IAEA;;;;;;GAMC,GACD,MAAMS,QACJC,WAAmB,EACnBT,OAAe,EACfU,OAAa,EACS;QACtB,IAAI;YACF,IAAI,CAAC7B,MAAM,CAACC,KAAK,CAAC,CAAC,YAAY,EAAE2B,YAAY,YAAY,CAAC;YAE1D,MAAME,YAAYD,SAASC,aAAa,IAAI,CAACC,gBAAgB;YAE7D,IAAI,CAACD,WAAW;gBACd,MAAM,IAAIR,MAAM;YAClB;YAEA,4BAA4B;YAC5B,MAAMxB,cAAc,MAAM,IAAI,CAACJ,cAAc;YAE7C,0BAA0B;YAC1B,MAAMsC,uBAAuB,IAAI,CAACC,iBAAiB,CAACL;YACpD,wBAAwB;YACxB,MAAMM,qBAAqBF,qBAAqBG,UAAU,CAAC,OACvDH,qBAAqBI,SAAS,CAAC,KAC/BJ;YAEJ,sBAAsB;YACtB,MAAM9B,WAAW,MAAMC,IAAAA,oBAAc,EACnC,IAAI,CAACC,WAAW,CACbC,IAAI,CACH,GAAG,IAAI,CAACC,MAAM,CAAC,mBAAmB,CAAC,EACnC;gBACEkB,cAAc1B;gBACde,YAAYhB,KAAKD,GAAG,GAAGkB,QAAQ;gBAC/BuB,WAAWP;gBACXQ,OAAOJ;gBACPK,SAASpB;gBACTqB,OAAOX,SAASY,SAAS;YAC3B,GACA;gBACE1B,SAAS;oBACP,gBAAgB;gBAClB;YACF,GAEDC,IAAI,CACHC,IAAAA,qBAAU,EAAC,CAACC;gBACV,IAAI,CAAClB,MAAM,CAACkB,KAAK,CACf,CAAC,2CAA2C,EAAEA,MAAMC,OAAO,EAAE,EAC7DD,MAAME,KAAK;gBAEb,MAAMF;YACR;YAIN,IAAIhB,SAASmB,IAAI,CAACqB,OAAO,KAAK,GAAG;gBAC/B,OAAO;oBACLC,SAAS;oBACTC,WAAW1C,SAASmB,IAAI,CAACwB,SAAS;oBAClCC,aAAa5C,SAASmB,IAAI;gBAC5B;YACF,OAAO;gBACL,OAAO;oBACLsB,SAAS;oBACTI,WAAW7C,SAASmB,IAAI,CAACqB,OAAO,CAAC5B,QAAQ;oBACzCkC,cAAc9C,SAASmB,IAAI,CAAC4B,QAAQ,IAAI;oBACxCH,aAAa5C,SAASmB,IAAI;gBAC5B;YACF;QACF,EAAE,OAAOH,OAAO;YACd,IAAI,CAAClB,MAAM,CAACkB,KAAK,CACf,CAAC,6BAA6B,EAAEA,MAAMC,OAAO,EAAE,EAC/CD,MAAME,KAAK;YAEb,OAAO;gBACLuB,SAAS;gBACTK,cAAc9B,MAAMC,OAAO,IAAI;YACjC;QACF;IACF;IAEA;;;;GAIC,GACD,MAAM+B,mBAAmBN,SAAiB,EAAkC;QAC1E,IAAI;YACF,IAAI,CAAC5C,MAAM,CAACC,KAAK,CACf,CAAC,6BAA6B,EAAE2C,UAAU,YAAY,CAAC;YAGzD,4BAA4B;YAC5B,MAAM9C,cAAc,MAAM,IAAI,CAACJ,cAAc;YAE7C,2CAA2C;YAC3C,MAAMQ,WAAW,MAAMC,IAAAA,oBAAc,EACnC,IAAI,CAACC,WAAW,CACbC,IAAI,CACH,GAAG,IAAI,CAACC,MAAM,CAAC,gBAAgB,CAAC,EAChC;gBACEkB,cAAc1B;gBACde,YAAYhB,KAAKD,GAAG,GAAGkB,QAAQ;gBAC/BqC,aAAaP;YACf,GACA;gBACE7B,SAAS;oBACP,gBAAgB;gBAClB;YACF,GAEDC,IAAI,CACHC,IAAAA,qBAAU,EAAC,CAACC;gBACV,IAAI,CAAClB,MAAM,CAACkB,KAAK,CACf,CAAC,iEAAiE,EAAEA,MAAMC,OAAO,EAAE,EACnFD,MAAME,KAAK;gBAEb,MAAMF;YACR;YAIN,wCAAwC;YACxC,IAAIkC,SAASC,mCAAa,CAACC,OAAO;YAClC,IAAIC,UAAU;YAEd,IAAIrD,SAASmB,IAAI,CAACqB,OAAO,KAAK,GAAG;gBAC/BU,SAASC,mCAAa,CAACG,SAAS;YAClC,OAAO;gBACLJ,SAASC,mCAAa,CAACI,MAAM;gBAC7BF,UAAUrD,SAASmB,IAAI,CAAC4B,QAAQ,IAAI;YACtC;YAEA,OAAO;gBACLL;gBACAQ;gBACAM,WAAW,IAAI7D;gBACf0D;gBACAT,aAAa5C,SAASmB,IAAI;YAC5B;QACF,EAAE,OAAOH,OAAO;YACd,IAAI,CAAClB,MAAM,CAACkB,KAAK,CACf,CAAC,kDAAkD,EAAEA,MAAMC,OAAO,EAAE,EACpED,MAAME,KAAK;YAEb,OAAO;gBACLwB;gBACAQ,QAAQC,mCAAa,CAACC,OAAO;gBAC7BI,WAAW,IAAI7D;gBACf0D,SAASrC,MAAMC,OAAO,IAAI;YAC5B;QACF;IACF;IAEA;;;;;;;GAOC,GACD,MAAMwC,iBACJ/B,WAAmB,EACnBT,OAAe,EACfyC,SAAiB,EACjB/B,OAAa,EACS;QACtB,IAAI;YACF,IAAI,CAAC7B,MAAM,CAACC,KAAK,CAAC,CAAC,sBAAsB,EAAE2B,YAAY,YAAY,CAAC;YAEpE,4BAA4B;YAC5B,MAAM9B,cAAc,MAAM,IAAI,CAACJ,cAAc;YAE7C,0BAA0B;YAC1B,MAAMsC,uBAAuB,IAAI,CAACC,iBAAiB,CAACL;YACpD,wBAAwB;YACxB,MAAMM,qBAAqBF,qBAAqBG,UAAU,CAAC,OACvDH,qBAAqBI,SAAS,CAAC,KAC/BJ;YAEJ,mCAAmC;YACnC,MAAM6B,mBAAmB,MAAM1D,IAAAA,oBAAc,EAC3C,IAAI,CAACC,WAAW,CACbC,IAAI,CACH,GAAG,IAAI,CAACC,MAAM,CAAC,gBAAgB,CAAC,EAChC;gBACEkB,cAAc1B;gBACde,YAAYhB,KAAKD,GAAG,GAAGkB,QAAQ;gBAC/BgD,cAAcjC,SAASkC,gBAAgB,CAAC,SAAS,EAAElE,KAAKD,GAAG,IAAI;gBAC/DyC,WAAWuB;gBACXrB,SAASpB;gBACT6C,cACEnC,SAASoC,gBAAgB,IAAI,CAACC,kBAAkB,CAAC,IAAIrE;gBACvD2C,OAAOX,SAASY,SAAS;YAC3B,GACA;gBACE1B,SAAS;oBACP,gBAAgB;gBAClB;YACF,GAEDC,IAAI,CACHC,IAAAA,qBAAU,EAAC,CAACC;gBACV,IAAI,CAAClB,MAAM,CAACkB,KAAK,CACf,CAAC,mDAAmD,EAAEA,MAAMC,OAAO,EAAE,EACrED,MAAME,KAAK;gBAEb,MAAMF;YACR;YAIN,IAAI2C,iBAAiBxC,IAAI,CAACqB,OAAO,KAAK,GAAG;gBACvC,OAAO;oBACLC,SAAS;oBACTI,WAAWc,iBAAiBxC,IAAI,CAACqB,OAAO,CAAC5B,QAAQ;oBACjDkC,cACEa,iBAAiBxC,IAAI,CAAC4B,QAAQ,IAAI;oBACpCH,aAAae,iBAAiBxC,IAAI;gBACpC;YACF;YAEA,oBAAoB;YACpB,MAAM8C,cAAc,MAAMhE,IAAAA,oBAAc,EACtC,IAAI,CAACC,WAAW,CACbC,IAAI,CACH,GAAG,IAAI,CAACC,MAAM,CAAC,SAAS,CAAC,EACzB;gBACEkB,cAAc1B;gBACde,YAAYhB,KAAKD,GAAG,GAAGkB,QAAQ;gBAC/BsD,cAAcP,iBAAiBxC,IAAI,CAACgD,YAAY;gBAChDC,WAAWpC;YACb,GACA;gBACEnB,SAAS;oBACP,gBAAgB;gBAClB;YACF,GAEDC,IAAI,CACHC,IAAAA,qBAAU,EAAC,CAACC;gBACV,IAAI,CAAClB,MAAM,CAACkB,KAAK,CACf,CAAC,qDAAqD,EAAEA,MAAMC,OAAO,EAAE,EACvED,MAAME,KAAK;gBAEb,MAAMF;YACR;YAIN,IAAIiD,YAAY9C,IAAI,CAACqB,OAAO,KAAK,GAAG;gBAClC,OAAO;oBACLC,SAAS;oBACTC,WAAWuB,YAAY9C,IAAI,CAACwB,SAAS;oBACrCC,aAAa;wBACXyB,UAAUV,iBAAiBxC,IAAI;wBAC/BmD,KAAKL,YAAY9C,IAAI;oBACvB;gBACF;YACF,OAAO;gBACL,OAAO;oBACLsB,SAAS;oBACTI,WAAWoB,YAAY9C,IAAI,CAACqB,OAAO,CAAC5B,QAAQ;oBAC5CkC,cAAcmB,YAAY9C,IAAI,CAAC4B,QAAQ,IAAI;oBAC3CH,aAAa;wBACXyB,UAAUV,iBAAiBxC,IAAI;wBAC/BmD,KAAKL,YAAY9C,IAAI;oBACvB;gBACF;YACF;QACF,EAAE,OAAOH,OAAO;YACd,IAAI,CAAClB,MAAM,CAACkB,KAAK,CACf,CAAC,uCAAuC,EAAEA,MAAMC,OAAO,EAAE,EACzDD,MAAME,KAAK;YAEb,OAAO;gBACLuB,SAAS;gBACTK,cAAc9B,MAAMC,OAAO,IAAI;YACjC;QACF;IACF;IAEA;;;;;;GAMC,GACD,MAAMsD,QACJ7C,WAAmB,EACnB8C,OAAe,EACf7C,OAAa,EACS;QACtB,IAAI,CAAC7B,MAAM,CAACC,KAAK,CACf,CAAC,WAAW,EAAEyE,QAAQ,mBAAmB,EAAE9C,YAAY,YAAY,CAAC;QAGtE,wBAAwB;QACxB,MAAMT,UAAUU,SAAS8C,WACrB9C,QAAQ8C,QAAQ,CAACC,OAAO,CAAC,UAAUF,WACnC,CAAC,wBAAwB,EAAEA,SAAS;QAExC,4CAA4C;QAC5C,OAAO,IAAI,CAAC/C,OAAO,CAACC,aAAaT,SAASU;IAC5C;IAEA;;;;GAIC,GACD,MAAMgD,eACJC,OAAiC,EACmF;QACpH,IAAI;YACF,IAAI,CAAC9E,MAAM,CAACC,KAAK,CACf,CAAC,mBAAmB,EAAE6E,QAAQf,YAAY,CAAC,aAAa,CAAC;YAG3D,4BAA4B;YAC5B,MAAMjE,cAAc,MAAM,IAAI,CAACJ,cAAc;YAE7C,qCAAqC;YACrC,MAAMqF,UAAU;gBACdvD,cAAc1B;gBACde,YAAYhB,KAAKD,GAAG,GAAGkB,QAAQ;gBAC/BgD,cAAcgB,QAAQf,YAAY;gBAClC1B,WAAWyC,QAAQhD,SAAS;gBAC5BS,SAASuC,QAAQ3D,OAAO;gBACxB6C,cAAcc,QAAQb,YAAY;gBAClCzB,OAAOsC,QAAQrC,KAAK;YACtB;YAEA,IAAI,CAACzC,MAAM,CAACC,KAAK,CAAC,6BAA6B8E;YAE/C,2BAA2B;YAC3B,MAAM7E,WAAW,MAAMC,IAAAA,oBAAc,EACnC,IAAI,CAACC,WAAW,CACbC,IAAI,CAAC,GAAG,IAAI,CAACC,MAAM,CAAC,gBAAgB,CAAC,EAAEyE,SAAS;gBAC/ChE,SAAS;oBACP,gBAAgB;gBAClB;YACF,GACCC,IAAI,CACHC,IAAAA,qBAAU,EAAC,CAACC;gBACV,IAAI,CAAClB,MAAM,CAACkB,KAAK,CACf,CAAC,kCAAkC,EAAEA,MAAMC,OAAO,EAAE,EACpDD,MAAME,KAAK;gBAEb,MAAMF;YACR;YAIN,6DAA6D;YAC7D,IAAIhB,SAASkD,MAAM,KAAK,OAAOlD,SAASmB,IAAI,CAAC+C,YAAY,EAAE;gBACzD,MAAMY,eAAe9E,SAASmB,IAAI;gBAClC,IAAI,CAACrB,MAAM,CAACC,KAAK,CACf,CAAC,UAAU,EAAE6E,QAAQf,YAAY,CAAC,iCAAiC,EAAEiB,aAAaZ,YAAY,EAAE;gBAElG,OAAO;oBACLzB,SAAS;oBACTsC,cAAcD,aAAaZ,YAAY;oBACvCtB,aAAakC;gBACf;YACF,OAAO,IAAI9E,SAASmB,IAAI,CAACH,KAAK,EAAE;gBAC9B,gCAAgC;gBAChC,IAAI,CAAClB,MAAM,CAACkF,IAAI,CACd,CAAC,kBAAkB,EAAEhF,SAASmB,IAAI,CAACH,KAAK,CAAC,GAAG,EAAEhB,SAASmB,IAAI,CAACE,iBAAiB,EAAE;gBAEjF,OAAO;oBACLoB,SAAS;oBACTI,WAAW7C,SAASmB,IAAI,CAACH,KAAK,CAACJ,QAAQ;oBACvCkC,cAAc9C,SAASmB,IAAI,CAACE,iBAAiB,IAAI;oBACjDuB,aAAa5C,SAASmB,IAAI;gBAC5B;YACF,OAAO;gBACL,kBAAkB;gBAClB,OAAO;oBACLsB,SAAS;oBACTK,cAAc;oBACdF,aAAa5C,SAASmB,IAAI;gBAC5B;YACF;QACF,EAAE,OAAOH,OAAO;YACd,IAAI,CAAClB,MAAM,CAACkB,KAAK,CACf,CAAC,kCAAkC,EAAEA,MAAMC,OAAO,EAAE,EACpDD,MAAME,KAAK;YAEb,OAAO;gBACLuB,SAAS;gBACTK,cAAc9B,MAAMC,OAAO,IAAI;YACjC;QACF;IACF;IAEA;;;;;;GAMC,GACD,MAAMgE,qBACJvD,WAAmB,EACnBT,OAAe,EACfU,OAAoC,EACd;QACtB,IAAI;YACF,IAAI,CAAC7B,MAAM,CAACC,KAAK,CACf,CAAC,yBAAyB,EAAE2B,YAAY,YAAY,CAAC;YAGvD,MAAME,YAAYD,SAASC,aAAa,IAAI,CAACC,gBAAgB;YAE7D,IAAI,CAACD,WAAW;gBACd,MAAM,IAAIR,MACR;YAEJ;YAEA,4BAA4B;YAC5B,MAAMxB,cAAc,MAAM,IAAI,CAACJ,cAAc;YAE7C,wCAAwC;YACxC,MAAM0F,iBAAiBC,OAAOC,IAAI,CAACnE,SAAS,QAAQL,QAAQ,CAAC;YAE7D,mBAAmB;YACnB,MAAMiE,UAAU;gBACdvD,cAAc1B;gBACde,YAAYhB,KAAKD,GAAG,GAAGkB,QAAQ;gBAC/BuB,WAAWP;gBACXQ,OAAOV;gBACPW,SAAS6C;gBACT,GAAIvD,SAAS0D,aAAa;oBAAEC,WAAW3D,QAAQ0D,SAAS;gBAAC,CAAC;YAC5D;YAEA,mCAAmC;YACnC,MAAMrF,WAAW,MAAMC,IAAAA,oBAAc,EACnC,IAAI,CAACC,WAAW,CACbC,IAAI,CAAC,GAAG,IAAI,CAACC,MAAM,CAAC,6BAA6B,CAAC,EAAEyE,SAAS;gBAC5DhE,SAAS;oBACP,gBAAgB;gBAClB;YACF,GACCC,IAAI,CACHC,IAAAA,qBAAU,EAAC,CAACC;gBACV,IAAI,CAAClB,MAAM,CAACkB,KAAK,CACf,CAAC,0CAA0C,EAAEA,MAAMC,OAAO,EAAE,EAC5DD,MAAME,KAAK;gBAEb,MAAMF;YACR;YAIN,0DAA0D;YAC1D,IAAIhB,SAASkD,MAAM,KAAK,OAAOlD,SAASmB,IAAI,CAACoE,SAAS,EAAE;gBACtD,MAAMT,eAAe9E,SAASmB,IAAI;gBAClC,OAAO;oBACLsB,SAAS;oBACTC,WAAWoC,aAAaS,SAAS,CAAC3E,QAAQ;oBAC1CgC,aAAakC;gBACf;YACF,OAAO,IAAI9E,SAASmB,IAAI,CAACH,KAAK,EAAE;gBAC9B,gCAAgC;gBAChC,OAAO;oBACLyB,SAAS;oBACTI,WAAW7C,SAASmB,IAAI,CAACH,KAAK,CAACJ,QAAQ;oBACvCkC,cAAc9C,SAASmB,IAAI,CAACE,iBAAiB,IAAI;oBACjDuB,aAAa5C,SAASmB,IAAI;gBAC5B;YACF,OAAO;gBACL,kBAAkB;gBAClB,OAAO;oBACLsB,SAAS;oBACTK,cAAc;oBACdF,aAAa5C,SAASmB,IAAI;gBAC5B;YACF;QACF,EAAE,OAAOH,OAAO;YACd,IAAI,CAAClB,MAAM,CAACkB,KAAK,CACf,CAAC,0CAA0C,EAAEA,MAAMC,OAAO,EAAE,EAC5DD,MAAME,KAAK;YAEb,OAAO;gBACLuB,SAAS;gBACTK,cAAc9B,MAAMC,OAAO,IAAI;YACjC;QACF;IACF;IAEA;;;;GAIC,GACD,MAAMuE,eAAeC,MAAoB,EAAmC;QAC1E,IAAI;YACF,IAAI,CAAC3F,MAAM,CAACC,KAAK,CAAC;YAElB,MAAMQ,WAAWkF,OAAOlF,QAAQ,IAAI,IAAI,CAACA,QAAQ;YACjD,MAAME,eAAegF,OAAOhF,YAAY,IAAI,IAAI,CAACA,YAAY;YAC7D,MAAMC,QAAQ+E,OAAO/E,KAAK,IAAI,IAAI,CAACA,KAAK;YACxC,MAAMN,SAASqF,OAAOrF,MAAM,IAAI,IAAI,CAACA,MAAM;YAE3C,IAAI,CAACG,YAAY,CAACE,cAAc;gBAC9B,OAAO;oBACLgC,SAAS;oBACTxB,SAAS;gBACX;YACF;YAEA,mCAAmC;YACnC,MAAMjB,WAAW,MAAMC,IAAAA,oBAAc,EACnC,IAAI,CAACC,WAAW,CACbC,IAAI,CACH,GAAGC,OAAO,aAAa,CAAC,EACxB;gBACEC,YAAY;gBACZC,WAAWC;gBACXC,eAAeC;gBACfC,OAAOA;gBACPC,YAAYhB,KAAKD,GAAG,GAAGkB,QAAQ;YACjC,GACA;gBACEC,SAAS;oBACP,gBAAgB;gBAClB;YACF,GAEDC,IAAI,CACHC,IAAAA,qBAAU,EAAC,CAACC;gBACV,IAAI,CAAClB,MAAM,CAACkB,KAAK,CACf,CAAC,+CAA+C,EAAEA,MAAMC,OAAO,EAAE,EACjED,MAAME,KAAK;gBAEb,MAAMF;YACR;YAIN,IAAIhB,SAASmB,IAAI,CAACH,KAAK,EAAE;gBACvB,OAAO;oBACLyB,SAAS;oBACTxB,SAASjB,SAASmB,IAAI,CAACE,iBAAiB,IAAI;oBAC5CgC,SAASrD,SAASmB,IAAI;gBACxB;YACF;YAEA,OAAO;gBACLsB,SAAS;gBACTxB,SAAS;gBACToC,SAAS;oBACPzD,aAAaI,SAASmB,IAAI,CAACG,YAAY;oBACvCoE,WAAW1F,SAASmB,IAAI,CAACK,UAAU;oBACnCmE,WAAW3F,SAASmB,IAAI,CAACyE,UAAU;oBACnClF,OAAOV,SAASmB,IAAI,CAACT,KAAK;gBAC5B;YACF;QACF,EAAE,OAAOM,OAAO;YACd,IAAI,CAAClB,MAAM,CAACkB,KAAK,CACf,CAAC,sCAAsC,EAAEA,MAAMC,OAAO,EAAE,EACxDD,MAAME,KAAK;YAEb,OAAO;gBACLuB,SAAS;gBACTxB,SAASD,MAAMC,OAAO,IAAI;YAC5B;QACF;IACF;IAEA;;;;GAIC,GACD,AAAQ+C,mBAAmB6B,IAAU,EAAU;QAC7C,MAAMC,OAAOD,KAAKE,WAAW;QAC7B,MAAMC,QAAQC,OAAOJ,KAAKK,QAAQ,KAAK,GAAGC,QAAQ,CAAC,GAAG;QACtD,MAAMC,MAAMH,OAAOJ,KAAKQ,OAAO,IAAIF,QAAQ,CAAC,GAAG;QAC/C,MAAMG,QAAQL,OAAOJ,KAAKU,QAAQ,IAAIJ,QAAQ,CAAC,GAAG;QAClD,MAAMK,UAAUP,OAAOJ,KAAKY,UAAU,IAAIN,QAAQ,CAAC,GAAG;QAEtD,OAAO,GAAGL,KAAK,CAAC,EAAEE,MAAM,CAAC,EAAEI,IAAI,CAAC,EAAEE,MAAM,CAAC,EAAEE,SAAS;IACtD;IAEA;;;;GAIC,GACD,OAAOE,2BAA2BC,SAAS,UAAU,EAAU;QAC7D,MAAMC,YAAYjH,KAAKD,GAAG;QAC1B,MAAMmH,SAASC,KAAKD,MAAM,GAAGjG,QAAQ,CAAC,IAAIsB,SAAS,CAAC,GAAG;QACvD,OAAO,GAAGyE,OAAO,CAAC,EAAEC,UAAU,CAAC,EAAEC,QAAQ;IAC3C;IAEA;;;;GAIC,GACD,OAAOE,qBAAqBhD,YAAoB,EAAW;QACzD,MAAMiD,QAAQ;QACd,IAAI,CAACA,MAAMC,IAAI,CAAClD,eAAe;YAC7B,OAAO;QACT;QAEA,qCAAqC;QACrC,MAAM,CAACmD,UAAUC,SAAS,GAAGpD,aAAaqD,KAAK,CAAC;QAChD,MAAM,CAACtB,MAAME,OAAOI,IAAI,GAAGc,SAASE,KAAK,CAAC,KAAKC,GAAG,CAACC;QACnD,MAAM,CAACC,MAAMC,OAAO,GAAGL,SAASC,KAAK,CAAC,KAAKC,GAAG,CAACC;QAE/C,MAAMzB,OAAO,IAAIlG,KAAKmG,MAAME,QAAQ,GAAGI,KAAKmB,MAAMC;QAClD,OACE3B,KAAKE,WAAW,OAAOD,QACvBD,KAAKK,QAAQ,OAAOF,QAAQ,KAC5BH,KAAKQ,OAAO,OAAOD,OACnBP,KAAKU,QAAQ,OAAOgB,QACpB1B,KAAKY,UAAU,OAAOe;IAE1B;IAEA;;;;;GAKC,GACD,OAAOC,eAAeC,aAAqB,EAAEC,UAAkB,EAAU;QACvE,gDAAgD;QAChD,iDAAiD;QACjD,MAAMC,gBAAgBd,KAAKe,IAAI,CAACH,gBAAgB;QAChD,OAAOE,gBAAgBD;IACzB;IA3tBAG,YACE,AAAiB5H,WAAwB,EACzC,AAAiB6H,aAA4B,CAC7C;QACA,KAAK,CAAC,wBAHW7H,cAAAA,kBACA6H,gBAAAA,oBAdVC,eAAe,gBAShBpI,cAA6B,WAC7BC,kBAA+B;QAQrC,gEAAgE;QAChE,IAAI,CAACU,QAAQ,GAAG0H,QAAG,CAACC,GAAG,CAACC,iBAAiB,IAAI;QAC7C,IAAI,CAAC1H,YAAY,GAAGwH,QAAG,CAACC,GAAG,CAACE,qBAAqB,IAAI;QACrD,IAAI,CAAC1H,KAAK,GAAGuH,QAAG,CAACC,GAAG,CAACG,aAAa,IAAI;QACtC,IAAI,CAACjI,MAAM,GAAG6H,QAAG,CAACC,GAAG,CAACI,eAAe,IAAI;QACzC,IAAI,CAACzG,gBAAgB,GAAGoG,QAAG,CAACC,GAAG,CAACK,iBAAiB,IAAI;IACvD;AAgtBF"}