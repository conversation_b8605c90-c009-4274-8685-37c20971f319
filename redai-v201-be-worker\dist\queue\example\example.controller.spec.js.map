{"version": 3, "sources": ["../../../src/queue/example/example.controller.spec.ts"], "sourcesContent": ["import { Test, TestingModule } from '@nestjs/testing';\r\nimport { ExampleController } from './example.controller';\r\n\r\ndescribe('ExampleController', () => {\r\n  let controller: ExampleController;\r\n\r\n  beforeEach(async () => {\r\n    const module: TestingModule = await Test.createTestingModule({\r\n      controllers: [ExampleController],\r\n    }).compile();\r\n\r\n    controller = module.get<ExampleController>(ExampleController);\r\n  });\r\n\r\n  it('should be defined', () => {\r\n    expect(controller).toBeDefined();\r\n  });\r\n});\r\n"], "names": ["describe", "controller", "beforeEach", "module", "Test", "createTestingModule", "controllers", "ExampleController", "compile", "get", "it", "expect", "toBeDefined"], "mappings": ";;;;yBAAoC;mCACF;AAElCA,SAAS,qBAAqB;IAC5B,IAAIC;IAEJC,WAAW;QACT,MAAMC,SAAwB,MAAMC,aAAI,CAACC,mBAAmB,CAAC;YAC3DC,aAAa;gBAACC,oCAAiB;aAAC;QAClC,GAAGC,OAAO;QAEVP,aAAaE,OAAOM,GAAG,CAAoBF,oCAAiB;IAC9D;IAEAG,GAAG,qBAAqB;QACtBC,OAAOV,YAAYW,WAAW;IAChC;AACF"}