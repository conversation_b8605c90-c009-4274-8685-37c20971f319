"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var ChatService_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.ChatService = void 0;
const common_1 = require("@nestjs/common");
const microservices_1 = require("@nestjs/microservices");
const typeorm_transactional_1 = require("typeorm-transactional");
const exceptions_1 = require("../../../common/exceptions");
const chat_error_codes_1 = require("../exceptions/chat-error-codes");
const agent_config_service_1 = require("./agent-config.service");
const database_1 = require("../database");
const message_request_dto_1 = require("../dto/message-request.dto");
const message_response_dto_1 = require("../dto/message-response.dto");
const enums_1 = require("../../../shared/enums");
const constants_1 = require("../constants");
let ChatService = ChatService_1 = class ChatService {
    agentConfigService;
    agentConfigQueries;
    userAgentRunsQueries;
    userMessagesQueries;
    redisClient;
    logger = new common_1.Logger(ChatService_1.name);
    constructor(agentConfigService, agentConfigQueries, userAgentRunsQueries, userMessagesQueries, redisClient) {
        this.agentConfigService = agentConfigService;
        this.agentConfigQueries = agentConfigQueries;
        this.userAgentRunsQueries = userAgentRunsQueries;
        this.userMessagesQueries = userMessagesQueries;
        this.redisClient = redisClient;
    }
    async processMessage(messageRequest, userId, jwt = '') {
        try {
            this.logger.log(`Processing message for agent supervisor from user ${userId}`);
            const agentConfigMap = await this.agentConfigQueries.getAllSystemAgentConfigs();
            const supervisorAgents = Object.values(agentConfigMap).filter((config) => config.system_is_supervisor === true);
            if (supervisorAgents.length === 0) {
                throw new exceptions_1.AppException(chat_error_codes_1.CHAT_ERROR_CODES.THREAD_VALIDATION_FAILED, 'No supervisor agent found. System requires exactly one agent with is_supervisor = true');
            }
            if (supervisorAgents.length > 1) {
                const supervisorIds = supervisorAgents.map((agent) => agent.id).join(', ');
                throw new exceptions_1.AppException(chat_error_codes_1.CHAT_ERROR_CODES.THREAD_VALIDATION_FAILED, `Multiple supervisor agents found: ${supervisorIds}. System requires exactly one agent with is_supervisor = true`);
            }
            const primaryAgent = supervisorAgents[0];
            this.logger.log(`Using supervisor agent ${primaryAgent.id} (${primaryAgent.name}) as primary agent`);
            const runPayload = this.buildRunPayload(messageRequest, primaryAgent, agentConfigMap, userId);
            const runId = await this.userAgentRunsQueries.createRun({
                payload: runPayload,
                status: enums_1.UserAgentRunStatus.CREATED,
                created_by: userId
            });
            this.logger.log(`Created run ${runId} for agent ${primaryAgent.id}`);
            const threadId = messageRequest.threadId;
            const userMessageId = await this.userMessagesQueries.createMessage({
                thread_id: threadId,
                role: 'user',
                content: {
                    contentBlocks: messageRequest.contentBlocks,
                    agentId: primaryAgent.id,
                    runId: runId,
                },
                created_by: userId
            });
            this.logger.log(`Persisted user message ${userMessageId} for thread ${threadId}`);
            const runTriggerEvent = {
                eventType: constants_1.REDIS_EVENTS.RUN_TRIGGER,
                runId,
                threadId: messageRequest.threadId,
                agentId: primaryAgent.id,
                userId,
                jwt,
                timestamp: Date.now(),
                priority: 'medium',
                publishedAt: Date.now(),
            };
            this.redisClient.emit(constants_1.REDIS_EVENTS.RUN_TRIGGER, runTriggerEvent);
            this.logger.log(`Published run trigger event for run ${runId}`);
            return new message_response_dto_1.MessageResponseDto({
                runId: runId,
                agentId: primaryAgent.id,
                agentName: primaryAgent.name,
                status: enums_1.UserAgentRunStatus.CREATED,
                createdAt: Date.now(),
            });
        }
        catch (error) {
            this.logger.error(`Message processing failed: ${error.message}`, error.stack);
            if (error instanceof exceptions_1.AppException) {
                throw error;
            }
            throw new exceptions_1.AppException(chat_error_codes_1.CHAT_ERROR_CODES.THREAD_CREATION_FAILED, `Message processing failed: ${error.message}`);
        }
    }
    buildRunPayload(messageRequest, primaryAgent, agentConfigMap, userId) {
        const contentBlocks = Array.isArray(messageRequest.contentBlocks)
            ? messageRequest.contentBlocks
            : [messageRequest.contentBlocks];
        const textContent = contentBlocks
            .filter(block => block.type === 'text' && block.content)
            .map(block => block.content)
            .join('\n');
        const payload = {
            message: {
                content: textContent,
                contentBlocks,
                threadId: messageRequest.threadId,
                sessionId: messageRequest.sessionId,
            },
            primaryAgentId: primaryAgent.id,
            agentConfigMap,
            metadata: {
                userId,
                requestId: `req_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`,
                version: '1.0',
            },
            processing: {
                maxRetries: 3,
                timeoutMs: 300000,
                enableMultiAgent: Object.keys(agentConfigMap).length > 1,
                alwaysApproveToolCall: messageRequest.alwaysApproveToolCall || false,
            },
        };
        this.logger.debug(`Built run payload for agent ${primaryAgent.id}`, {
            agentId: primaryAgent.id,
            messageLength: textContent.length,
            contentBlockCount: contentBlocks.length,
            multiAgentEnabled: payload.processing.enableMultiAgent,
            agentCount: Object.keys(agentConfigMap).length,
            alwaysApproveToolCall: payload.processing.alwaysApproveToolCall,
        });
        return payload;
    }
    async getAgentConfigSummary() {
        return this.agentConfigService.getConfigSummary();
    }
    async cancelRun(runId, reason = 'User requested cancellation') {
        try {
            this.logger.log(`Cancelling run ${runId}: ${reason}`);
            const runData = await this.userAgentRunsQueries.getRunById(runId);
            if (!runData) {
                this.logger.error(`Run not found for cancellation: ${runId}`);
                return false;
            }
            const threadId = runData.payload?.message?.threadId || runId;
            const updateSuccess = await this.userAgentRunsQueries.updateRunStatus(runId, enums_1.UserAgentRunStatus.FAILED);
            if (!updateSuccess) {
                this.logger.error(`Failed to update run status for cancellation: ${runId}`);
                return false;
            }
            try {
                const runCancelEvent = {
                    eventType: constants_1.REDIS_EVENTS.RUN_CANCEL,
                    threadId,
                    runId,
                    reason,
                    timestamp: Date.now(),
                    publishedAt: Date.now(),
                };
                this.redisClient.emit(constants_1.REDIS_EVENTS.RUN_CANCEL, runCancelEvent);
                this.logger.log(`Published run cancel event for thread ${threadId} (run ${runId})`);
            }
            catch (error) {
                this.logger.warn(`Failed to publish run cancel event for run ${runId}: ${error.message}`);
            }
            this.logger.log(`Successfully cancelled run ${runId} (thread ${threadId})`);
            return true;
        }
        catch (error) {
            this.logger.error(`Failed to cancel run ${runId}: ${error.message}`, error.stack);
            return false;
        }
    }
    async validateAgent(agentId) {
        try {
            const agentConfig = await this.agentConfigService.getAgentConfig(agentId);
            return agentConfig ? this.agentConfigService.validateAgentConfig(agentConfig) : false;
        }
        catch (error) {
            this.logger.error(`Error validating agent ${agentId}: ${error.message}`);
            return false;
        }
    }
    async getRedisHealth() {
        try {
            this.redisClient.emit('health.check', { timestamp: Date.now() });
            return true;
        }
        catch (error) {
            this.logger.error(`Redis health check failed: ${error.message}`);
            return false;
        }
    }
};
exports.ChatService = ChatService;
__decorate([
    (0, typeorm_transactional_1.Transactional)(),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [message_request_dto_1.MessageRequestDto, Number, String]),
    __metadata("design:returntype", Promise)
], ChatService.prototype, "processMessage", null);
exports.ChatService = ChatService = ChatService_1 = __decorate([
    (0, common_1.Injectable)(),
    __param(4, (0, common_1.Inject)('REDIS_CLIENT')),
    __metadata("design:paramtypes", [agent_config_service_1.AgentConfigService,
        database_1.AgentConfigQueries,
        database_1.UserAgentRunsQueries,
        database_1.UserMessagesQueries,
        microservices_1.ClientProxy])
], ChatService);
//# sourceMappingURL=chat.service.js.map