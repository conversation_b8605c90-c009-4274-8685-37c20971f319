"use strict";
Object.defineProperty(exports, "__esModule", {
    value: true
});
Object.defineProperty(exports, "UpdateTagDto", {
    enumerable: true,
    get: function() {
        return UpdateTagDto;
    }
});
const _classvalidator = require("class-validator");
const _swagger = require("@nestjs/swagger");
function _ts_decorate(decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for(var i = decorators.length - 1; i >= 0; i--)if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
}
function _ts_metadata(k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
}
let UpdateTagDto = class UpdateTagDto {
};
_ts_decorate([
    (0, _swagger.ApiProperty)({
        description: 'Tên tag',
        example: 'Khách hàng VIP',
        required: false
    }),
    (0, _classvalidator.IsOptional)(),
    (0, _classvalidator.IsString)({
        message: 'Tên tag phải là chuỗi'
    }),
    (0, _classvalidator.Length)(1, 255, {
        message: 'Tên tag phải từ 1 đến 255 ký tự'
    }),
    _ts_metadata("design:type", String)
], UpdateTagDto.prototype, "name", void 0);
_ts_decorate([
    (0, _swagger.ApiProperty)({
        description: 'Mã màu của tag (định dạng HEX)',
        example: '#FF5733',
        required: false
    }),
    (0, _classvalidator.IsOptional)(),
    (0, _classvalidator.IsString)({
        message: 'Mã màu phải là chuỗi'
    }),
    (0, _classvalidator.Matches)(/^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$/, {
        message: 'Mã màu phải có định dạng HEX hợp lệ (ví dụ: #FF5733)'
    }),
    _ts_metadata("design:type", String)
], UpdateTagDto.prototype, "color", void 0);

//# sourceMappingURL=update-tag.dto.js.map