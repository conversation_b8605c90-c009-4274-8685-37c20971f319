"use strict";
Object.defineProperty(exports, "__esModule", {
    value: true
});
Object.defineProperty(exports, "EmailSystemController", {
    enumerable: true,
    get: function() {
        return EmailSystemController;
    }
});
const _common = require("@nestjs/common");
const _emailsystemservice = require("./email-system.service");
function _ts_decorate(decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for(var i = decorators.length - 1; i >= 0; i--)if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
}
function _ts_metadata(k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
}
function _ts_param(paramIndex, decorator) {
    return function(target, key) {
        decorator(target, key, paramIndex);
    };
}
let EmailSystemController = class EmailSystemController {
    /**
   * Test thêm job email vào queue
   */ async testAddJob(jobData) {
        try {
            // Dữ liệu test mặc định
            const testJobData = {
                category: 'test',
                data: {
                    name: 'Test User',
                    email: '<EMAIL>',
                    message: 'This is a test email from worker app'
                },
                to: '<EMAIL>',
                ...jobData
            };
            this.logger.log(`Đang thêm test job vào queue: ${JSON.stringify(testJobData)}`);
            const jobId = await this.emailSystemService.addEmailJob(testJobData);
            return {
                success: true,
                message: 'Job đã được thêm vào queue thành công',
                jobId,
                data: testJobData
            };
        } catch (error) {
            this.logger.error(`Lỗi khi thêm job: ${error.message}`, error.stack);
            return {
                success: false,
                message: 'Lỗi khi thêm job vào queue',
                error: error.message
            };
        }
    }
    /**
   * Lấy thông tin queue
   */ async getQueueInfo() {
        try {
            // Thông tin cơ bản về queue
            return {
                success: true,
                message: 'Email system queue đang hoạt động',
                queueName: 'EMAIL_SYSTEM',
                timestamp: new Date().toISOString()
            };
        } catch (error) {
            this.logger.error(`Lỗi khi lấy thông tin queue: ${error.message}`, error.stack);
            return {
                success: false,
                message: 'Lỗi khi lấy thông tin queue',
                error: error.message
            };
        }
    }
    constructor(emailSystemService){
        this.emailSystemService = emailSystemService;
        this.logger = new _common.Logger(EmailSystemController.name);
    }
};
_ts_decorate([
    (0, _common.Post)('test/add-job'),
    _ts_param(0, (0, _common.Body)()),
    _ts_metadata("design:type", Function),
    _ts_metadata("design:paramtypes", [
        typeof Partial === "undefined" ? Object : Partial
    ]),
    _ts_metadata("design:returntype", Promise)
], EmailSystemController.prototype, "testAddJob", null);
_ts_decorate([
    (0, _common.Get)('queue/info'),
    _ts_metadata("design:type", Function),
    _ts_metadata("design:paramtypes", []),
    _ts_metadata("design:returntype", Promise)
], EmailSystemController.prototype, "getQueueInfo", null);
EmailSystemController = _ts_decorate([
    (0, _common.Controller)('api/email-system'),
    _ts_metadata("design:type", Function),
    _ts_metadata("design:paramtypes", [
        typeof _emailsystemservice.EmailSystemService === "undefined" ? Object : _emailsystemservice.EmailSystemService
    ])
], EmailSystemController);

//# sourceMappingURL=email-system.controller.js.map