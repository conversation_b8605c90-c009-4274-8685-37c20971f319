"use strict";
Object.defineProperty(exports, "__esModule", {
    value: true
});
Object.defineProperty(exports, "UserCampaignHistory", {
    enumerable: true,
    get: function() {
        return UserCampaignHistory;
    }
});
const _typeorm = require("typeorm");
function _ts_decorate(decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for(var i = decorators.length - 1; i >= 0; i--)if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
}
function _ts_metadata(k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
}
let UserCampaignHistory = class UserCampaignHistory {
};
_ts_decorate([
    (0, _typeorm.PrimaryGeneratedColumn)({
        name: 'id',
        type: 'bigint'
    }),
    _ts_metadata("design:type", Number)
], UserCampaignHistory.prototype, "id", void 0);
_ts_decorate([
    (0, _typeorm.Column)({
        name: 'campaign_id',
        type: 'bigint',
        nullable: true,
        comment: 'Mã chiến dịch'
    }),
    _ts_metadata("design:type", Number)
], UserCampaignHistory.prototype, "campaignId", void 0);
_ts_decorate([
    (0, _typeorm.Column)({
        name: 'audience_id',
        type: 'bigint',
        nullable: true,
        comment: 'Mã khách hàng'
    }),
    _ts_metadata("design:type", Number)
], UserCampaignHistory.prototype, "audienceId", void 0);
_ts_decorate([
    (0, _typeorm.Column)({
        name: 'status',
        length: 20,
        nullable: true,
        comment: 'Trạng thái'
    }),
    _ts_metadata("design:type", String)
], UserCampaignHistory.prototype, "status", void 0);
_ts_decorate([
    (0, _typeorm.Column)({
        name: 'sent_at',
        type: 'bigint',
        nullable: true,
        comment: 'Thời gian gửi'
    }),
    _ts_metadata("design:type", Number)
], UserCampaignHistory.prototype, "sentAt", void 0);
_ts_decorate([
    (0, _typeorm.Column)({
        name: 'created_at',
        type: 'bigint',
        nullable: true,
        comment: 'Thời gian tạo'
    }),
    _ts_metadata("design:type", Number)
], UserCampaignHistory.prototype, "createdAt", void 0);
UserCampaignHistory = _ts_decorate([
    (0, _typeorm.Entity)('user_campaign_history')
], UserCampaignHistory);

//# sourceMappingURL=user-campaign-history.entity.js.map