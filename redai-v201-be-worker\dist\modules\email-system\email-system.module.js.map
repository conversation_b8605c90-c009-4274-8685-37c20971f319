{"version": 3, "sources": ["../../../src/modules/email-system/email-system.module.ts"], "sourcesContent": ["import { Modu<PERSON> } from '@nestjs/common';\r\nimport { TypeOrmModule } from '@nestjs/typeorm';\r\nimport { AdminTemplateEmail } from './entities/admin-template-email.entity';\r\nimport { EmailSystemService } from './email-system.service';\r\nimport { EmailSystemProcessor } from './email-system.processor';\r\nimport { EmailSystemController } from './email-system.controller';\r\nimport { BullModule } from '@nestjs/bullmq';\r\nimport { QueueName } from '../../queue';\r\n\r\n/**\r\n * Module xử lý hệ thống email\r\n */\r\n@Module({\r\n  imports: [\r\n    TypeOrmModule.forFeature([AdminTemplateEmail]),\r\n    BullModule.registerQueue({\r\n      name: QueueName.EMAIL_SYSTEM,\r\n    }),\r\n  ],\r\n  providers: [EmailSystemService, EmailSystemProcessor],\r\n  controllers: [EmailSystemController],\r\n  exports: [EmailSystemService],\r\n})\r\nexport class EmailSystemModule {}\r\n"], "names": ["EmailSystemModule", "imports", "TypeOrmModule", "forFeature", "AdminTemplateEmail", "BullModule", "registerQueue", "name", "QueueName", "EMAIL_SYSTEM", "providers", "EmailSystemService", "EmailSystemProcessor", "controllers", "EmailSystemController", "exports"], "mappings": ";;;;+BAuBaA;;;eAAAA;;;wBAvBU;yBACO;0CACK;oCACA;sCACE;uCACC;wBACX;uBACD;;;;;;;AAgBnB,IAAA,AAAMA,oBAAN,MAAMA;AAAmB;;;QAV9BC,SAAS;YACPC,sBAAa,CAACC,UAAU,CAAC;gBAACC,4CAAkB;aAAC;YAC7CC,kBAAU,CAACC,aAAa,CAAC;gBACvBC,MAAMC,gBAAS,CAACC,YAAY;YAC9B;SACD;QACDC,WAAW;YAACC,sCAAkB;YAAEC,0CAAoB;SAAC;QACrDC,aAAa;YAACC,4CAAqB;SAAC;QACpCC,SAAS;YAACJ,sCAAkB;SAAC"}