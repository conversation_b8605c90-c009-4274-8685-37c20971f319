{"version": 3, "sources": ["../../../../../src/modules/marketing/dto/zalo/message-response.dto.ts"], "sourcesContent": ["import { ApiProperty } from '@nestjs/swagger';\r\n\r\n/**\r\n * DTO cho phản hồi thông tin tin nhắn\r\n */\r\nexport class MessageResponseDto {\r\n  @ApiProperty({\r\n    description: 'ID của tin nhắn trong hệ thống',\r\n    example: 1,\r\n  })\r\n  id: number;\r\n\r\n  @ApiProperty({\r\n    description: 'ID của tin nhắn trên Zalo',\r\n    example: 'msg123456789',\r\n    nullable: true,\r\n  })\r\n  messageId?: string;\r\n\r\n  @ApiProperty({\r\n    description: 'Loại tin nhắn (text, image, file, template)',\r\n    example: 'text',\r\n  })\r\n  messageType: string;\r\n\r\n  @ApiProperty({\r\n    description: 'Nội dung tin nhắn',\r\n    example: 'Xin chào! Cảm ơn bạn đã quan tâm đến sản phẩm của chúng tôi.',\r\n    nullable: true,\r\n  })\r\n  content?: string;\r\n\r\n  @ApiProperty({\r\n    description: 'Dữ liệu bổ sung của tin nhắn',\r\n    example: { url: 'https://example.com/image.jpg' },\r\n    nullable: true,\r\n  })\r\n  data?: any;\r\n\r\n  @ApiProperty({\r\n    description: 'H<PERSON>ớng tin nhắn (incoming, outgoing)',\r\n    example: 'outgoing',\r\n  })\r\n  direction: string;\r\n\r\n  @ApiProperty({\r\n    description: 'Thời điểm gửi/nhận (Unix timestamp)',\r\n    example: 1625097600000,\r\n  })\r\n  timestamp: number;\r\n}\r\n"], "names": ["MessageResponseDto", "description", "example", "nullable", "url"], "mappings": ";;;;+BAKaA;;;eAAAA;;;yBALe;;;;;;;;;;AAKrB,IAAA,AAAMA,qBAAN,MAAMA;AA6Cb;;;QA3CIC,aAAa;QACbC,SAAS;;;;;;QAKTD,aAAa;QACbC,SAAS;QACTC,UAAU;;;;;;QAKVF,aAAa;QACbC,SAAS;;;;;;QAKTD,aAAa;QACbC,SAAS;QACTC,UAAU;;;;;;QAKVF,aAAa;QACbC,SAAS;YAAEE,KAAK;QAAgC;QAChDD,UAAU;;;;;;QAKVF,aAAa;QACbC,SAAS;;;;;;QAKTD,aAAa;QACbC,SAAS"}