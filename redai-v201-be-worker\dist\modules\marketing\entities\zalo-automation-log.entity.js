"use strict";
Object.defineProperty(exports, "__esModule", {
    value: true
});
Object.defineProperty(exports, "ZaloAutomationLog", {
    enumerable: true,
    get: function() {
        return ZaloAutomationLog;
    }
});
const _typeorm = require("typeorm");
function _ts_decorate(decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for(var i = decorators.length - 1; i >= 0; i--)if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
}
function _ts_metadata(k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
}
let ZaloAutomationLog = class ZaloAutomationLog {
};
_ts_decorate([
    (0, _typeorm.PrimaryGeneratedColumn)(),
    _ts_metadata("design:type", Number)
], ZaloAutomationLog.prototype, "id", void 0);
_ts_decorate([
    (0, _typeorm.Column)({
        name: 'automation_id'
    }),
    _ts_metadata("design:type", Number)
], ZaloAutomationLog.prototype, "automationId", void 0);
_ts_decorate([
    (0, _typeorm.Column)({
        name: 'user_id'
    }),
    _ts_metadata("design:type", Number)
], ZaloAutomationLog.prototype, "userId", void 0);
_ts_decorate([
    (0, _typeorm.Column)({
        name: 'oa_id'
    }),
    _ts_metadata("design:type", String)
], ZaloAutomationLog.prototype, "oaId", void 0);
_ts_decorate([
    (0, _typeorm.Column)({
        name: 'follower_id'
    }),
    _ts_metadata("design:type", Number)
], ZaloAutomationLog.prototype, "followerId", void 0);
_ts_decorate([
    (0, _typeorm.Column)({
        name: 'follower_user_id'
    }),
    _ts_metadata("design:type", String)
], ZaloAutomationLog.prototype, "followerUserId", void 0);
_ts_decorate([
    (0, _typeorm.Column)({
        name: 'trigger_type'
    }),
    _ts_metadata("design:type", String)
], ZaloAutomationLog.prototype, "triggerType", void 0);
_ts_decorate([
    (0, _typeorm.Column)({
        name: 'action_type'
    }),
    _ts_metadata("design:type", String)
], ZaloAutomationLog.prototype, "actionType", void 0);
_ts_decorate([
    (0, _typeorm.Column)(),
    _ts_metadata("design:type", String)
], ZaloAutomationLog.prototype, "status", void 0);
_ts_decorate([
    (0, _typeorm.Column)({
        nullable: true
    }),
    _ts_metadata("design:type", String)
], ZaloAutomationLog.prototype, "error", void 0);
_ts_decorate([
    (0, _typeorm.Column)({
        name: 'created_at',
        type: 'bigint'
    }),
    _ts_metadata("design:type", Number)
], ZaloAutomationLog.prototype, "createdAt", void 0);
ZaloAutomationLog = _ts_decorate([
    (0, _typeorm.Entity)('zalo_automation_logs')
], ZaloAutomationLog);

//# sourceMappingURL=zalo-automation-log.entity.js.map