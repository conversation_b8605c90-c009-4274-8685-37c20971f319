{"version": 3, "sources": ["../../../../../src/modules/marketing/dto/zalo/zns-template-response.dto.ts"], "sourcesContent": ["import { ApiProperty } from '@nestjs/swagger';\r\n\r\n/**\r\n * DTO cho phản hồi thông tin template ZNS\r\n */\r\nexport class ZnsTemplateResponseDto {\r\n  @ApiProperty({\r\n    description: 'ID của template trong hệ thống',\r\n    example: 1,\r\n  })\r\n  id: number;\r\n\r\n  @ApiProperty({\r\n    description: 'ID của Official Account',\r\n    example: '*********',\r\n  })\r\n  oaId: string;\r\n\r\n  @ApiProperty({\r\n    description: 'ID của template trên Zalo',\r\n    example: 'template*********',\r\n  })\r\n  templateId: string;\r\n\r\n  @ApiProperty({\r\n    description: 'Tên của template',\r\n    example: 'Thông báo đơn hàng',\r\n  })\r\n  templateName: string;\r\n\r\n  @ApiProperty({\r\n    description: 'Nội dung của template',\r\n    example:\r\n      'Đơn hàng #{orderId} của bạn đã được xác nhận. Cảm ơn bạn đã mua hàng tại {shopName}.',\r\n  })\r\n  templateContent: string;\r\n\r\n  @ApiProperty({\r\n    description: '<PERSON><PERSON><PERSON> tham số của template',\r\n    example: ['orderId', 'shopName'],\r\n    type: [String],\r\n  })\r\n  params: string[];\r\n\r\n  @ApiProperty({\r\n    description: 'Trạng thái của template (approved, pending, rejected)',\r\n    example: 'approved',\r\n  })\r\n  status: string;\r\n\r\n  @ApiProperty({\r\n    description: 'Thời điểm tạo (Unix timestamp)',\r\n    example: 1625097600000,\r\n  })\r\n  createdAt: number;\r\n\r\n  @ApiProperty({\r\n    description: 'Thời điểm cập nhật (Unix timestamp)',\r\n    example: 1625097600000,\r\n  })\r\n  updatedAt: number;\r\n}\r\n"], "names": ["ZnsTemplateResponseDto", "description", "example", "type", "String"], "mappings": ";;;;+BAKaA;;;eAAAA;;;yBALe;;;;;;;;;;AAKrB,IAAA,AAAMA,yBAAN,MAAMA;AAwDb;;;QAtDIC,aAAa;QACbC,SAAS;;;;;;QAKTD,aAAa;QACbC,SAAS;;;;;;QAKTD,aAAa;QACbC,SAAS;;;;;;QAKTD,aAAa;QACbC,SAAS;;;;;;QAKTD,aAAa;QACbC,SACE;;;;;;QAKFD,aAAa;QACbC,SAAS;YAAC;YAAW;SAAW;QAChCC,MAAM;YAACC;SAAO;;;;;;QAKdH,aAAa;QACbC,SAAS;;;;;;QAKTD,aAAa;QACbC,SAAS;;;;;;QAKTD,aAAa;QACbC,SAAS"}