"use strict";
Object.defineProperty(exports, "__esModule", {
    value: true
});
function _export(target, all) {
    for(var name in all)Object.defineProperty(target, name, {
        enumerable: true,
        get: Object.getOwnPropertyDescriptor(all, name).get
    });
}
_export(exports, {
    get SmsProviderFactory () {
        return SmsProviderFactory;
    },
    get SmsProviderType () {
        return SmsProviderType;
    }
});
const _common = require("@nestjs/common");
const _speedsmsproviderservice = require("./speed-sms-provider.service");
const _twilioproviderservice = require("./twilio-provider.service");
const _vonageproviderservice = require("./vonage-provider.service");
const _fptsmsproviderservice = require("./fpt-sms-provider.service");
function _ts_decorate(decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for(var i = decorators.length - 1; i >= 0; i--)if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
}
function _ts_metadata(k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
}
var SmsProviderType = /*#__PURE__*/ function(SmsProviderType) {
    SmsProviderType["SPEED_SMS"] = "SPEED_SMS";
    SmsProviderType["TWILIO"] = "TWILIO";
    SmsProviderType["VONAGE"] = "VONAGE";
    SmsProviderType["FPT_SMS"] = "FPT_SMS";
    return SmsProviderType;
}({});
let SmsProviderFactory = class SmsProviderFactory {
    /**
   * Crée une instance de fournisseur SMS en fonction du type spécifié
   * @param providerType Type de fournisseur SMS
   * @returns Instance du fournisseur SMS
   * @throws Error si le type de fournisseur n'est pas supporté
   */ createProvider(providerType) {
        switch(providerType){
            case "SPEED_SMS":
                return this.speedSmsProvider;
            case "TWILIO":
                return this.twilioProvider;
            case "VONAGE":
                return this.vonageProvider;
            case "FPT_SMS":
                return this.fptSmsProvider;
            default:
                throw new Error(`Type de fournisseur SMS non supporté: ${providerType}`);
        }
    }
    /**
   * Crée une instance de fournisseur SMS en fonction du nom du fournisseur
   * @param providerName Nom du fournisseur SMS
   * @returns Instance du fournisseur SMS
   * @throws Error si le nom du fournisseur n'est pas supporté
   */ createProviderByName(providerName) {
        const normalizedName = providerName.toUpperCase().replace(/[^A-Z0-9_]/g, '_');
        try {
            const providerType = SmsProviderType[normalizedName];
            return this.createProvider(providerType);
        } catch (error) {
            throw new Error(`Nom de fournisseur SMS non supporté: ${providerName}`);
        }
    }
    constructor(speedSmsProvider, twilioProvider, vonageProvider, fptSmsProvider){
        this.speedSmsProvider = speedSmsProvider;
        this.twilioProvider = twilioProvider;
        this.vonageProvider = vonageProvider;
        this.fptSmsProvider = fptSmsProvider;
    }
};
SmsProviderFactory = _ts_decorate([
    (0, _common.Injectable)(),
    _ts_metadata("design:type", Function),
    _ts_metadata("design:paramtypes", [
        typeof _speedsmsproviderservice.SpeedSmsProvider === "undefined" ? Object : _speedsmsproviderservice.SpeedSmsProvider,
        typeof _twilioproviderservice.TwilioProvider === "undefined" ? Object : _twilioproviderservice.TwilioProvider,
        typeof _vonageproviderservice.VonageProvider === "undefined" ? Object : _vonageproviderservice.VonageProvider,
        typeof _fptsmsproviderservice.FptSmsProvider === "undefined" ? Object : _fptsmsproviderservice.FptSmsProvider
    ])
], SmsProviderFactory);

//# sourceMappingURL=sms-provider-factory.service.js.map