/**
 * Enum for user agent run status
 * 
 * IMPORTANT: This enum must be kept identical in both redai-v201-be-app and redai-v201-be-worker
 * to maintain consistency across separate codebases.
 * 
 * Database enum values: 'created', 'running', 'completed', 'failed'
 */ "use strict";
Object.defineProperty(exports, "__esModule", {
    value: true
});
function _export(target, all) {
    for(var name in all)Object.defineProperty(target, name, {
        enumerable: true,
        get: Object.getOwnPropertyDescriptor(all, name).get
    });
}
_export(exports, {
    get UserAgentRunStatus () {
        return UserAgentRunStatus;
    },
    get VALID_STATUS_TRANSITIONS () {
        return VALID_STATUS_TRANSITIONS;
    },
    get getValidNextStatuses () {
        return getValidNextStatuses;
    },
    get isValidStatusTransition () {
        return isValidStatusTransition;
    }
});
var UserAgentRunStatus = /*#__PURE__*/ function(UserAgentRunStatus) {
    /**
   * Initial state when run is created
   */ UserAgentRunStatus["CREATED"] = "created";
    /**
   * When worker starts processing the run
   */ UserAgentRunStatus["RUNNING"] = "running";
    /**
   * Successful completion of the run
   */ UserAgentRunStatus["COMPLETED"] = "completed";
    /**
   * Error or failure during run processing
   */ UserAgentRunStatus["FAILED"] = "failed";
    return UserAgentRunStatus;
}({});
const VALID_STATUS_TRANSITIONS = {
    ["created"]: [
        "running",
        "failed"
    ],
    ["running"]: [
        "completed",
        "failed"
    ],
    ["completed"]: [],
    ["failed"]: [] // Terminal state
};
function isValidStatusTransition(from, to) {
    return VALID_STATUS_TRANSITIONS[from]?.includes(to) ?? false;
}
function getValidNextStatuses(current) {
    return VALID_STATUS_TRANSITIONS[current] || [];
}

//# sourceMappingURL=user-agent-run-status.enum.js.map