{"version": 3, "sources": ["../../../../../src/modules/marketing/email/services/email-marketing.service.ts"], "sourcesContent": ["import { Injectable, Logger } from '@nestjs/common';\r\nimport { InjectRepository } from '@nestjs/typeorm';\r\nimport { Repository } from 'typeorm';\r\nimport { InjectQueue } from '@nestjs/bullmq';\r\nimport { Queue } from 'bullmq';\r\nimport { UserCampaign } from '../../entities/user-campaign.entity';\r\nimport { UserAudience } from '../../entities/user-audience.entity';\r\nimport { UserAudienceCustomField } from '../../entities/user-audience-custom-field.entity';\r\nimport { EmailMarketingJobDto } from '../dto';\r\nimport { EmailTrackingService } from './email-tracking.service';\r\nimport { QueueName } from '../../../../queue';\r\n\r\n/**\r\n * Service chính xử lý email marketing\r\n */\r\n@Injectable()\r\nexport class EmailMarketingService {\r\n  private readonly logger = new Logger(EmailMarketingService.name);\r\n\r\n  constructor(\r\n    @InjectRepository(UserCampaign)\r\n    private readonly campaignRepository: Repository<UserCampaign>,\r\n    @InjectRepository(UserAudience)\r\n    private readonly audienceRepository: Repository<UserAudience>,\r\n    @InjectRepository(UserAudienceCustomField)\r\n    private readonly customFieldRepository: Repository<UserAudienceCustomField>,\r\n    @InjectQueue(QueueName.EMAIL_MARKETING)\r\n    private readonly emailMarketingQueue: Queue,\r\n    private readonly emailTrackingService: EmailTrackingService,\r\n  ) {}\r\n\r\n  /**\r\n   * Tạo jobs email marketing cho một campaign\r\n   * @param campaignId ID của campaign\r\n   * @returns Số lượng jobs đã tạo\r\n   */\r\n  async createEmailMarketingJobs(campaignId: number): Promise<number> {\r\n    try {\r\n      this.logger.log(\r\n        `Creating email marketing jobs for campaign: ${campaignId}`,\r\n      );\r\n\r\n      // Lấy thông tin campaign\r\n      const campaign = await this.campaignRepository.findOne({\r\n        where: { id: campaignId },\r\n      });\r\n\r\n      if (!campaign) {\r\n        throw new Error(`Campaign not found: ${campaignId}`);\r\n      }\r\n\r\n      if (campaign.platform !== 'email') {\r\n        throw new Error(`Campaign ${campaignId} is not an email campaign`);\r\n      }\r\n\r\n      // Lấy danh sách audience IDs\r\n      let audienceIds: number[] = [];\r\n\r\n      if (campaign.audienceIds && Array.isArray(campaign.audienceIds)) {\r\n        audienceIds = campaign.audienceIds;\r\n      } else if (campaign.segmentId) {\r\n        // TODO: Implement segment logic if needed\r\n        this.logger.warn(\r\n          `Segment logic not implemented for campaign ${campaignId}`,\r\n        );\r\n        return 0;\r\n      } else {\r\n        this.logger.warn(\r\n          `No audience or segment found for campaign ${campaignId}`,\r\n        );\r\n        return 0;\r\n      }\r\n\r\n      // Lấy thông tin audiences\r\n      const audiences = await this.audienceRepository.findByIds(audienceIds);\r\n\r\n      if (audiences.length === 0) {\r\n        this.logger.warn(`No valid audiences found for campaign ${campaignId}`);\r\n        return 0;\r\n      }\r\n\r\n      let jobCount = 0;\r\n\r\n      // Tạo job cho từng audience\r\n      for (const audience of audiences) {\r\n        if (!audience.email) {\r\n          this.logger.warn(`Audience ${audience.id} has no email, skipping`);\r\n          continue;\r\n        }\r\n\r\n        try {\r\n          // Lấy custom fields cho audience này\r\n          const customFields = await this.getAudienceCustomFields(audience.id);\r\n\r\n          // Tạo tracking ID\r\n          const trackingId = this.emailTrackingService.generateTrackingId(\r\n            campaignId,\r\n            audience.id,\r\n          );\r\n\r\n          // Tạo job data\r\n          const jobData: EmailMarketingJobDto = {\r\n            campaignId: campaign.id,\r\n            audienceId: audience.id,\r\n            email: audience.email,\r\n            subject: campaign.subject || '',\r\n            content: campaign.content || '',\r\n            customFields,\r\n            server: campaign.server || undefined,\r\n            trackingId,\r\n            createdAt: Date.now(),\r\n          };\r\n\r\n          // Thêm job vào queue\r\n          await this.emailMarketingQueue.add('send-email', jobData, {\r\n            delay: campaign.scheduledAt\r\n              ? Math.max(0, campaign.scheduledAt - Date.now())\r\n              : 0,\r\n            attempts: 3,\r\n            backoff: {\r\n              type: 'exponential',\r\n              delay: 2000,\r\n            },\r\n          });\r\n\r\n          jobCount++;\r\n          this.logger.debug(\r\n            `Created job for audience ${audience.id} (${audience.email})`,\r\n          );\r\n        } catch (error) {\r\n          this.logger.error(\r\n            `Error creating job for audience ${audience.id}: ${error.message}`,\r\n          );\r\n        }\r\n      }\r\n\r\n      this.logger.log(\r\n        `Created ${jobCount} email marketing jobs for campaign ${campaignId}`,\r\n      );\r\n      return jobCount;\r\n    } catch (error) {\r\n      this.logger.error(\r\n        `Error creating email marketing jobs: ${error.message}`,\r\n        error.stack,\r\n      );\r\n      throw error;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Lấy custom fields cho một audience\r\n   * @param audienceId ID của audience\r\n   * @returns Object chứa custom fields\r\n   */\r\n  private async getAudienceCustomFields(\r\n    audienceId: number,\r\n  ): Promise<Record<string, any>> {\r\n    try {\r\n      const customFields = await this.customFieldRepository.find({\r\n        where: { audienceId },\r\n      });\r\n\r\n      const result: Record<string, any> = {};\r\n\r\n      for (const field of customFields) {\r\n        if (field.fieldName) {\r\n          result[field.fieldName] = field.fieldValue;\r\n        }\r\n      }\r\n\r\n      return result;\r\n    } catch (error) {\r\n      this.logger.error(\r\n        `Error getting custom fields for audience ${audienceId}: ${error.message}`,\r\n      );\r\n      return {};\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Lấy thống kê campaign\r\n   * @param campaignId ID của campaign\r\n   * @returns Thống kê campaign\r\n   */\r\n  async getCampaignStats(campaignId: number): Promise<any> {\r\n    try {\r\n      // TODO: Implement campaign statistics\r\n      // Có thể lấy từ UserCampaignHistory để tính toán các metrics\r\n      return {\r\n        campaignId,\r\n        totalSent: 0,\r\n        totalOpened: 0,\r\n        totalClicked: 0,\r\n        totalBounced: 0,\r\n        totalFailed: 0,\r\n      };\r\n    } catch (error) {\r\n      this.logger.error(\r\n        `Error getting campaign stats: ${error.message}`,\r\n        error.stack,\r\n      );\r\n      throw error;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Hủy tất cả jobs của một campaign\r\n   * @param campaignId ID của campaign\r\n   * @returns Số lượng jobs đã hủy\r\n   */\r\n  async cancelCampaignJobs(campaignId: number): Promise<number> {\r\n    try {\r\n      this.logger.log(`Canceling jobs for campaign: ${campaignId}`);\r\n\r\n      // Lấy tất cả jobs đang chờ\r\n      const jobs = await this.emailMarketingQueue.getJobs([\r\n        'waiting',\r\n        'delayed',\r\n      ]);\r\n\r\n      let canceledCount = 0;\r\n\r\n      for (const job of jobs) {\r\n        const jobData = job.data as EmailMarketingJobDto;\r\n        if (jobData.campaignId === campaignId) {\r\n          await job.remove();\r\n          canceledCount++;\r\n        }\r\n      }\r\n\r\n      this.logger.log(\r\n        `Canceled ${canceledCount} jobs for campaign ${campaignId}`,\r\n      );\r\n      return canceledCount;\r\n    } catch (error) {\r\n      this.logger.error(\r\n        `Error canceling campaign jobs: ${error.message}`,\r\n        error.stack,\r\n      );\r\n      throw error;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Lấy trạng thái queue\r\n   * @returns Thông tin trạng thái queue\r\n   */\r\n  async getQueueStatus(): Promise<any> {\r\n    try {\r\n      const waiting = await this.emailMarketingQueue.getWaiting();\r\n      const active = await this.emailMarketingQueue.getActive();\r\n      const completed = await this.emailMarketingQueue.getCompleted();\r\n      const failed = await this.emailMarketingQueue.getFailed();\r\n      const delayed = await this.emailMarketingQueue.getDelayed();\r\n\r\n      return {\r\n        waiting: waiting.length,\r\n        active: active.length,\r\n        completed: completed.length,\r\n        failed: failed.length,\r\n        delayed: delayed.length,\r\n      };\r\n    } catch (error) {\r\n      this.logger.error(\r\n        `Error getting queue status: ${error.message}`,\r\n        error.stack,\r\n      );\r\n      throw error;\r\n    }\r\n  }\r\n}\r\n"], "names": ["EmailMarketingService", "createEmailMarketingJobs", "campaignId", "logger", "log", "campaign", "campaignRepository", "findOne", "where", "id", "Error", "platform", "audienceIds", "Array", "isArray", "segmentId", "warn", "audiences", "audienceRepository", "findByIds", "length", "jobCount", "audience", "email", "customFields", "getAudience<PERSON>ustomFields", "trackingId", "emailTrackingService", "generateTrackingId", "jobData", "audienceId", "subject", "content", "server", "undefined", "createdAt", "Date", "now", "emailMarketingQueue", "add", "delay", "scheduledAt", "Math", "max", "attempts", "backoff", "type", "debug", "error", "message", "stack", "customFieldRepository", "find", "result", "field", "fieldName", "fieldValue", "getCampaignStats", "totalSent", "totalOpened", "totalClicked", "totalBounced", "totalFailed", "cancelCampaignJobs", "jobs", "getJobs", "canceledCount", "job", "data", "remove", "getQueueStatus", "waiting", "getWaiting", "active", "getActive", "completed", "getCompleted", "failed", "getFailed", "delayed", "<PERSON><PERSON><PERSON><PERSON>", "constructor", "<PERSON><PERSON>", "name", "EMAIL_MARKETING"], "mappings": ";;;;+BAgBaA;;;eAAAA;;;wBAhBsB;yBACF;0BACN;wBACC;yBACN;oCACO;oCACA;+CACW;sCAEH;uBACX;;;;;;;;;;;;;;;AAMnB,IAAA,AAAMA,wBAAN,MAAMA;IAeX;;;;GAIC,GACD,MAAMC,yBAAyBC,UAAkB,EAAmB;QAClE,IAAI;YACF,IAAI,CAACC,MAAM,CAACC,GAAG,CACb,CAAC,4CAA4C,EAAEF,YAAY;YAG7D,yBAAyB;YACzB,MAAMG,WAAW,MAAM,IAAI,CAACC,kBAAkB,CAACC,OAAO,CAAC;gBACrDC,OAAO;oBAAEC,IAAIP;gBAAW;YAC1B;YAEA,IAAI,CAACG,UAAU;gBACb,MAAM,IAAIK,MAAM,CAAC,oBAAoB,EAAER,YAAY;YACrD;YAEA,IAAIG,SAASM,QAAQ,KAAK,SAAS;gBACjC,MAAM,IAAID,MAAM,CAAC,SAAS,EAAER,WAAW,yBAAyB,CAAC;YACnE;YAEA,6BAA6B;YAC7B,IAAIU,cAAwB,EAAE;YAE9B,IAAIP,SAASO,WAAW,IAAIC,MAAMC,OAAO,CAACT,SAASO,WAAW,GAAG;gBAC/DA,cAAcP,SAASO,WAAW;YACpC,OAAO,IAAIP,SAASU,SAAS,EAAE;gBAC7B,0CAA0C;gBAC1C,IAAI,CAACZ,MAAM,CAACa,IAAI,CACd,CAAC,2CAA2C,EAAEd,YAAY;gBAE5D,OAAO;YACT,OAAO;gBACL,IAAI,CAACC,MAAM,CAACa,IAAI,CACd,CAAC,0CAA0C,EAAEd,YAAY;gBAE3D,OAAO;YACT;YAEA,0BAA0B;YAC1B,MAAMe,YAAY,MAAM,IAAI,CAACC,kBAAkB,CAACC,SAAS,CAACP;YAE1D,IAAIK,UAAUG,MAAM,KAAK,GAAG;gBAC1B,IAAI,CAACjB,MAAM,CAACa,IAAI,CAAC,CAAC,sCAAsC,EAAEd,YAAY;gBACtE,OAAO;YACT;YAEA,IAAImB,WAAW;YAEf,4BAA4B;YAC5B,KAAK,MAAMC,YAAYL,UAAW;gBAChC,IAAI,CAACK,SAASC,KAAK,EAAE;oBACnB,IAAI,CAACpB,MAAM,CAACa,IAAI,CAAC,CAAC,SAAS,EAAEM,SAASb,EAAE,CAAC,uBAAuB,CAAC;oBACjE;gBACF;gBAEA,IAAI;oBACF,qCAAqC;oBACrC,MAAMe,eAAe,MAAM,IAAI,CAACC,uBAAuB,CAACH,SAASb,EAAE;oBAEnE,kBAAkB;oBAClB,MAAMiB,aAAa,IAAI,CAACC,oBAAoB,CAACC,kBAAkB,CAC7D1B,YACAoB,SAASb,EAAE;oBAGb,eAAe;oBACf,MAAMoB,UAAgC;wBACpC3B,YAAYG,SAASI,EAAE;wBACvBqB,YAAYR,SAASb,EAAE;wBACvBc,OAAOD,SAASC,KAAK;wBACrBQ,SAAS1B,SAAS0B,OAAO,IAAI;wBAC7BC,SAAS3B,SAAS2B,OAAO,IAAI;wBAC7BR;wBACAS,QAAQ5B,SAAS4B,MAAM,IAAIC;wBAC3BR;wBACAS,WAAWC,KAAKC,GAAG;oBACrB;oBAEA,qBAAqB;oBACrB,MAAM,IAAI,CAACC,mBAAmB,CAACC,GAAG,CAAC,cAAcV,SAAS;wBACxDW,OAAOnC,SAASoC,WAAW,GACvBC,KAAKC,GAAG,CAAC,GAAGtC,SAASoC,WAAW,GAAGL,KAAKC,GAAG,MAC3C;wBACJO,UAAU;wBACVC,SAAS;4BACPC,MAAM;4BACNN,OAAO;wBACT;oBACF;oBAEAnB;oBACA,IAAI,CAAClB,MAAM,CAAC4C,KAAK,CACf,CAAC,yBAAyB,EAAEzB,SAASb,EAAE,CAAC,EAAE,EAAEa,SAASC,KAAK,CAAC,CAAC,CAAC;gBAEjE,EAAE,OAAOyB,OAAO;oBACd,IAAI,CAAC7C,MAAM,CAAC6C,KAAK,CACf,CAAC,gCAAgC,EAAE1B,SAASb,EAAE,CAAC,EAAE,EAAEuC,MAAMC,OAAO,EAAE;gBAEtE;YACF;YAEA,IAAI,CAAC9C,MAAM,CAACC,GAAG,CACb,CAAC,QAAQ,EAAEiB,SAAS,mCAAmC,EAAEnB,YAAY;YAEvE,OAAOmB;QACT,EAAE,OAAO2B,OAAO;YACd,IAAI,CAAC7C,MAAM,CAAC6C,KAAK,CACf,CAAC,qCAAqC,EAAEA,MAAMC,OAAO,EAAE,EACvDD,MAAME,KAAK;YAEb,MAAMF;QACR;IACF;IAEA;;;;GAIC,GACD,MAAcvB,wBACZK,UAAkB,EACY;QAC9B,IAAI;YACF,MAAMN,eAAe,MAAM,IAAI,CAAC2B,qBAAqB,CAACC,IAAI,CAAC;gBACzD5C,OAAO;oBAAEsB;gBAAW;YACtB;YAEA,MAAMuB,SAA8B,CAAC;YAErC,KAAK,MAAMC,SAAS9B,aAAc;gBAChC,IAAI8B,MAAMC,SAAS,EAAE;oBACnBF,MAAM,CAACC,MAAMC,SAAS,CAAC,GAAGD,MAAME,UAAU;gBAC5C;YACF;YAEA,OAAOH;QACT,EAAE,OAAOL,OAAO;YACd,IAAI,CAAC7C,MAAM,CAAC6C,KAAK,CACf,CAAC,yCAAyC,EAAElB,WAAW,EAAE,EAAEkB,MAAMC,OAAO,EAAE;YAE5E,OAAO,CAAC;QACV;IACF;IAEA;;;;GAIC,GACD,MAAMQ,iBAAiBvD,UAAkB,EAAgB;QACvD,IAAI;YACF,sCAAsC;YACtC,6DAA6D;YAC7D,OAAO;gBACLA;gBACAwD,WAAW;gBACXC,aAAa;gBACbC,cAAc;gBACdC,cAAc;gBACdC,aAAa;YACf;QACF,EAAE,OAAOd,OAAO;YACd,IAAI,CAAC7C,MAAM,CAAC6C,KAAK,CACf,CAAC,8BAA8B,EAAEA,MAAMC,OAAO,EAAE,EAChDD,MAAME,KAAK;YAEb,MAAMF;QACR;IACF;IAEA;;;;GAIC,GACD,MAAMe,mBAAmB7D,UAAkB,EAAmB;QAC5D,IAAI;YACF,IAAI,CAACC,MAAM,CAACC,GAAG,CAAC,CAAC,6BAA6B,EAAEF,YAAY;YAE5D,2BAA2B;YAC3B,MAAM8D,OAAO,MAAM,IAAI,CAAC1B,mBAAmB,CAAC2B,OAAO,CAAC;gBAClD;gBACA;aACD;YAED,IAAIC,gBAAgB;YAEpB,KAAK,MAAMC,OAAOH,KAAM;gBACtB,MAAMnC,UAAUsC,IAAIC,IAAI;gBACxB,IAAIvC,QAAQ3B,UAAU,KAAKA,YAAY;oBACrC,MAAMiE,IAAIE,MAAM;oBAChBH;gBACF;YACF;YAEA,IAAI,CAAC/D,MAAM,CAACC,GAAG,CACb,CAAC,SAAS,EAAE8D,cAAc,mBAAmB,EAAEhE,YAAY;YAE7D,OAAOgE;QACT,EAAE,OAAOlB,OAAO;YACd,IAAI,CAAC7C,MAAM,CAAC6C,KAAK,CACf,CAAC,+BAA+B,EAAEA,MAAMC,OAAO,EAAE,EACjDD,MAAME,KAAK;YAEb,MAAMF;QACR;IACF;IAEA;;;GAGC,GACD,MAAMsB,iBAA+B;QACnC,IAAI;YACF,MAAMC,UAAU,MAAM,IAAI,CAACjC,mBAAmB,CAACkC,UAAU;YACzD,MAAMC,SAAS,MAAM,IAAI,CAACnC,mBAAmB,CAACoC,SAAS;YACvD,MAAMC,YAAY,MAAM,IAAI,CAACrC,mBAAmB,CAACsC,YAAY;YAC7D,MAAMC,SAAS,MAAM,IAAI,CAACvC,mBAAmB,CAACwC,SAAS;YACvD,MAAMC,UAAU,MAAM,IAAI,CAACzC,mBAAmB,CAAC0C,UAAU;YAEzD,OAAO;gBACLT,SAASA,QAAQnD,MAAM;gBACvBqD,QAAQA,OAAOrD,MAAM;gBACrBuD,WAAWA,UAAUvD,MAAM;gBAC3ByD,QAAQA,OAAOzD,MAAM;gBACrB2D,SAASA,QAAQ3D,MAAM;YACzB;QACF,EAAE,OAAO4B,OAAO;YACd,IAAI,CAAC7C,MAAM,CAAC6C,KAAK,CACf,CAAC,4BAA4B,EAAEA,MAAMC,OAAO,EAAE,EAC9CD,MAAME,KAAK;YAEb,MAAMF;QACR;IACF;IA1PAiC,YACE,AACiB3E,kBAA4C,EAC7D,AACiBY,kBAA4C,EAC7D,AACiBiC,qBAA0D,EAC3E,AACiBb,mBAA0B,EAC3C,AAAiBX,oBAA0C,CAC3D;aARiBrB,qBAAAA;aAEAY,qBAAAA;aAEAiC,wBAAAA;aAEAb,sBAAAA;aACAX,uBAAAA;aAXFxB,SAAS,IAAI+E,cAAM,CAAClF,sBAAsBmF,IAAI;IAY5D;AAiPL;;;;;;2DApP2BC"}