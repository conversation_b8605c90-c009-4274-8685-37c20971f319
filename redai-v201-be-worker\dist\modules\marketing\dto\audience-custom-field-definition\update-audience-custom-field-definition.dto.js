"use strict";
Object.defineProperty(exports, "__esModule", {
    value: true
});
Object.defineProperty(exports, "UpdateAudienceCustomFieldDefinitionDto", {
    enumerable: true,
    get: function() {
        return UpdateAudienceCustomFieldDefinitionDto;
    }
});
const _swagger = require("@nestjs/swagger");
const _classvalidator = require("class-validator");
const _createaudiencecustomfielddefinitiondto = require("./create-audience-custom-field-definition.dto");
function _ts_decorate(decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for(var i = decorators.length - 1; i >= 0; i--)if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
}
function _ts_metadata(k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
}
let UpdateAudienceCustomFieldDefinitionDto = class UpdateAudienceCustomFieldDefinitionDto {
};
_ts_decorate([
    (0, _swagger.ApiProperty)({
        description: 'Tên hiển thị thân thiện với người dùng',
        example: 'Địa chỉ khách hàng',
        required: false
    }),
    (0, _classvalidator.IsOptional)(),
    (0, _classvalidator.IsString)({
        message: 'Tên hiển thị phải là chuỗi'
    }),
    _ts_metadata("design:type", String)
], UpdateAudienceCustomFieldDefinitionDto.prototype, "displayName", void 0);
_ts_decorate([
    (0, _swagger.ApiProperty)({
        description: 'Kiểu dữ liệu',
        enum: _createaudiencecustomfielddefinitiondto.CustomFieldDataType,
        example: _createaudiencecustomfielddefinitiondto.CustomFieldDataType.STRING,
        required: false
    }),
    (0, _classvalidator.IsOptional)(),
    (0, _classvalidator.IsEnum)(_createaudiencecustomfielddefinitiondto.CustomFieldDataType, {
        message: `Kiểu dữ liệu phải là một trong các giá trị: ${Object.values(_createaudiencecustomfielddefinitiondto.CustomFieldDataType).join(', ')}`
    }),
    _ts_metadata("design:type", typeof _createaudiencecustomfielddefinitiondto.CustomFieldDataType === "undefined" ? Object : _createaudiencecustomfielddefinitiondto.CustomFieldDataType)
], UpdateAudienceCustomFieldDefinitionDto.prototype, "dataType", void 0);
_ts_decorate([
    (0, _swagger.ApiProperty)({
        description: 'Mô tả chi tiết hoặc ghi chú về trường tùy chỉnh',
        example: 'Địa chỉ liên hệ của khách hàng',
        required: false
    }),
    (0, _classvalidator.IsOptional)(),
    (0, _classvalidator.IsString)({
        message: 'Mô tả phải là chuỗi'
    }),
    _ts_metadata("design:type", String)
], UpdateAudienceCustomFieldDefinitionDto.prototype, "description", void 0);

//# sourceMappingURL=update-audience-custom-field-definition.dto.js.map