{"version": 3, "sources": ["../../../../../src/modules/marketing/dto/zalo/zalo-automation.dto.ts"], "sourcesContent": ["import { ApiProperty } from '@nestjs/swagger';\r\nimport {\r\n  IsArray,\r\n  IsEnum,\r\n  IsNotEmpty,\r\n  IsObject,\r\n  IsOptional,\r\n  IsString,\r\n} from 'class-validator';\r\nimport {\r\n  ZaloCampaignMessageContentDto,\r\n  ZaloCampaignZnsContentDto,\r\n} from './zalo-campaign.dto';\r\nimport { ZaloSegmentConditionDto } from './zalo-segment.dto';\r\n\r\n/**\r\n * Enum cho loại sự kiện tự động hóa Zalo\r\n */\r\nexport enum ZaloAutomationTriggerType {\r\n  FOLLOW = 'follow',\r\n  UNFOLLOW = 'unfollow',\r\n  MESSAGE = 'message',\r\n  TAG_ADDED = 'tag_added',\r\n  TAG_REMOVED = 'tag_removed',\r\n}\r\n\r\n/**\r\n * Enum cho loại hành động tự động hóa Zalo\r\n */\r\nexport enum ZaloAutomationActionType {\r\n  SEND_MESSAGE = 'send_message',\r\n  SEND_ZNS = 'send_zns',\r\n  ADD_TAG = 'add_tag',\r\n  REMOVE_TAG = 'remove_tag',\r\n}\r\n\r\n/**\r\n * Enum cho trạng thái tự động hóa Zalo\r\n */\r\nexport enum ZaloAutomationStatus {\r\n  ACTIVE = 'active',\r\n  INACTIVE = 'inactive',\r\n}\r\n\r\n/**\r\n * DTO cho điều kiện kích hoạt tự động hóa Zalo\r\n */\r\nexport class ZaloAutomationTriggerDto {\r\n  @ApiProperty({\r\n    description: 'Loại sự kiện kích hoạt',\r\n    enum: ZaloAutomationTriggerType,\r\n    example: ZaloAutomationTriggerType.FOLLOW,\r\n  })\r\n  @IsEnum(ZaloAutomationTriggerType)\r\n  @IsNotEmpty()\r\n  type: ZaloAutomationTriggerType;\r\n\r\n  @ApiProperty({\r\n    description: 'Điều kiện bổ sung (nếu có)',\r\n    type: [ZaloSegmentConditionDto],\r\n    required: false,\r\n  })\r\n  @IsArray()\r\n  @IsOptional()\r\n  conditions?: ZaloSegmentConditionDto[];\r\n}\r\n\r\n/**\r\n * DTO cho hành động tự động hóa Zalo\r\n */\r\nexport class ZaloAutomationActionDto {\r\n  @ApiProperty({\r\n    description: 'Loại hành động',\r\n    enum: ZaloAutomationActionType,\r\n    example: ZaloAutomationActionType.SEND_MESSAGE,\r\n  })\r\n  @IsEnum(ZaloAutomationActionType)\r\n  @IsNotEmpty()\r\n  type: ZaloAutomationActionType;\r\n\r\n  @ApiProperty({\r\n    description: 'Nội dung tin nhắn (chỉ dùng khi type là send_message)',\r\n    type: ZaloCampaignMessageContentDto,\r\n    required: false,\r\n  })\r\n  @IsObject()\r\n  @IsOptional()\r\n  messageContent?: ZaloCampaignMessageContentDto;\r\n\r\n  @ApiProperty({\r\n    description: 'Nội dung ZNS (chỉ dùng khi type là send_zns)',\r\n    type: ZaloCampaignZnsContentDto,\r\n    required: false,\r\n  })\r\n  @IsObject()\r\n  @IsOptional()\r\n  znsContent?: ZaloCampaignZnsContentDto;\r\n\r\n  @ApiProperty({\r\n    description:\r\n      'Tag cần thêm/xóa (chỉ dùng khi type là add_tag hoặc remove_tag)',\r\n    example: 'vip',\r\n    required: false,\r\n  })\r\n  @IsString()\r\n  @IsOptional()\r\n  tag?: string;\r\n}\r\n\r\n/**\r\n * DTO cho việc tạo tự động hóa Zalo\r\n */\r\nexport class CreateZaloAutomationDto {\r\n  @ApiProperty({\r\n    description: 'ID của Official Account',\r\n    example: '*********',\r\n  })\r\n  @IsString()\r\n  @IsNotEmpty()\r\n  oaId: string;\r\n\r\n  @ApiProperty({\r\n    description: 'Tên của tự động hóa',\r\n    example: 'Chào mừng người theo dõi mới',\r\n  })\r\n  @IsString()\r\n  @IsNotEmpty()\r\n  name: string;\r\n\r\n  @ApiProperty({\r\n    description: 'Mô tả của tự động hóa',\r\n    example: 'Gửi tin nhắn chào mừng khi có người theo dõi mới',\r\n    required: false,\r\n  })\r\n  @IsString()\r\n  @IsOptional()\r\n  description?: string;\r\n\r\n  @ApiProperty({\r\n    description: 'Điều kiện kích hoạt',\r\n    type: ZaloAutomationTriggerDto,\r\n  })\r\n  @IsObject()\r\n  @IsNotEmpty()\r\n  trigger: ZaloAutomationTriggerDto;\r\n\r\n  @ApiProperty({\r\n    description: 'Danh sách hành động',\r\n    type: [ZaloAutomationActionDto],\r\n  })\r\n  @IsArray()\r\n  @IsNotEmpty()\r\n  actions: ZaloAutomationActionDto[];\r\n}\r\n\r\n/**\r\n * DTO cho việc cập nhật tự động hóa Zalo\r\n */\r\nexport class UpdateZaloAutomationDto {\r\n  @ApiProperty({\r\n    description: 'Tên của tự động hóa',\r\n    example: 'Chào mừng người theo dõi mới - Cập nhật',\r\n    required: false,\r\n  })\r\n  @IsString()\r\n  @IsOptional()\r\n  name?: string;\r\n\r\n  @ApiProperty({\r\n    description: 'Mô tả của tự động hóa',\r\n    example: 'Gửi tin nhắn chào mừng khi có người theo dõi mới - Cập nhật',\r\n    required: false,\r\n  })\r\n  @IsString()\r\n  @IsOptional()\r\n  description?: string;\r\n\r\n  @ApiProperty({\r\n    description: 'Điều kiện kích hoạt',\r\n    type: ZaloAutomationTriggerDto,\r\n    required: false,\r\n  })\r\n  @IsObject()\r\n  @IsOptional()\r\n  trigger?: ZaloAutomationTriggerDto;\r\n\r\n  @ApiProperty({\r\n    description: 'Danh sách hành động',\r\n    type: [ZaloAutomationActionDto],\r\n    required: false,\r\n  })\r\n  @IsArray()\r\n  @IsOptional()\r\n  actions?: ZaloAutomationActionDto[];\r\n\r\n  @ApiProperty({\r\n    description: 'Trạng thái tự động hóa',\r\n    enum: ZaloAutomationStatus,\r\n    example: ZaloAutomationStatus.ACTIVE,\r\n    required: false,\r\n  })\r\n  @IsEnum(ZaloAutomationStatus)\r\n  @IsOptional()\r\n  status?: ZaloAutomationStatus;\r\n}\r\n\r\n/**\r\n * DTO cho phản hồi thông tin tự động hóa Zalo\r\n */\r\nexport class ZaloAutomationResponseDto {\r\n  @ApiProperty({\r\n    description: 'ID của tự động hóa',\r\n    example: 1,\r\n  })\r\n  id: number;\r\n\r\n  @ApiProperty({\r\n    description: 'ID của người dùng',\r\n    example: 123,\r\n  })\r\n  userId: number;\r\n\r\n  @ApiProperty({\r\n    description: 'ID của Official Account',\r\n    example: '*********',\r\n  })\r\n  oaId: string;\r\n\r\n  @ApiProperty({\r\n    description: 'Tên của tự động hóa',\r\n    example: 'Chào mừng người theo dõi mới',\r\n  })\r\n  name: string;\r\n\r\n  @ApiProperty({\r\n    description: 'Mô tả của tự động hóa',\r\n    example: 'Gửi tin nhắn chào mừng khi có người theo dõi mới',\r\n    nullable: true,\r\n  })\r\n  description?: string;\r\n\r\n  @ApiProperty({\r\n    description: 'Điều kiện kích hoạt',\r\n    type: ZaloAutomationTriggerDto,\r\n  })\r\n  trigger: ZaloAutomationTriggerDto;\r\n\r\n  @ApiProperty({\r\n    description: 'Danh sách hành động',\r\n    type: [ZaloAutomationActionDto],\r\n  })\r\n  actions: ZaloAutomationActionDto[];\r\n\r\n  @ApiProperty({\r\n    description: 'Trạng thái tự động hóa',\r\n    enum: ZaloAutomationStatus,\r\n    example: ZaloAutomationStatus.ACTIVE,\r\n  })\r\n  status: ZaloAutomationStatus;\r\n\r\n  @ApiProperty({\r\n    description: 'Số lần đã kích hoạt',\r\n    example: 100,\r\n  })\r\n  triggerCount: number;\r\n\r\n  @ApiProperty({\r\n    description: 'Thời điểm tạo (Unix timestamp)',\r\n    example: 1625097600000,\r\n  })\r\n  createdAt: number;\r\n\r\n  @ApiProperty({\r\n    description: 'Thời điểm cập nhật (Unix timestamp)',\r\n    example: 1625097600000,\r\n  })\r\n  updatedAt: number;\r\n}\r\n\r\n/**\r\n * DTO cho việc truy vấn danh sách tự động hóa Zalo\r\n */\r\nexport class ZaloAutomationQueryDto {\r\n  @ApiProperty({\r\n    description: 'Tìm kiếm theo tên tự động hóa',\r\n    example: 'chào mừng',\r\n    required: false,\r\n  })\r\n  @IsString()\r\n  @IsOptional()\r\n  name?: string;\r\n\r\n  @ApiProperty({\r\n    description: 'Lọc theo loại sự kiện kích hoạt',\r\n    enum: ZaloAutomationTriggerType,\r\n    example: ZaloAutomationTriggerType.FOLLOW,\r\n    required: false,\r\n  })\r\n  @IsEnum(ZaloAutomationTriggerType)\r\n  @IsOptional()\r\n  triggerType?: ZaloAutomationTriggerType;\r\n\r\n  @ApiProperty({\r\n    description: 'Lọc theo trạng thái tự động hóa',\r\n    enum: ZaloAutomationStatus,\r\n    example: ZaloAutomationStatus.ACTIVE,\r\n    required: false,\r\n  })\r\n  @IsEnum(ZaloAutomationStatus)\r\n  @IsOptional()\r\n  status?: ZaloAutomationStatus;\r\n\r\n  @ApiProperty({\r\n    description: 'Số trang',\r\n    example: 1,\r\n    default: 1,\r\n    required: false,\r\n  })\r\n  @IsOptional()\r\n  page?: number = 1;\r\n\r\n  @ApiProperty({\r\n    description: 'Số lượng tự động hóa trên mỗi trang',\r\n    example: 10,\r\n    default: 10,\r\n    required: false,\r\n  })\r\n  @IsOptional()\r\n  limit?: number = 10;\r\n}\r\n"], "names": ["CreateZaloAutomationDto", "UpdateZaloAutomationDto", "ZaloAutomationActionDto", "ZaloAutomationActionType", "ZaloAutomationQueryDto", "ZaloAutomationResponseDto", "ZaloAutomationStatus", "ZaloAutomationTriggerDto", "ZaloAutomationTriggerType", "description", "enum", "example", "type", "ZaloSegmentConditionDto", "required", "ZaloCampaignMessageContentDto", "ZaloCampaignZnsContentDto", "nullable", "page", "limit", "default"], "mappings": ";;;;;;;;;;;QAgHaA;eAAAA;;QA8CAC;eAAAA;;QAxFAC;eAAAA;;QAzCDC;eAAAA;;QA6PCC;eAAAA;;QAzEAC;eAAAA;;QA1KDC;eAAAA;;QAQCC;eAAAA;;QA7BDC;eAAAA;;;yBAlBgB;gCAQrB;iCAIA;gCACiC;;;;;;;;;;AAKjC,IAAA,AAAKA,mDAAAA;;;;;;WAAAA;;AAWL,IAAA,AAAKL,kDAAAA;;;;;WAAAA;;AAUL,IAAA,AAAKG,8CAAAA;;;WAAAA;;AAQL,IAAA,AAAMC,2BAAN,MAAMA;AAkBb;;;QAhBIE,aAAa;QACbC,MAAMF;QACNG,OAAO;;;;;;;;QAOPF,aAAa;QACbG,MAAM;YAACC,uCAAuB;SAAC;QAC/BC,UAAU;;;;;;AAUP,IAAA,AAAMZ,0BAAN,MAAMA;AAqCb;;;QAnCIO,aAAa;QACbC,MAAMP;QACNQ,OAAO;;;;;;;;QAOPF,aAAa;QACbG,MAAMG,8CAA6B;QACnCD,UAAU;;;;;;;;QAOVL,aAAa;QACbG,MAAMI,0CAAyB;QAC/BF,UAAU;;;;;;;;QAOVL,aACE;QACFE,SAAS;QACTG,UAAU;;;;;;AAUP,IAAA,AAAMd,0BAAN,MAAMA;AAyCb;;;QAvCIS,aAAa;QACbE,SAAS;;;;;;;;QAOTF,aAAa;QACbE,SAAS;;;;;;;;QAOTF,aAAa;QACbE,SAAS;QACTG,UAAU;;;;;;;;QAOVL,aAAa;QACbG,MAAML;;;;;;;;QAONE,aAAa;QACbG,MAAM;YAACV;SAAwB;;;;;;AAU5B,IAAA,AAAMD,0BAAN,MAAMA;AA8Cb;;;QA5CIQ,aAAa;QACbE,SAAS;QACTG,UAAU;;;;;;;;QAOVL,aAAa;QACbE,SAAS;QACTG,UAAU;;;;;;;;QAOVL,aAAa;QACbG,MAAML;QACNO,UAAU;;;;;;;;QAOVL,aAAa;QACbG,MAAM;YAACV;SAAwB;QAC/BY,UAAU;;;;;;;;QAOVL,aAAa;QACbC,MAAMJ;QACNK,OAAO;QACPG,UAAU;;;;;;AAUP,IAAA,AAAMT,4BAAN,MAAMA;AAoEb;;;QAlEII,aAAa;QACbE,SAAS;;;;;;QAKTF,aAAa;QACbE,SAAS;;;;;;QAKTF,aAAa;QACbE,SAAS;;;;;;QAKTF,aAAa;QACbE,SAAS;;;;;;QAKTF,aAAa;QACbE,SAAS;QACTM,UAAU;;;;;;QAKVR,aAAa;QACbG,MAAML;;;;;;QAKNE,aAAa;QACbG,MAAM;YAACV;SAAwB;;;;;;QAK/BO,aAAa;QACbC,MAAMJ;QACNK,OAAO;;;;;;QAKPF,aAAa;QACbE,SAAS;;;;;;QAKTF,aAAa;QACbE,SAAS;;;;;;QAKTF,aAAa;QACbE,SAAS;;;;AAQN,IAAA,AAAMP,yBAAN,MAAMA;;aAqCXc,OAAgB;aAShBC,QAAiB;;AACnB;;;QA7CIV,aAAa;QACbE,SAAS;QACTG,UAAU;;;;;;;;QAOVL,aAAa;QACbC,MAAMF;QACNG,OAAO;QACPG,UAAU;;;;;;;;QAOVL,aAAa;QACbC,MAAMJ;QACNK,OAAO;QACPG,UAAU;;;;;;;;QAOVL,aAAa;QACbE,SAAS;QACTS,SAAS;QACTN,UAAU;;;;;;;QAMVL,aAAa;QACbE,SAAS;QACTS,SAAS;QACTN,UAAU"}