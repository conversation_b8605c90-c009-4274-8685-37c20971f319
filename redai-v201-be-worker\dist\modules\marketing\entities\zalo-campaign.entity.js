"use strict";
Object.defineProperty(exports, "__esModule", {
    value: true
});
Object.defineProperty(exports, "ZaloCampaign", {
    enumerable: true,
    get: function() {
        return ZaloCampaign;
    }
});
const _typeorm = require("typeorm");
const _zalo = require("../dto/zalo");
function _ts_decorate(decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for(var i = decorators.length - 1; i >= 0; i--)if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
}
function _ts_metadata(k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
}
let ZaloCampaign = class ZaloCampaign {
};
_ts_decorate([
    (0, _typeorm.PrimaryGeneratedColumn)(),
    _ts_metadata("design:type", Number)
], ZaloCampaign.prototype, "id", void 0);
_ts_decorate([
    (0, _typeorm.Column)({
        name: 'user_id'
    }),
    _ts_metadata("design:type", Number)
], ZaloCampaign.prototype, "userId", void 0);
_ts_decorate([
    (0, _typeorm.Column)({
        name: 'oa_id'
    }),
    _ts_metadata("design:type", String)
], ZaloCampaign.prototype, "oaId", void 0);
_ts_decorate([
    (0, _typeorm.Column)(),
    _ts_metadata("design:type", String)
], ZaloCampaign.prototype, "name", void 0);
_ts_decorate([
    (0, _typeorm.Column)({
        nullable: true
    }),
    _ts_metadata("design:type", String)
], ZaloCampaign.prototype, "description", void 0);
_ts_decorate([
    (0, _typeorm.Column)({
        type: 'enum',
        enum: _zalo.ZaloCampaignType
    }),
    _ts_metadata("design:type", typeof _zalo.ZaloCampaignType === "undefined" ? Object : _zalo.ZaloCampaignType)
], ZaloCampaign.prototype, "type", void 0);
_ts_decorate([
    (0, _typeorm.Column)({
        name: 'segment_id'
    }),
    _ts_metadata("design:type", Number)
], ZaloCampaign.prototype, "segmentId", void 0);
_ts_decorate([
    (0, _typeorm.Column)({
        type: 'enum',
        enum: _zalo.ZaloCampaignStatus,
        default: _zalo.ZaloCampaignStatus.DRAFT
    }),
    _ts_metadata("design:type", typeof _zalo.ZaloCampaignStatus === "undefined" ? Object : _zalo.ZaloCampaignStatus)
], ZaloCampaign.prototype, "status", void 0);
_ts_decorate([
    (0, _typeorm.Column)({
        name: 'scheduled_at',
        type: 'bigint',
        nullable: true
    }),
    _ts_metadata("design:type", Number)
], ZaloCampaign.prototype, "scheduledAt", void 0);
_ts_decorate([
    (0, _typeorm.Column)({
        name: 'started_at',
        type: 'bigint',
        nullable: true
    }),
    _ts_metadata("design:type", Number)
], ZaloCampaign.prototype, "startedAt", void 0);
_ts_decorate([
    (0, _typeorm.Column)({
        name: 'completed_at',
        type: 'bigint',
        nullable: true
    }),
    _ts_metadata("design:type", Number)
], ZaloCampaign.prototype, "completedAt", void 0);
_ts_decorate([
    (0, _typeorm.Column)({
        name: 'message_content',
        type: 'json',
        nullable: true
    }),
    _ts_metadata("design:type", typeof _zalo.ZaloCampaignMessageContentDto === "undefined" ? Object : _zalo.ZaloCampaignMessageContentDto)
], ZaloCampaign.prototype, "messageContent", void 0);
_ts_decorate([
    (0, _typeorm.Column)({
        name: 'zns_content',
        type: 'json',
        nullable: true
    }),
    _ts_metadata("design:type", typeof _zalo.ZaloCampaignZnsContentDto === "undefined" ? Object : _zalo.ZaloCampaignZnsContentDto)
], ZaloCampaign.prototype, "znsContent", void 0);
_ts_decorate([
    (0, _typeorm.Column)({
        name: 'total_recipients',
        default: 0
    }),
    _ts_metadata("design:type", Number)
], ZaloCampaign.prototype, "totalRecipients", void 0);
_ts_decorate([
    (0, _typeorm.Column)({
        name: 'success_count',
        default: 0
    }),
    _ts_metadata("design:type", Number)
], ZaloCampaign.prototype, "successCount", void 0);
_ts_decorate([
    (0, _typeorm.Column)({
        name: 'failure_count',
        default: 0
    }),
    _ts_metadata("design:type", Number)
], ZaloCampaign.prototype, "failureCount", void 0);
_ts_decorate([
    (0, _typeorm.Column)({
        name: 'created_at',
        type: 'bigint'
    }),
    _ts_metadata("design:type", Number)
], ZaloCampaign.prototype, "createdAt", void 0);
_ts_decorate([
    (0, _typeorm.Column)({
        name: 'updated_at',
        type: 'bigint'
    }),
    _ts_metadata("design:type", Number)
], ZaloCampaign.prototype, "updatedAt", void 0);
ZaloCampaign = _ts_decorate([
    (0, _typeorm.Entity)('zalo_campaigns')
], ZaloCampaign);

//# sourceMappingURL=zalo-campaign.entity.js.map