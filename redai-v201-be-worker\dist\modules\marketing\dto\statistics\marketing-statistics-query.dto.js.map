{"version": 3, "sources": ["../../../../../src/modules/marketing/dto/statistics/marketing-statistics-query.dto.ts"], "sourcesContent": ["import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';\r\nimport { IsEnum, IsNumber, IsOptional, IsString } from 'class-validator';\r\nimport { Type } from 'class-transformer';\r\n\r\n/**\r\n * Enum cho loại thống kê\r\n */\r\nexport enum StatisticsType {\r\n  DAILY = 'daily',\r\n  WEEKLY = 'weekly',\r\n  MONTHLY = 'monthly',\r\n  YEARLY = 'yearly',\r\n}\r\n\r\n/**\r\n * DTO cho tham số truy vấn thống kê marketing\r\n */\r\nexport class MarketingStatisticsQueryDto {\r\n  @ApiPropertyOptional({\r\n    description: 'Thời gian bắt đầu thống kê (Unix timestamp)',\r\n    example: 1672531200,\r\n  })\r\n  @IsOptional()\r\n  @IsNumber()\r\n  @Type(() => Number)\r\n  startDate?: number;\r\n\r\n  @ApiPropertyOptional({\r\n    description: 'Thời gian kết thúc thống kê (Unix timestamp)',\r\n    example: 1675209600,\r\n  })\r\n  @IsOptional()\r\n  @IsNumber()\r\n  @Type(() => Number)\r\n  endDate?: number;\r\n\r\n  @ApiPropertyOptional({\r\n    description: '<PERSON>ại thống kê',\r\n    enum: StatisticsType,\r\n    default: StatisticsType.MONTHLY,\r\n  })\r\n  @IsOptional()\r\n  @IsEnum(StatisticsType)\r\n  type?: StatisticsType = StatisticsType.MONTHLY;\r\n}\r\n"], "names": ["MarketingStatisticsQueryDto", "StatisticsType", "type", "description", "example", "Number", "enum", "default"], "mappings": ";;;;;;;;;;;QAiBaA;eAAAA;;QAVDC;eAAAA;;;yBAPqC;gCACM;kCAClC;;;;;;;;;;AAKd,IAAA,AAAKA,wCAAAA;;;;;WAAAA;;AAUL,IAAA,AAAMD,8BAAN,MAAMA;;aA0BXE;;AACF;;;QAzBIC,aAAa;QACbC,SAAS;;;;oCAICC;;;;;QAIVF,aAAa;QACbC,SAAS;;;;oCAICC;;;;;QAIVF,aAAa;QACbG,MAAML;QACNM,OAAO"}