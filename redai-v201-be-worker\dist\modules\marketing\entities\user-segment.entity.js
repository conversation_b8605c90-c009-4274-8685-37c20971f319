"use strict";
Object.defineProperty(exports, "__esModule", {
    value: true
});
Object.defineProperty(exports, "UserSegment", {
    enumerable: true,
    get: function() {
        return UserSegment;
    }
});
const _typeorm = require("typeorm");
function _ts_decorate(decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for(var i = decorators.length - 1; i >= 0; i--)if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
}
function _ts_metadata(k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
}
let UserSegment = class UserSegment {
};
_ts_decorate([
    (0, _typeorm.PrimaryGeneratedColumn)({
        name: 'id',
        type: 'bigint'
    }),
    _ts_metadata("design:type", Number)
], UserSegment.prototype, "id", void 0);
_ts_decorate([
    (0, _typeorm.Column)({
        name: 'user_id',
        comment: 'Mã khách hàng'
    }),
    _ts_metadata("design:type", Number)
], UserSegment.prototype, "userId", void 0);
_ts_decorate([
    (0, _typeorm.Column)({
        name: 'name',
        length: 255,
        nullable: true,
        comment: 'Tên tập khách hàng'
    }),
    _ts_metadata("design:type", String)
], UserSegment.prototype, "name", void 0);
_ts_decorate([
    (0, _typeorm.Column)({
        name: 'description',
        type: 'text',
        nullable: true,
        comment: 'Mô tả'
    }),
    _ts_metadata("design:type", String)
], UserSegment.prototype, "description", void 0);
_ts_decorate([
    (0, _typeorm.Column)({
        name: 'criteria',
        type: 'jsonb',
        nullable: true,
        comment: 'Lưu trữ điều kiện lọc khách hàng khi tạo segment'
    }),
    _ts_metadata("design:type", Object)
], UserSegment.prototype, "criteria", void 0);
_ts_decorate([
    (0, _typeorm.Column)({
        name: 'created_at',
        type: 'bigint',
        nullable: true,
        comment: 'Thời gian tạo'
    }),
    _ts_metadata("design:type", Number)
], UserSegment.prototype, "createdAt", void 0);
_ts_decorate([
    (0, _typeorm.Column)({
        name: 'updated_at',
        type: 'bigint',
        nullable: true,
        comment: 'Thời gian cập nhật'
    }),
    _ts_metadata("design:type", Number)
], UserSegment.prototype, "updatedAt", void 0);
UserSegment = _ts_decorate([
    (0, _typeorm.Entity)('user_segments')
], UserSegment);

//# sourceMappingURL=user-segment.entity.js.map