"use strict";
Object.defineProperty(exports, "__esModule", {
    value: true
});
function _export(target, all) {
    for(var name in all)Object.defineProperty(target, name, {
        enumerable: true,
        get: Object.getOwnPropertyDescriptor(all, name).get
    });
}
_export(exports, {
    get CampaignPerformanceDto () {
        return CampaignPerformanceDto;
    },
    get CampaignPerformanceStatisticsDto () {
        return CampaignPerformanceStatisticsDto;
    }
});
const _swagger = require("@nestjs/swagger");
function _ts_decorate(decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for(var i = decorators.length - 1; i >= 0; i--)if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
}
function _ts_metadata(k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
}
let CampaignPerformanceDto = class CampaignPerformanceDto {
};
_ts_decorate([
    (0, _swagger.ApiProperty)({
        description: 'ID của campaign',
        example: 1
    }),
    _ts_metadata("design:type", Number)
], CampaignPerformanceDto.prototype, "id", void 0);
_ts_decorate([
    (0, _swagger.ApiProperty)({
        description: 'Tên campaign',
        example: 'Chiến dịch khuyến mãi mùa hè'
    }),
    _ts_metadata("design:type", String)
], CampaignPerformanceDto.prototype, "name", void 0);
_ts_decorate([
    (0, _swagger.ApiProperty)({
        description: 'Tổng số người nhận',
        example: 100
    }),
    _ts_metadata("design:type", Number)
], CampaignPerformanceDto.prototype, "totalRecipients", void 0);
_ts_decorate([
    (0, _swagger.ApiProperty)({
        description: 'Số lượng đã gửi',
        example: 100
    }),
    _ts_metadata("design:type", Number)
], CampaignPerformanceDto.prototype, "sent", void 0);
_ts_decorate([
    (0, _swagger.ApiProperty)({
        description: 'Số lượng đã nhận',
        example: 95
    }),
    _ts_metadata("design:type", Number)
], CampaignPerformanceDto.prototype, "delivered", void 0);
_ts_decorate([
    (0, _swagger.ApiProperty)({
        description: 'Số lượng đã mở (chỉ áp dụng cho email)',
        example: 50
    }),
    _ts_metadata("design:type", Number)
], CampaignPerformanceDto.prototype, "opened", void 0);
_ts_decorate([
    (0, _swagger.ApiProperty)({
        description: 'Số lượng đã nhấp (chỉ áp dụng cho email)',
        example: 20
    }),
    _ts_metadata("design:type", Number)
], CampaignPerformanceDto.prototype, "clicked", void 0);
_ts_decorate([
    (0, _swagger.ApiProperty)({
        description: 'Tỷ lệ mở (%)',
        example: 50
    }),
    _ts_metadata("design:type", Number)
], CampaignPerformanceDto.prototype, "openRate", void 0);
_ts_decorate([
    (0, _swagger.ApiProperty)({
        description: 'Tỷ lệ nhấp (%)',
        example: 20
    }),
    _ts_metadata("design:type", Number)
], CampaignPerformanceDto.prototype, "clickRate", void 0);
_ts_decorate([
    (0, _swagger.ApiProperty)({
        description: 'Thời gian chạy campaign (Unix timestamp)',
        example: 1619171200
    }),
    _ts_metadata("design:type", Number)
], CampaignPerformanceDto.prototype, "runAt", void 0);
let CampaignPerformanceStatisticsDto = class CampaignPerformanceStatisticsDto {
};
_ts_decorate([
    (0, _swagger.ApiProperty)({
        description: 'Danh sách hiệu suất campaign',
        type: [
            CampaignPerformanceDto
        ]
    }),
    _ts_metadata("design:type", Array)
], CampaignPerformanceStatisticsDto.prototype, "campaigns", void 0);
_ts_decorate([
    (0, _swagger.ApiProperty)({
        description: 'Tỷ lệ mở trung bình (%)',
        example: 45.5
    }),
    _ts_metadata("design:type", Number)
], CampaignPerformanceStatisticsDto.prototype, "averageOpenRate", void 0);
_ts_decorate([
    (0, _swagger.ApiProperty)({
        description: 'Tỷ lệ nhấp trung bình (%)',
        example: 18.3
    }),
    _ts_metadata("design:type", Number)
], CampaignPerformanceStatisticsDto.prototype, "averageClickRate", void 0);
_ts_decorate([
    (0, _swagger.ApiProperty)({
        description: 'Thời gian cập nhật thống kê (Unix timestamp)',
        example: 1619171200
    }),
    _ts_metadata("design:type", Number)
], CampaignPerformanceStatisticsDto.prototype, "updatedAt", void 0);

//# sourceMappingURL=campaign-performance-statistics.dto.js.map