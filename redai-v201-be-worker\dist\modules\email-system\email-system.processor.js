"use strict";
Object.defineProperty(exports, "__esModule", {
    value: true
});
Object.defineProperty(exports, "EmailSystemProcessor", {
    enumerable: true,
    get: function() {
        return EmailSystemProcessor;
    }
});
const _bullmq = require("@nestjs/bullmq");
const _common = require("@nestjs/common");
const _queue = require("../../queue");
const _emailsystemservice = require("./email-system.service");
const _emailservice = require("../../shared/services/email.service");
function _ts_decorate(decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for(var i = decorators.length - 1; i >= 0; i--)if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
}
function _ts_metadata(k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
}
let EmailSystemProcessor = class EmailSystemProcessor extends _bullmq.WorkerHost {
    /**
   * Xử lý job gửi email
   * @param job Job từ queue
   */ async process(job) {
        try {
            this.logger.log(`Đang xử lý job email ${job.id} với category: ${job.data.category}`);
            const { category, data, to } = job.data;
            // Lấy template email từ database
            const template = await this.emailSystemService.getTemplateByCategory(category);
            if (!template) {
                throw new Error(`Không tìm thấy template email với category: ${category}`);
            }
            // Thay thế các placeholder trong tiêu đề và nội dung
            const subject = this.emailSystemService.replacePlaceholders(template.subject, data);
            const content = this.emailSystemService.replacePlaceholders(template.content, data);
            // Gửi email
            const result = await this.emailService.sendEmail(to, subject, content);
            if (result) {
                this.logger.log(`Đã gửi email thành công đến ${to} với category: ${category}`);
                return {
                    success: true,
                    message: 'Email đã được gửi thành công'
                };
            } else {
                throw new Error('Không thể gửi email');
            }
        } catch (error) {
            this.logger.error(`Lỗi khi xử lý job email: ${error.message}`, error.stack);
            throw error;
        }
    }
    constructor(emailSystemService, emailService){
        super(), this.emailSystemService = emailSystemService, this.emailService = emailService, this.logger = new _common.Logger(EmailSystemProcessor.name);
    }
};
EmailSystemProcessor = _ts_decorate([
    (0, _common.Injectable)(),
    (0, _bullmq.Processor)(_queue.QueueName.EMAIL_SYSTEM),
    _ts_metadata("design:type", Function),
    _ts_metadata("design:paramtypes", [
        typeof _emailsystemservice.EmailSystemService === "undefined" ? Object : _emailsystemservice.EmailSystemService,
        typeof _emailservice.EmailService === "undefined" ? Object : _emailservice.EmailService
    ])
], EmailSystemProcessor);

//# sourceMappingURL=email-system.processor.js.map