{"version": 3, "sources": ["../../../../../src/modules/marketing/dto/audience/audience-response.dto.ts"], "sourcesContent": ["import { ApiProperty } from '@nestjs/swagger';\r\nimport { CustomFieldResponseDto } from './custom-field-response.dto';\r\nimport { TagResponseDto } from '../tag/tag-response.dto';\r\n\r\n/**\r\n * DTO cho phản hồi thông tin audience\r\n */\r\nexport class AudienceResponseDto {\r\n  /**\r\n   * ID của audience\r\n   * @example 1\r\n   */\r\n  @ApiProperty({\r\n    description: 'ID của audience',\r\n    example: 1,\r\n  })\r\n  id: number;\r\n\r\n  /**\r\n   * Email của khách hàng\r\n   * @example \"<EMAIL>\"\r\n   */\r\n  @ApiProperty({\r\n    description: 'Email của khách hàng',\r\n    example: '<EMAIL>',\r\n  })\r\n  email: string;\r\n\r\n  /**\r\n   * Số điện thoại của khách hàng\r\n   * @example \"+84912345678\"\r\n   */\r\n  @ApiProperty({\r\n    description: 'Số điện thoại của khách hàng',\r\n    example: '+84912345678',\r\n    nullable: true,\r\n  })\r\n  phone: string | null;\r\n\r\n  /**\r\n   * <PERSON>h sách các trường tùy chỉnh\r\n   */\r\n  @ApiProperty({\r\n    description: 'Danh sách các trường tùy chỉnh',\r\n    type: [CustomFieldResponseDto],\r\n  })\r\n  customFields: CustomFieldResponseDto[];\r\n\r\n  /**\r\n   * Danh sách các tag\r\n   */\r\n  @ApiProperty({\r\n    description: 'Danh sách các tag',\r\n    type: [TagResponseDto],\r\n  })\r\n  tags: TagResponseDto[];\r\n\r\n  /**\r\n   * Thời gian tạo (Unix timestamp)\r\n   * @example 1619171200\r\n   */\r\n  @ApiProperty({\r\n    description: 'Thời gian tạo (Unix timestamp)',\r\n    example: 1619171200,\r\n  })\r\n  createdAt: number;\r\n\r\n  /**\r\n   * Thời gian cập nhật (Unix timestamp)\r\n   * @example 1619171200\r\n   */\r\n  @ApiProperty({\r\n    description: 'Thời gian cập nhật (Unix timestamp)',\r\n    example: 1619171200,\r\n  })\r\n  updatedAt: number;\r\n}\r\n"], "names": ["AudienceResponseDto", "description", "example", "nullable", "type", "CustomFieldResponseDto", "TagResponseDto"], "mappings": ";;;;+BAOaA;;;eAAAA;;;yBAPe;wCACW;gCACR;;;;;;;;;;AAKxB,IAAA,AAAMA,sBAAN,MAAMA;AAqEb;;;QA/DIC,aAAa;QACbC,SAAS;;;;;;QASTD,aAAa;QACbC,SAAS;;;;;;QASTD,aAAa;QACbC,SAAS;QACTC,UAAU;;;;;;QAQVF,aAAa;QACbG,MAAM;YAACC,8CAAsB;SAAC;;;;;;QAQ9BJ,aAAa;QACbG,MAAM;YAACE,8BAAc;SAAC;;;;;;QAStBL,aAAa;QACbC,SAAS;;;;;;QASTD,aAAa;QACbC,SAAS"}