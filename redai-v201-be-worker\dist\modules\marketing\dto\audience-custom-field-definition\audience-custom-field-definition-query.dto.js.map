{"version": 3, "sources": ["../../../../../src/modules/marketing/dto/audience-custom-field-definition/audience-custom-field-definition-query.dto.ts"], "sourcesContent": ["import { ApiProperty } from '@nestjs/swagger';\r\nimport { IsEnum, IsOptional, IsString } from 'class-validator';\r\nimport { QueryDto, SortDirection } from '@common/dto';\r\nimport { CustomFieldDataType } from './create-audience-custom-field-definition.dto';\r\n\r\n/**\r\n * Enum cho các trường sắp xếp\r\n */\r\nexport enum CustomFieldDefinitionSortField {\r\n  ID = 'id',\r\n  FIELD_KEY = 'fieldKey',\r\n  DISPLAY_NAME = 'displayName',\r\n  DATA_TYPE = 'dataType',\r\n}\r\n\r\n/**\r\n * DTO cho truy vấn danh sách trường tùy chỉnh\r\n */\r\nexport class AudienceCustomFieldDefinitionQueryDto extends QueryDto {\r\n  /**\r\n   * Tìm kiếm theo định danh\r\n   * @example \"customer\"\r\n   */\r\n  @ApiProperty({\r\n    description: 'Tìm kiếm theo định danh',\r\n    example: 'customer',\r\n    required: false,\r\n  })\r\n  @IsOptional()\r\n  @IsString({ message: 'Định danh phải là chuỗi' })\r\n  fieldKey?: string;\r\n\r\n  /**\r\n   * Tìm kiếm theo tên hiển thị\r\n   * @example \"Khách hàng\"\r\n   */\r\n  @ApiProperty({\r\n    description: 'Tìm kiếm theo tên hiển thị',\r\n    example: 'Khách hàng',\r\n    required: false,\r\n  })\r\n  @IsOptional()\r\n  @IsString({ message: 'Tên hiển thị phải là chuỗi' })\r\n  displayName?: string;\r\n\r\n  /**\r\n   * Tìm kiếm theo kiểu dữ liệu\r\n   * @example \"string\"\r\n   */\r\n  @ApiProperty({\r\n    description: 'Tìm kiếm theo kiểu dữ liệu',\r\n    enum: CustomFieldDataType,\r\n    example: CustomFieldDataType.STRING,\r\n    required: false,\r\n  })\r\n  @IsOptional()\r\n  @IsEnum(CustomFieldDataType, {\r\n    message: `Kiểu dữ liệu phải là một trong các giá trị: ${Object.values(CustomFieldDataType).join(', ')}`,\r\n  })\r\n  dataType?: CustomFieldDataType;\r\n\r\n  /**\r\n   * Sắp xếp theo trường\r\n   * @example \"displayName\"\r\n   */\r\n  @ApiProperty({\r\n    description: 'Sắp xếp theo trường',\r\n    enum: CustomFieldDefinitionSortField,\r\n    example: CustomFieldDefinitionSortField.DISPLAY_NAME,\r\n    default: CustomFieldDefinitionSortField.DISPLAY_NAME,\r\n    required: false,\r\n  })\r\n  @IsOptional()\r\n  @IsEnum(CustomFieldDefinitionSortField, {\r\n    message: `Trường sắp xếp phải là một trong các giá trị: ${Object.values(CustomFieldDefinitionSortField).join(', ')}`,\r\n  })\r\n  sortBy?: CustomFieldDefinitionSortField =\r\n    CustomFieldDefinitionSortField.DISPLAY_NAME;\r\n\r\n  /**\r\n   * Ghi đè thuộc tính sortDirection từ QueryDto để thay đổi giá trị mặc định\r\n   * @example \"ASC\"\r\n   */\r\n  @ApiProperty({\r\n    description: 'Thứ tự sắp xếp',\r\n    enum: SortDirection,\r\n    example: SortDirection.ASC,\r\n    default: SortDirection.ASC,\r\n    required: false,\r\n  })\r\n  @IsOptional()\r\n  @IsEnum(SortDirection, {\r\n    message: `Thứ tự sắp xếp phải là một trong các giá trị: ${Object.values(SortDirection).join(', ')}`,\r\n  })\r\n  sortDirection?: SortDirection = SortDirection.ASC;\r\n}\r\n"], "names": ["AudienceCustomFieldDefinitionQueryDto", "CustomFieldDefinitionSortField", "QueryDto", "sortBy", "sortDirection", "SortDirection", "ASC", "description", "example", "required", "message", "enum", "CustomFieldDataType", "STRING", "Object", "values", "join", "default"], "mappings": ";;;;;;;;;;;QAkBaA;eAAAA;;QAVDC;eAAAA;;;yBARgB;gCACiB;qBACL;wDACJ;;;;;;;;;;AAK7B,IAAA,AAAKA,wDAAAA;;;;;WAAAA;;AAUL,IAAA,AAAMD,wCAAN,MAAMA,8CAA8CE,aAAQ;;QAA5D,gBA2CL;;;GAGC,QAYDC,wBAGA;;;GAGC,QAYDC,gBAAgCC,kBAAa,CAACC,GAAG;;AACnD;;;QAvEIC,aAAa;QACbC,SAAS;QACTC,UAAU;;;;QAGAC,SAAS;;;;;;QAQnBH,aAAa;QACbC,SAAS;QACTC,UAAU;;;;QAGAC,SAAS;;;;;;QAQnBH,aAAa;QACbI,MAAMC,2DAAmB;QACzBJ,SAASI,2DAAmB,CAACC,MAAM;QACnCJ,UAAU;;;;QAIVC,SAAS,CAAC,4CAA4C,EAAEI,OAAOC,MAAM,CAACH,2DAAmB,EAAEI,IAAI,CAAC,OAAO;;;;;;QASvGT,aAAa;QACbI,MAAMV;QACNO,OAAO;QACPS,OAAO;QACPR,UAAU;;;;QAIVC,SAAS,CAAC,8CAA8C,EAAEI,OAAOC,MAAM,CAACd,gCAAgCe,IAAI,CAAC,OAAO;;;;;;QAUpHT,aAAa;QACbI,MAAMN,kBAAa;QACnBG,SAASH,kBAAa,CAACC,GAAG;QAC1BW,SAASZ,kBAAa,CAACC,GAAG;QAC1BG,UAAU;;;;QAIVC,SAAS,CAAC,8CAA8C,EAAEI,OAAOC,MAAM,CAACV,kBAAa,EAAEW,IAAI,CAAC,OAAO"}