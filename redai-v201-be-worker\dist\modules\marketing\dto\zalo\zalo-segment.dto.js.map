{"version": 3, "sources": ["../../../../../src/modules/marketing/dto/zalo/zalo-segment.dto.ts"], "sourcesContent": ["import { ApiProperty } from '@nestjs/swagger';\r\nimport {\r\n  IsArray,\r\n  IsEnum,\r\n  IsNotEmpty,\r\n  IsOptional,\r\n  IsString,\r\n} from 'class-validator';\r\n\r\n/**\r\n * Enum cho loại điều kiện phân đoạn\r\n */\r\nexport enum ZaloSegmentConditionType {\r\n  TAG = 'tag',\r\n  GENDER = 'gender',\r\n  FOLLOW_DATE = 'follow_date',\r\n  INTERACTION = 'interaction',\r\n}\r\n\r\n/**\r\n * Enum cho toán tử so sánh\r\n */\r\nexport enum ZaloSegmentOperator {\r\n  EQUAL = 'equal',\r\n  NOT_EQUAL = 'not_equal',\r\n  CONTAINS = 'contains',\r\n  NOT_CONTAINS = 'not_contains',\r\n  GREATER_THAN = 'greater_than',\r\n  LESS_THAN = 'less_than',\r\n  BETWEEN = 'between',\r\n}\r\n\r\n/**\r\n * DTO cho điều kiện phân đoạn Zalo\r\n */\r\nexport class ZaloSegmentConditionDto {\r\n  @ApiProperty({\r\n    description: 'Loại điều kiện',\r\n    enum: ZaloSegmentConditionType,\r\n    example: ZaloSegmentConditionType.TAG,\r\n  })\r\n  @IsEnum(ZaloSegmentConditionType)\r\n  @IsNotEmpty()\r\n  type: ZaloSegmentConditionType;\r\n\r\n  @ApiProperty({\r\n    description: 'Toán tử so sánh',\r\n    enum: ZaloSegmentOperator,\r\n    example: ZaloSegmentOperator.CONTAINS,\r\n  })\r\n  @IsEnum(ZaloSegmentOperator)\r\n  @IsNotEmpty()\r\n  operator: ZaloSegmentOperator;\r\n\r\n  @ApiProperty({\r\n    description: 'Giá trị điều kiện',\r\n    example: ['vip', 'new-customer'],\r\n    type: [String],\r\n  })\r\n  @IsArray()\r\n  @IsString({ each: true })\r\n  values: string[];\r\n}\r\n\r\n/**\r\n * DTO cho việc tạo phân đoạn Zalo\r\n */\r\nexport class CreateZaloSegmentDto {\r\n  @ApiProperty({\r\n    description: 'ID của Official Account',\r\n    example: '*********',\r\n  })\r\n  @IsString()\r\n  @IsNotEmpty()\r\n  oaId: string;\r\n\r\n  @ApiProperty({\r\n    description: 'Tên của phân đoạn',\r\n    example: 'Khách hàng VIP',\r\n  })\r\n  @IsString()\r\n  @IsNotEmpty()\r\n  name: string;\r\n\r\n  @ApiProperty({\r\n    description: 'Mô tả của phân đoạn',\r\n    example: 'Phân đoạn dành cho khách hàng VIP',\r\n    required: false,\r\n  })\r\n  @IsString()\r\n  @IsOptional()\r\n  description?: string;\r\n\r\n  @ApiProperty({\r\n    description: 'Danh sách điều kiện phân đoạn',\r\n    type: [ZaloSegmentConditionDto],\r\n  })\r\n  @IsArray()\r\n  @IsNotEmpty()\r\n  conditions: ZaloSegmentConditionDto[];\r\n}\r\n\r\n/**\r\n * DTO cho việc cập nhật phân đoạn Zalo\r\n */\r\nexport class UpdateZaloSegmentDto {\r\n  @ApiProperty({\r\n    description: 'Tên của phân đoạn',\r\n    example: 'Khách hàng VIP - Cập nhật',\r\n    required: false,\r\n  })\r\n  @IsString()\r\n  @IsOptional()\r\n  name?: string;\r\n\r\n  @ApiProperty({\r\n    description: 'Mô tả của phân đoạn',\r\n    example: 'Phân đoạn dành cho khách hàng VIP - Cập nhật',\r\n    required: false,\r\n  })\r\n  @IsString()\r\n  @IsOptional()\r\n  description?: string;\r\n\r\n  @ApiProperty({\r\n    description: 'Danh sách điều kiện phân đoạn',\r\n    type: [ZaloSegmentConditionDto],\r\n    required: false,\r\n  })\r\n  @IsArray()\r\n  @IsOptional()\r\n  conditions?: ZaloSegmentConditionDto[];\r\n}\r\n\r\n/**\r\n * DTO cho phản hồi thông tin phân đoạn Zalo\r\n */\r\nexport class ZaloSegmentResponseDto {\r\n  @ApiProperty({\r\n    description: 'ID của phân đoạn',\r\n    example: 1,\r\n  })\r\n  id: number;\r\n\r\n  @ApiProperty({\r\n    description: 'ID của người dùng',\r\n    example: 123,\r\n  })\r\n  userId: number;\r\n\r\n  @ApiProperty({\r\n    description: 'ID của Official Account',\r\n    example: '*********',\r\n  })\r\n  oaId: string;\r\n\r\n  @ApiProperty({\r\n    description: 'Tên của phân đoạn',\r\n    example: 'Khách hàng VIP',\r\n  })\r\n  name: string;\r\n\r\n  @ApiProperty({\r\n    description: 'Mô tả của phân đoạn',\r\n    example: 'Phân đoạn dành cho khách hàng VIP',\r\n    nullable: true,\r\n  })\r\n  description?: string;\r\n\r\n  @ApiProperty({\r\n    description: 'Danh sách điều kiện phân đoạn',\r\n    type: [ZaloSegmentConditionDto],\r\n  })\r\n  conditions: ZaloSegmentConditionDto[];\r\n\r\n  @ApiProperty({\r\n    description: 'Số lượng người theo dõi trong phân đoạn',\r\n    example: 100,\r\n  })\r\n  followerCount: number;\r\n\r\n  @ApiProperty({\r\n    description: 'Thời điểm tạo (Unix timestamp)',\r\n    example: *************,\r\n  })\r\n  createdAt: number;\r\n\r\n  @ApiProperty({\r\n    description: 'Thời điểm cập nhật (Unix timestamp)',\r\n    example: *************,\r\n  })\r\n  updatedAt: number;\r\n}\r\n\r\n/**\r\n * DTO cho việc truy vấn danh sách phân đoạn Zalo\r\n */\r\nexport class ZaloSegmentQueryDto {\r\n  @ApiProperty({\r\n    description: 'Tìm kiếm theo tên phân đoạn',\r\n    example: 'VIP',\r\n    required: false,\r\n  })\r\n  @IsString()\r\n  @IsOptional()\r\n  name?: string;\r\n\r\n  @ApiProperty({\r\n    description: 'Số trang',\r\n    example: 1,\r\n    default: 1,\r\n    required: false,\r\n  })\r\n  @IsOptional()\r\n  page?: number = 1;\r\n\r\n  @ApiProperty({\r\n    description: 'Số lượng phân đoạn trên mỗi trang',\r\n    example: 10,\r\n    default: 10,\r\n    required: false,\r\n  })\r\n  @IsOptional()\r\n  limit?: number = 10;\r\n}\r\n"], "names": ["CreateZaloSegmentDto", "UpdateZaloSegmentDto", "ZaloSegmentConditionDto", "ZaloSegmentConditionType", "ZaloSegmentOperator", "ZaloSegmentQueryDto", "ZaloSegmentResponseDto", "description", "enum", "example", "type", "String", "each", "required", "nullable", "page", "limit", "default"], "mappings": ";;;;;;;;;;;QAmEaA;eAAAA;;QAsCAC;eAAAA;;QAtEAC;eAAAA;;QAvBDC;eAAAA;;QAUAC;eAAAA;;QA+KCC;eAAAA;;QA5DAC;eAAAA;;;yBAzIe;gCAOrB;;;;;;;;;;AAKA,IAAA,AAAKH,kDAAAA;;;;;WAAAA;;AAUL,IAAA,AAAKC,6CAAAA;;;;;;;;WAAAA;;AAaL,IAAA,AAAMF,0BAAN,MAAMA;AA2Bb;;;QAzBIK,aAAa;QACbC,MAAML;QACNM,OAAO;;;;;;;;QAOPF,aAAa;QACbC,MAAMJ;QACNK,OAAO;;;;;;;;QAOPF,aAAa;QACbE,SAAS;YAAC;YAAO;SAAe;QAChCC,MAAM;YAACC;SAAO;;;;QAGJC,MAAM;;;;AAOb,IAAA,AAAMZ,uBAAN,MAAMA;AAiCb;;;QA/BIO,aAAa;QACbE,SAAS;;;;;;;;QAOTF,aAAa;QACbE,SAAS;;;;;;;;QAOTF,aAAa;QACbE,SAAS;QACTI,UAAU;;;;;;;;QAOVN,aAAa;QACbG,MAAM;YAACR;SAAwB;;;;;;AAU5B,IAAA,AAAMD,uBAAN,MAAMA;AA2Bb;;;QAzBIM,aAAa;QACbE,SAAS;QACTI,UAAU;;;;;;;;QAOVN,aAAa;QACbE,SAAS;QACTI,UAAU;;;;;;;;QAOVN,aAAa;QACbG,MAAM;YAACR;SAAwB;QAC/BW,UAAU;;;;;;AAUP,IAAA,AAAMP,yBAAN,MAAMA;AAuDb;;;QArDIC,aAAa;QACbE,SAAS;;;;;;QAKTF,aAAa;QACbE,SAAS;;;;;;QAKTF,aAAa;QACbE,SAAS;;;;;;QAKTF,aAAa;QACbE,SAAS;;;;;;QAKTF,aAAa;QACbE,SAAS;QACTK,UAAU;;;;;;QAKVP,aAAa;QACbG,MAAM;YAACR;SAAwB;;;;;;QAK/BK,aAAa;QACbE,SAAS;;;;;;QAKTF,aAAa;QACbE,SAAS;;;;;;QAKTF,aAAa;QACbE,SAAS;;;;AAQN,IAAA,AAAMJ,sBAAN,MAAMA;;aAiBXU,OAAgB;aAShBC,QAAiB;;AACnB;;;QAzBIT,aAAa;QACbE,SAAS;QACTI,UAAU;;;;;;;;QAOVN,aAAa;QACbE,SAAS;QACTQ,SAAS;QACTJ,UAAU;;;;;;;QAMVN,aAAa;QACbE,SAAS;QACTQ,SAAS;QACTJ,UAAU"}