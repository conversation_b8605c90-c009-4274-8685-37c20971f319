"use strict";
Object.defineProperty(exports, "__esModule", {
    value: true
});
Object.defineProperty(exports, "EmailTrackingController", {
    enumerable: true,
    get: function() {
        return EmailTrackingController;
    }
});
const _common = require("@nestjs/common");
const _express = require("express");
const _services = require("./services");
function _ts_decorate(decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for(var i = decorators.length - 1; i >= 0; i--)if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
}
function _ts_metadata(k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
}
function _ts_param(paramIndex, decorator) {
    return function(target, key) {
        decorator(target, key, paramIndex);
    };
}
let EmailTrackingController = class EmailTrackingController {
    /**
   * Endpoint cho tracking pixel
   * @param trackingId ID tracking từ URL
   * @param req Request object
   * @param res Response object
   */ async trackPixel(trackingId, req, res) {
        try {
            // Lấy thông tin metadata từ request
            const metadata = {
                ip: req.ip || req.connection.remoteAddress,
                userAgent: req.get('User-Agent'),
                referer: req.get('Referer'),
                timestamp: Date.now()
            };
            // Track email opened
            await this.emailTrackingService.trackEmailOpened(trackingId, metadata);
            this.logger.debug(`Email opened tracked: ${trackingId}`);
            // Trả về ảnh pixel 1x1 transparent
            const pixelBuffer = Buffer.from('iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg==', 'base64');
            res.set({
                'Content-Type': 'image/png',
                'Content-Length': pixelBuffer.length.toString(),
                'Cache-Control': 'no-cache, no-store, must-revalidate',
                Pragma: 'no-cache',
                Expires: '0'
            });
            res.send(pixelBuffer);
        } catch (error) {
            this.logger.error(`Error tracking pixel: ${error.message}`, error.stack);
            // Vẫn trả về pixel để không làm hỏng email
            const pixelBuffer = Buffer.from('iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg==', 'base64');
            res.set({
                'Content-Type': 'image/png',
                'Content-Length': pixelBuffer.length.toString()
            });
            res.send(pixelBuffer);
        }
    }
    /**
   * Endpoint để track click links (optional)
   * @param trackingId ID tracking
   * @param url URL gốc cần redirect
   * @param req Request object
   * @param res Response object
   */ async trackClick(trackingId, req, res) {
        try {
            const url = req.query.url;
            if (!url) {
                res.status(400).send('Missing URL parameter');
                return;
            }
            // Lấy thông tin metadata từ request
            const metadata = {
                ip: req.ip || req.connection.remoteAddress,
                userAgent: req.get('User-Agent'),
                referer: req.get('Referer'),
                clickedUrl: url,
                timestamp: Date.now()
            };
            // Track email clicked (có thể extend EmailTrackingService để support click tracking)
            // await this.emailTrackingService.trackEmailClicked(trackingId, metadata);
            this.logger.debug(`Email click tracked: ${trackingId} -> ${url}`);
            // Redirect đến URL gốc
            res.redirect(url);
        } catch (error) {
            this.logger.error(`Error tracking click: ${error.message}`, error.stack);
            // Redirect về trang chủ nếu có lỗi
            const fallbackUrl = req.query.url || 'https://redai.com';
            res.redirect(fallbackUrl);
        }
    }
    constructor(emailTrackingService){
        this.emailTrackingService = emailTrackingService;
        this.logger = new _common.Logger(EmailTrackingController.name);
    }
};
_ts_decorate([
    (0, _common.Get)('pixel/:trackingId'),
    _ts_param(0, (0, _common.Param)('trackingId')),
    _ts_param(1, (0, _common.Req)()),
    _ts_param(2, (0, _common.Res)()),
    _ts_metadata("design:type", Function),
    _ts_metadata("design:paramtypes", [
        String,
        typeof _express.Request === "undefined" ? Object : _express.Request,
        typeof _express.Response === "undefined" ? Object : _express.Response
    ]),
    _ts_metadata("design:returntype", Promise)
], EmailTrackingController.prototype, "trackPixel", null);
_ts_decorate([
    (0, _common.Get)('click/:trackingId'),
    _ts_param(0, (0, _common.Param)('trackingId')),
    _ts_param(1, (0, _common.Req)()),
    _ts_param(2, (0, _common.Res)()),
    _ts_metadata("design:type", Function),
    _ts_metadata("design:paramtypes", [
        String,
        typeof _express.Request === "undefined" ? Object : _express.Request,
        typeof _express.Response === "undefined" ? Object : _express.Response
    ]),
    _ts_metadata("design:returntype", Promise)
], EmailTrackingController.prototype, "trackClick", null);
EmailTrackingController = _ts_decorate([
    (0, _common.Controller)('api/email-tracking'),
    _ts_metadata("design:type", Function),
    _ts_metadata("design:paramtypes", [
        typeof _services.EmailTrackingService === "undefined" ? Object : _services.EmailTrackingService
    ])
], EmailTrackingController);

//# sourceMappingURL=email-tracking.controller.js.map