{"version": 3, "sources": ["../../../../../src/modules/marketing/dto/audience-custom-field-definition/update-audience-custom-field-definition.dto.ts"], "sourcesContent": ["import { ApiProperty } from '@nestjs/swagger';\r\nimport { IsEnum, IsOptional, IsString } from 'class-validator';\r\nimport { CustomFieldDataType } from './create-audience-custom-field-definition.dto';\r\n\r\n/**\r\n * DTO cho việc cập nhật trường tùy chỉnh\r\n */\r\nexport class UpdateAudienceCustomFieldDefinitionDto {\r\n  /**\r\n   * Tên hiển thị thân thiện với người dùng\r\n   * @example \"Địa chỉ khách hàng\"\r\n   */\r\n  @ApiProperty({\r\n    description: 'Tên hiển thị thân thiện với người dùng',\r\n    example: 'Địa chỉ khách hàng',\r\n    required: false,\r\n  })\r\n  @IsOptional()\r\n  @IsString({ message: 'Tên hiển thị phải là chuỗi' })\r\n  displayName?: string;\r\n\r\n  /**\r\n   * Kiểu dữ liệu: string, integer, date, boolean, json\r\n   * @example \"string\"\r\n   */\r\n  @ApiProperty({\r\n    description: 'Kiểu dữ liệu',\r\n    enum: CustomFieldDataType,\r\n    example: CustomFieldDataType.STRING,\r\n    required: false,\r\n  })\r\n  @IsOptional()\r\n  @IsEnum(CustomFieldDataType, {\r\n    message: `Kiểu dữ liệu phải là một trong các giá trị: ${Object.values(CustomFieldDataType).join(', ')}`,\r\n  })\r\n  dataType?: CustomFieldDataType;\r\n\r\n  /**\r\n   * Mô tả chi tiết hoặc ghi chú về trường tùy chỉnh\r\n   * @example \"Địa chỉ liên hệ của khách hàng\"\r\n   */\r\n  @ApiProperty({\r\n    description: 'Mô tả chi tiết hoặc ghi chú về trường tùy chỉnh',\r\n    example: 'Địa chỉ liên hệ của khách hàng',\r\n    required: false,\r\n  })\r\n  @IsOptional()\r\n  @IsString({ message: 'Mô tả phải là chuỗi' })\r\n  description?: string;\r\n}\r\n"], "names": ["UpdateAudienceCustomFieldDefinitionDto", "description", "example", "required", "message", "enum", "CustomFieldDataType", "STRING", "Object", "values", "join"], "mappings": ";;;;+BAOaA;;;eAAAA;;;yBAPe;gCACiB;wDACT;;;;;;;;;;AAK7B,IAAA,AAAMA,yCAAN,MAAMA;AA0Cb;;;QApCIC,aAAa;QACbC,SAAS;QACTC,UAAU;;;;QAGAC,SAAS;;;;;;QAQnBH,aAAa;QACbI,MAAMC,2DAAmB;QACzBJ,SAASI,2DAAmB,CAACC,MAAM;QACnCJ,UAAU;;;;QAIVC,SAAS,CAAC,4CAA4C,EAAEI,OAAOC,MAAM,CAACH,2DAAmB,EAAEI,IAAI,CAAC,OAAO;;;;;;QASvGT,aAAa;QACbC,SAAS;QACTC,UAAU;;;;QAGAC,SAAS"}