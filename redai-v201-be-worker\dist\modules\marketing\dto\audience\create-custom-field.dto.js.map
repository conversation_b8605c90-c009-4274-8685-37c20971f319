{"version": 3, "sources": ["../../../../../src/modules/marketing/dto/audience/create-custom-field.dto.ts"], "sourcesContent": ["import { IsIn, IsNotEmpty, IsString } from 'class-validator';\r\nimport { ApiProperty } from '@nestjs/swagger';\r\n\r\n/**\r\n * Enum cho các kiểu dữ liệu của trường tùy chỉnh\r\n */\r\nexport enum CustomFieldType {\r\n  TEXT = 'text',\r\n  NUMBER = 'number',\r\n  DATE = 'date',\r\n  BOOLEAN = 'boolean',\r\n  JSON = 'json',\r\n}\r\n\r\n/**\r\n * DTO cho việc tạo trường tùy chỉnh\r\n */\r\nexport class CreateCustomFieldDto {\r\n  /**\r\n   * Tên trường\r\n   * @example \"Địa chỉ\"\r\n   */\r\n  @ApiProperty({\r\n    description: 'Tên trường',\r\n    example: 'Địa chỉ',\r\n  })\r\n  @IsNotEmpty({ message: 'Tên trường không được để trống' })\r\n  @IsString({ message: 'Tên trường phải là chuỗi' })\r\n  fieldName: string;\r\n\r\n  /**\r\n   * <PERSON>i<PERSON> trị trường\r\n   * @example \"<PERSON>à N<PERSON>, Vi<PERSON>t Nam\"\r\n   */\r\n  @ApiProperty({\r\n    description: '<PERSON>i<PERSON> trị trường',\r\n    example: 'Hà Nội, Việt Nam',\r\n  })\r\n  @IsNotEmpty({ message: 'Giá trị trường không được để trống' })\r\n  fieldValue: any;\r\n\r\n  /**\r\n   * Kiểu dữ liệu của trường\r\n   * @example \"text\"\r\n   */\r\n  @ApiProperty({\r\n    description: 'Kiểu dữ liệu của trường',\r\n    enum: CustomFieldType,\r\n    example: CustomFieldType.TEXT,\r\n  })\r\n  @IsNotEmpty({ message: 'Kiểu dữ liệu không được để trống' })\r\n  @IsIn(Object.values(CustomFieldType), {\r\n    message: `Kiểu dữ liệu phải là một trong các giá trị: ${Object.values(CustomFieldType).join(', ')}`,\r\n  })\r\n  fieldType: CustomFieldType;\r\n}\r\n"], "names": ["CreateCustomFieldDto", "CustomFieldType", "description", "example", "message", "enum", "values", "Object", "join"], "mappings": ";;;;;;;;;;;QAiBaA;eAAAA;;QAXDC;eAAAA;;;gCAN+B;yBACf;;;;;;;;;;AAKrB,IAAA,AAAKA,yCAAAA;;;;;;WAAAA;;AAWL,IAAA,AAAMD,uBAAN,MAAMA;AAsCb;;;QAhCIE,aAAa;QACbC,SAAS;;;QAEGC,SAAS;;;QACXA,SAAS;;;;;;QAQnBF,aAAa;QACbC,SAAS;;;QAEGC,SAAS;;;;;;QAQrBF,aAAa;QACbG,MAAMJ;QACNE,OAAO;;;QAEKC,SAAS;;qCACVE;QACXF,SAAS,CAAC,4CAA4C,EAAEG,OAAOD,MAAM,CAACL,iBAAiBO,IAAI,CAAC,OAAO"}