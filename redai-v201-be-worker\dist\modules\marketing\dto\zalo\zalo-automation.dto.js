"use strict";
Object.defineProperty(exports, "__esModule", {
    value: true
});
function _export(target, all) {
    for(var name in all)Object.defineProperty(target, name, {
        enumerable: true,
        get: Object.getOwnPropertyDescriptor(all, name).get
    });
}
_export(exports, {
    get CreateZaloAutomationDto () {
        return CreateZaloAutomationDto;
    },
    get UpdateZaloAutomationDto () {
        return UpdateZaloAutomationDto;
    },
    get ZaloAutomationActionDto () {
        return ZaloAutomationActionDto;
    },
    get ZaloAutomationActionType () {
        return ZaloAutomationActionType;
    },
    get ZaloAutomationQueryDto () {
        return ZaloAutomationQueryDto;
    },
    get ZaloAutomationResponseDto () {
        return ZaloAutomationResponseDto;
    },
    get ZaloAutomationStatus () {
        return ZaloAutomationStatus;
    },
    get ZaloAutomationTriggerDto () {
        return ZaloAutomationTriggerDto;
    },
    get ZaloAutomationTriggerType () {
        return ZaloAutomationTriggerType;
    }
});
const _swagger = require("@nestjs/swagger");
const _classvalidator = require("class-validator");
const _zalocampaigndto = require("./zalo-campaign.dto");
const _zalosegmentdto = require("./zalo-segment.dto");
function _ts_decorate(decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for(var i = decorators.length - 1; i >= 0; i--)if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
}
function _ts_metadata(k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
}
var ZaloAutomationTriggerType = /*#__PURE__*/ function(ZaloAutomationTriggerType) {
    ZaloAutomationTriggerType["FOLLOW"] = "follow";
    ZaloAutomationTriggerType["UNFOLLOW"] = "unfollow";
    ZaloAutomationTriggerType["MESSAGE"] = "message";
    ZaloAutomationTriggerType["TAG_ADDED"] = "tag_added";
    ZaloAutomationTriggerType["TAG_REMOVED"] = "tag_removed";
    return ZaloAutomationTriggerType;
}({});
var ZaloAutomationActionType = /*#__PURE__*/ function(ZaloAutomationActionType) {
    ZaloAutomationActionType["SEND_MESSAGE"] = "send_message";
    ZaloAutomationActionType["SEND_ZNS"] = "send_zns";
    ZaloAutomationActionType["ADD_TAG"] = "add_tag";
    ZaloAutomationActionType["REMOVE_TAG"] = "remove_tag";
    return ZaloAutomationActionType;
}({});
var ZaloAutomationStatus = /*#__PURE__*/ function(ZaloAutomationStatus) {
    ZaloAutomationStatus["ACTIVE"] = "active";
    ZaloAutomationStatus["INACTIVE"] = "inactive";
    return ZaloAutomationStatus;
}({});
let ZaloAutomationTriggerDto = class ZaloAutomationTriggerDto {
};
_ts_decorate([
    (0, _swagger.ApiProperty)({
        description: 'Loại sự kiện kích hoạt',
        enum: ZaloAutomationTriggerType,
        example: "follow"
    }),
    (0, _classvalidator.IsEnum)(ZaloAutomationTriggerType),
    (0, _classvalidator.IsNotEmpty)(),
    _ts_metadata("design:type", String)
], ZaloAutomationTriggerDto.prototype, "type", void 0);
_ts_decorate([
    (0, _swagger.ApiProperty)({
        description: 'Điều kiện bổ sung (nếu có)',
        type: [
            _zalosegmentdto.ZaloSegmentConditionDto
        ],
        required: false
    }),
    (0, _classvalidator.IsArray)(),
    (0, _classvalidator.IsOptional)(),
    _ts_metadata("design:type", Array)
], ZaloAutomationTriggerDto.prototype, "conditions", void 0);
let ZaloAutomationActionDto = class ZaloAutomationActionDto {
};
_ts_decorate([
    (0, _swagger.ApiProperty)({
        description: 'Loại hành động',
        enum: ZaloAutomationActionType,
        example: "send_message"
    }),
    (0, _classvalidator.IsEnum)(ZaloAutomationActionType),
    (0, _classvalidator.IsNotEmpty)(),
    _ts_metadata("design:type", String)
], ZaloAutomationActionDto.prototype, "type", void 0);
_ts_decorate([
    (0, _swagger.ApiProperty)({
        description: 'Nội dung tin nhắn (chỉ dùng khi type là send_message)',
        type: _zalocampaigndto.ZaloCampaignMessageContentDto,
        required: false
    }),
    (0, _classvalidator.IsObject)(),
    (0, _classvalidator.IsOptional)(),
    _ts_metadata("design:type", typeof _zalocampaigndto.ZaloCampaignMessageContentDto === "undefined" ? Object : _zalocampaigndto.ZaloCampaignMessageContentDto)
], ZaloAutomationActionDto.prototype, "messageContent", void 0);
_ts_decorate([
    (0, _swagger.ApiProperty)({
        description: 'Nội dung ZNS (chỉ dùng khi type là send_zns)',
        type: _zalocampaigndto.ZaloCampaignZnsContentDto,
        required: false
    }),
    (0, _classvalidator.IsObject)(),
    (0, _classvalidator.IsOptional)(),
    _ts_metadata("design:type", typeof _zalocampaigndto.ZaloCampaignZnsContentDto === "undefined" ? Object : _zalocampaigndto.ZaloCampaignZnsContentDto)
], ZaloAutomationActionDto.prototype, "znsContent", void 0);
_ts_decorate([
    (0, _swagger.ApiProperty)({
        description: 'Tag cần thêm/xóa (chỉ dùng khi type là add_tag hoặc remove_tag)',
        example: 'vip',
        required: false
    }),
    (0, _classvalidator.IsString)(),
    (0, _classvalidator.IsOptional)(),
    _ts_metadata("design:type", String)
], ZaloAutomationActionDto.prototype, "tag", void 0);
let CreateZaloAutomationDto = class CreateZaloAutomationDto {
};
_ts_decorate([
    (0, _swagger.ApiProperty)({
        description: 'ID của Official Account',
        example: '*********'
    }),
    (0, _classvalidator.IsString)(),
    (0, _classvalidator.IsNotEmpty)(),
    _ts_metadata("design:type", String)
], CreateZaloAutomationDto.prototype, "oaId", void 0);
_ts_decorate([
    (0, _swagger.ApiProperty)({
        description: 'Tên của tự động hóa',
        example: 'Chào mừng người theo dõi mới'
    }),
    (0, _classvalidator.IsString)(),
    (0, _classvalidator.IsNotEmpty)(),
    _ts_metadata("design:type", String)
], CreateZaloAutomationDto.prototype, "name", void 0);
_ts_decorate([
    (0, _swagger.ApiProperty)({
        description: 'Mô tả của tự động hóa',
        example: 'Gửi tin nhắn chào mừng khi có người theo dõi mới',
        required: false
    }),
    (0, _classvalidator.IsString)(),
    (0, _classvalidator.IsOptional)(),
    _ts_metadata("design:type", String)
], CreateZaloAutomationDto.prototype, "description", void 0);
_ts_decorate([
    (0, _swagger.ApiProperty)({
        description: 'Điều kiện kích hoạt',
        type: ZaloAutomationTriggerDto
    }),
    (0, _classvalidator.IsObject)(),
    (0, _classvalidator.IsNotEmpty)(),
    _ts_metadata("design:type", typeof ZaloAutomationTriggerDto === "undefined" ? Object : ZaloAutomationTriggerDto)
], CreateZaloAutomationDto.prototype, "trigger", void 0);
_ts_decorate([
    (0, _swagger.ApiProperty)({
        description: 'Danh sách hành động',
        type: [
            ZaloAutomationActionDto
        ]
    }),
    (0, _classvalidator.IsArray)(),
    (0, _classvalidator.IsNotEmpty)(),
    _ts_metadata("design:type", Array)
], CreateZaloAutomationDto.prototype, "actions", void 0);
let UpdateZaloAutomationDto = class UpdateZaloAutomationDto {
};
_ts_decorate([
    (0, _swagger.ApiProperty)({
        description: 'Tên của tự động hóa',
        example: 'Chào mừng người theo dõi mới - Cập nhật',
        required: false
    }),
    (0, _classvalidator.IsString)(),
    (0, _classvalidator.IsOptional)(),
    _ts_metadata("design:type", String)
], UpdateZaloAutomationDto.prototype, "name", void 0);
_ts_decorate([
    (0, _swagger.ApiProperty)({
        description: 'Mô tả của tự động hóa',
        example: 'Gửi tin nhắn chào mừng khi có người theo dõi mới - Cập nhật',
        required: false
    }),
    (0, _classvalidator.IsString)(),
    (0, _classvalidator.IsOptional)(),
    _ts_metadata("design:type", String)
], UpdateZaloAutomationDto.prototype, "description", void 0);
_ts_decorate([
    (0, _swagger.ApiProperty)({
        description: 'Điều kiện kích hoạt',
        type: ZaloAutomationTriggerDto,
        required: false
    }),
    (0, _classvalidator.IsObject)(),
    (0, _classvalidator.IsOptional)(),
    _ts_metadata("design:type", typeof ZaloAutomationTriggerDto === "undefined" ? Object : ZaloAutomationTriggerDto)
], UpdateZaloAutomationDto.prototype, "trigger", void 0);
_ts_decorate([
    (0, _swagger.ApiProperty)({
        description: 'Danh sách hành động',
        type: [
            ZaloAutomationActionDto
        ],
        required: false
    }),
    (0, _classvalidator.IsArray)(),
    (0, _classvalidator.IsOptional)(),
    _ts_metadata("design:type", Array)
], UpdateZaloAutomationDto.prototype, "actions", void 0);
_ts_decorate([
    (0, _swagger.ApiProperty)({
        description: 'Trạng thái tự động hóa',
        enum: ZaloAutomationStatus,
        example: "active",
        required: false
    }),
    (0, _classvalidator.IsEnum)(ZaloAutomationStatus),
    (0, _classvalidator.IsOptional)(),
    _ts_metadata("design:type", String)
], UpdateZaloAutomationDto.prototype, "status", void 0);
let ZaloAutomationResponseDto = class ZaloAutomationResponseDto {
};
_ts_decorate([
    (0, _swagger.ApiProperty)({
        description: 'ID của tự động hóa',
        example: 1
    }),
    _ts_metadata("design:type", Number)
], ZaloAutomationResponseDto.prototype, "id", void 0);
_ts_decorate([
    (0, _swagger.ApiProperty)({
        description: 'ID của người dùng',
        example: 123
    }),
    _ts_metadata("design:type", Number)
], ZaloAutomationResponseDto.prototype, "userId", void 0);
_ts_decorate([
    (0, _swagger.ApiProperty)({
        description: 'ID của Official Account',
        example: '*********'
    }),
    _ts_metadata("design:type", String)
], ZaloAutomationResponseDto.prototype, "oaId", void 0);
_ts_decorate([
    (0, _swagger.ApiProperty)({
        description: 'Tên của tự động hóa',
        example: 'Chào mừng người theo dõi mới'
    }),
    _ts_metadata("design:type", String)
], ZaloAutomationResponseDto.prototype, "name", void 0);
_ts_decorate([
    (0, _swagger.ApiProperty)({
        description: 'Mô tả của tự động hóa',
        example: 'Gửi tin nhắn chào mừng khi có người theo dõi mới',
        nullable: true
    }),
    _ts_metadata("design:type", String)
], ZaloAutomationResponseDto.prototype, "description", void 0);
_ts_decorate([
    (0, _swagger.ApiProperty)({
        description: 'Điều kiện kích hoạt',
        type: ZaloAutomationTriggerDto
    }),
    _ts_metadata("design:type", typeof ZaloAutomationTriggerDto === "undefined" ? Object : ZaloAutomationTriggerDto)
], ZaloAutomationResponseDto.prototype, "trigger", void 0);
_ts_decorate([
    (0, _swagger.ApiProperty)({
        description: 'Danh sách hành động',
        type: [
            ZaloAutomationActionDto
        ]
    }),
    _ts_metadata("design:type", Array)
], ZaloAutomationResponseDto.prototype, "actions", void 0);
_ts_decorate([
    (0, _swagger.ApiProperty)({
        description: 'Trạng thái tự động hóa',
        enum: ZaloAutomationStatus,
        example: "active"
    }),
    _ts_metadata("design:type", String)
], ZaloAutomationResponseDto.prototype, "status", void 0);
_ts_decorate([
    (0, _swagger.ApiProperty)({
        description: 'Số lần đã kích hoạt',
        example: 100
    }),
    _ts_metadata("design:type", Number)
], ZaloAutomationResponseDto.prototype, "triggerCount", void 0);
_ts_decorate([
    (0, _swagger.ApiProperty)({
        description: 'Thời điểm tạo (Unix timestamp)',
        example: 1625097600000
    }),
    _ts_metadata("design:type", Number)
], ZaloAutomationResponseDto.prototype, "createdAt", void 0);
_ts_decorate([
    (0, _swagger.ApiProperty)({
        description: 'Thời điểm cập nhật (Unix timestamp)',
        example: 1625097600000
    }),
    _ts_metadata("design:type", Number)
], ZaloAutomationResponseDto.prototype, "updatedAt", void 0);
let ZaloAutomationQueryDto = class ZaloAutomationQueryDto {
    constructor(){
        this.page = 1;
        this.limit = 10;
    }
};
_ts_decorate([
    (0, _swagger.ApiProperty)({
        description: 'Tìm kiếm theo tên tự động hóa',
        example: 'chào mừng',
        required: false
    }),
    (0, _classvalidator.IsString)(),
    (0, _classvalidator.IsOptional)(),
    _ts_metadata("design:type", String)
], ZaloAutomationQueryDto.prototype, "name", void 0);
_ts_decorate([
    (0, _swagger.ApiProperty)({
        description: 'Lọc theo loại sự kiện kích hoạt',
        enum: ZaloAutomationTriggerType,
        example: "follow",
        required: false
    }),
    (0, _classvalidator.IsEnum)(ZaloAutomationTriggerType),
    (0, _classvalidator.IsOptional)(),
    _ts_metadata("design:type", String)
], ZaloAutomationQueryDto.prototype, "triggerType", void 0);
_ts_decorate([
    (0, _swagger.ApiProperty)({
        description: 'Lọc theo trạng thái tự động hóa',
        enum: ZaloAutomationStatus,
        example: "active",
        required: false
    }),
    (0, _classvalidator.IsEnum)(ZaloAutomationStatus),
    (0, _classvalidator.IsOptional)(),
    _ts_metadata("design:type", String)
], ZaloAutomationQueryDto.prototype, "status", void 0);
_ts_decorate([
    (0, _swagger.ApiProperty)({
        description: 'Số trang',
        example: 1,
        default: 1,
        required: false
    }),
    (0, _classvalidator.IsOptional)(),
    _ts_metadata("design:type", Number)
], ZaloAutomationQueryDto.prototype, "page", void 0);
_ts_decorate([
    (0, _swagger.ApiProperty)({
        description: 'Số lượng tự động hóa trên mỗi trang',
        example: 10,
        default: 10,
        required: false
    }),
    (0, _classvalidator.IsOptional)(),
    _ts_metadata("design:type", Number)
], ZaloAutomationQueryDto.prototype, "limit", void 0);

//# sourceMappingURL=zalo-automation.dto.js.map