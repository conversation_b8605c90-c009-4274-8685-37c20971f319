"use strict";
Object.defineProperty(exports, "__esModule", {
    value: true
});
Object.defineProperty(exports, "SmsService", {
    enumerable: true,
    get: function() {
        return SmsService;
    }
});
const _common = require("@nestjs/common");
const _config = require("@nestjs/config");
const _smsproviderfactoryservice = require("./sms-provider-factory.service");
function _ts_decorate(decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for(var i = decorators.length - 1; i >= 0; i--)if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
}
function _ts_metadata(k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
}
let SmsService = class SmsService {
    /**
   * Envoie un SMS à un numéro de téléphone
   * @param phoneNumber Numéro de téléphone du destinataire
   * @param message Contenu du message
   * @param options Options supplémentaires
   * @returns Promesse contenant l'ID du message ou une erreur
   */ async sendSms(phoneNumber, message, options) {
        const providerType = options?.providerType || this.defaultProviderType;
        try {
            this.logger.debug(`Envoi d'un SMS à ${phoneNumber} via ${providerType}`);
            const provider = this.smsProviderFactory.createProvider(providerType);
            // Supprimer providerType des options pour éviter les conflits
            const { providerType: _, ...providerOptions } = options || {};
            return await provider.sendSms(phoneNumber, message, providerOptions);
        } catch (error) {
            this.logger.error(`Erreur lors de l'envoi du SMS à ${phoneNumber}: ${error.message}`, error.stack);
            return {
                success: false,
                errorMessage: error.message || 'Erreur inconnue'
            };
        }
    }
    /**
   * Envoie un SMS à plusieurs numéros de téléphone
   * @param phoneNumbers Liste des numéros de téléphone des destinataires
   * @param message Contenu du message
   * @param options Options supplémentaires
   * @returns Promesse contenant les résultats pour chaque destinataire
   */ async sendBulkSms(phoneNumbers, message, options) {
        const providerType = options?.providerType || this.defaultProviderType;
        try {
            this.logger.debug(`Envoi de SMS en masse à ${phoneNumbers.length} destinataires via ${providerType}`);
            const provider = this.smsProviderFactory.createProvider(providerType);
            // Supprimer providerType des options pour éviter les conflits
            const { providerType: _, ...providerOptions } = options || {};
            return await provider.sendBulkSms(phoneNumbers, message, providerOptions);
        } catch (error) {
            this.logger.error(`Erreur lors de l'envoi des SMS en masse: ${error.message}`, error.stack);
            // En cas d'exception, tous les messages sont considérés comme échoués
            const results = phoneNumbers.map((phone)=>({
                    phoneNumber: phone,
                    success: false,
                    errorMessage: error.message || 'Erreur inconnue'
                }));
            return {
                successCount: 0,
                failureCount: phoneNumbers.length,
                results
            };
        }
    }
    /**
   * Vérifie le statut d'un message envoyé
   * @param messageId ID du message à vérifier
   * @param providerType Type de fournisseur SMS
   * @returns Promesse contenant le statut du message
   */ async checkMessageStatus(messageId, providerType = this.defaultProviderType) {
        try {
            this.logger.debug(`Vérification du statut du message ${messageId} via ${providerType}`);
            const provider = this.smsProviderFactory.createProvider(providerType);
            return await provider.checkMessageStatus(messageId);
        } catch (error) {
            this.logger.error(`Erreur lors de la vérification du statut du message ${messageId}: ${error.message}`, error.stack);
            throw error;
        }
    }
    /**
   * Envoie un SMS avec un brandname
   * @param phoneNumber Numéro de téléphone du destinataire
   * @param message Contenu du message
   * @param brandname Nom de la marque à utiliser comme expéditeur
   * @param options Options supplémentaires
   * @returns Promesse contenant l'ID du message ou une erreur
   */ async sendBrandnameSms(phoneNumber, message, brandname, options) {
        const providerType = options?.providerType || this.defaultProviderType;
        try {
            this.logger.debug(`Envoi d'un SMS brandname à ${phoneNumber} via ${providerType}`);
            const provider = this.smsProviderFactory.createProvider(providerType);
            // Supprimer providerType des options pour éviter les conflits
            const { providerType: _, ...providerOptions } = options || {};
            return await provider.sendBrandnameSms(phoneNumber, message, brandname, providerOptions);
        } catch (error) {
            this.logger.error(`Erreur lors de l'envoi du SMS brandname à ${phoneNumber}: ${error.message}`, error.stack);
            return {
                success: false,
                errorMessage: error.message || 'Erreur inconnue'
            };
        }
    }
    /**
   * Envoie un SMS OTP (One-Time Password)
   * @param phoneNumber Numéro de téléphone du destinataire
   * @param otpCode Code OTP à envoyer
   * @param options Options supplémentaires
   * @returns Promesse contenant l'ID du message ou une erreur
   */ async sendOtp(phoneNumber, otpCode, options) {
        const providerType = options?.providerType || this.defaultProviderType;
        try {
            this.logger.debug(`Envoi d'un code OTP à ${phoneNumber} via ${providerType}`);
            const provider = this.smsProviderFactory.createProvider(providerType);
            // Supprimer providerType des options pour éviter les conflits
            const { providerType: _, ...providerOptions } = options || {};
            return await provider.sendOtp(phoneNumber, otpCode, providerOptions);
        } catch (error) {
            this.logger.error(`Erreur lors de l'envoi du code OTP à ${phoneNumber}: ${error.message}`, error.stack);
            return {
                success: false,
                errorMessage: error.message || 'Erreur inconnue'
            };
        }
    }
    /**
   * Teste la connexion avec un fournisseur SMS
   * @param providerType Type de fournisseur SMS
   * @param config Configuration du fournisseur
   * @returns Promesse indiquant si la connexion est réussie
   */ async testConnection(providerType = this.defaultProviderType, config = {}) {
        try {
            this.logger.debug(`Test de connexion avec ${providerType}`);
            const provider = this.smsProviderFactory.createProvider(providerType);
            return await provider.testConnection(config);
        } catch (error) {
            this.logger.error(`Erreur lors du test de connexion avec ${providerType}: ${error.message}`, error.stack);
            return {
                success: false,
                message: error.message || 'Erreur inconnue'
            };
        }
    }
    constructor(smsProviderFactory, configService){
        this.smsProviderFactory = smsProviderFactory;
        this.configService = configService;
        this.logger = new _common.Logger(SmsService.name);
        // Charger le type de fournisseur par défaut depuis la configuration
        const defaultProvider = this.configService.get('SMS_DEFAULT_PROVIDER') || 'SPEED_SMS';
        this.defaultProviderType = _smsproviderfactoryservice.SmsProviderType[defaultProvider] || _smsproviderfactoryservice.SmsProviderType.SPEED_SMS;
        this.logger.log(`Fournisseur SMS par défaut: ${this.defaultProviderType}`);
    }
};
SmsService = _ts_decorate([
    (0, _common.Injectable)(),
    _ts_metadata("design:type", Function),
    _ts_metadata("design:paramtypes", [
        typeof _smsproviderfactoryservice.SmsProviderFactory === "undefined" ? Object : _smsproviderfactoryservice.SmsProviderFactory,
        typeof _config.ConfigService === "undefined" ? Object : _config.ConfigService
    ])
], SmsService);

//# sourceMappingURL=sms.service.js.map