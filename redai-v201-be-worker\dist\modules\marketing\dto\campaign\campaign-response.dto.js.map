{"version": 3, "sources": ["../../../../../src/modules/marketing/dto/campaign/campaign-response.dto.ts"], "sourcesContent": ["import { ApiProperty } from '@nestjs/swagger';\r\nimport { CampaignPlatform } from './create-campaign.dto';\r\nimport { CampaignServerDto } from './campaign-server.dto';\r\nimport { CampaignStatus } from './update-campaign.dto';\r\n\r\n/**\r\n * DTO cho thống kê campaign\r\n */\r\nexport class CampaignStatsDto {\r\n  /**\r\n   * Tổng số đối tượng\r\n   * @example 100\r\n   */\r\n  @ApiProperty({\r\n    description: 'Tổng số đối tượng',\r\n    example: 100,\r\n  })\r\n  totalRecipients: number;\r\n\r\n  /**\r\n   * Số lượng đã gửi\r\n   * @example 80\r\n   */\r\n  @ApiProperty({\r\n    description: 'Số lượng đã gửi',\r\n    example: 80,\r\n  })\r\n  sent: number;\r\n\r\n  /**\r\n   * Số lượng đã nhận\r\n   * @example 75\r\n   */\r\n  @ApiProperty({\r\n    description: 'Số lượng đã nhận',\r\n    example: 75,\r\n  })\r\n  delivered: number;\r\n\r\n  /**\r\n   * Số lượng đã mở (chỉ áp dụng cho email)\r\n   * @example 50\r\n   */\r\n  @ApiProperty({\r\n    description: 'Số lượng đã mở (chỉ áp dụng cho email)',\r\n    example: 50,\r\n  })\r\n  opened: number;\r\n\r\n  /**\r\n   * Số lượng đã nhấp (chỉ áp dụng cho email)\r\n   * @example 20\r\n   */\r\n  @ApiProperty({\r\n    description: 'Số lượng đã nhấp (chỉ áp dụng cho email)',\r\n    example: 20,\r\n  })\r\n  clicked: number;\r\n\r\n  /**\r\n   * Số lượng thất bại\r\n   * @example 5\r\n   */\r\n  @ApiProperty({\r\n    description: 'Số lượng thất bại',\r\n    example: 5,\r\n  })\r\n  failed: number;\r\n}\r\n\r\n/**\r\n * DTO cho phản hồi thông tin campaign\r\n */\r\nexport class CampaignResponseDto {\r\n  /**\r\n   * ID của campaign\r\n   * @example 1\r\n   */\r\n  @ApiProperty({\r\n    description: 'ID của campaign',\r\n    example: 1,\r\n  })\r\n  id: number;\r\n\r\n  /**\r\n   * Tiêu đề chiến dịch\r\n   * @example \"Khuyến mãi tháng 5\"\r\n   */\r\n  @ApiProperty({\r\n    description: 'Tiêu đề chiến dịch',\r\n    example: 'Khuyến mãi tháng 5',\r\n  })\r\n  title: string;\r\n\r\n  /**\r\n   * Mô tả chiến dịch\r\n   * @example \"Chiến dịch khuyến mãi dành cho khách hàng VIP\"\r\n   */\r\n  @ApiProperty({\r\n    description: 'Mô tả chiến dịch',\r\n    example: 'Chiến dịch khuyến mãi dành cho khách hàng VIP',\r\n  })\r\n  description: string;\r\n\r\n  /**\r\n   * Nền tảng gửi\r\n   * @example \"email\"\r\n   */\r\n  @ApiProperty({\r\n    description: 'Nền tảng gửi',\r\n    enum: CampaignPlatform,\r\n    example: CampaignPlatform.EMAIL,\r\n  })\r\n  platform: CampaignPlatform;\r\n\r\n  /**\r\n   * Nội dung chiến dịch\r\n   * @example \"<p>Xin chào quý khách,</p><p>Chúng tôi xin gửi đến quý khách chương trình khuyến mãi...</p>\"\r\n   */\r\n  @ApiProperty({\r\n    description: 'Nội dung chiến dịch',\r\n    example:\r\n      '<p>Xin chào quý khách,</p><p>Chúng tôi xin gửi đến quý khách chương trình khuyến mãi...</p>',\r\n  })\r\n  content: string;\r\n\r\n  /**\r\n   * Thông tin máy chủ gửi\r\n   */\r\n  @ApiProperty({\r\n    description: 'Thông tin máy chủ gửi',\r\n    type: CampaignServerDto,\r\n  })\r\n  server: CampaignServerDto;\r\n\r\n  /**\r\n   * Thời gian dự kiến gửi chiến dịch (Unix timestamp)\r\n   * @example 1619171200\r\n   */\r\n  @ApiProperty({\r\n    description: 'Thời gian dự kiến gửi chiến dịch (Unix timestamp)',\r\n    example: 1619171200,\r\n  })\r\n  scheduledAt: number;\r\n\r\n  /**\r\n   * Tiêu đề email (chỉ áp dụng cho chiến dịch email)\r\n   * @example \"Khuyến mãi đặc biệt dành cho bạn\"\r\n   */\r\n  @ApiProperty({\r\n    description: 'Tiêu đề email (chỉ áp dụng cho chiến dịch email)',\r\n    example: 'Khuyến mãi đặc biệt dành cho bạn',\r\n  })\r\n  subject: string;\r\n\r\n  /**\r\n   * Trạng thái chiến dịch\r\n   * @example \"draft\"\r\n   */\r\n  @ApiProperty({\r\n    description: 'Trạng thái chiến dịch',\r\n    enum: CampaignStatus,\r\n    example: CampaignStatus.DRAFT,\r\n  })\r\n  status: CampaignStatus;\r\n\r\n  /**\r\n   * Thời gian tạo (Unix timestamp)\r\n   * @example 1619171200\r\n   */\r\n  @ApiProperty({\r\n    description: 'Thời gian tạo (Unix timestamp)',\r\n    example: 1619171200,\r\n  })\r\n  createdAt: number;\r\n\r\n  /**\r\n   * Thời gian cập nhật (Unix timestamp)\r\n   * @example 1619171200\r\n   */\r\n  @ApiProperty({\r\n    description: 'Thời gian cập nhật (Unix timestamp)',\r\n    example: 1619171200,\r\n  })\r\n  updatedAt: number;\r\n\r\n  /**\r\n   * Thống kê chiến dịch\r\n   */\r\n  @ApiProperty({\r\n    description: 'Thống kê chiến dịch',\r\n    type: CampaignStatsDto,\r\n    required: false,\r\n  })\r\n  stats?: CampaignStatsDto;\r\n}\r\n"], "names": ["CampaignResponseDto", "CampaignStatsDto", "description", "example", "enum", "CampaignPlatform", "EMAIL", "type", "CampaignServerDto", "CampaignStatus", "DRAFT", "required"], "mappings": ";;;;;;;;;;;QAyEaA;eAAAA;;QAjEAC;eAAAA;;;yBARe;mCACK;mCACC;mCACH;;;;;;;;;;AAKxB,IAAA,AAAMA,mBAAN,MAAMA;AA4Db;;;QAtDIC,aAAa;QACbC,SAAS;;;;;;QASTD,aAAa;QACbC,SAAS;;;;;;QASTD,aAAa;QACbC,SAAS;;;;;;QASTD,aAAa;QACbC,SAAS;;;;;;QASTD,aAAa;QACbC,SAAS;;;;;;QASTD,aAAa;QACbC,SAAS;;;;AAQN,IAAA,AAAMH,sBAAN,MAAMA;AA0Hb;;;QApHIE,aAAa;QACbC,SAAS;;;;;;QASTD,aAAa;QACbC,SAAS;;;;;;QASTD,aAAa;QACbC,SAAS;;;;;;QASTD,aAAa;QACbE,MAAMC,mCAAgB;QACtBF,SAASE,mCAAgB,CAACC,KAAK;;;;;;QAS/BJ,aAAa;QACbC,SACE;;;;;;QAQFD,aAAa;QACbK,MAAMC,oCAAiB;;;;;;QASvBN,aAAa;QACbC,SAAS;;;;;;QASTD,aAAa;QACbC,SAAS;;;;;;QASTD,aAAa;QACbE,MAAMK,iCAAc;QACpBN,SAASM,iCAAc,CAACC,KAAK;;;;;;QAS7BR,aAAa;QACbC,SAAS;;;;;;QASTD,aAAa;QACbC,SAAS;;;;;;QAQTD,aAAa;QACbK,MAAMN;QACNU,UAAU"}