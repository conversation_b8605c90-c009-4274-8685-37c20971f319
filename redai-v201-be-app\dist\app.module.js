"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AppModule = void 0;
const common_1 = require("@nestjs/common");
const data_module_1 = require("./modules/data/data.module");
const database_module_1 = require("./shared/database/database.module");
const services_module_1 = require("./shared/services/services.module");
const agent_module_1 = require("./modules/agent/agent.module");
const auth_module_1 = require("./modules/auth/auth.module");
const user_module_1 = require("./modules/user/user.module");
const _config_1 = require("./config");
const subscription_module_1 = require("./modules/subscription/subscription.module");
const affiliate_module_1 = require("./modules/affiliate/affiliate.module");
const marketing_user_module_1 = require("./modules/marketing/user/marketing-user.module");
const marketplace_module_1 = require("./modules/marketplace/marketplace.module");
const integration_module_1 = require("./modules/integration/integration.module");
const helper_module_1 = require("./common/helpers/helper.module");
const employee_module_1 = require("./modules/employee/employee.module");
const knowledge_files_module_1 = require("./modules/data/knowledge-files/knowledge-files.module");
const common_2 = require("./common");
const common_module_1 = require("./modules/common/common.module");
const system_configuration_1 = require("./modules/system-configuration");
const blog_module_1 = require("./modules/blog/blog.module");
const r_point_module_1 = require("./modules/r-point/r-point.module");
const google_module_1 = require("./modules/google/google.module");
const business_module_1 = require("./modules/business/business.module");
const tools_module_1 = require("./modules/tools/tools.module");
const tools_build_in_module_1 = require("./modules/tools-build-in/tools-build-in.module");
const queue_module_1 = require("./shared/queue/queue.module");
const chat_module_1 = require("./modules/chat/chat.module");
const test_module_1 = require("./modules/test/test.module");
const rule_contract_module_1 = require("./modules/rule-contract/rule-contract.module");
const models_module_1 = require("./modules/models/models.module");
let AppModule = class AppModule {
    configure(consumer) {
        consumer.apply(common_2.RequestLoggerMiddleware).forRoutes('*');
    }
};
exports.AppModule = AppModule;
exports.AppModule = AppModule = __decorate([
    (0, common_1.Module)({
        imports: [
            _config_1.ConfigModule,
            database_module_1.DatabaseModule,
            services_module_1.ServicesModule,
            helper_module_1.HelperModule,
            common_2.CommonModule,
            common_module_1.CommonModule,
            queue_module_1.QueueModule,
            data_module_1.DataModule,
            agent_module_1.AgentModule,
            user_module_1.UserModule,
            auth_module_1.AuthModule,
            affiliate_module_1.AffiliateModule,
            marketing_user_module_1.MarketingUserModule,
            marketplace_module_1.MarketplaceModule,
            subscription_module_1.SubscriptionModule,
            integration_module_1.IntegrationModule,
            employee_module_1.EmployeeModule,
            knowledge_files_module_1.KnowledgeFilesModule,
            employee_module_1.EmployeeModule,
            system_configuration_1.SystemConfigurationModule,
            blog_module_1.BlogModule,
            r_point_module_1.RPointModule,
            google_module_1.GoogleModule,
            business_module_1.BusinessModule,
            tools_module_1.ToolsModule,
            tools_build_in_module_1.ToolsBuildInModule,
            chat_module_1.ChatModule,
            test_module_1.TestModule,
            rule_contract_module_1.RuleContractModule,
            models_module_1.ModelsModule,
        ],
    })
], AppModule);
//# sourceMappingURL=app.module.js.map