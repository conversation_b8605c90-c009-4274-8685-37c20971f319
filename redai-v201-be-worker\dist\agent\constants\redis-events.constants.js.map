{"version": 3, "sources": ["../../../src/agent/constants/redis-events.constants.ts"], "sourcesContent": ["/**\n * Redis Event Patterns for Pub/Sub Communication (Agent Module)\n * \n * IMPORTANT: These constants must be kept identical to the ones in redai-v201-be-app\n * to maintain consistency across separate codebases.\n * \n * These constants define the event patterns used for Redis pub/sub communication\n * between the backend API and worker services.\n */\n\n/**\n * Event patterns for Redis pub/sub communication between backend and worker\n */\nexport const REDIS_EVENTS = {\n  /**\n   * Trigger event to start processing a run\n   * Published by: Backend API (Chat Module)\n   * Consumed by: Worker Service (Agent Module)\n   */\n  RUN_TRIGGER: 'run.trigger',\n\n  /**\n   * Cancel event to stop processing a run\n   * Published by: Backend API (Chat Module)\n   * Consumed by: Worker Service (Agent Module)\n   */\n  RUN_CANCEL: 'run.cancel',\n\n  /**\n   * Status update event for run progress\n   * Published by: Worker Service (Agent Module)\n   * Consumed by: Backend API (optional)\n   */\n  RUN_STATUS_UPDATE: 'run.status.update',\n\n  /**\n   * Completion event when run finishes\n   * Published by: Worker Service (Agent Module)\n   * Consumed by: Backend API (optional)\n   */\n  RUN_COMPLETED: 'run.completed',\n\n  /**\n   * Error event when run fails\n   * Published by: Worker Service (Agent Module)\n   * Consumed by: Backend API (optional)\n   */\n  RUN_ERROR: 'run.error',\n} as const;\n\n/**\n * Type for Redis event patterns\n */\nexport type RedisEventPattern = typeof REDIS_EVENTS[keyof typeof REDIS_EVENTS];\n\n/**\n * Base interface for all Redis events\n */\nexport interface BaseRedisEvent {\n  eventType: RedisEventPattern;\n  publishedAt: number;\n  version?: string;\n}\n\n/**\n * Interface for run trigger event payload\n */\nexport interface RunTriggerEvent extends BaseRedisEvent {\n  eventType: typeof REDIS_EVENTS.RUN_TRIGGER;\n  runId: string;\n  threadId: string; // LangGraph thread ID for processing\n  sessionId: string; // Frontend-generated session ID for stream management\n  agentId: string;\n  userId: number;\n  jwt: string; // JWT token for authenticated API calls\n  timestamp: number;\n  priority?: 'high' | 'medium' | 'low';\n}\n\n/**\n * Interface for run cancel event payload\n */\nexport interface RunCancelEvent extends BaseRedisEvent {\n  eventType: typeof REDIS_EVENTS.RUN_CANCEL;\n  threadId: string; // LangGraph thread ID for cancellation\n  runId?: string; // Optional, for logging purposes only\n  reason: string;\n  timestamp: number;\n}\n\n/**\n * Interface for run status update event payload\n */\nexport interface RunStatusUpdateEvent extends BaseRedisEvent {\n  eventType: typeof REDIS_EVENTS.RUN_STATUS_UPDATE;\n  runId: string;\n  status: string;\n  timestamp: number;\n  metadata?: any;\n}\n\n/**\n * Interface for run completed event payload\n */\nexport interface RunCompletedEvent extends BaseRedisEvent {\n  eventType: typeof REDIS_EVENTS.RUN_COMPLETED;\n  runId: string;\n  result: any;\n  timestamp: number;\n  duration?: number;\n}\n\n/**\n * Interface for run error event payload\n */\nexport interface RunErrorEvent extends BaseRedisEvent {\n  eventType: typeof REDIS_EVENTS.RUN_ERROR;\n  runId: string;\n  error: {\n    message: string;\n    code?: string;\n    stack?: string;\n  };\n  timestamp: number;\n}\n\n/**\n * Union type for all Redis event payloads\n */\nexport type RedisEventPayload = \n  | RunTriggerEvent\n  | RunCancelEvent\n  | RunStatusUpdateEvent\n  | RunCompletedEvent\n  | RunErrorEvent;\n"], "names": ["REDIS_EVENTS", "RUN_TRIGGER", "RUN_CANCEL", "RUN_STATUS_UPDATE", "RUN_COMPLETED", "RUN_ERROR"], "mappings": "AAAA;;;;;;;;CAQC,GAED;;CAEC;;;;+BACYA;;;eAAAA;;;AAAN,MAAMA,eAAe;IAC1B;;;;GAIC,GACDC,aAAa;IAEb;;;;GAIC,GACDC,YAAY;IAEZ;;;;GAIC,GACDC,mBAAmB;IAEnB;;;;GAIC,GACDC,eAAe;IAEf;;;;GAIC,GACDC,WAAW;AACb"}