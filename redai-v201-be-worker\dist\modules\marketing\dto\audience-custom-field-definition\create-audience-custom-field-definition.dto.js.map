{"version": 3, "sources": ["../../../../../src/modules/marketing/dto/audience-custom-field-definition/create-audience-custom-field-definition.dto.ts"], "sourcesContent": ["import { ApiProperty } from '@nestjs/swagger';\r\nimport {\r\n  IsEnum,\r\n  IsNotEmpty,\r\n  IsOptional,\r\n  IsString,\r\n  Matches,\r\n} from 'class-validator';\r\n\r\n/**\r\n * Enum cho các kiểu dữ liệu của trường tùy chỉnh\r\n */\r\nexport enum CustomFieldDataType {\r\n  STRING = 'string',\r\n  INTEGER = 'integer',\r\n  DATE = 'date',\r\n  BOOLEAN = 'boolean',\r\n  JSON = 'json',\r\n}\r\n\r\n/**\r\n * DTO cho việc tạo trường tùy chỉnh\r\n */\r\nexport class CreateAudienceCustomFieldDefinitionDto {\r\n  /**\r\n   * Định danh duy nhất cho trường tùy chỉnh (chữ thường, không dấu cách)\r\n   * @example \"customer_address\"\r\n   */\r\n  @ApiProperty({\r\n    description:\r\n      'Định danh duy nhất cho trường tùy chỉnh (chữ thường, không dấu cách)',\r\n    example: 'customer_address',\r\n  })\r\n  @IsNotEmpty({ message: '<PERSON><PERSON><PERSON> danh không được để trống' })\r\n  @IsString({ message: 'Định danh phải là chuỗi' })\r\n  @Matches(/^[a-z0-9_]+$/, {\r\n    message: 'Định danh chỉ được chứa chữ thường, số và dấu gạch dưới',\r\n  })\r\n  fieldKey: string;\r\n\r\n  /**\r\n   * Tên hiển thị thân thiện với người dùng\r\n   * @example \"Địa chỉ khách hàng\"\r\n   */\r\n  @ApiProperty({\r\n    description: 'Tên hiển thị thân thiện với người dùng',\r\n    example: 'Địa chỉ khách hàng',\r\n  })\r\n  @IsNotEmpty({ message: 'Tên hiển thị không được để trống' })\r\n  @IsString({ message: 'Tên hiển thị phải là chuỗi' })\r\n  displayName: string;\r\n\r\n  /**\r\n   * Kiểu dữ liệu: string, integer, date, boolean, json\r\n   * @example \"string\"\r\n   */\r\n  @ApiProperty({\r\n    description: 'Kiểu dữ liệu',\r\n    enum: CustomFieldDataType,\r\n    example: CustomFieldDataType.STRING,\r\n  })\r\n  @IsNotEmpty({ message: 'Kiểu dữ liệu không được để trống' })\r\n  @IsEnum(CustomFieldDataType, {\r\n    message: `Kiểu dữ liệu phải là một trong các giá trị: ${Object.values(CustomFieldDataType).join(', ')}`,\r\n  })\r\n  dataType: CustomFieldDataType;\r\n\r\n  /**\r\n   * Mô tả chi tiết hoặc ghi chú về trường tùy chỉnh\r\n   * @example \"Địa chỉ liên hệ của khách hàng\"\r\n   */\r\n  @ApiProperty({\r\n    description: 'Mô tả chi tiết hoặc ghi chú về trường tùy chỉnh',\r\n    example: 'Địa chỉ liên hệ của khách hàng',\r\n    required: false,\r\n  })\r\n  @IsOptional()\r\n  @IsString({ message: 'Mô tả phải là chuỗi' })\r\n  description?: string;\r\n}\r\n"], "names": ["CreateAudienceCustomFieldDefinitionDto", "CustomFieldDataType", "description", "example", "message", "enum", "Object", "values", "join", "required"], "mappings": ";;;;;;;;;;;QAuBaA;eAAAA;;QAXDC;eAAAA;;;yBAZgB;gCAOrB;;;;;;;;;;AAKA,IAAA,AAAKA,6CAAAA;;;;;;WAAAA;;AAWL,IAAA,AAAMD,yCAAN,MAAMA;AAwDb;;;QAlDIE,aACE;QACFC,SAAS;;;QAEGC,SAAS;;;QACXA,SAAS;;;QAEnBA,SAAS;;;;;;QASTF,aAAa;QACbC,SAAS;;;QAEGC,SAAS;;;QACXA,SAAS;;;;;;QAQnBF,aAAa;QACbG,MAAMJ;QACNE,OAAO;;;QAEKC,SAAS;;;QAErBA,SAAS,CAAC,4CAA4C,EAAEE,OAAOC,MAAM,CAACN,qBAAqBO,IAAI,CAAC,OAAO;;;;;;QASvGN,aAAa;QACbC,SAAS;QACTM,UAAU;;;;QAGAL,SAAS"}