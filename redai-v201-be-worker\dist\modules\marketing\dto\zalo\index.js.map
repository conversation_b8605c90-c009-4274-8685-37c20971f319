{"version": 3, "sources": ["../../../../../src/modules/marketing/dto/zalo/index.ts"], "sourcesContent": ["export * from './connect-official-account.dto';\r\nexport * from './official-account-response.dto';\r\nexport * from './follower-response.dto';\r\nexport * from './follower-query.dto';\r\nexport * from './message-request.dto';\r\nexport * from './message-response.dto';\r\nexport * from './message-query.dto';\r\nexport * from './tag-request.dto';\r\n\r\n// ZNS DTOs\r\nexport * from './zns-template-response.dto';\r\nexport * from './zns-template-query.dto';\r\nexport * from './register-zns-template.dto';\r\nexport * from './send-zns-message.dto';\r\nexport * from './zns-message-response.dto';\r\nexport * from './zns-message-query.dto';\r\n\r\n// Marketing Integration DTOs\r\nexport * from './zalo-segment.dto';\r\nexport * from './zalo-campaign.dto';\r\nexport * from './zalo-campaign-log-query.dto';\r\nexport * from './zalo-campaign-log-response.dto';\r\nexport * from './zalo-automation.dto';\r\nexport * from './zalo-automation-log-query.dto';\r\nexport * from './zalo-automation-log-response.dto';\r\n"], "names": [], "mappings": ";;;;qBAAc;qBACA;qBACA;qBACA;qBACA;qBACA;qBACA;qBACA;qBAGA;qBACA;qBACA;qBACA;qBACA;qBACA;qBAGA;qBACA;qBACA;qBACA;qBACA;qBACA;qBACA"}