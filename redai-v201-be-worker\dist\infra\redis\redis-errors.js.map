{"version": 3, "sources": ["../../../src/infra/redis/redis-errors.ts"], "sourcesContent": ["/**\n * Custom Redis Error Classes for Enhanced Error Handling\n * \n * These error classes provide structured error information for different\n * types of Redis failures, enabling better error handling and logging.\n */\n\nexport enum RedisErrorCode {\n  CONNECTION_FAILED = 'REDIS_CONNECTION_FAILED',\n  CONNECTION_TIMEOUT = 'REDIS_CONNECTION_TIMEOUT',\n  COMMAND_TIMEOUT = 'REDIS_COMMAND_TIMEOUT',\n  AUTHENTICATION_FAILED = 'REDIS_AUTH_FAILED',\n  PERMISSION_DENIED = 'REDIS_PERMISSION_DENIED',\n  INVALID_COMMAND = 'REDIS_INVALID_COMMAND',\n  MEMORY_FULL = 'REDIS_MEMORY_FULL',\n  SERVER_ERROR = 'REDIS_SERVER_ERROR',\n  NETWORK_ERROR = 'REDIS_NETWORK_ERROR',\n  CIRCUIT_BREAKER_OPEN = 'REDIS_CIRCUIT_BREAKER_OPEN',\n  RETRY_EXHAUSTED = 'REDIS_RETRY_EXHAUSTED',\n  STREAM_ERROR = 'REDIS_STREAM_ERROR',\n  PUBSUB_ERROR = 'REDIS_PUBSUB_ERROR',\n  UNKNOWN_ERROR = 'REDIS_UNKNOWN_ERROR',\n}\n\nexport interface RedisErrorContext {\n  operation: string;\n  stream?: string;\n  channel?: string;\n  data?: any;\n  attempt?: number;\n  maxAttempts?: number;\n  duration?: number;\n  timestamp: number;\n}\n\n/**\n * Base Redis Error Class\n */\nexport class RedisError extends Error {\n  public readonly code: RedisErrorCode;\n  public readonly context: RedisErrorContext;\n  public readonly originalError?: Error;\n  public readonly isRetryable: boolean;\n\n  constructor(\n    code: RedisErrorCode,\n    message: string,\n    context: RedisErrorContext,\n    originalError?: Error,\n    isRetryable: boolean = false\n  ) {\n    super(message);\n    this.name = 'RedisError';\n    this.code = code;\n    this.context = context;\n    this.originalError = originalError;\n    this.isRetryable = isRetryable;\n\n    // Maintain proper stack trace\n    if (Error.captureStackTrace) {\n      Error.captureStackTrace(this, RedisError);\n    }\n  }\n\n  /**\n   * Get structured error information for logging\n   */\n  toLogObject(): Record<string, any> {\n    return {\n      errorCode: this.code,\n      message: this.message,\n      context: this.context,\n      isRetryable: this.isRetryable,\n      originalError: this.originalError ? {\n        name: this.originalError.name,\n        message: this.originalError.message,\n        stack: this.originalError.stack,\n      } : undefined,\n      timestamp: new Date().toISOString(),\n    };\n  }\n}\n\n/**\n * Connection-related Redis errors\n */\nexport class RedisConnectionError extends RedisError {\n  constructor(message: string, context: RedisErrorContext, originalError?: Error) {\n    super(RedisErrorCode.CONNECTION_FAILED, message, context, originalError, true);\n    this.name = 'RedisConnectionError';\n  }\n}\n\n/**\n * Timeout-related Redis errors\n */\nexport class RedisTimeoutError extends RedisError {\n  constructor(message: string, context: RedisErrorContext, originalError?: Error) {\n    super(RedisErrorCode.COMMAND_TIMEOUT, message, context, originalError, true);\n    this.name = 'RedisTimeoutError';\n  }\n}\n\n/**\n * Stream operation errors\n */\nexport class RedisStreamError extends RedisError {\n  constructor(message: string, context: RedisErrorContext, originalError?: Error) {\n    super(RedisErrorCode.STREAM_ERROR, message, context, originalError, true);\n    this.name = 'RedisStreamError';\n  }\n}\n\n/**\n * Pub/Sub operation errors\n */\nexport class RedisPubSubError extends RedisError {\n  constructor(message: string, context: RedisErrorContext, originalError?: Error) {\n    super(RedisErrorCode.PUBSUB_ERROR, message, context, originalError, true);\n    this.name = 'RedisPubSubError';\n  }\n}\n\n/**\n * Circuit breaker errors\n */\nexport class RedisCircuitBreakerError extends RedisError {\n  constructor(message: string, context: RedisErrorContext) {\n    super(RedisErrorCode.CIRCUIT_BREAKER_OPEN, message, context, undefined, false);\n    this.name = 'RedisCircuitBreakerError';\n  }\n}\n\n/**\n * Retry exhausted errors\n */\nexport class RedisRetryExhaustedError extends RedisError {\n  constructor(message: string, context: RedisErrorContext, originalError?: Error) {\n    super(RedisErrorCode.RETRY_EXHAUSTED, message, context, originalError, false);\n    this.name = 'RedisRetryExhaustedError';\n  }\n}\n\n/**\n * Error factory for creating appropriate Redis errors based on original error\n */\nexport class RedisErrorFactory {\n  static createFromError(\n    originalError: any,\n    operation: string,\n    context: Partial<RedisErrorContext> = {}\n  ): RedisError {\n    const fullContext: RedisErrorContext = {\n      operation,\n      timestamp: Date.now(),\n      ...context,\n    };\n\n    const errorMessage = originalError?.message || 'Unknown Redis error';\n    const errorCode = originalError?.code;\n\n    // Connection errors\n    if (this.isConnectionError(originalError)) {\n      return new RedisConnectionError(\n        `Redis connection failed during ${operation}: ${errorMessage}`,\n        fullContext,\n        originalError\n      );\n    }\n\n    // Timeout errors\n    if (this.isTimeoutError(originalError)) {\n      return new RedisTimeoutError(\n        `Redis operation timed out during ${operation}: ${errorMessage}`,\n        fullContext,\n        originalError\n      );\n    }\n\n    // Stream errors\n    if (operation.includes('XADD') || operation.includes('XREAD')) {\n      return new RedisStreamError(\n        `Redis stream operation failed during ${operation}: ${errorMessage}`,\n        fullContext,\n        originalError\n      );\n    }\n\n    // Pub/Sub errors\n    if (operation.includes('PUBLISH') || operation.includes('SUBSCRIBE')) {\n      return new RedisPubSubError(\n        `Redis pub/sub operation failed during ${operation}: ${errorMessage}`,\n        fullContext,\n        originalError\n      );\n    }\n\n    // Circuit breaker errors\n    if (errorMessage.includes('circuit breaker')) {\n      return new RedisCircuitBreakerError(\n        `Redis circuit breaker is open for ${operation}`,\n        fullContext\n      );\n    }\n\n    // Default to generic Redis error\n    return new RedisError(\n      RedisErrorCode.UNKNOWN_ERROR,\n      `Redis operation failed during ${operation}: ${errorMessage}`,\n      fullContext,\n      originalError,\n      this.isRetryableError(originalError)\n    );\n  }\n\n  private static isConnectionError(error: any): boolean {\n    const connectionErrorPatterns = [\n      'ECONNRESET',\n      'ECONNREFUSED',\n      'ENOTFOUND',\n      'EAI_AGAIN',\n      'Connection is closed',\n      'Redis connection lost',\n    ];\n\n    const errorMessage = error?.message || '';\n    const errorCode = error?.code || '';\n\n    return connectionErrorPatterns.some(pattern =>\n      errorMessage.includes(pattern) || errorCode === pattern\n    );\n  }\n\n  private static isTimeoutError(error: any): boolean {\n    const timeoutErrorPatterns = [\n      'ETIMEDOUT',\n      'timeout',\n      'Connection timeout',\n      'Command timeout',\n    ];\n\n    const errorMessage = error?.message || '';\n    const errorCode = error?.code || '';\n\n    return timeoutErrorPatterns.some(pattern =>\n      errorMessage.toLowerCase().includes(pattern.toLowerCase()) || errorCode === pattern\n    );\n  }\n\n  private static isRetryableError(error: any): boolean {\n    const retryablePatterns = [\n      'ECONNRESET',\n      'ECONNREFUSED',\n      'ETIMEDOUT',\n      'ENOTFOUND',\n      'EAI_AGAIN',\n      'EPIPE',\n      'READONLY',\n      'LOADING',\n      'BUSY',\n    ];\n\n    const errorMessage = error?.message || '';\n    const errorCode = error?.code || '';\n\n    return retryablePatterns.some(pattern =>\n      errorMessage.includes(pattern) || errorCode === pattern\n    );\n  }\n}\n"], "names": ["RedisCircuitBreakerError", "RedisConnectionError", "RedisError", "RedisErrorCode", "RedisErrorFactory", "RedisPubSubError", "RedisRetryExhaustedError", "RedisStreamError", "RedisTimeoutError", "Error", "toLogObject", "errorCode", "code", "message", "context", "isRetryable", "originalError", "name", "stack", "undefined", "timestamp", "Date", "toISOString", "constructor", "captureStackTrace", "createFromError", "operation", "fullContext", "now", "errorMessage", "isConnectionError", "isTimeoutError", "includes", "isRetryableError", "error", "connectionErrorPatterns", "some", "pattern", "timeoutErrorPatterns", "toLowerCase", "retryablePatterns"], "mappings": "AAAA;;;;;CAKC;;;;;;;;;;;QAyHYA;eAAAA;;QAxCAC;eAAAA;;QAhDAC;eAAAA;;QA/BDC;eAAAA;;QA2ICC;eAAAA;;QA9BAC;eAAAA;;QAoBAC;eAAAA;;QA9BAC;eAAAA;;QAVAC;eAAAA;;;AAzFN,IAAA,AAAKL,wCAAAA;;;;;;;;;;;;;;;WAAAA;;AA+BL,IAAA,AAAMD,aAAN,MAAMA,mBAAmBO;IA0B9B;;GAEC,GACDC,cAAmC;QACjC,OAAO;YACLC,WAAW,IAAI,CAACC,IAAI;YACpBC,SAAS,IAAI,CAACA,OAAO;YACrBC,SAAS,IAAI,CAACA,OAAO;YACrBC,aAAa,IAAI,CAACA,WAAW;YAC7BC,eAAe,IAAI,CAACA,aAAa,GAAG;gBAClCC,MAAM,IAAI,CAACD,aAAa,CAACC,IAAI;gBAC7BJ,SAAS,IAAI,CAACG,aAAa,CAACH,OAAO;gBACnCK,OAAO,IAAI,CAACF,aAAa,CAACE,KAAK;YACjC,IAAIC;YACJC,WAAW,IAAIC,OAAOC,WAAW;QACnC;IACF;IApCAC,YACEX,IAAoB,EACpBC,OAAe,EACfC,OAA0B,EAC1BE,aAAqB,EACrBD,cAAuB,KAAK,CAC5B;QACA,KAAK,CAACF;QACN,IAAI,CAACI,IAAI,GAAG;QACZ,IAAI,CAACL,IAAI,GAAGA;QACZ,IAAI,CAACE,OAAO,GAAGA;QACf,IAAI,CAACE,aAAa,GAAGA;QACrB,IAAI,CAACD,WAAW,GAAGA;QAEnB,8BAA8B;QAC9B,IAAIN,MAAMe,iBAAiB,EAAE;YAC3Bf,MAAMe,iBAAiB,CAAC,IAAI,EAAEtB;QAChC;IACF;AAmBF;AAKO,IAAA,AAAMD,uBAAN,MAAMA,6BAA6BC;IACxCqB,YAAYV,OAAe,EAAEC,OAA0B,EAAEE,aAAqB,CAAE;QAC9E,KAAK,4BAAmCH,SAASC,SAASE,eAAe;QACzE,IAAI,CAACC,IAAI,GAAG;IACd;AACF;AAKO,IAAA,AAAMT,oBAAN,MAAMA,0BAA0BN;IACrCqB,YAAYV,OAAe,EAAEC,OAA0B,EAAEE,aAAqB,CAAE;QAC9E,KAAK,0BAAiCH,SAASC,SAASE,eAAe;QACvE,IAAI,CAACC,IAAI,GAAG;IACd;AACF;AAKO,IAAA,AAAMV,mBAAN,MAAMA,yBAAyBL;IACpCqB,YAAYV,OAAe,EAAEC,OAA0B,EAAEE,aAAqB,CAAE;QAC9E,KAAK,uBAA8BH,SAASC,SAASE,eAAe;QACpE,IAAI,CAACC,IAAI,GAAG;IACd;AACF;AAKO,IAAA,AAAMZ,mBAAN,MAAMA,yBAAyBH;IACpCqB,YAAYV,OAAe,EAAEC,OAA0B,EAAEE,aAAqB,CAAE;QAC9E,KAAK,uBAA8BH,SAASC,SAASE,eAAe;QACpE,IAAI,CAACC,IAAI,GAAG;IACd;AACF;AAKO,IAAA,AAAMjB,2BAAN,MAAMA,iCAAiCE;IAC5CqB,YAAYV,OAAe,EAAEC,OAA0B,CAAE;QACvD,KAAK,+BAAsCD,SAASC,SAASK,WAAW;QACxE,IAAI,CAACF,IAAI,GAAG;IACd;AACF;AAKO,IAAA,AAAMX,2BAAN,MAAMA,iCAAiCJ;IAC5CqB,YAAYV,OAAe,EAAEC,OAA0B,EAAEE,aAAqB,CAAE;QAC9E,KAAK,0BAAiCH,SAASC,SAASE,eAAe;QACvE,IAAI,CAACC,IAAI,GAAG;IACd;AACF;AAKO,IAAA,AAAMb,oBAAN,MAAMA;IACX,OAAOqB,gBACLT,aAAkB,EAClBU,SAAiB,EACjBZ,UAAsC,CAAC,CAAC,EAC5B;QACZ,MAAMa,cAAiC;YACrCD;YACAN,WAAWC,KAAKO,GAAG;YACnB,GAAGd,OAAO;QACZ;QAEA,MAAMe,eAAeb,eAAeH,WAAW;QAC/C,MAAMF,YAAYK,eAAeJ;QAEjC,oBAAoB;QACpB,IAAI,IAAI,CAACkB,iBAAiB,CAACd,gBAAgB;YACzC,OAAO,IAAIf,qBACT,CAAC,+BAA+B,EAAEyB,UAAU,EAAE,EAAEG,cAAc,EAC9DF,aACAX;QAEJ;QAEA,iBAAiB;QACjB,IAAI,IAAI,CAACe,cAAc,CAACf,gBAAgB;YACtC,OAAO,IAAIR,kBACT,CAAC,iCAAiC,EAAEkB,UAAU,EAAE,EAAEG,cAAc,EAChEF,aACAX;QAEJ;QAEA,gBAAgB;QAChB,IAAIU,UAAUM,QAAQ,CAAC,WAAWN,UAAUM,QAAQ,CAAC,UAAU;YAC7D,OAAO,IAAIzB,iBACT,CAAC,qCAAqC,EAAEmB,UAAU,EAAE,EAAEG,cAAc,EACpEF,aACAX;QAEJ;QAEA,iBAAiB;QACjB,IAAIU,UAAUM,QAAQ,CAAC,cAAcN,UAAUM,QAAQ,CAAC,cAAc;YACpE,OAAO,IAAI3B,iBACT,CAAC,sCAAsC,EAAEqB,UAAU,EAAE,EAAEG,cAAc,EACrEF,aACAX;QAEJ;QAEA,yBAAyB;QACzB,IAAIa,aAAaG,QAAQ,CAAC,oBAAoB;YAC5C,OAAO,IAAIhC,yBACT,CAAC,kCAAkC,EAAE0B,WAAW,EAChDC;QAEJ;QAEA,iCAAiC;QACjC,OAAO,IAAIzB,kCAET,CAAC,8BAA8B,EAAEwB,UAAU,EAAE,EAAEG,cAAc,EAC7DF,aACAX,eACA,IAAI,CAACiB,gBAAgB,CAACjB;IAE1B;IAEA,OAAec,kBAAkBI,KAAU,EAAW;QACpD,MAAMC,0BAA0B;YAC9B;YACA;YACA;YACA;YACA;YACA;SACD;QAED,MAAMN,eAAeK,OAAOrB,WAAW;QACvC,MAAMF,YAAYuB,OAAOtB,QAAQ;QAEjC,OAAOuB,wBAAwBC,IAAI,CAACC,CAAAA,UAClCR,aAAaG,QAAQ,CAACK,YAAY1B,cAAc0B;IAEpD;IAEA,OAAeN,eAAeG,KAAU,EAAW;QACjD,MAAMI,uBAAuB;YAC3B;YACA;YACA;YACA;SACD;QAED,MAAMT,eAAeK,OAAOrB,WAAW;QACvC,MAAMF,YAAYuB,OAAOtB,QAAQ;QAEjC,OAAO0B,qBAAqBF,IAAI,CAACC,CAAAA,UAC/BR,aAAaU,WAAW,GAAGP,QAAQ,CAACK,QAAQE,WAAW,OAAO5B,cAAc0B;IAEhF;IAEA,OAAeJ,iBAAiBC,KAAU,EAAW;QACnD,MAAMM,oBAAoB;YACxB;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;SACD;QAED,MAAMX,eAAeK,OAAOrB,WAAW;QACvC,MAAMF,YAAYuB,OAAOtB,QAAQ;QAEjC,OAAO4B,kBAAkBJ,IAAI,CAACC,CAAAA,UAC5BR,aAAaG,QAAQ,CAACK,YAAY1B,cAAc0B;IAEpD;AACF"}